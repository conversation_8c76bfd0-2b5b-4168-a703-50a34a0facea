'use client';

import { usePathname } from 'next/navigation';
import { useState } from 'react';

type Link = { label: string; href: string; external?: boolean };

const BROWSE_COLS: Link[][] = [
  [
    { label: 'Featured products', href: '/category/featured-products' },
    { label: 'UI Kits', href: '/category/ui-kits' },
    { label: 'Coded Templates', href: '/category/coded-templates' },
    { label: 'Wireframe kits', href: '/category/wireframe-kits' },
    { label: 'Illustrations', href: '/category/illustrations' },
  ],
  [
    { label: 'Fonts', href: '/category/fonts' },
    { label: 'Presentation', href: '/category/presentation' },
    { label: 'Mockups', href: '/category/mockups' },
    { label: '3D Assets', href: '/category/3d-assets' },
    { label: 'Icon Sets', href: '/category/icons' },
  ],
  [
    { label: 'Themes & Templates', href: '/category/themes' },
    { label: 'For Figma', href: '/category/figma' },
    { label: 'For Sketch', href: '/category/sketch' },
    { label: 'For Lunacy', href: '/category/lunacy' },
    { label: 'Freebies', href: '/category/freebies' },
  ],
];

const PLATFORM: Link[] = [
  { label: 'All-Access Pass', href: '/products/all-access-pass' },
  { label: 'UI8 Design Studio', href: 'https://studio.ui8.net', external: true },
  { label: 'Become an author', href: '/authors' },
  { label: 'Affiliate program', href: '/affiliates' },
  { label: 'Terms & Licensing', href: '/terms' },
];

const SOCIALS: Link[] = [
  { label: 'Dribbble', href: 'https://dribbble.com/ui8', external: true },
  { label: 'Instagram', href: 'https://www.instagram.com/ui8net/', external: true },
  { label: 'Twitter', href: 'https://twitter.com/ui8', external: true },
  { label: 'Email', href: 'mailto:<EMAIL>', external: true },
];

export default function Footer() {
  const pathname = usePathname();
  const isAuthRoute = pathname?.startsWith('/auth');
  const [email, setEmail] = useState('');
  const [pending, setPending] = useState(false);
  const [success, setSuccess] = useState<boolean | null>(null);

  if (isAuthRoute) return null;

  const onSubscribe = async () => {
    try {
      setPending(true);
      // TODO: integrate your newsletter API endpoint
      await new Promise((r) => setTimeout(r, 700));
      setSuccess(true);
    } catch {
      setSuccess(false);
    } finally {
      setPending(false);
    }
  };

  return (
    <footer className="footer border-t border-[var(--footer-border-color)] bg-[var(--background)] text-[var(--main)]">
      <div className="footer__container container mx-auto px-4">
        {/* Back to top button */}
        <div className="pt-6 flex justify-end">
          <button
            onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
            aria-label="Back to top"
            className="footer__top inline-flex items-center justify-center rounded-md border border-[var(--button-border)] text-[var(--main)]
                       hover:bg-[var(--card-hover)] transition-colors w-10 h-10"
          >
            <svg width="20" height="20" viewBox="0 0 24 24" aria-hidden="true">
              <path
                d="M11.857 3.01l.163-.01.097.007.113.02.112.033.082.034.113.062.089.064.082.073 6 6a1 1 0 0 1-1.32 1.497l-.094-.083L13 6.415V20a1 1 0 0 1-1.993.117L11 20V6.414l-4.293 4.293a1 1 0 0 1-1.497-1.32l.083-.094 6-6 .058-.054.094-.071.071-.043.113-.053.142-.045.086-.016z"
                fill="currentColor"
              />
            </svg>
          </button>
        </div>

        <div className="footer__row grid grid-cols-1 gap-y-12 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 py-10">
          {/* Logo */}
          <div className="footer__col col-span-1">
            <a
              href="/"
              className="footer__logo block w-10 h-10 rounded-sm"
              style={{ background: 'linear-gradient(180deg, var(--highlight), var(--gradient1))' }}
              aria-label="TemplGen"
            />
          </div>

          {/* Browse (3 columns) */}
          <div className="footer__col col-span-1 xl:col-span-3">
            <div className="footer__title text-sm font-semibold mb-4 text-[var(--headline)]">Browse</div>
            <div className="footer__nav footer__nav--cols grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
              {BROWSE_COLS.map((list, i) => (
                <ul key={i} className="space-y-2 text-[13px] tracking-[-0.15px]">
                  {list.map((l) => (
                    <li key={l.href} className="leading-none h-6 flex items-center">
                      <a
                        href={l.href}
                        className="opacity-70 hover:opacity-100 transition-opacity"
                      >
                        {l.label}
                      </a>
                    </li>
                  ))}
                </ul>
              ))}
            </div>
          </div>

          {/* Platform */}
          <div className="footer__col col-span-1">
            <div className="footer__title text-sm font-semibold mb-4 text-[var(--headline)]">Platform</div>
            <ul className="footer__nav space-y-2 text-[13px] tracking-[-0.15px]">
              {PLATFORM.map((l) => (
                <li key={l.label} className="leading-none h-6 flex items-center">
                  <a
                    href={l.href}
                    target={l.external ? '_blank' : undefined}
                    rel={l.external ? 'noopener noreferrer' : undefined}
                    className="opacity-70 hover:opacity-100 transition-opacity"
                  >
                    {l.label}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          {/* Newsletter + Socials */}
          <div className="footer__col col-span-1">
            {/* Newsletter */}
            <div className="footer__subscribe">
              {success !== true && (
                <div className="footer__title text-sm font-semibold mb-3 text-[var(--headline)]">
                  Sign up for our newsletter!
                </div>
              )}

              {success !== true && (
                <div className="field">
                  <label className="field__label block text-xs text-[var(--paragraph)] mb-1">Email</label>
                  <div className="relative">
                    <input
                      type="email"
                      placeholder="<EMAIL>"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      className="field__input w-full rounded-md bg-[var(--input-background)] text-[var(--input-text)]
                                 border border-[var(--input-border-color)] px-3 py-2 text-sm outline-none
                                 focus:border-[var(--selectBox-border)]"
                    />
                    <button
                      onClick={onSubscribe}
                      disabled={pending || !email}
                      aria-label="Subscribe to newsletter"
                      className="field__action absolute right-1 top-1 h-[30px] w-[30px] inline-flex items-center justify-center
                                 rounded-md border border-[var(--button-border)]
                                 bg-[var(--button2)] text-[var(--button-text)]
                                 hover:bg-[var(--button)] transition-colors disabled:opacity-50"
                    >
                      <svg width="18" height="18" viewBox="0 0 24 24" aria-hidden="true">
                        <path
                          d="M12 0c6.627 0 12 5.373 12 12s-5.373 12-12 12S0 18.627 0 12 5.373 0 12 0zm.613 6.81l.094.083 4.4 4.4c.028.028.055.059.08.09L17.4 12a1.01 1.01 0 0 1-.011.149c-.003.017-.006.034-.009.052a.94.94 0 0 1-.015.065 1.11 1.11 0 0 1-.014.046.95.95 0 0 1-.021.059c-.007.018-.015.035-.023.052a1.02 1.02 0 0 1-.031.061.88.88 0 0 1-.021.036c-.016.026-.032.05-.05.074l-.083.094-4.4 4.4a1 1 0 0 1-1.497-1.32l.083-.094L13.985 13H7.6a1 1 0 0 1-.117-1.993L7.6 11h6.385l-2.692-2.693a1 1 0 0 1 1.32-1.497z"
                          fill="currentColor"
                        />
                      </svg>
                    </button>
                  </div>
                </div>
              )}

              {success === true && (
                <div className="mt-2 text-sm text-[var(--tertiary)]">Thanks for subscribing!</div>
              )}
              {success === false && (
                <div className="mt-2 text-sm text-red-400">Subscription failed. Please try again.</div>
              )}
            </div>

            {/* Socials */}
            <div className="footer__title text-sm font-semibold mt-6 mb-3 text-[var(--headline)]">
              Connect with us
            </div>
            <ul className="socials flex items-center gap-3">
              {SOCIALS.map((s) => (
                <li key={s.label}>
                  <a
                    href={s.href}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center justify-center w-9 h-9 rounded-md
                               border border-[var(--card-border-color)]
                               hover:bg-[var(--card-hover)] transition-colors"
                    aria-label={s.label}
                    title={s.label}
                  >
                    {/* Simple glyphs; swap with brand SVGs if desired */}
                    <span className="text-xs text-[var(--paragraph)]">{s.label[0]}</span>
                  </a>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Copyright */}
        <div className="footer__copyright border-t border-[var(--footer-border-color)] py-6 text-xs text-[var(--footer-text)]">
          © {new Date().getFullYear()}, Robot Global FZCO / UI8
        </div>
      </div>
    </footer>
  );
}

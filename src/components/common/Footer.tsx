'use client';

import { usePathname } from 'next/navigation';

export default function Footer() {
  const pathname = usePathname();
  const isAuthRoute = pathname?.startsWith('/auth');

  if (isAuthRoute) return null;

  return (
    <footer className="mt-auto border-t border-[var(--footer-border-color)] bg-[var(--background)] text-[var(--footer-text)]">
      <div className="mx-auto max-w-7xl px-4 py-8 text-sm">
        <h1 className="sr-only">footer</h1>
        <p>© {new Date().getFullYear()} TemplGen. All rights reserved.</p>
      </div>
    </footer>
  );
}

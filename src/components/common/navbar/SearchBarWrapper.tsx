"use client";

import RotatingText from "@/components/ui/RotatingTextRef";
import { cn } from "@/lib/utils";
import { AnimatePresence, motion } from "framer-motion";
import Image from "next/image";
import { useRef, useState } from "react";
import { FiX } from "react-icons/fi";

export default function SearchBarWrapper() {
  const [query, setQuery] = useState("");
  const [focused, setFocused] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  const animatedSuggestions = [
    "Search Cosmos...",
    "Explore subtle feelings...",
    "Discover blockchain ideas...",
    "Find NFTs & decentralization...",
    "Dive into melancholy thoughts...",
  ];

  const suggestions = [
    "subtle feelings of melancholy",
    "cosmos",
    "blockchain",
    "NFT",
    "decentralized",
  ].filter((s) => s.toLowerCase().includes(query.toLowerCase()));

  const selectSuggestion = (item: string) => {
    setQuery(item);
    setFocused(false);
    inputRef.current?.focus();
  };

  const onFocus = () => setFocused(true);
  const onBlur = () => setTimeout(() => setFocused(false), 150);

  return (
    <>
      {/* Overlay with smooth fade */}
      <AnimatePresence>
        {focused && (
          <motion.div
            key="overlay"
            initial={{ opacity: 0 }}
            animate={{ opacity: 0.3 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="fixed inset-0 bg-black z-40"
            onClick={() => {
              setFocused(false);
              inputRef.current?.blur();
            }}
          />
        )}
      </AnimatePresence>

      {/* Search Box Container */}
      <div
        role="combobox"
        aria-expanded={focused}
        aria-controls="search-listbox"
        aria-haspopup="listbox"
        className={cn(
          "relative w-full max-md:w-full",
          focused ? "md:max-w-xl z-50 relative" : "md:max-w-lg"
        )}
      >
        <div
          className={cn(
            "relative flex items-center rounded-[12px] px-3 py-2 border transition-all duration-300 ease-in-out bg-[var(--input-background)]",
            focused
              ? "ring-2 ring-[var(--highlight)] border-[var(--input-border-color)]"
              : "border-[var(--input-border-color)]"
          )}
          style={{ position: "relative", zIndex: 50 }}
        >
          <Image
            src="/icons/star.svg"
            alt="Star Icon"
            width={20}
            height={20}
            className="mr-2 flex-shrink-0 transition-transform duration-300 ease-in-out hover:scale-125 focus-within:scale-125"
          />
          <input
            ref={inputRef}
            type="search"
            role="searchbox"
            aria-autocomplete="list"
            aria-controls="search-listbox"
            placeholder={focused || query ? "" : " "}
            className="flex-grow bg-transparent text-[var(--input-text)] placeholder-transparent outline-none text-sm transition-colors duration-500"
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            onFocus={onFocus}
            onBlur={onBlur}
            autoComplete="off"
          />

          {/* Static 'Try' + RotatingText with fade effect */}
          {!focused && !query && (
            <div
              className="pointer-events-none absolute left-[38px] top-1/2 -translate-y-1/2 flex items-center space-x-1 text-[var(--paragraph)] text-sm select-none overflow-hidden whitespace-nowrap"
              style={{ width: "calc(100% - 38px)" }}
            >
              <span>Try</span>
              <RotatingText
                texts={animatedSuggestions}
                rotationInterval={4000}
                mainClassName="inline-block"
                staggerFrom="first"
                staggerDuration={0}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 1 }}
                animatePresenceMode="wait"
                splitBy="lines"
              />
            </div>
          )}

          {query && (
            <button
              aria-label="Clear search"
              onClick={() => {
                setQuery("");
                inputRef.current?.focus();
              }}
              className="ml-2 text-[var(--menu-color)] hover:text-[var(--highlight)]"
            >
              <FiX />
            </button>
          )}
        </div>

        {focused && suggestions.length > 0 && (
          <ul
            id="search-listbox"
            role="listbox"
            className="absolute z-50 mt-1 max-h-48 w-full overflow-auto rounded-[12px] border border-[var(--input-border-color)] bg-[var(--input-background)] shadow-lg"
          >
            {suggestions.map((item, i) => (
              <li
                key={i}
                role="option"
                tabIndex={-1}
                className="cursor-pointer px-4 py-2 text-sm text-[var(--paragraph)] hover:bg-[var(--highlight)] hover:text-[var(--active-text)]"
                onPointerDown={(e) => {
                  e.preventDefault();
                  selectSuggestion(item);
                }}
              >
                {item}
              </li>
            ))}
          </ul>
        )}
      </div>
    </>
  );
}

'use client';

import { AnimatePresence, motion } from 'framer-motion';
import { usePathname } from 'next/navigation';
import { useEffect, useState } from 'react';
import { FiMenu, FiX } from 'react-icons/fi';
import AuthButtons from './AuthButtons';
import LogoLink from './LogoLink';
import NavItems from './NavItems';
import SearchBarWrapper from './SearchBarWrapper';

export default function Navbar() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const pathname = usePathname();
  const isAuthRoute = pathname?.startsWith('/auth');

  // Close mobile menu on route change
  useEffect(() => {
    if (mobileMenuOpen) setMobileMenuOpen(false);
  }, [pathname]);

  if (isAuthRoute) {
    return null;
  }

  return (
    <header className="border-b border-[var(--border)] bg-[var(--background)] text-[var(--main)]">
      <div className="container mx-auto px-4">
        {/* Mobile */}
        <div className="flex items-center justify-between h-16 md:hidden gap-2">
          <LogoLink />
          <SearchBarWrapper />
          <button
            aria-label={mobileMenuOpen ? 'Close menu' : 'Open menu'}
            className="text-[var(--menu-color)]"
            onClick={() => setMobileMenuOpen((v) => !v)}
          >
            {mobileMenuOpen ? <FiX size={24} /> : <FiMenu size={24} />}
          </button>
        </div>

        <AnimatePresence>
          {mobileMenuOpen && (
            <motion.nav
              key="mobile-menu"
              initial={{ x: '100%', opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              exit={{ x: '100%', opacity: 0 }}
              transition={{ duration: 0.3, ease: 'easeInOut' }}
              className="fixed top-0 left-0 w-full h-screen bg-[var(--background)] z-50 m-auto overflow-y-auto p-4 flex justify-center"
              aria-label="Mobile navigation"
            >
              <div className="w-full max-w-md">
                <div className="flex justify-end mb-4">
                  <button
                    aria-label="Close menu"
                    className="text-[var(--menu-color)]"
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    <FiX size={28} />
                  </button>
                </div>
                <NavItems isMobile />
              </div>
            </motion.nav>
          )}
        </AnimatePresence>

        {/* Desktop */}
        <div className="hidden md:flex h-16 items-center justify-between gap-4">
          <div className="flex items-center gap-6 flex-1">
            <LogoLink />
            <NavItems />
          </div>
          <div className="flex-1 flex justify-center">
            <SearchBarWrapper />
          </div>
          <div className="flex items-center gap-2 flex-1 justify-end">
            <AuthButtons />
          </div>
        </div>
      </div>
    </header>
  );
}

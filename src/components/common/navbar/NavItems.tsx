'use client';

import {
  NavigationMenu,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
} from '@/components/ui/navigation-menu';

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuTrigger as DropdownTrigger,
} from '@/components/ui/dropdown-menu';

import { MdKeyboardArrowRight } from 'react-icons/md';
import { cn } from '@/lib/utils';
import { AnimatePresence, motion } from 'framer-motion';
import { BookOpenIcon, InfoIcon, LifeBuoyIcon } from 'lucide-react';
import { useState } from 'react';
import { FiArrowLeft } from 'react-icons/fi';

type NavLinkItem = {
  href: string;
  label: string;
  description?: string;
  icon?: 'BookOpenIcon' | 'LifeBuoyIcon' | 'InfoIcon';
};

type NavLink = {
  href?: string;
  label: string;
  submenu?: boolean;
  type?: 'description' | 'simple' | 'icon' | 'mega';
  items?: NavLinkItem[];
};

const CATEGORIES: { href: string; label: string; count: number }[] = [
  { href: '/category/ui-kits', label: 'UI Kits', count: 4824 },
  { href: '/category/coded-templates', label: 'Coded Templates', count: 239 },
  { href: '/category/no-code', label: 'No-code Assets', count: 347 },
  { href: '/category/illustrations', label: 'Illustrations', count: 1289 },
  { href: '/category/fonts', label: 'Fonts', count: 715 },
  { href: '/category/wireframe-kits', label: 'Wireframes', count: 161 },
  { href: '/category/presentation', label: 'Presentation', count: 482 },
  { href: '/category/mockups', label: 'Mockups', count: 723 },
  { href: '/category/3d-assets', label: '3D Assets', count: 1340 },
  { href: '/category/icons', label: 'Icon Sets', count: 1301 },
  { href: '/category/themes', label: 'Themes', count: 799 },
  { href: '/category/freebies', label: 'Freebies', count: 1968 },
];

const POPULAR_APPS: { alt: string; img: string; onClick?: () => void }[] = [
  { alt: 'Figma', img: '/img/app-icons/figma-prog.svg' },
  { alt: 'Framer', img: '/img/app-icons/framer-prog.svg' },
  { alt: 'React', img: '/img/app-icons/react-prog.svg' },
  { alt: 'HTML/CSS', img: '/img/app-icons/html-prog.svg' },
  { alt: 'Photoshop', img: '/img/app-icons/ps-prog.svg' },
  { alt: 'Blender', img: '/img/app-icons/blender-prog.svg' },
];

const navigationLinks: NavLink[] = [
  { href: '/', label: 'Home' },
  {
    label: 'About',
    submenu: true,
    type: 'icon',
    items: [
      { href: '#', label: 'Getting Started', icon: 'BookOpenIcon' },
      { href: '#', label: 'Tutorials', icon: 'LifeBuoyIcon' },
      { href: '#', label: 'About Us', icon: 'InfoIcon' },
    ],
  },
  {
    label: 'Browse',
    submenu: true,
    type: 'mega',
    // items optional for mega; we’re rendering custom sections
  },
];

const renderIcon = (icon?: NavLinkItem['icon']) => {
  switch (icon) {
    case 'BookOpenIcon':
      return <BookOpenIcon size={16} className="text-[var(--menu-color)] opacity-60" aria-hidden="true" />;
    case 'LifeBuoyIcon':
      return <LifeBuoyIcon size={16} className="text-[var(--menu-color)] opacity-60" aria-hidden="true" />;
    case 'InfoIcon':
      return <InfoIcon size={16} className="text-[var(--menu-color)] opacity-60" aria-hidden="true" />;
    default:
      return null;
  }
};

export default function NavItems({ isMobile = false }: { isMobile?: boolean }) {
  const [openDropdownIndex, setOpenDropdownIndex] = useState<number | null>(null);
  const [hoverIndex, setHoverIndex] = useState<number | null>(null);

  if (!isMobile) {
    return (
      <NavigationMenu viewport={false}>
        <NavigationMenuList className="gap-2 flex">
          {navigationLinks.map((link, index) =>
            link.submenu ? (
              <NavigationMenuItem
                key={index}
                onMouseEnter={() => setHoverIndex(index)}
                onMouseLeave={() => setHoverIndex((prev) => (prev === index ? null : prev))}
                className="relative"
              >
                {link.type !== 'mega' ? (
                  <DropdownMenu>
                    <DropdownTrigger asChild>
                      <NavigationMenuTrigger className="text-[var(--nav-item)] bg-transparent px-2 py-1.5 font-medium cursor-pointer *:[svg]:-me-0.5 *:[svg]:size-3.5 inline-flex items-center gap-1">
                        {link.label}
                      </NavigationMenuTrigger>
                    </DropdownTrigger>

                    <DropdownMenuContent
                      side="bottom"
                      align="start"
                      className={cn(
                        'bg-[var(--background)] border border-[var(--border)] p-2 min-w-[12rem]'
                      )}
                    >
                      {link.type === 'description' && (
                        <DropdownMenuGroup>
                          {link.items?.map((item, idx) => (
                            <DropdownMenuItem
                              key={idx}
                              asChild
                              className="flex flex-col items-start gap-0.5 py-2 px-3 rounded cursor-pointer"
                            >
                              <a href={item.href} className="w-full text-[var(--paragraph)]">
                                <span>{item.label}</span>
                                {item.description && (
                                  <span className="text-xs text-[var(--paragraph)] opacity-70">
                                    {item.description}
                                  </span>
                                )}
                              </a>
                            </DropdownMenuItem>
                          ))}
                        </DropdownMenuGroup>
                      )}

                      {link.type === 'simple' && (
                        <DropdownMenuGroup>
                          {link.items?.map((item, idx) => (
                            <DropdownMenuItem key={idx} asChild className="py-2 px-3 rounded cursor-pointer">
                              <a href={item.href} className="text-[var(--paragraph)] block w-full">
                                {item.label}
                              </a>
                            </DropdownMenuItem>
                          ))}
                        </DropdownMenuGroup>
                      )}

                      {link.type === 'icon' && (
                        <DropdownMenuGroup>
                          {link.items?.map((item, idx) => (
                            <DropdownMenuItem
                              key={idx}
                              asChild
                              className="flex items-center gap-2 py-2 px-3 rounded cursor-pointer"
                            >
                              <a href={item.href} className="flex items-center gap-2 w-full text-[var(--paragraph)]">
                                {item.icon && renderIcon(item.icon)}
                                {item.label}
                              </a>
                            </DropdownMenuItem>
                          ))}
                        </DropdownMenuGroup>
                      )}
                    </DropdownMenuContent>
                  </DropdownMenu>
                ) : (
                  // Mega / Browse
                  <div className="relative">
                    <button
                      className="text-[var(--nav-item)] bg-transparent px-2 py-1.5 font-medium inline-flex items-center gap-1"
                      aria-haspopup="true"
                      aria-expanded={hoverIndex === index}
                    >
                      {link.label}
                    </button>

                    <AnimatePresence>
                      {hoverIndex === index && (
                        <motion.div
                          key={`hover-panel-${index}`}
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          exit={{ opacity: 0 }}
                          transition={{ duration: 0.18, ease: 'easeOut' }}
                          className="absolute left-0 top-full mt-2 w-[900px] max-w-[95vw] z-50"
                          onMouseLeave={() => setHoverIndex(null)}
                        >
                          <div className="bg-[var(--background)] border border-[var(--border)] rounded-lg shadow-lg p-4">
                            {/* 3-column grid with fixed gaps */}
                            <div className="grid grid-cols-12 gap-x-6 gap-y-4">
                              {/* Categories (span 6) */}
                              <div className="col-span-12 md:col-span-6">
                                <div className="text-xs uppercase tracking-wide text-[var(--paragraph)] mb-3">
                                  Categories
                                </div>
                                <ul className="grid grid-cols-2 gap-x-4 gap-y-2">
                                  {CATEGORIES.map((c) => (
                                    <li key={c.href}>
                                      <a
                                        href={c.href}
                                        className="flex items-center justify-between rounded-md px-2 py-2 hover:bg-[var(--card-hover)] transition-colors"
                                      >
                                        <span className="text-[var(--main)] text-sm">{c.label}</span>
                                        <span className="text-[var(--paragraph)] text-xs opacity-80">
                                          {c.count.toLocaleString()}
                                        </span>
                                      </a>
                                    </li>
                                  ))}
                                </ul>
                              </div>

                              {/* Space / Banner (span 3) */}
                              <div className="col-span-12 md:col-span-3">
                                <div className="text-xs uppercase tracking-wide text-[var(--paragraph)] mb-3">
                                  Featured
                                </div>
                                <a
                                  href="https://studio.ui8.net"
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="block"
                                >
                                  <img
                                    src="/img/banners/studio-menu.avif"
                                    alt="UI8 Studio"
                                    className="w-full rounded-md border border-[var(--card-border-color)]"
                                  />
                                </a>
                              </div>

                              {/* Formats (span 3) */}
                              <div className="col-span-12 md:col-span-3">
                                <div className="text-xs uppercase tracking-wide text-[var(--paragraph)] mb-3">
                                  Browse by most popular formats
                                </div>
                                <ul className="grid grid-cols-3 gap-3">
                                  {POPULAR_APPS.map((app, i) => (
                                    <li key={i}>
                                      <button
                                        type="button"
                                        onClick={app.onClick}
                                        className="w-12 h-12 rounded-md bg-[var(--card-background)] border border-[var(--card-border-color)] hover:bg-[var(--card-hover)] flex items-center justify-center transition-colors"
                                        aria-label={app.alt}
                                        title={app.alt}
                                      >
                                        <img src={app.img} alt={app.alt} className="w-6 h-6" />
                                      </button>
                                    </li>
                                  ))}
                                </ul>
                              </div>
                            </div>
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </div>
                )}
              </NavigationMenuItem>
            ) : (
              <NavigationMenuItem key={index}>
                <NavigationMenuLink href={link.href} className="text-[var(--nav-item)] py-1.5 font-medium">
                  {link.label}
                </NavigationMenuLink>
              </NavigationMenuItem>
            )
          )}
        </NavigationMenuList>
      </NavigationMenu>
    );
  }

  // Mobile drilldown (Browse renders like a submenu list)
  return (
    <NavigationMenu
      className="fixed top-0 left-0 w-full h-screen z-50 bg-[var(--background)] p-4 overflow-hidden"
      role="navigation"
      aria-label="Mobile navigation"
    >
      <AnimatePresence initial={false} mode="wait">
        {openDropdownIndex === null ? (
          <motion.div
            key="main-menu"
            initial={{ x: 300, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            exit={{ x: -300, opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="flex flex-col h-full w-screen"
          >
            <header>
              <h1 className="text-[var(--main)]">Menu</h1>
            </header>
            <NavigationMenuList className="flex flex-col gap-3 w-full overflow-auto">
              {navigationLinks.map((link, i) => (
                <div key={i} className="flex flex-col w-full">
                  {link.submenu ? (
                    <button
                      onClick={() => setOpenDropdownIndex(i)}
                      aria-label={`Open submenu for ${link.label}`}
                      className="flex justify-between items-center w-full text-[var(--nav-item)] px-3 py-4 text-base font-medium rounded touch-manipulation"
                      style={{ touchAction: 'manipulation' }}
                    >
                      <span>{link.label}</span>
                      <span aria-hidden="true" style={{ fontSize: 20 }}>
                        <MdKeyboardArrowRight />
                      </span>
                    </button>
                  ) : (
                    <NavigationMenuLink
                      href={link.href}
                      tabIndex={0}
                      className="py-4 px-3 text-[var(--paragraph)] block font-medium w-full"
                    >
                      {link.label}
                    </NavigationMenuLink>
                  )}
                </div>
              ))}
            </NavigationMenuList>
          </motion.div>
        ) : (
          <motion.div
            key="submenu"
            initial={{ x: 300, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            exit={{ x: 300, opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="flex flex-col w-full h-full"
          >
            <button
              onClick={() => setOpenDropdownIndex(null)}
              aria-label="Go back to main menu"
              className="flex items-center gap-2 text-[var(--nav-item)] mb-4 px-3 py-3 font-medium rounded touch-manipulation w-full"
              style={{ touchAction: 'manipulation' }}
            >
              <FiArrowLeft size={20} aria-hidden="true" />
            </button>
            {/* Browse mobile list (Categories only for brevity) */}
            <NavigationMenuList className="flex flex-col gap-1 overflow-auto">
              {navigationLinks[openDropdownIndex].type === 'mega' ? (
                <>
                  <div className="px-3 py-2 text-xs uppercase tracking-wide text-[var(--paragraph)]">
                    Categories
                  </div>
                  {CATEGORIES.map((c) => (
                    <NavigationMenuLink
                      key={c.href}
                      href={c.href}
                      className="px-3 py-3 text-[var(--paragraph)] font-medium flex items-center justify-between w-full hover:bg-[var(--card-hover)] rounded"
                    >
                      <span className="text-[var(--main)]">{c.label}</span>
                      <span className="text-xs text-[var(--paragraph)] opacity-80">
                        {c.count.toLocaleString()}
                      </span>
                    </NavigationMenuLink>
                  ))}
                </>
              ) : (
                navigationLinks[openDropdownIndex].items?.map((item, idx) => (
                  <NavigationMenuLink
                    key={idx}
                    href={item.href}
                    tabIndex={0}
                    className="py-4 px-3 text-[var(--paragraph)] font-medium flex items-center gap-2 w-full"
                  >
                    {navigationLinks[openDropdownIndex].type === 'icon' && item.icon && renderIcon(item.icon)}
                    <div className="flex flex-col">
                      <span>{item.label}</span>
                      {item.description && (
                        <span className="text-xs text-[var(--paragraph)] opacity-70">{item.description}</span>
                      )}
                    </div>
                  </NavigationMenuLink>
                ))
              )}
            </NavigationMenuList>
          </motion.div>
        )}
      </AnimatePresence>
    </NavigationMenu>
  );
}

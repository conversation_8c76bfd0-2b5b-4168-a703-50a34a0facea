"use client";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";

export default function AuthButtons() {
  return (
    <>
      <Button
        asChild
        variant="ghost"
        size="sm"
        className="text-sm text-[var(--button-text)] hover:bg-[var(--card-hover)]"
      >
        <Link href="#">Sign In</Link>
      </Button>
      <Button
        asChild
        size="sm"
        className="text-sm bg-[var(--button)] text-[var(--button-text)]"
      >
        <Link href="#">Signup</Link>
      </Button>
    </>
  );
}

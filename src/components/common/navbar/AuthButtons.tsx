"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import Link from "next/link";

export default function AuthButtons() {
  return (
    <>
      <Button
        asChild
        variant="ghost"
        size="sm"
        className="text-sm text-[var(--button-text)] hover:bg-[var(--card-hover)]"
      >
        <Link href="/auth/login">Sign In</Link>
      </Button>
      <Button
        asChild
        size="sm"
        className="text-sm bg-[var(--button)] text-[var(--button-text)]"
      >
        <Link href="/auth/signup">Signup</Link>
      </Button>
    </>
  );
}

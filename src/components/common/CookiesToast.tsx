'use client';

import { AnimatePresence, motion } from 'framer-motion';
import { useCallback, useEffect, useState } from 'react';

type Props = {
  defaultOpen?: boolean;      // default: true
  autoHideMs?: number;        // optional: e.g., 6000 to auto-close after 6s
};

export default function CookiesToast({ defaultOpen = true, autoHideMs }: Props) {
  const [open, setOpen] = useState<boolean>(defaultOpen);

  // Optional auto-hide timer
  useEffect(() => {
    if (!open || !autoHideMs) return;
    const t = setTimeout(() => setOpen(false), autoHideMs);
    return () => clearTimeout(t);
  }, [open, autoHideMs]);

  const close = useCallback(() => setOpen(false), []);

  // Optional: close with Esc
  useEffect(() => {
    if (!open) return;
    const onKey = (e: KeyboardEvent) => {
      if (e.key === 'Escape') setOpen(false);
    };
    window.addEventListener('keydown', onKey);
    return () => window.removeEventListener('keydown', onKey);
  }, [open]);

  return (
    <AnimatePresence>
      {open && (
        <motion.div
          key="cookies-toast"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ type: 'spring', stiffness: 420, damping: 28 }}
          style={{
            position: 'fixed',
            left: 16,
            bottom: 16,
            zIndex: 9999,
            pointerEvents: 'none',
          }}
          aria-live="polite"
          className='max-w-[360px]'
        >
          <div
            className="pointer-events-auto flex   rounded-xl shadow-lg flex-row p-4"
            role="dialog"
            aria-label="Cookies notice"
            style={{
              // Use tokens from :root
              background: 'var(--card-background)',
              color: 'var(--main)',
              boxShadow: '0 10px 15px -3px rgba(0,0,0,0.3), 0 4px 6px -4px rgba(0,0,0,0.3)',
              border: '1px solid var(--card-border-color)',
              opacity: 1,
              transform: 'none',
            }}
          >
            <p
              className="text-body-sm"
              style={{
                margin: 0,
                lineHeight: 1.5,
                color: 'var(--card-paragraph)',
                flex: 1,
              }}
            >
              We use cookies to personalize content, run ads, and analyze traffic.
            </p>

            <button
              onClick={close}
              className=""
              style={{
                background: 'var(--button2)',        // subtle button background
                color: 'var(--button-text)',
                border: '1px solid var(--button-border)',
                borderRadius: 10,
                padding: '8px 12px',
                fontSize: 14,
                fontWeight: 600,
                cursor: 'pointer',
                whiteSpace: 'nowrap',
                transition: 'background 150ms ease, color 150ms ease, border-color 150ms ease',
              }}
              onMouseEnter={(e) => {
                (e.currentTarget as HTMLButtonElement).style.background = 'var(--button)';
                (e.currentTarget as HTMLButtonElement).style.color = 'var(--button-text)';
                (e.currentTarget as HTMLButtonElement).style.borderColor = 'var(--button)';
              }}
              onMouseLeave={(e) => {
                (e.currentTarget as HTMLButtonElement).style.background = 'var(--button2)';
                (e.currentTarget as HTMLButtonElement).style.color = 'var(--button-text)';
                (e.currentTarget as HTMLButtonElement).style.borderColor = 'var(--button-border)';
              }}
            >
              Okay
            </button>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}

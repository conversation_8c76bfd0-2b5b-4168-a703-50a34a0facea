'use client';

import { useCart } from '@/contexts/CartContext';
import { AnimatePresence, motion } from 'framer-motion';
import { useEffect, useRef } from 'react';
import { FiMinus, FiPlus, FiTrash2, FiX } from 'react-icons/fi';

function formatCurrency(value: number) {
  // You can customize locale/currency
  return new Intl.NumberFormat(undefined, { style: 'currency', currency: 'USD' }).format(value);
}

export default function CartSheet() {
  const { isOpen, close, items, updateQuantity, removeItem, subtotal, clear } = useCart();
  const ref = useRef<HTMLDivElement | null>(null);

  // Close on ESC
  useEffect(() => {
    if (!isOpen) return;
    const onKey = (e: KeyboardEvent) => e.key === 'Escape' && close();
    window.addEventListener('keydown', onKey);
    return () => window.removeEventListener('keydown', onKey);
  }, [isOpen, close]);

  // Trap focus (simple)
  useEffect(() => {
    if (!isOpen || !ref.current) return;
    const el = ref.current;
    const focusable = el.querySelector<HTMLElement>(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );
    focusable?.focus();
  }, [isOpen]);

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            key="cart-backdrop"
            className="fixed inset-0 z-[80] bg-black/50"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={close}
          />

          {/* Sheet */}
          <motion.aside
            key="cart-sheet"
            role="dialog"
            aria-label="Shopping cart"
            aria-modal="true"
            ref={ref}
            className="fixed right-0 top-0 h-screen w-full max-w-[420px] z-
                       bg-[var(--card-background)] text-[var(--main)]
                       border-l border-[var(--card-border-color)]
                       shadow-2xl flex flex-col"
            initial={{ x: '100%' }}
            animate={{ x: 0 }}
            exit={{ x: '100%' }}
            transition={{ type: 'tween', duration: 0.25, ease: 'easeOut' }}
          >
            {/* Header */}
            <div className="flex items-center justify-between px-4 py-3 border-b border-[var(--card-border-color)]">
              <h2 className="text-base font-semibold">Your Cart</h2>
              <button
                onClick={close}
                aria-label="Close cart"
                className="p-2 rounded-md hover:bg-[var(--card-hover)]"
              >
                <FiX size={18} />
              </button>
            </div>

            {/* Content */}
            <div className="flex-1 overflow-y-auto px-2 py-2">
              {items.length === 0 ? (
                <div className="h-full grid place-items-center text-[var(--paragraph)]">
                  Your cart is empty.
                </div>
              ) : (
                <ul className="space-y-2">
                  {items.map((item) => (
                    <li
                      key={item.id}
                      className="flex gap-3 rounded-lg border border-[var(--card-border-color)] bg-[var(--card-background)]
                                 p-2 hover:bg-[var(--card-hover)] transition-colors"
                    >
                      {/* Image */}
                      <div className="w-16 h-16 rounded-md overflow-hidden bg-[var(--skeleton-color)] flex items-center justify-center">
                        {item.image ? (
                          // eslint-disable-next-line @next/next/no-img-element
                          <img
                            src={item.image}
                            alt={item.title}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <span className="text-xs text-[var(--paragraph)]">No image</span>
                        )}
                      </div>

                      {/* Details */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between gap-2">
                          <div className="min-w-0">
                            <div className="truncate text-sm font-medium">{item.title}</div>
                            {item.variant && (
                              <div className="text-xs text-[var(--paragraph)]">{item.variant}</div>
                            )}
                          </div>
                          <div className="text-sm font-semibold shrink-0">
                            {formatCurrency(item.price * item.quantity)}
                          </div>
                        </div>

                        {/* Quantity controls */}
                        <div className="mt-2 flex items-center gap-2">
                          <div className="inline-flex items-center border border-[var(--card-border-color)] rounded-md">
                            <button
                              onClick={() => updateQuantity(item.id, item.quantity - 1)}
                              className="p-1.5 hover:bg-[var(--card-hover)]"
                              aria-label={`Decrease ${item.title} quantity`}
                            >
                              <FiMinus size={14} />
                            </button>
                            <input
                              type="number"
                              min={1}
                              value={item.quantity}
                              onChange={(e) =>
                                updateQuantity(item.id, Number(e.target.value) || 1)
                              }
                              className="w-12 bg-transparent text-center outline-none text-sm py-1"
                            />
                            <button
                              onClick={() => updateQuantity(item.id, item.quantity + 1)}
                              className="p-1.5 hover:bg-[var(--card-hover)]"
                              aria-label={`Increase ${item.title} quantity`}
                            >
                              <FiPlus size={14} />
                            </button>
                          </div>

                          <button
                            onClick={() => removeItem(item.id)}
                            className="ml-auto inline-flex items-center gap-1 text-[var(--paragraph)] hover:text-[var(--link-hover)]"
                          >
                            <FiTrash2 size={14} />
                            <span className="text-xs">Remove</span>
                          </button>
                        </div>
                      </div>
                    </li>
                  ))}
                </ul>
              )}
            </div>

            {/* Footer */}
            <div className="border-t border-[var(--card-border-color)] p-4 space-y-3">
              <div className="flex items-center justify-between text-sm">
                <span className="text-[var(--paragraph)]">Subtotal</span>
                <span className="font-semibold">{formatCurrency(subtotal)}</span>
              </div>

              <button
                className="w-full rounded-md py-2.5 font-semibold
                           bg-[var(--button)] text-[var(--button-text)]
                           hover:bg-[var(--highlight)]
                           border border-[var(--button-border)]
                           transition-[background,border,color] duration-150"
                onClick={() => {
                  // TODO: connect to checkout
                  alert('Proceeding to checkout...');
                }}
              >
                Checkout
              </button>

              <button
                className="w-full rounded-md py-2 text-sm text-[var(--paragraph)] hover:text-[var(--link-hover)]"
                onClick={clear}
              >
                Clear cart
              </button>
            </div>
          </motion.aside>
        </>
      )}
    </AnimatePresence>
  );
}

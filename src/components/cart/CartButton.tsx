'use client';

import { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useCart } from '@/contexts/CartContext';

type Props = {
  className?: string;
  size?: number; // icon size in px
  showLabel?: boolean; // show "Cart" text next to icon
  ordered?: boolean; // toggle to show the green circle effect (order placed)
  orderedAutoHideMs?: number; // auto-hide delay for the indicator
  indicatorSide?: 'left' | 'right'; // choose which side to place the green circle
};

export default function CartButton({
  className,
  size = 24,
  showLabel = false,
  ordered = false,
  orderedAutoHideMs = 1200,
  indicatorSide = 'right',
}: Props) {
  const { totalQuantity, toggle } = useCart();

  // "Order placed" indicator visibility
  const [showIndicator, setShowIndicator] = useState(ordered);

  useEffect(() => {
    setShowIndicator(ordered);
  }, [ordered]);

  useEffect(() => {
    if (!showIndicator) return;
    const t = setTimeout(() => setShowIndicator(false), orderedAutoHideMs);
    return () => clearTimeout(t);
  }, [showIndicator, orderedAutoHideMs]);

  const indicatorPos = indicatorSide === 'right' ? '-right-1' : '-left-1';

  return (
    <button
      onClick={toggle}
      aria-label="Open cart"
      className={[
        'relative inline-flex items-center justify-center rounded-md p-2',
        'text-[var(--nav-item)] hover:text-[var(--link-hover)]',
        'bg-transparent',
        className ?? '',
      ].join(' ')}
    >
      {/* Cart icon (clean, scalable) */}
      <svg
        width={size}
        height={size}
        viewBox="0 0 24 24"
        fill="none"
        aria-hidden="true"
        className="text-[var(--nav-item)]"
      >
        <path
          d="M3 3h2l2.4 12.2a2 2 0 0 0 2 1.6h7.9a2 2 0 0 0 2-1.6L21 7H6"
          stroke="currentColor"
          strokeWidth="1.6"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <circle cx="10" cy="20" r="1.5" fill="currentColor" />
        <circle cx="18" cy="20" r="1.5" fill="currentColor" />
      </svg>

      {showLabel && (
        <span className="ml-2 text-sm font-medium text-[var(--nav-item)]">Cart</span>
      )}

      {/* Quantity badge (re-animated on count change) */}
      <AnimatePresence initial={false}>
        {totalQuantity > 0 && (
          <motion.span
            key={totalQuantity}
            initial={{ scale: 0, opacity: 0, y: -2 }}
            animate={{ scale: 1, opacity: 1, y: 0 }}
            exit={{ scale: 0.8, opacity: 0 }}
            transition={{ type: 'spring', stiffness: 500, damping: 28 }}
            className="absolute -top-1 -right-1 min-w-[18px] h-[18px] px-1
                       text-[10px] leading-[18px] text-center rounded-full
                       bg-[var(--badge-background)] text-[var(--badge-text)]"
            aria-live="polite"
          >
            {totalQuantity}
          </motion.span>
        )}
      </AnimatePresence>

      {/* Green circle indicator (order placed) */}
      <AnimatePresence>
        {showIndicator && (
          <motion.span
            key="order-indicator"
            initial={{ scale: 0.6, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.8, opacity: 0 }}
            transition={{ type: 'spring', stiffness: 420, damping: 22 }}
            className={`absolute -bottom-1 ${indicatorPos} h-3 w-3 rounded-full bg-[var(--tertiary)]`}
            aria-hidden="true"
          />
        )}
      </AnimatePresence>

      {/* Pulse ring (one-shot) */}
      <AnimatePresence>
        {showIndicator && (
          <motion.span
            key="order-indicator-pulse"
            initial={{ opacity: 0.5, scale: 0.9 }}
            animate={{ opacity: 0, scale: 1.9 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.8, ease: 'easeOut' }}
            className={`pointer-events-none absolute -bottom-1 ${indicatorPos} h-3 w-3 rounded-full ring-4 ring-[color-mix(in_oklab,var(--tertiary)_50%,transparent)]`}
            aria-hidden="true"
          />
        )}
      </AnimatePresence>
    </button>
  );
}

"use client";

import React, { createContext, useContext, useState, ReactNode } from "react";

type NavbarContextType = {
  mobileMenuOpen: boolean;
  setMobileMenuOpen: (open: boolean) => void;
  openDropdownIndex: number | null;
  setOpenDropdownIndex: (index: number | null) => void;
};

const NavbarContext = createContext<NavbarContextType | undefined>(undefined);

export function NavbarProvider({ children }: { children: ReactNode }) {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [openDropdownIndex, setOpenDropdownIndex] = useState<number | null>(
    null
  );

  return (
    <NavbarContext.Provider
      value={{
        mobileMenuOpen,
        setMobileMenuOpen,
        openDropdownIndex,
        setOpenDropdownIndex,
      }}
    >
      {children}
    </NavbarContext.Provider>
  );
}

export function useNavbarContext() {
  const context = useContext(NavbarContext);
  if (!context) {
    throw new Error("useNavbarContext must be used within a NavbarProvider");
  }
  return context;
}

@import url("https://fonts.googleapis.com/css2?family=Jura:wght@300..700&display=swap");

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: #16161a;
    --headline: #fffffe;
    --paragraph: #94a1b2;
    --button: #7f5af0;
    --button2: #7f5af08a;
    --button-border: #7f5af054;
    --button-text: #fffffe;
    --illustration-stroke: #01010179;
    --main: #fffffe;
    --highlight: #7f5af0;
    --secondary: #72757e;
    --tertiary: #2cb67d;
    --card-background: #242629;
    --card-background-effect: #2426295c;
    --card-border-color: #ffffff16;
    --border: #ffffff1a;
    --card-headline: #fffffe;
    --card-paragraph: #94a1b2;
    --card-hover: #d9d9d91c;
    --hover-select: #ffffff75;
    --link-color: #7f5af0;
    --link-hover: #fffffe;
    --nav-item: #94a1b2;
    --Logo-background: #fffffe;
    --Logo-text-color: #fffffe;
    --input-background: #242629;
    --input-border-color: #7575755e;
    --badge-background: #7f5af0;
    --badge-text: #fefeff;
    --skeleton-color: #353f4e;
    --footer-border-color: #ffffff25;
    --footer-text: #94a1b2;
    --menu-color: #94a1b2;
    --input-text: #fffffe;
    --selectBox-border: #7f5af0;
    --outline-button-text: #cbbaff;
    --mark: #000;
    --active: #fffffe;
    --active-text: #16161a;
    --mobile-nav: #16161aa6;
    --gradient1: #7f5af0;
    --gradient1: #baa3fd;
  }

  * {
    @apply m-0 box-border p-0 border-border;
    scroll-behavior: smooth;
  }

  html {
    @apply text-base;
  }

  a {
    @apply cursor-pointer;
  }

  body {
    @apply bg-background text-foreground;
    font-family: "Cairo", sans-serif;
    overscroll-behavior: none;
  }
}

.hoverd {
  transition: all 0.3s ease 0s !important;
}

@layer components {
  .container {
    max-width: 1600px;
    width: 100%;
    margin-left: auto;
    margin-right: auto;
    padding-left: 16px;
    padding-right: 16px;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    @apply text-[var(--headline)];
  }

  p {
    @apply text-[var(--paragraph)];
  }

  a {
    @apply text-[var(--link-color)];
  }
}

@layer utilities {
  /* Animation utilities */
  .fade-in-right {
    animation: fade-in-right 0.3s ease-out;
  }

  .fade-out-right {
    animation: fade-out-right 0.3s ease-in;
  }

  /* Glass effect */
  .glass-effect {
    @apply bg-opacity-20 backdrop-blur-lg backdrop-filter;
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.1);
  }

  /* Gradient text */
  .gradient-text {
    @apply bg-clip-text text-transparent;
    background-image: linear-gradient(to right, var(--link-color), #baa3fd);
  }

  /* Subtle hover effect */
  .hover-lift {
    @apply transition-all duration-300;
    transform: translateY(0);
  }

  .hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  }

  /* Text highlight effect */
  .highlight-text {
    position: relative;
    display: inline-block;
  }

  .highlight-text::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 30%;
    background-color: var(--link-color);
    opacity: 0.2;
    z-index: -1;
    transform: skew(-12deg);
  }

  /* Animated border */
  .animated-border {
    position: relative;
    overflow: hidden;
  }

  .animated-border::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 2px solid transparent;
    border-radius: inherit;
    background: linear-gradient(
        90deg,
        var(--link-color),
        #baa3fd,
        var(--link-color)
      )
      border-box;
    -webkit-mask:
      linear-gradient(#fff 0 0) padding-box,
      linear-gradient(#fff 0 0);
    mask:
      linear-gradient(#fff 0 0) padding-box,
      linear-gradient(#fff 0 0);
    -webkit-mask-composite: destination-out;
    mask-composite: exclude;
    animation: border-rotate 4s linear infinite;
  }

  @keyframes border-rotate {
    0% {
      background-position: 0% center;
    }
    100% {
      background-position: 200% center;
    }
  }

  /* Shimmer effect */
  .shimmer {
    position: relative;
    overflow: hidden;
  }

  .shimmer::after {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 50%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.1),
      transparent
    );
    animation: shimmer 2s infinite;
  }

  @keyframes shimmer {
    100% {
      left: 150%;
    }
  }

  /* Glow effect */
  .glow-on-hover {
    transition: all 0.3s ease;
  }

  .glow-on-hover:hover {
    box-shadow: 0 0 15px var(--link-color);
  }

  @keyframes fade-in-right {
    from {
      opacity: 0;
      transform: translateX(100%);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes fade-out-right {
    from {
      opacity: 1;
      transform: translateX(0);
    }
    to {
      opacity: 0;
      transform: translateX(100%);
    }
  }

  @keyframes scale {
    100% {
      transform: scale(1);
    }
  }

  @keyframes fade-in {
    100% {
      opacity: 1;
      filter: blur(0);
    }
  }

  @keyframes moving {
    50% {
      width: 100%;
    }
    100% {
      width: 0;
      right: 0;
      left: unset;
    }
  }
}

.page {
  @apply min-h-[100dvh] w-full;
}

.test {
  @apply bg-green-300 border border-black;
}

'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';

function IconGoogle({ className }: { className?: string }) {
  return (
    <svg className={className} viewBox="0 0 48 48" aria-hidden="true" xmlns="http://www.w3.org/2000/svg">
      <path fill="#FFC107" d="M43.6 20.5H42V20H24v8h11.3C33.9 32.6 29.3 36 24 36c-6.6 0-12-5.4-12-12S17.4 12 24 12c3 0 5.7 1.1 7.7 3l5.7-5.7C33.9 6.1 29.2 4 24 4 12.9 4 4 12.9 4 24s8.9 20 20 20 20-8.9 20-20c0-1.2-.1-2.3-.4-3.5z"/>
      <path fill="#FF3D00" d="M6.3 14.7l6.6 4.8C14.6 16.2 18.9 12 24 12c3 0 5.7 1.1 7.7 3l5.7-5.7C33.9 6.1 29.2 4 24 4c-7.7 0-14.3 4.3-17.7 10.7z"/>
      <path fill="#4CAF50" d="M24 44c5.2 0 9.9-2 13.3-5.3l-6.2-5.1C29.1 35.6 26.7 36 24 36c-5.3 0-9.9-3.4-11.3-8H6.3l-6.6 5C3.7 39.6 13 44 24 44z"/>
      <path fill="#1976D2" d="M43.6 20.5H42V20H24v8h11.3c-1.3 3.8-5.1 6.5-9.3 6.5-5.3 0-9.9-3.4-11.3-8H6.3l-6.6 5C3.7 39.6 13 44 24 44c11 0 20-9 20-20 0-1.2-.1-2.3-.4-3.5z"/>
    </svg>
  );
}

export default function RegisterPage() {
  const router = useRouter();
  const [email, setEmail] = useState('');
  const [touched, setTouched] = useState(false);

  const isValidEmail = (value: string) =>
    /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value);

  const showError = touched && !isValidEmail(email);

  const onSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setTouched(true);
    if (!isValidEmail(email)) return;

    // TODO: handle registration request
    // await fetch('/api/register', { method: 'POST', body: JSON.stringify({ email }) });

    // Navigate to next step
    router.push('/verify');
  };

  const continueWithGoogle = async () => {
    // TODO: hook up OAuth (e.g., NextAuth signIn('google'))
    router.push('/onboarding');
  };

  return (
    <main className="min-h-screen w-full flex items-center justify-center bg-[var(--background)] text-[var(--main)] px-6">
      <div className="w-full max-w-md">
        {/* Logo + Title */}
        <div className="mb-6">
          <div className="flex items-center gap-3">
            {/* Reference-like logo block */}
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="30" aria-hidden="true">
              <path d="M-.14-.003h20.28v10.006H10Z" fill="#FFF"></path>
              <defs>
                <linearGradient id="a" x1=".49" x2=".51" y1="0" y2="1">
                  <stop offset="0" stopColor="#0AF" />
                  <stop offset="1" stopColor="#09F" />
                </linearGradient>
              </defs>
              <path d="M-.14 10.003H10L20.14 20.01H-.14Z" fill="url(#a)"></path>
              <defs>
                <linearGradient id="b" x1=".50" x2=".50" y1="0" y2="1">
                  <stop offset="0" stopColor="#03F" />
                  <stop offset="1" stopColor="#05F" />
                </linearGradient>
              </defs>
              <path d="M-.14 20.01H10v10.006Z" fill="url(#b)"></path>
            </svg>

            <h5 className="text-lg font-medium text-[var(--headline)]">
              Welcome to Framer
            </h5>
          </div>
        </div>

        {/* Continue with Google */}
        <div className="mb-6">
          <button
            onClick={continueWithGoogle}
            className="w-full inline-flex items-center justify-center gap-3 rounded-lg px-4 py-3 font-medium
                       bg-[var(--button)] text-[var(--button-text)]
                       hover:bg-[var(--highlight)]
                       border border-[var(--button-border)]
                       transition-colors"
          >
            <IconGoogle className="w-5 h-5" />
            <span>Continue with Google</span>
          </button>
        </div>

        {/* Email form with error like reference */}
        <form noValidate onSubmit={onSubmit} className="space-y-3">
          <div>
            <input
              className={[
                'w-full rounded-lg px-4 py-3 outline-none',
                'bg-[var(--input-background)] text-[var(--input-text)]',
                'placeholder-[var(--paragraph)]',
                'border',
                showError
                  ? 'border-[var(--selectBox-border)] border-red-400'
                  : 'border-[var(--input-border-color)] focus:border-[var(--selectBox-border)]',
              ].join(' ')}
              style={{
                marginTop: 20,
                backgroundColor: 'rgba(0,0,0,0.2)',
                color: 'rgb(255,255,255)',
                boxShadow: 'inset 0 0 0 9999px rgba(0,0,0,0)',
              }}
              type="email"
              placeholder="Email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              onBlur={() => setTouched(true)}
              autoCapitalize="none"
              autoCorrect="off"
              spellCheck={false}
            />
          </div>

          {showError && (
            <div>
              <p
                className="text-xs"
                style={{ color: 'var(--fraction-color-danger, #f36)' }}
              >
                Please enter a valid email address
              </p>
            </div>
          )}

          <button
            type="submit"
            className="w-full rounded-lg px-4 py-3 font-medium
                       bg-[var(--button)] text-[var(--button-text)]
                       hover:bg-[var(--highlight)]
                       border border-[var(--button-border)]
                       transition-[background,border-color] duration-150"
          >
            <span className="label" aria-hidden="false">Continue</span>
          </button>
        </form>
      </div>
    </main>
  );
}

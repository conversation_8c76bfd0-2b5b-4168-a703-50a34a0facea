import type { <PERSON>ada<PERSON>, Viewport } from "next";
import "./globals.css";

import CartSheet from "@/components/cart/CartSheet";
import Footer from "@/components/common/Footer";
import Navbar from "@/components/common/navbar/Navbar";
import BackgroundEffect from "@/components/ui/backgroundEffect";
import FloatingActionButton from "@/components/ui/FloatingActionButton";
import { CartProvider } from "@/contexts/CartContext";
import { NavbarProvider } from "@/contexts/NavbarContext";
import { Analytics } from "@vercel/analytics/react";
import { SpeedInsights } from "@vercel/speed-insights/next";
import { Toaster } from "sonner";

const domain = "templgen.com";
const websitePath = {
  main: `https://${domain}`,
};
const webImage = `${websitePath.main}/images/profile.jpg`; // Adjust this image path as needed
const email = "<EMAIL>"; // Replace with your actual email

const keywords = [
  "templgen",
  "website templates",
  "mobile templates",
  "React templates",
  "Next.js templates",
  "MERN templates",
  "UI templates",
  "frontend templates",
  "backend templates",
  "software templates",
  "web development",
  "template marketplace",
  "responsive templates",
];

export const metadata: Metadata = {
  metadataBase: new URL(websitePath.main),
  title: {
    template: "TemplGen - %s",
    default: "TemplGen - Website & Mobile Templates Marketplace",
  },
  description:
    "TemplGen offers premium website and mobile templates for developers and businesses. Explore React, Next.js, and MERN templates to accelerate your projects.",
  keywords: keywords.join(", "),
  authors: [{ name: "TemplGen", url: websitePath.main }],
  creator: "TemplGen",
  publisher: "TemplGen",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  alternates: {
    canonical: websitePath.main,
    languages: {
      en: `${websitePath.main}/en`,
      ar: `${websitePath.main}/ar`,
    },
  },
  openGraph: {
    type: "website",
    locale: "en_US",
    alternateLocale: "ar_SA",
    title: "TemplGen Marketplace",
    description:
      "Browse and purchase premium website and mobile templates from TemplGen. High-quality React, Next.js, and MERN templates ready to use.",
    url: websitePath.main,
    siteName: "TemplGen",
    images: [
      {
        url: webImage,
        width: 400,
        height: 400,
        alt: "TemplGen Logo",
      },
    ],
    countryName: "Global",
    emails: [email],
  },
  twitter: {
    card: "summary_large_image",
    title: "TemplGen Marketplace",
    description:
      "Discover and buy premium templates for web and mobile development at TemplGen.",
    images: webImage,
    creator: "@templgen",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  verification: {
    // Add verification tokens here if any
  },
};

export const viewport: Viewport = {
  themeColor: "#16161a",
  width: "device-width",
  initialScale: 1,
  maximumScale: 5,
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" dir="ltr">
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link
          rel="preconnect"
          href="https://fonts.gstatic.com"
          crossOrigin="anonymous"
        />
        <meta name="apple-mobile-web-app-title" content="TemplGen" />

        <script
          dangerouslySetInnerHTML={{
            __html: `
              (function() {
                const observer = new MutationObserver((mutations) => {
                  mutations.forEach(({ target }) => {
                    if (target.nodeType === 1) {
                      const elem = target;
                      if (elem.hasAttribute('data-gr-ext-installed') ||
                          elem.hasAttribute('data-new-gr-c-s-check-loaded')) {
                        elem.removeAttribute('data-gr-ext-installed');
                        elem.removeAttribute('data-new-gr-c-s-check-loaded');
                      }
                    }
                  });
                });
                observer.observe(document.documentElement, {
                  attributes: true,
                  subtree: true,
                  attributeFilter: ['data-gr-ext-installed', 'data-new-gr-c-s-check-loaded']
                });
              })();
            `,
          }}
        />

        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "Organization",
              name: "TemplGen",
              url: websitePath.main,
              logo: webImage,
              sameAs: [
                "https://github.com/templgen",
                "https://www.linkedin.com/company/templgen",
                "https://www.youtube.com/@templgen",
              ],
              description:
                "Marketplace for premium website and mobile templates including React, Next.js, and MERN stacks.",
            }),
          }}
        />
      </head>
      <body className="flex relative dark flex-col min-h-screen">

        <SpeedInsights />
        <Analytics />
        <Toaster />
        <CartProvider>
          <NavbarProvider>
            <Navbar />
            <BackgroundEffect />
            <CartSheet />

            <main className="z-40 max-md:z-30 mx-auto w-full flex-grow">
              {children}
            </main>
            <Footer />
          </NavbarProvider>

        </CartProvider>


        <FloatingActionButton threshold={400} />
      </body>
    </html>
  );
}

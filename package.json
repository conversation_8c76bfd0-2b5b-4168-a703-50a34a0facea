{"name": "al<PERSON>er", "version": "22.0.0", "private": true, "scripts": {"dev": "next dev --turbopack -H 0.0.0.0 -p 4000", "build": "next build", "start": "next start", "lint": "next lint", "generate-sitemap": "node scripts/generate-sitemap.js", "deploy": "node scripts/deploy.js", "format": "prettier --write ."}, "dependencies": {"@formatjs/intl-localematcher": "^0.6.0", "@google/generative-ai": "^0.24.1", "@hookform/resolvers": "^4.1.2", "@radix-ui/react-accordion": "^1.2.8", "@radix-ui/react-alert-dialog": "^1.1.11", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.7", "@radix-ui/react-checkbox": "^1.2.3", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.4", "@radix-ui/react-radio-group": "^1.3.4", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.2", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@vercel/analytics": "^1.5.0", "@vercel/speed-insights": "^1.2.0", "bcryptjs": "^3.0.2", "chart.js": "^4.4.9", "class-variance-authority": "^0.7.1", "cloudinary": "^2.6.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "color-thief-browser": "^2.0.2", "emailjs-com": "^3.2.0", "embla-carousel-react": "^8.6.0", "framer-motion": "^12.4.1", "gsap": "^3.13.0", "input-otp": "^1.4.2", "jose": "^6.0.11", "lucide-react": "^0.471.2", "mongoose": "^8.15.0", "negotiator": "^1.0.0", "next": "15.1.4", "next-themes": "^0.4.4", "radix-ui": "^1.4.2", "react": "^19.0.0", "react-chartjs-2": "^5.3.0", "react-day-picker": "^9.7.0", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "react-icons": "^5.5.0", "react-resizable-panels": "^3.0.2", "react-responsive-carousel": "^3.2.23", "react-vertical-timeline-component": "^3.5.3", "recharts": "^2.15.3", "rough-notation": "^0.5.1", "server-only": "^0.0.1", "sonner": "^1.7.4", "tailwind-merge": "^2.6.0", "vaul": "^1.1.2", "zod": "^3.24.2"}, "devDependencies": {"@types/negotiator": "^0.6.3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-vertical-timeline-component": "^3.3.6", "autoprefixer": "^10.4.20", "globby": "^11.1.0", "postcss": "^8", "prettier": "^3.5.2", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^3.4.1", "tailwindcss-animate": "^1.0.7", "typescript": "^5"}}
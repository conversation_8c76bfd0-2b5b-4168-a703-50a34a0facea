{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-rsc'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,0HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0]}}, {"offset": {"line": 8, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 14, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/%40vercel/analytics/dist/react/index.mjs/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Analytics = registerClientReference(\n    function() { throw new Error(\"Attempted to call Analytics() from the server but Analytics is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@vercel/analytics/dist/react/index.mjs <module evaluation>\",\n    \"Analytics\",\n);\nexport const track = registerClientReference(\n    function() { throw new Error(\"Attempted to call track() from the server but track is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@vercel/analytics/dist/react/index.mjs <module evaluation>\",\n    \"track\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,qFACA;AAEG,MAAM,QAAQ,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,qFACA", "ignoreList": [0]}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/%40vercel/analytics/dist/react/index.mjs/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Analytics = registerClientReference(\n    function() { throw new Error(\"Attempted to call Analytics() from the server but Analytics is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@vercel/analytics/dist/react/index.mjs\",\n    \"Analytics\",\n);\nexport const track = registerClientReference(\n    function() { throw new Error(\"Attempted to call track() from the server but track is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@vercel/analytics/dist/react/index.mjs\",\n    \"track\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,iEACA;AAEG,MAAM,QAAQ,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,iEACA", "ignoreList": [0]}}, {"offset": {"line": 44, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/%40vercel/analytics/src/react/index.tsx", "file:///home/<USER>/Desktop/templgen/node_modules/%40vercel/analytics/package.json", "file:///home/<USER>/Desktop/templgen/node_modules/%40vercel/analytics/src/queue.ts", "file:///home/<USER>/Desktop/templgen/node_modules/%40vercel/analytics/src/utils.ts", "file:///home/<USER>/Desktop/templgen/node_modules/%40vercel/analytics/src/generic.ts", "file:///home/<USER>/Desktop/templgen/node_modules/%40vercel/analytics/src/react/utils.ts"], "sourcesContent": ["'use client';\nimport { useEffect } from 'react';\nimport { inject, track, pageview } from '../generic';\nimport type { AnalyticsProps, BeforeSend, BeforeSendEvent } from '../types';\nimport { getBasePath } from './utils';\n\n/**\n * Injects the Vercel Web Analytics script into the page head and starts tracking page views. Read more in our [documentation](https://vercel.com/docs/concepts/analytics/package).\n * @param [props] - Analytics options.\n * @param [props.mode] - The mode to use for the analytics script. Defaults to `auto`.\n *  - `auto` - Automatically detect the environment.  Uses `production` if the environment cannot be determined.\n *  - `production` - Always use the production script. (Sends events to the server)\n *  - `development` - Always use the development script. (Logs events to the console)\n * @param [props.debug] - Whether to enable debug logging in development. Defaults to `true`.\n * @param [props.beforeSend] - A middleware function to modify events before they are sent. Should return the event object or `null` to cancel the event.\n * @example\n * ```js\n * import { Analytics } from '@vercel/analytics/react';\n *\n * export default function App() {\n *  return (\n *   <div>\n *    <Analytics />\n *    <h1>My App</h1>\n *  </div>\n * );\n * }\n * ```\n */\nfunction Analytics(\n  props: AnalyticsProps & {\n    framework?: string;\n    route?: string | null;\n    path?: string | null;\n    basePath?: string;\n  }\n): null {\n  useEffect(() => {\n    if (props.beforeSend) {\n      window.va?.('beforeSend', props.beforeSend);\n    }\n  }, [props.beforeSend]);\n\n  // biome-ignore lint/correctness/useExhaustiveDependencies: only run once\n  useEffect(() => {\n    inject({\n      framework: props.framework || 'react',\n      basePath: props.basePath ?? getBasePath(),\n      ...(props.route !== undefined && { disableAutoTrack: true }),\n      ...props,\n    });\n    // eslint-disable-next-line react-hooks/exhaustive-deps -- only run once\n  }, []);\n\n  useEffect(() => {\n    // explicitely track page view, since we disabled auto tracking\n    if (props.route && props.path) {\n      pageview({ route: props.route, path: props.path });\n    }\n  }, [props.route, props.path]);\n\n  return null;\n}\n\nexport { track, Analytics };\nexport type { AnalyticsProps, BeforeSend, BeforeSendEvent };\n", "{\n  \"name\": \"@vercel/analytics\",\n  \"version\": \"1.5.0\",\n  \"description\": \"Gain real-time traffic insights with Vercel Web Analytics\",\n  \"keywords\": [\n    \"analytics\",\n    \"vercel\"\n  ],\n  \"repository\": {\n    \"url\": \"github:vercel/analytics\",\n    \"directory\": \"packages/web\"\n  },\n  \"license\": \"MPL-2.0\",\n  \"exports\": {\n    \"./package.json\": \"./package.json\",\n    \".\": {\n      \"browser\": \"./dist/index.mjs\",\n      \"import\": \"./dist/index.mjs\",\n      \"require\": \"./dist/index.js\"\n    },\n    \"./astro\": {\n      \"import\": \"./dist/astro/component.ts\"\n    },\n    \"./next\": {\n      \"browser\": \"./dist/next/index.mjs\",\n      \"import\": \"./dist/next/index.mjs\",\n      \"require\": \"./dist/next/index.js\"\n    },\n    \"./nuxt\": {\n      \"browser\": \"./dist/nuxt/index.mjs\",\n      \"import\": \"./dist/nuxt/index.mjs\",\n      \"require\": \"./dist/nuxt/index.js\"\n    },\n    \"./react\": {\n      \"browser\": \"./dist/react/index.mjs\",\n      \"import\": \"./dist/react/index.mjs\",\n      \"require\": \"./dist/react/index.js\"\n    },\n    \"./remix\": {\n      \"browser\": \"./dist/remix/index.mjs\",\n      \"import\": \"./dist/remix/index.mjs\",\n      \"require\": \"./dist/remix/index.js\"\n    },\n    \"./server\": {\n      \"node\": \"./dist/server/index.mjs\",\n      \"edge-light\": \"./dist/server/index.mjs\",\n      \"import\": \"./dist/server/index.mjs\",\n      \"require\": \"./dist/server/index.js\",\n      \"default\": \"./dist/server/index.js\"\n    },\n    \"./sveltekit\": {\n      \"svelte\": \"./dist/sveltekit/index.mjs\",\n      \"types\": \"./dist/sveltekit/index.d.ts\"\n    },\n    \"./vue\": {\n      \"browser\": \"./dist/vue/index.mjs\",\n      \"import\": \"./dist/vue/index.mjs\",\n      \"require\": \"./dist/vue/index.js\"\n    }\n  },\n  \"main\": \"./dist/index.mjs\",\n  \"types\": \"./dist/index.d.ts\",\n  \"typesVersions\": {\n    \"*\": {\n      \"*\": [\n        \"dist/index.d.ts\"\n      ],\n      \"next\": [\n        \"dist/next/index.d.ts\"\n      ],\n      \"nuxt\": [\n        \"dist/nuxt/index.d.ts\"\n      ],\n      \"react\": [\n        \"dist/react/index.d.ts\"\n      ],\n      \"remix\": [\n        \"dist/remix/index.d.ts\"\n      ],\n      \"server\": [\n        \"dist/server/index.d.ts\"\n      ],\n      \"sveltekit\": [\n        \"dist/sveltekit/index.d.ts\"\n      ],\n      \"vue\": [\n        \"dist/vue/index.d.ts\"\n      ]\n    }\n  },\n  \"scripts\": {\n    \"build\": \"tsup && pnpm copy-astro\",\n    \"copy-astro\": \"cp -R src/astro dist/\",\n    \"dev\": \"pnpm copy-astro && tsup --watch\",\n    \"lint\": \"eslint .\",\n    \"lint-fix\": \"eslint . --fix\",\n    \"test\": \"vitest\",\n    \"type-check\": \"tsc --noEmit\"\n  },\n  \"eslintConfig\": {\n    \"extends\": [\n      \"@vercel/eslint-config\"\n    ],\n    \"rules\": {\n      \"tsdoc/syntax\": \"off\"\n    },\n    \"ignorePatterns\": [\n      \"jest.setup.ts\"\n    ]\n  },\n  \"devDependencies\": {\n    \"@swc/core\": \"^1.9.2\",\n    \"@testing-library/jest-dom\": \"^6.6.3\",\n    \"@testing-library/react\": \"^16.0.1\",\n    \"@types/node\": \"^22.9.0\",\n    \"@types/react\": \"^18.3.12\",\n    \"@vercel/eslint-config\": \"workspace:0.0.0\",\n    \"server-only\": \"^0.0.1\",\n    \"svelte\": \"^5.1.10\",\n    \"tsup\": \"8.3.5\",\n    \"vitest\": \"^2.1.5\",\n    \"vue\": \"^3.5.12\",\n    \"vue-router\": \"^4.4.5\"\n  },\n  \"peerDependencies\": {\n    \"@remix-run/react\": \"^2\",\n    \"@sveltejs/kit\": \"^1 || ^2\",\n    \"next\": \">= 13\",\n    \"react\": \"^18 || ^19 || ^19.0.0-rc\",\n    \"svelte\": \">= 4\",\n    \"vue\": \"^3\",\n    \"vue-router\": \"^4\"\n  },\n  \"peerDependenciesMeta\": {\n    \"@remix-run/react\": {\n      \"optional\": true\n    },\n    \"@sveltejs/kit\": {\n      \"optional\": true\n    },\n    \"next\": {\n      \"optional\": true\n    },\n    \"react\": {\n      \"optional\": true\n    },\n    \"svelte\": {\n      \"optional\": true\n    },\n    \"vue\": {\n      \"optional\": true\n    },\n    \"vue-router\": {\n      \"optional\": true\n    }\n  }\n}\n", "export const initQueue = (): void => {\n  // initialize va until script is loaded\n  if (window.va) return;\n\n  window.va = function a(...params): void {\n    (window.vaq = window.vaq || []).push(params);\n  };\n};\n", "import type { AllowedPropertyValues, AnalyticsProps, Mode } from './types';\n\nexport function isBrowser(): boolean {\n  return typeof window !== 'undefined';\n}\n\nfunction detectEnvironment(): 'development' | 'production' {\n  try {\n    const env = process.env.NODE_ENV;\n    if (env === 'development' || env === 'test') {\n      return 'development';\n    }\n  } catch (e) {\n    // do nothing, this is okay\n  }\n  return 'production';\n}\n\nexport function setMode(mode: Mode = 'auto'): void {\n  if (mode === 'auto') {\n    window.vam = detectEnvironment();\n    return;\n  }\n\n  window.vam = mode;\n}\n\nexport function getMode(): Mode {\n  const mode = isBrowser() ? window.vam : detectEnvironment();\n  return mode || 'production';\n}\n\nexport function isProduction(): boolean {\n  return getMode() === 'production';\n}\n\nexport function isDevelopment(): boolean {\n  return getMode() === 'development';\n}\n\nfunction removeKey(\n  key: string,\n  { [key]: _, ...rest }\n): Record<string, unknown> {\n  return rest;\n}\n\nexport function parseProperties(\n  properties: Record<string, unknown> | undefined,\n  options: {\n    strip?: boolean;\n  }\n): Error | Record<string, AllowedPropertyValues> | undefined {\n  if (!properties) return undefined;\n  let props = properties;\n  const errorProperties: string[] = [];\n  for (const [key, value] of Object.entries(properties)) {\n    if (typeof value === 'object' && value !== null) {\n      if (options.strip) {\n        props = removeKey(key, props);\n      } else {\n        errorProperties.push(key);\n      }\n    }\n  }\n\n  if (errorProperties.length > 0 && !options.strip) {\n    throw Error(\n      `The following properties are not valid: ${errorProperties.join(\n        ', '\n      )}. Only strings, numbers, booleans, and null are allowed.`\n    );\n  }\n  return props as Record<string, AllowedPropertyValues>;\n}\n\nexport function computeRoute(\n  pathname: string | null,\n  pathParams: Record<string, string | string[]> | null\n): string | null {\n  if (!pathname || !pathParams) {\n    return pathname;\n  }\n\n  let result = pathname;\n  try {\n    const entries = Object.entries(pathParams);\n    // simple keys must be handled first\n    for (const [key, value] of entries) {\n      if (!Array.isArray(value)) {\n        const matcher = turnValueToRegExp(value);\n        if (matcher.test(result)) {\n          result = result.replace(matcher, `/[${key}]`);\n        }\n      }\n    }\n    // array values next\n    for (const [key, value] of entries) {\n      if (Array.isArray(value)) {\n        const matcher = turnValueToRegExp(value.join('/'));\n        if (matcher.test(result)) {\n          result = result.replace(matcher, `/[...${key}]`);\n        }\n      }\n    }\n    return result;\n  } catch (e) {\n    return pathname;\n  }\n}\n\nfunction turnValueToRegExp(value: string): RegExp {\n  return new RegExp(`/${escapeRegExp(value)}(?=[/?#]|$)`);\n}\n\nfunction escapeRegExp(string: string): string {\n  return string.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&');\n}\n\nexport function getScriptSrc(\n  props: AnalyticsProps & { basePath?: string }\n): string {\n  if (props.scriptSrc) {\n    return props.scriptSrc;\n  }\n  if (isDevelopment()) {\n    return 'https://va.vercel-scripts.com/v1/script.debug.js';\n  }\n  if (props.basePath) {\n    return `${props.basePath}/insights/script.js`;\n  }\n  return '/_vercel/insights/script.js';\n}\n", "import { name as packageName, version } from '../package.json';\nimport { initQueue } from './queue';\nimport type {\n  AllowedPropertyValues,\n  AnalyticsProps,\n  FlagsDataInput,\n  BeforeSend,\n  BeforeSendEvent,\n} from './types';\nimport {\n  isBrowser,\n  parseProperties,\n  setMode,\n  isDevelopment,\n  isProduction,\n  computeRoute,\n  getScriptSrc,\n} from './utils';\n\n/**\n * Injects the Vercel Web Analytics script into the page head and starts tracking page views. Read more in our [documentation](https://vercel.com/docs/concepts/analytics/package).\n * @param [props] - Analytics options.\n * @param [props.mode] - The mode to use for the analytics script. Defaults to `auto`.\n *  - `auto` - Automatically detect the environment.  Uses `production` if the environment cannot be determined.\n *  - `production` - Always use the production script. (Sends events to the server)\n *  - `development` - Always use the development script. (Logs events to the console)\n * @param [props.debug] - Whether to enable debug logging in development. Defaults to `true`.\n * @param [props.beforeSend] - A middleware function to modify events before they are sent. Should return the event object or `null` to cancel the event.\n * @param [props.dsn] - The DSN of the project to send events to. Only required when self-hosting.\n * @param [props.disableAutoTrack] - Whether the injected script should track page views from pushState events. Disable if route is updated after pushState, a manually call page pageview().\n */\nfunction inject(\n  props: AnalyticsProps & {\n    framework?: string;\n    disableAutoTrack?: boolean;\n    basePath?: string;\n  } = {\n    debug: true,\n  }\n): void {\n  if (!isBrowser()) return;\n\n  setMode(props.mode);\n\n  initQueue();\n\n  if (props.beforeSend) {\n    window.va?.('beforeSend', props.beforeSend);\n  }\n\n  const src = getScriptSrc(props);\n\n  if (document.head.querySelector(`script[src*=\"${src}\"]`)) return;\n\n  const script = document.createElement('script');\n  script.src = src;\n  script.defer = true;\n  script.dataset.sdkn =\n    packageName + (props.framework ? `/${props.framework}` : '');\n  script.dataset.sdkv = version;\n\n  if (props.disableAutoTrack) {\n    script.dataset.disableAutoTrack = '1';\n  }\n  if (props.endpoint) {\n    script.dataset.endpoint = props.endpoint;\n  } else if (props.basePath) {\n    script.dataset.endpoint = `${props.basePath}/insights`;\n  }\n  if (props.dsn) {\n    script.dataset.dsn = props.dsn;\n  }\n\n  script.onerror = (): void => {\n    const errorMessage = isDevelopment()\n      ? 'Please check if any ad blockers are enabled and try again.'\n      : 'Be sure to enable Web Analytics for your project and deploy again. See https://vercel.com/docs/analytics/quickstart for more information.';\n\n    // eslint-disable-next-line no-console -- Logging to console is intentional\n    console.log(\n      `[Vercel Web Analytics] Failed to load script from ${src}. ${errorMessage}`\n    );\n  };\n\n  if (isDevelopment() && props.debug === false) {\n    script.dataset.debug = 'false';\n  }\n\n  document.head.appendChild(script);\n}\n\n/**\n * Tracks a custom event. Please refer to the [documentation](https://vercel.com/docs/concepts/analytics/custom-events) for more information on custom events.\n * @param name - The name of the event.\n * * Examples: `Purchase`, `Click Button`, or `Play Video`.\n * @param [properties] - Additional properties of the event. Nested objects are not supported. Allowed values are `string`, `number`, `boolean`, and `null`.\n */\nfunction track(\n  name: string,\n  properties?: Record<string, AllowedPropertyValues>,\n  options?: {\n    flags?: FlagsDataInput;\n  }\n): void {\n  if (!isBrowser()) {\n    const msg =\n      '[Vercel Web Analytics] Please import `track` from `@vercel/analytics/server` when using this function in a server environment';\n\n    if (isProduction()) {\n      // eslint-disable-next-line no-console -- Show warning in production\n      console.warn(msg);\n    } else {\n      throw new Error(msg);\n    }\n\n    return;\n  }\n\n  if (!properties) {\n    window.va?.('event', { name, options });\n    return;\n  }\n\n  try {\n    const props = parseProperties(properties, {\n      strip: isProduction(),\n    });\n\n    window.va?.('event', {\n      name,\n      data: props,\n      options,\n    });\n  } catch (err) {\n    if (err instanceof Error && isDevelopment()) {\n      // eslint-disable-next-line no-console -- Logging to console is intentional\n      console.error(err);\n    }\n  }\n}\n\nfunction pageview({\n  route,\n  path,\n}: {\n  route?: string | null;\n  path?: string;\n}): void {\n  window.va?.('pageview', { route, path });\n}\n\nexport { inject, track, pageview, computeRoute };\nexport type { AnalyticsProps, BeforeSend, BeforeSendEvent };\n\n// eslint-disable-next-line import/no-default-export -- Default export is intentional\nexport default {\n  inject,\n  track,\n  computeRoute,\n};\n", "export function getBasePath(): string | undefined {\n  // !! important !!\n  // do not access env variables using process.env[varname]\n  // some bundles won't replace the value at build time.\n  // eslint-disable-next-line @typescript-eslint/prefer-optional-chain -- we can't use optionnal here, it'll break if process does not exist.\n  if (typeof process === 'undefined' || typeof process.env === 'undefined') {\n    return undefined;\n  }\n  return process.env.REACT_APP_VERCEL_OBSERVABILITY_BASEPATH;\n}\n"], "names": ["name"], "mappings": "", "ignoreList": [0, 1, 2, 3, 4, 5]}}, {"offset": {"line": 54, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/%40vercel/speed-insights/dist/next/index.mjs/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const SpeedInsights = registerClientReference(\n    function() { throw new Error(\"Attempted to call SpeedInsights() from the server but SpeedInsights is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@vercel/speed-insights/dist/next/index.mjs <module evaluation>\",\n    \"SpeedInsights\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,yFACA", "ignoreList": [0]}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 74, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/%40vercel/speed-insights/dist/next/index.mjs/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const SpeedInsights = registerClientReference(\n    function() { throw new Error(\"Attempted to call SpeedInsights() from the server but SpeedInsights is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@vercel/speed-insights/dist/next/index.mjs\",\n    \"SpeedInsights\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,qEACA", "ignoreList": [0]}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 88, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/%40vercel/speed-insights/src/nextjs/index.tsx", "file:///home/<USER>/Desktop/templgen/node_modules/%40vercel/speed-insights/src/react/index.tsx", "file:///home/<USER>/Desktop/templgen/node_modules/%40vercel/speed-insights/package.json", "file:///home/<USER>/Desktop/templgen/node_modules/%40vercel/speed-insights/src/queue.ts", "file:///home/<USER>/Desktop/templgen/node_modules/%40vercel/speed-insights/src/utils.ts", "file:///home/<USER>/Desktop/templgen/node_modules/%40vercel/speed-insights/src/generic.ts", "file:///home/<USER>/Desktop/templgen/node_modules/%40vercel/speed-insights/src/react/utils.ts", "file:///home/<USER>/Desktop/templgen/node_modules/%40vercel/speed-insights/src/nextjs/utils.ts"], "sourcesContent": ["'use client';\n\nimport React, { Suspense } from 'react';\nimport { SpeedInsights as SpeedInsightsScript } from '../react';\nimport type { SpeedInsightsProps } from '../types';\nimport { getBasePath, useRoute } from './utils';\n\ntype Props = Omit<SpeedInsightsProps, 'route'>;\n\nfunction SpeedInsightsComponent(props: Props): React.ReactElement {\n  const route = useRoute();\n\n  return (\n    <SpeedInsightsScript\n      route={route}\n      {...props}\n      framework=\"next\"\n      basePath={getBasePath()}\n    />\n  );\n}\n\nexport function SpeedInsights(props: Props): null {\n  // Because of incompatible types between ReactNode in React 19 and React 18 we return null (which is also what we render)\n  return (\n    <Suspense fallback={null}>\n      <SpeedInsightsComponent {...props} />\n    </Suspense>\n  ) as never;\n}\n", "'use client';\n\nimport { useEffect, useRef } from 'react';\nimport type { SpeedInsightsProps } from '../types';\nimport { computeRoute, injectSpeedInsights } from '../generic';\nimport { getBasePath } from './utils';\n\nexport function SpeedInsights(\n  props: SpeedInsightsProps & {\n    framework?: string;\n    basePath?: string;\n  },\n): JSX.Element | null {\n  useEffect(() => {\n    if (props.beforeSend) {\n      window.si?.('beforeSend', props.beforeSend);\n    }\n  }, [props.beforeSend]);\n\n  const setScriptRoute = useRef<((path: string) => void) | null>(null);\n  useEffect(() => {\n    if (!setScriptRoute.current) {\n      const script = injectSpeedInsights({\n        framework: props.framework ?? 'react',\n        basePath: props.basePath ?? getBasePath(),\n        ...props,\n      });\n      if (script) {\n        setScriptRoute.current = script.setRoute;\n      }\n    } else if (props.route) {\n      setScriptRoute.current(props.route);\n    }\n  }, [props.route]);\n\n  return null;\n}\n\nexport { computeRoute };\n", "{\n  \"name\": \"@vercel/speed-insights\",\n  \"version\": \"1.2.0\",\n  \"description\": \"Speed Insights is a tool for measuring web performance and providing suggestions for improvement.\",\n  \"keywords\": [\n    \"speed-insights\",\n    \"vercel\"\n  ],\n  \"repository\": {\n    \"url\": \"github:vercel/speed-insights\",\n    \"directory\": \"packages/web\"\n  },\n  \"license\": \"Apache-2.0\",\n  \"exports\": {\n    \"./package.json\": \"./package.json\",\n    \".\": {\n      \"browser\": \"./dist/index.mjs\",\n      \"import\": \"./dist/index.mjs\",\n      \"require\": \"./dist/index.js\"\n    },\n    \"./astro\": {\n      \"import\": \"./dist/astro/component.ts\"\n    },\n    \"./next\": {\n      \"browser\": \"./dist/next/index.mjs\",\n      \"import\": \"./dist/next/index.mjs\",\n      \"require\": \"./dist/next/index.js\"\n    },\n    \"./nuxt\": {\n      \"browser\": \"./dist/nuxt/index.mjs\",\n      \"import\": \"./dist/nuxt/index.mjs\",\n      \"require\": \"./dist/nuxt/index.js\"\n    },\n    \"./react\": {\n      \"browser\": \"./dist/react/index.mjs\",\n      \"import\": \"./dist/react/index.mjs\",\n      \"require\": \"./dist/react/index.js\"\n    },\n    \"./remix\": {\n      \"browser\": \"./dist/remix/index.mjs\",\n      \"import\": \"./dist/remix/index.mjs\",\n      \"require\": \"./dist/remix/index.js\"\n    },\n    \"./sveltekit\": {\n      \"svelte\": \"./dist/sveltekit/index.mjs\",\n      \"types\": \"./dist/sveltekit/index.d.ts\"\n    },\n    \"./vue\": {\n      \"browser\": \"./dist/vue/index.mjs\",\n      \"import\": \"./dist/vue/index.mjs\",\n      \"require\": \"./dist/vue/index.js\"\n    }\n  },\n  \"main\": \"./dist/index.js\",\n  \"types\": \"./dist/index.d.ts\",\n  \"typesVersions\": {\n    \"*\": {\n      \"*\": [\n        \"dist/index.d.ts\"\n      ],\n      \"react\": [\n        \"dist/react/index.d.ts\"\n      ],\n      \"next\": [\n        \"dist/next/index.d.ts\"\n      ],\n      \"nuxt\": [\n        \"dist/nuxt/index.d.ts\"\n      ],\n      \"remix\": [\n        \"dist/remix/index.d.ts\"\n      ],\n      \"sveltekit\": [\n        \"dist/sveltekit/index.d.ts\"\n      ],\n      \"vue\": [\n        \"dist/vue/index.d.ts\"\n      ]\n    }\n  },\n  \"scripts\": {\n    \"build\": \"tsup && pnpm copy-astro\",\n    \"copy-astro\": \"cp -R src/astro dist/\",\n    \"dev\": \"pnpm copy-astro && tsup --watch\",\n    \"postinstall\": \"node scripts/postinstall.mjs\",\n    \"lint\": \"eslint .\",\n    \"lint-fix\": \"eslint . --fix\",\n    \"test\": \"vitest\",\n    \"type-check\": \"tsc --noEmit\"\n  },\n  \"devDependencies\": {\n    \"@remix-run/react\": \"^2.14.0\",\n    \"@sveltejs/kit\": \"^2.8.1\",\n    \"@swc/core\": \"^1.9.2\",\n    \"@testing-library/jest-dom\": \"^6.6.3\",\n    \"@testing-library/react\": \"^16.0.1\",\n    \"@types/node\": \"^22.9.1\",\n    \"@types/react\": \"^18.3.12\",\n    \"copyfiles\": \"^2.4.1\",\n    \"jsdom\": \"^25.0.1\",\n    \"next\": \"^14.0.4\",\n    \"react\": \"^18.3.1\",\n    \"react-dom\": \"^18.3.1\",\n    \"svelte\": \"^5.2.7\",\n    \"tsup\": \"8.3.5\",\n    \"vitest\": \"^2.1.5\",\n    \"vue\": \"^3.5.13\",\n    \"vue-router\": \"^4.4.5\"\n  },\n  \"peerDependencies\": {\n    \"@sveltejs/kit\": \"^1 || ^2\",\n    \"next\": \">= 13\",\n    \"react\": \"^18 || ^19 || ^19.0.0-rc\",\n    \"svelte\": \">= 4\",\n    \"vue\": \"^3\",\n    \"vue-router\": \"^4\"\n  },\n  \"peerDependenciesMeta\": {\n    \"@sveltejs/kit\": {\n      \"optional\": true\n    },\n    \"next\": {\n      \"optional\": true\n    },\n    \"react\": {\n      \"optional\": true\n    },\n    \"svelte\": {\n      \"optional\": true\n    },\n    \"vue\": {\n      \"optional\": true\n    },\n    \"vue-router\": {\n      \"optional\": true\n    }\n  }\n}\n", "export const initQueue = (): void => {\n  // initialize va until script is loaded\n  if (window.si) return;\n\n  window.si = function a(...params): void {\n    (window.siq = window.siq || []).push(params);\n  };\n};\n", "import type { SpeedInsightsProps } from './types';\n\nexport function isBrowser(): boolean {\n  return typeof window !== 'undefined';\n}\n\nfunction detectEnvironment(): 'development' | 'production' {\n  try {\n    const env = process.env.NODE_ENV;\n    if (env === 'development' || env === 'test') {\n      return 'development';\n    }\n  } catch (e) {\n    // do nothing, this is okay\n  }\n  return 'production';\n}\n\nexport function isProduction(): boolean {\n  return detectEnvironment() === 'production';\n}\n\nexport function isDevelopment(): boolean {\n  return detectEnvironment() === 'development';\n}\n\nexport function computeRoute(\n  pathname: string | null,\n  pathParams: Record<string, string | string[]> | null,\n): string | null {\n  if (!pathname || !pathParams) {\n    return pathname;\n  }\n\n  let result = pathname;\n  try {\n    const entries = Object.entries(pathParams);\n    // simple keys must be handled first\n    for (const [key, value] of entries) {\n      if (!Array.isArray(value)) {\n        const matcher = turnValueToRegExp(value);\n        if (matcher.test(result)) {\n          result = result.replace(matcher, `/[${key}]`);\n        }\n      }\n    }\n    // array values next\n    for (const [key, value] of entries) {\n      if (Array.isArray(value)) {\n        const matcher = turnValueToRegExp(value.join('/'));\n        if (matcher.test(result)) {\n          result = result.replace(matcher, `/[...${key}]`);\n        }\n      }\n    }\n    return result;\n  } catch (e) {\n    return pathname;\n  }\n}\n\nfunction turnValueToRegExp(value: string): RegExp {\n  return new RegExp(`/${escapeRegExp(value)}(?=[/?#]|$)`);\n}\n\nfunction escapeRegExp(string: string): string {\n  return string.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&');\n}\n\nexport function getScriptSrc(\n  props: SpeedInsightsProps & { basePath?: string },\n): string {\n  if (props.scriptSrc) {\n    return props.scriptSrc;\n  }\n  if (isDevelopment()) {\n    return 'https://va.vercel-scripts.com/v1/speed-insights/script.debug.js';\n  }\n  if (props.dsn) {\n    return 'https://va.vercel-scripts.com/v1/speed-insights/script.js';\n  }\n  if (props.basePath) {\n    return `${props.basePath}/speed-insights/script.js`;\n  }\n  return '/_vercel/speed-insights/script.js';\n}\n", "import { name as packageName, version } from '../package.json';\nimport { initQueue } from './queue';\nimport type { SpeedInsightsProps } from './types';\nimport { computeRoute, getScriptSrc, isBrowser, isDevelopment } from './utils';\n\n/**\n * Injects the Vercel Speed Insights script into the page head and starts tracking page views. Read more in our [documentation](https://vercel.com/docs/speed-insights).\n * @param [props] - Speed Insights options.\n * @param [props.debug] - Whether to enable debug logging in development. Defaults to `true`.\n * @param [props.beforeSend] - A middleware function to modify events before they are sent. Should return the event object or `null` to cancel the event.\n * @param [props.sampleRate] - When setting to 0.5, 50% of the events will be sent to Vercel Speed Insights. Defaults to `1`.\n * @param [props.route] - The dynamic route of the page.\n * @param [props.dsn] - The DSN of the project to send events to. Only required when self-hosting.\n */\nfunction injectSpeedInsights(\n  props: SpeedInsightsProps & {\n    framework?: string;\n    basePath?: string;\n  } = {},\n): {\n  setRoute: (route: string | null) => void;\n} | null {\n  // When route is null, it means that pages router is not ready yet. Will resolve soon\n  if (!isBrowser() || props.route === null) return null;\n\n  initQueue();\n\n  const src = getScriptSrc(props);\n\n  if (document.head.querySelector(`script[src*=\"${src}\"]`)) return null;\n\n  if (props.beforeSend) {\n    window.si?.('beforeSend', props.beforeSend);\n  }\n\n  const script = document.createElement('script');\n  script.src = src;\n  script.defer = true;\n  script.dataset.sdkn =\n    packageName + (props.framework ? `/${props.framework}` : '');\n  script.dataset.sdkv = version;\n\n  if (props.sampleRate) {\n    script.dataset.sampleRate = props.sampleRate.toString();\n  }\n  if (props.route) {\n    script.dataset.route = props.route;\n  }\n  if (props.endpoint) {\n    script.dataset.endpoint = props.endpoint;\n  } else if (props.basePath) {\n    script.dataset.endpoint = `${props.basePath}/speed-insights/vitals`;\n  }\n  if (props.dsn) {\n    script.dataset.dsn = props.dsn;\n  }\n  if (isDevelopment() && props.debug === false) {\n    script.dataset.debug = 'false';\n  }\n\n  script.onerror = (): void => {\n    // eslint-disable-next-line no-console -- Logging is okay here\n    console.log(\n      `[Vercel Speed Insights] Failed to load script from ${src}. Please check if any content blockers are enabled and try again.`,\n    );\n  };\n\n  document.head.appendChild(script);\n\n  return {\n    setRoute: (route: string | null): void => {\n      script.dataset.route = route ?? undefined;\n    },\n  };\n}\n\nexport { injectSpeedInsights, computeRoute };\nexport type { SpeedInsightsProps };\n\n// eslint-disable-next-line import/no-default-export -- Allow default export\nexport default {\n  injectSpeedInsights,\n  computeRoute,\n};\n", "export function getBasePath(): string | undefined {\n  // !! important !!\n  // do not access env variables using process.env[varname]\n  // some bundles won't replace the value at build time.\n  // eslint-disable-next-line @typescript-eslint/prefer-optional-chain -- we can't use optionnal here, it'll break if process does not exist.\n  if (typeof process === 'undefined' || typeof process.env === 'undefined') {\n    return undefined;\n  }\n  return process.env.REACT_APP_VERCEL_OBSERVABILITY_BASEPATH;\n}\n", "'use client';\n/* eslint-disable @typescript-eslint/no-unnecessary-condition -- can be empty in pages router */\nimport { useParams, usePathname, useSearchParams } from 'next/navigation.js';\nimport { computeRoute } from '../utils';\n\nexport const useRoute = (): string | null => {\n  const params = useParams();\n  const searchParams = useSearchParams() || new URLSearchParams();\n  const path = usePathname();\n  // Until we have route parameters, we don't compute the route\n  if (!params) {\n    return null;\n  }\n  // in Next.js@13, useParams() could return an empty object for pages router, and we default to searchParams.\n  const finalParams = Object.keys(params).length\n    ? params\n    : Object.fromEntries(searchParams.entries());\n  return computeRoute(path, finalParams);\n};\n\nexport function getBasePath(): string | undefined {\n  // !! important !!\n  // do not access env variables using process.env[varname]\n  // some bundles won't replace the value at build time.\n  // eslint-disable-next-line @typescript-eslint/prefer-optional-chain -- we can't use optionnal here, it'll break if process does not exist.\n  if (typeof process === 'undefined' || typeof process.env === 'undefined') {\n    return undefined;\n  }\n  return process.env.NEXT_PUBLIC_VERCEL_OBSERVABILITY_BASEPATH;\n}\n"], "names": ["get<PERSON><PERSON><PERSON><PERSON>", "get<PERSON><PERSON><PERSON><PERSON>", "SpeedInsights"], "mappings": "", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7]}}, {"offset": {"line": 92, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 98, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/sonner/dist/index.mjs/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Toaster = registerClientReference(\n    function() { throw new Error(\"Attempted to call Toaster() from the server but To<PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/sonner/dist/index.mjs <module evaluation>\",\n    \"Toaster\",\n);\nexport const toast = registerClientReference(\n    function() { throw new Error(\"Attempted to call toast() from the server but toast is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/sonner/dist/index.mjs <module evaluation>\",\n    \"toast\",\n);\nexport const useSonner = registerClientReference(\n    function() { throw new Error(\"Attempted to call useSonner() from the server but useSonner is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/sonner/dist/index.mjs <module evaluation>\",\n    \"useSonner\",\n);\n"], "names": [], "mappings": ";;;;;AAAA;;AACO,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,oEACA;AAEG,MAAM,QAAQ,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,oEACA;AAEG,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,oEACA", "ignoreList": [0]}}, {"offset": {"line": 114, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 120, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/sonner/dist/index.mjs/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Toaster = registerClientReference(\n    function() { throw new Error(\"Attempted to call Toaster() from the server but To<PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/sonner/dist/index.mjs\",\n    \"Toaster\",\n);\nexport const toast = registerClientReference(\n    function() { throw new Error(\"Attempted to call toast() from the server but toast is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/sonner/dist/index.mjs\",\n    \"toast\",\n);\nexport const useSonner = registerClientReference(\n    function() { throw new Error(\"Attempted to call useSonner() from the server but useSonner is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/sonner/dist/index.mjs\",\n    \"useSonner\",\n);\n"], "names": [], "mappings": ";;;;;AAAA;;AACO,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,gDACA;AAEG,MAAM,QAAQ,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,gDACA;AAEG,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,gDACA", "ignoreList": [0]}}, {"offset": {"line": 136, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 142, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/sonner/src/index.tsx", "file:///home/<USER>/Desktop/templgen/node_modules/sonner/src/assets.tsx", "file:///home/<USER>/Desktop/templgen/node_modules/sonner/src/hooks.tsx", "file:///home/<USER>/Desktop/templgen/node_modules/sonner/src/state.ts", "file:///home/<USER>/Desktop/templgen/node_modules/sonner/dist/%23style-inject%3A%23style-inject", "file:///home/<USER>/Desktop/templgen/node_modules/sonner/src/styles.css", "file:///home/<USER>/Desktop/templgen/node_modules/sonner/src/types.ts"], "sourcesContent": ["'use client';\n\nimport React, { forwardRef, isValidElement } from 'react';\nimport ReactDOM from 'react-dom';\n\nimport { CloseIcon, getAsset, Loader } from './assets';\nimport { useIsDocumentHidden } from './hooks';\nimport { toast, ToastState } from './state';\nimport './styles.css';\nimport {\n  isAction,\n  SwipeDirection,\n  type ExternalToast,\n  type HeightT,\n  type ToasterProps,\n  type ToastProps,\n  type ToastT,\n  type ToastToDismiss,\n} from './types';\n\n// Visible toasts amount\nconst VISIBLE_TOASTS_AMOUNT = 3;\n\n// Viewport padding\nconst VIEWPORT_OFFSET = '32px';\n\n// Mobile viewport padding\nconst MOBILE_VIEWPORT_OFFSET = '16px';\n\n// Default lifetime of a toasts (in ms)\nconst TOAST_LIFETIME = 4000;\n\n// Default toast width\nconst TOAST_WIDTH = 356;\n\n// Default gap between toasts\nconst GAP = 14;\n\n// Threshold to dismiss a toast\nconst SWIPE_THRESHOLD = 20;\n\n// Equal to exit animation duration\nconst TIME_BEFORE_UNMOUNT = 200;\n\nfunction cn(...classes: (string | undefined)[]) {\n  return classes.filter(Boolean).join(' ');\n}\n\nfunction getDefaultSwipeDirections(position: string): Array<SwipeDirection> {\n  const [y, x] = position.split('-');\n  const directions: Array<SwipeDirection> = [];\n\n  if (y) {\n    directions.push(y as SwipeDirection);\n  }\n\n  if (x) {\n    directions.push(x as SwipeDirection);\n  }\n\n  return directions;\n}\n\nconst Toast = (props: ToastProps) => {\n  const {\n    invert: ToasterInvert,\n    toast,\n    unstyled,\n    interacting,\n    setHeights,\n    visibleToasts,\n    heights,\n    index,\n    toasts,\n    expanded,\n    removeToast,\n    defaultRichColors,\n    closeButton: closeButtonFromToaster,\n    style,\n    cancelButtonStyle,\n    actionButtonStyle,\n    className = '',\n    descriptionClassName = '',\n    duration: durationFromToaster,\n    position,\n    gap,\n    loadingIcon: loadingIconProp,\n    expandByDefault,\n    classNames,\n    icons,\n    closeButtonAriaLabel = 'Close toast',\n    pauseWhenPageIsHidden,\n  } = props;\n  const [swipeDirection, setSwipeDirection] = React.useState<'x' | 'y' | null>(null);\n  const [swipeOutDirection, setSwipeOutDirection] = React.useState<'left' | 'right' | 'up' | 'down' | null>(null);\n  const [mounted, setMounted] = React.useState(false);\n  const [removed, setRemoved] = React.useState(false);\n  const [swiping, setSwiping] = React.useState(false);\n  const [swipeOut, setSwipeOut] = React.useState(false);\n  const [isSwiped, setIsSwiped] = React.useState(false);\n  const [offsetBeforeRemove, setOffsetBeforeRemove] = React.useState(0);\n  const [initialHeight, setInitialHeight] = React.useState(0);\n  const remainingTime = React.useRef(toast.duration || durationFromToaster || TOAST_LIFETIME);\n  const dragStartTime = React.useRef<Date | null>(null);\n  const toastRef = React.useRef<HTMLLIElement>(null);\n  const isFront = index === 0;\n  const isVisible = index + 1 <= visibleToasts;\n  const toastType = toast.type;\n  const dismissible = toast.dismissible !== false;\n  const toastClassname = toast.className || '';\n  const toastDescriptionClassname = toast.descriptionClassName || '';\n  // Height index is used to calculate the offset as it gets updated before the toast array, which means we can calculate the new layout faster.\n  const heightIndex = React.useMemo(\n    () => heights.findIndex((height) => height.toastId === toast.id) || 0,\n    [heights, toast.id],\n  );\n  const closeButton = React.useMemo(\n    () => toast.closeButton ?? closeButtonFromToaster,\n    [toast.closeButton, closeButtonFromToaster],\n  );\n  const duration = React.useMemo(\n    () => toast.duration || durationFromToaster || TOAST_LIFETIME,\n    [toast.duration, durationFromToaster],\n  );\n  const closeTimerStartTimeRef = React.useRef(0);\n  const offset = React.useRef(0);\n  const lastCloseTimerStartTimeRef = React.useRef(0);\n  const pointerStartRef = React.useRef<{ x: number; y: number } | null>(null);\n  const [y, x] = position.split('-');\n  const toastsHeightBefore = React.useMemo(() => {\n    return heights.reduce((prev, curr, reducerIndex) => {\n      // Calculate offset up until current toast\n      if (reducerIndex >= heightIndex) {\n        return prev;\n      }\n\n      return prev + curr.height;\n    }, 0);\n  }, [heights, heightIndex]);\n  const isDocumentHidden = useIsDocumentHidden();\n\n  const invert = toast.invert || ToasterInvert;\n  const disabled = toastType === 'loading';\n\n  offset.current = React.useMemo(() => heightIndex * gap + toastsHeightBefore, [heightIndex, toastsHeightBefore]);\n\n  React.useEffect(() => {\n    remainingTime.current = duration;\n  }, [duration]);\n\n  React.useEffect(() => {\n    // Trigger enter animation without using CSS animation\n    setMounted(true);\n  }, []);\n\n  React.useEffect(() => {\n    const toastNode = toastRef.current;\n    if (toastNode) {\n      const height = toastNode.getBoundingClientRect().height;\n      // Add toast height to heights array after the toast is mounted\n      setInitialHeight(height);\n      setHeights((h) => [{ toastId: toast.id, height, position: toast.position }, ...h]);\n      return () => setHeights((h) => h.filter((height) => height.toastId !== toast.id));\n    }\n  }, [setHeights, toast.id]);\n\n  React.useLayoutEffect(() => {\n    if (!mounted) return;\n    const toastNode = toastRef.current;\n    const originalHeight = toastNode.style.height;\n    toastNode.style.height = 'auto';\n    const newHeight = toastNode.getBoundingClientRect().height;\n    toastNode.style.height = originalHeight;\n\n    setInitialHeight(newHeight);\n\n    setHeights((heights) => {\n      const alreadyExists = heights.find((height) => height.toastId === toast.id);\n      if (!alreadyExists) {\n        return [{ toastId: toast.id, height: newHeight, position: toast.position }, ...heights];\n      } else {\n        return heights.map((height) => (height.toastId === toast.id ? { ...height, height: newHeight } : height));\n      }\n    });\n  }, [mounted, toast.title, toast.description, setHeights, toast.id]);\n\n  const deleteToast = React.useCallback(() => {\n    // Save the offset for the exit swipe animation\n    setRemoved(true);\n    setOffsetBeforeRemove(offset.current);\n    setHeights((h) => h.filter((height) => height.toastId !== toast.id));\n\n    setTimeout(() => {\n      removeToast(toast);\n    }, TIME_BEFORE_UNMOUNT);\n  }, [toast, removeToast, setHeights, offset]);\n\n  React.useEffect(() => {\n    if ((toast.promise && toastType === 'loading') || toast.duration === Infinity || toast.type === 'loading') return;\n    let timeoutId: NodeJS.Timeout;\n\n    // Pause the timer on each hover\n    const pauseTimer = () => {\n      if (lastCloseTimerStartTimeRef.current < closeTimerStartTimeRef.current) {\n        // Get the elapsed time since the timer started\n        const elapsedTime = new Date().getTime() - closeTimerStartTimeRef.current;\n\n        remainingTime.current = remainingTime.current - elapsedTime;\n      }\n\n      lastCloseTimerStartTimeRef.current = new Date().getTime();\n    };\n\n    const startTimer = () => {\n      // setTimeout(, Infinity) behaves as if the delay is 0.\n      // As a result, the toast would be closed immediately, giving the appearance that it was never rendered.\n      // See: https://github.com/denysdovhan/wtfjs?tab=readme-ov-file#an-infinite-timeout\n      if (remainingTime.current === Infinity) return;\n\n      closeTimerStartTimeRef.current = new Date().getTime();\n\n      // Let the toast know it has started\n      timeoutId = setTimeout(() => {\n        toast.onAutoClose?.(toast);\n        deleteToast();\n      }, remainingTime.current);\n    };\n\n    if (expanded || interacting || (pauseWhenPageIsHidden && isDocumentHidden)) {\n      pauseTimer();\n    } else {\n      startTimer();\n    }\n\n    return () => clearTimeout(timeoutId);\n  }, [expanded, interacting, toast, toastType, pauseWhenPageIsHidden, isDocumentHidden, deleteToast]);\n\n  React.useEffect(() => {\n    if (toast.delete) {\n      deleteToast();\n    }\n  }, [deleteToast, toast.delete]);\n\n  function getLoadingIcon() {\n    if (icons?.loading) {\n      return (\n        <div\n          className={cn(classNames?.loader, toast?.classNames?.loader, 'sonner-loader')}\n          data-visible={toastType === 'loading'}\n        >\n          {icons.loading}\n        </div>\n      );\n    }\n\n    if (loadingIconProp) {\n      return (\n        <div\n          className={cn(classNames?.loader, toast?.classNames?.loader, 'sonner-loader')}\n          data-visible={toastType === 'loading'}\n        >\n          {loadingIconProp}\n        </div>\n      );\n    }\n    return <Loader className={cn(classNames?.loader, toast?.classNames?.loader)} visible={toastType === 'loading'} />;\n  }\n\n  return (\n    <li\n      tabIndex={0}\n      ref={toastRef}\n      className={cn(\n        className,\n        toastClassname,\n        classNames?.toast,\n        toast?.classNames?.toast,\n        classNames?.default,\n        classNames?.[toastType],\n        toast?.classNames?.[toastType],\n      )}\n      data-sonner-toast=\"\"\n      data-rich-colors={toast.richColors ?? defaultRichColors}\n      data-styled={!Boolean(toast.jsx || toast.unstyled || unstyled)}\n      data-mounted={mounted}\n      data-promise={Boolean(toast.promise)}\n      data-swiped={isSwiped}\n      data-removed={removed}\n      data-visible={isVisible}\n      data-y-position={y}\n      data-x-position={x}\n      data-index={index}\n      data-front={isFront}\n      data-swiping={swiping}\n      data-dismissible={dismissible}\n      data-type={toastType}\n      data-invert={invert}\n      data-swipe-out={swipeOut}\n      data-swipe-direction={swipeOutDirection}\n      data-expanded={Boolean(expanded || (expandByDefault && mounted))}\n      style={\n        {\n          '--index': index,\n          '--toasts-before': index,\n          '--z-index': toasts.length - index,\n          '--offset': `${removed ? offsetBeforeRemove : offset.current}px`,\n          '--initial-height': expandByDefault ? 'auto' : `${initialHeight}px`,\n          ...style,\n          ...toast.style,\n        } as React.CSSProperties\n      }\n      onDragEnd={() => {\n        setSwiping(false);\n        setSwipeDirection(null);\n        pointerStartRef.current = null;\n      }}\n      onPointerDown={(event) => {\n        if (disabled || !dismissible) return;\n        dragStartTime.current = new Date();\n        setOffsetBeforeRemove(offset.current);\n        // Ensure we maintain correct pointer capture even when going outside of the toast (e.g. when swiping)\n        (event.target as HTMLElement).setPointerCapture(event.pointerId);\n        if ((event.target as HTMLElement).tagName === 'BUTTON') return;\n        setSwiping(true);\n        pointerStartRef.current = { x: event.clientX, y: event.clientY };\n      }}\n      onPointerUp={() => {\n        if (swipeOut || !dismissible) return;\n\n        pointerStartRef.current = null;\n        const swipeAmountX = Number(\n          toastRef.current?.style.getPropertyValue('--swipe-amount-x').replace('px', '') || 0,\n        );\n        const swipeAmountY = Number(\n          toastRef.current?.style.getPropertyValue('--swipe-amount-y').replace('px', '') || 0,\n        );\n        const timeTaken = new Date().getTime() - dragStartTime.current?.getTime();\n\n        const swipeAmount = swipeDirection === 'x' ? swipeAmountX : swipeAmountY;\n        const velocity = Math.abs(swipeAmount) / timeTaken;\n\n        if (Math.abs(swipeAmount) >= SWIPE_THRESHOLD || velocity > 0.11) {\n          setOffsetBeforeRemove(offset.current);\n          toast.onDismiss?.(toast);\n\n          if (swipeDirection === 'x') {\n            setSwipeOutDirection(swipeAmountX > 0 ? 'right' : 'left');\n          } else {\n            setSwipeOutDirection(swipeAmountY > 0 ? 'down' : 'up');\n          }\n\n          deleteToast();\n          setSwipeOut(true);\n          setIsSwiped(false);\n          return;\n        }\n\n        setSwiping(false);\n        setSwipeDirection(null);\n      }}\n      onPointerMove={(event) => {\n        if (!pointerStartRef.current || !dismissible) return;\n\n        const isHighlighted = window.getSelection()?.toString().length > 0;\n        if (isHighlighted) return;\n\n        const yDelta = event.clientY - pointerStartRef.current.y;\n        const xDelta = event.clientX - pointerStartRef.current.x;\n\n        const swipeDirections = props.swipeDirections ?? getDefaultSwipeDirections(position);\n\n        // Determine swipe direction if not already locked\n        if (!swipeDirection && (Math.abs(xDelta) > 1 || Math.abs(yDelta) > 1)) {\n          setSwipeDirection(Math.abs(xDelta) > Math.abs(yDelta) ? 'x' : 'y');\n        }\n\n        let swipeAmount = { x: 0, y: 0 };\n\n        // Only apply swipe in the locked direction\n        if (swipeDirection === 'y') {\n          // Handle vertical swipes\n          if (swipeDirections.includes('top') || swipeDirections.includes('bottom')) {\n            if (swipeDirections.includes('top') && yDelta < 0) {\n              swipeAmount.y = yDelta;\n            } else if (swipeDirections.includes('bottom') && yDelta > 0) {\n              swipeAmount.y = yDelta;\n            }\n          }\n        } else if (swipeDirection === 'x') {\n          // Handle horizontal swipes\n          if (swipeDirections.includes('left') || swipeDirections.includes('right')) {\n            if (swipeDirections.includes('left') && xDelta < 0) {\n              swipeAmount.x = xDelta;\n            } else if (swipeDirections.includes('right') && xDelta > 0) {\n              swipeAmount.x = xDelta;\n            }\n          }\n        }\n\n        if (Math.abs(swipeAmount.x) > 0 || Math.abs(swipeAmount.y) > 0) {\n          setIsSwiped(true);\n        }\n\n        // Apply transform using both x and y values\n        toastRef.current?.style.setProperty('--swipe-amount-x', `${swipeAmount.x}px`);\n        toastRef.current?.style.setProperty('--swipe-amount-y', `${swipeAmount.y}px`);\n      }}\n    >\n      {closeButton && !toast.jsx ? (\n        <button\n          aria-label={closeButtonAriaLabel}\n          data-disabled={disabled}\n          data-close-button\n          onClick={\n            disabled || !dismissible\n              ? () => {}\n              : () => {\n                  deleteToast();\n                  toast.onDismiss?.(toast);\n                }\n          }\n          className={cn(classNames?.closeButton, toast?.classNames?.closeButton)}\n        >\n          {icons?.close ?? CloseIcon}\n        </button>\n      ) : null}\n      {/* TODO: This can be cleaner */}\n      {toast.jsx || isValidElement(toast.title) ? (\n        toast.jsx ? (\n          toast.jsx\n        ) : typeof toast.title === 'function' ? (\n          toast.title()\n        ) : (\n          toast.title\n        )\n      ) : (\n        <>\n          {toastType || toast.icon || toast.promise ? (\n            <div data-icon=\"\" className={cn(classNames?.icon, toast?.classNames?.icon)}>\n              {toast.promise || (toast.type === 'loading' && !toast.icon) ? toast.icon || getLoadingIcon() : null}\n              {toast.type !== 'loading' ? toast.icon || icons?.[toastType] || getAsset(toastType) : null}\n            </div>\n          ) : null}\n\n          <div data-content=\"\" className={cn(classNames?.content, toast?.classNames?.content)}>\n            <div data-title=\"\" className={cn(classNames?.title, toast?.classNames?.title)}>\n              {typeof toast.title === 'function' ? toast.title() : toast.title}\n            </div>\n            {toast.description ? (\n              <div\n                data-description=\"\"\n                className={cn(\n                  descriptionClassName,\n                  toastDescriptionClassname,\n                  classNames?.description,\n                  toast?.classNames?.description,\n                )}\n              >\n                {typeof toast.description === 'function' ? toast.description() : toast.description}\n              </div>\n            ) : null}\n          </div>\n          {isValidElement(toast.cancel) ? (\n            toast.cancel\n          ) : toast.cancel && isAction(toast.cancel) ? (\n            <button\n              data-button\n              data-cancel\n              style={toast.cancelButtonStyle || cancelButtonStyle}\n              onClick={(event) => {\n                // We need to check twice because typescript\n                if (!isAction(toast.cancel)) return;\n                if (!dismissible) return;\n                toast.cancel.onClick?.(event);\n                deleteToast();\n              }}\n              className={cn(classNames?.cancelButton, toast?.classNames?.cancelButton)}\n            >\n              {toast.cancel.label}\n            </button>\n          ) : null}\n          {isValidElement(toast.action) ? (\n            toast.action\n          ) : toast.action && isAction(toast.action) ? (\n            <button\n              data-button\n              data-action\n              style={toast.actionButtonStyle || actionButtonStyle}\n              onClick={(event) => {\n                // We need to check twice because typescript\n                if (!isAction(toast.action)) return;\n                toast.action.onClick?.(event);\n                if (event.defaultPrevented) return;\n                deleteToast();\n              }}\n              className={cn(classNames?.actionButton, toast?.classNames?.actionButton)}\n            >\n              {toast.action.label}\n            </button>\n          ) : null}\n        </>\n      )}\n    </li>\n  );\n};\n\nfunction getDocumentDirection(): ToasterProps['dir'] {\n  if (typeof window === 'undefined') return 'ltr';\n  if (typeof document === 'undefined') return 'ltr'; // For Fresh purpose\n\n  const dirAttribute = document.documentElement.getAttribute('dir');\n\n  if (dirAttribute === 'auto' || !dirAttribute) {\n    return window.getComputedStyle(document.documentElement).direction as ToasterProps['dir'];\n  }\n\n  return dirAttribute as ToasterProps['dir'];\n}\n\nfunction assignOffset(defaultOffset: ToasterProps['offset'], mobileOffset: ToasterProps['mobileOffset']) {\n  const styles = {} as React.CSSProperties;\n\n  [defaultOffset, mobileOffset].forEach((offset, index) => {\n    const isMobile = index === 1;\n    const prefix = isMobile ? '--mobile-offset' : '--offset';\n    const defaultValue = isMobile ? MOBILE_VIEWPORT_OFFSET : VIEWPORT_OFFSET;\n\n    function assignAll(offset: string | number) {\n      ['top', 'right', 'bottom', 'left'].forEach((key) => {\n        styles[`${prefix}-${key}`] = typeof offset === 'number' ? `${offset}px` : offset;\n      });\n    }\n\n    if (typeof offset === 'number' || typeof offset === 'string') {\n      assignAll(offset);\n    } else if (typeof offset === 'object') {\n      ['top', 'right', 'bottom', 'left'].forEach((key) => {\n        if (offset[key] === undefined) {\n          styles[`${prefix}-${key}`] = defaultValue;\n        } else {\n          styles[`${prefix}-${key}`] = typeof offset[key] === 'number' ? `${offset[key]}px` : offset[key];\n        }\n      });\n    } else {\n      assignAll(defaultValue);\n    }\n  });\n\n  return styles;\n}\n\nfunction useSonner() {\n  const [activeToasts, setActiveToasts] = React.useState<ToastT[]>([]);\n\n  React.useEffect(() => {\n    return ToastState.subscribe((toast) => {\n      if ((toast as ToastToDismiss).dismiss) {\n        setTimeout(() => {\n          ReactDOM.flushSync(() => {\n            setActiveToasts((toasts) => toasts.filter((t) => t.id !== toast.id));\n          });\n        });\n        return;\n      }\n\n      // Prevent batching, temp solution.\n      setTimeout(() => {\n        ReactDOM.flushSync(() => {\n          setActiveToasts((toasts) => {\n            const indexOfExistingToast = toasts.findIndex((t) => t.id === toast.id);\n\n            // Update the toast if it already exists\n            if (indexOfExistingToast !== -1) {\n              return [\n                ...toasts.slice(0, indexOfExistingToast),\n                { ...toasts[indexOfExistingToast], ...toast },\n                ...toasts.slice(indexOfExistingToast + 1),\n              ];\n            }\n\n            return [toast, ...toasts];\n          });\n        });\n      });\n    });\n  }, []);\n\n  return {\n    toasts: activeToasts,\n  };\n}\n\nconst Toaster = forwardRef<HTMLElement, ToasterProps>(function Toaster(props, ref) {\n  const {\n    invert,\n    position = 'bottom-right',\n    hotkey = ['altKey', 'KeyT'],\n    expand,\n    closeButton,\n    className,\n    offset,\n    mobileOffset,\n    theme = 'light',\n    richColors,\n    duration,\n    style,\n    visibleToasts = VISIBLE_TOASTS_AMOUNT,\n    toastOptions,\n    dir = getDocumentDirection(),\n    gap = GAP,\n    loadingIcon,\n    icons,\n    containerAriaLabel = 'Notifications',\n    pauseWhenPageIsHidden,\n  } = props;\n  const [toasts, setToasts] = React.useState<ToastT[]>([]);\n  const possiblePositions = React.useMemo(() => {\n    return Array.from(\n      new Set([position].concat(toasts.filter((toast) => toast.position).map((toast) => toast.position))),\n    );\n  }, [toasts, position]);\n  const [heights, setHeights] = React.useState<HeightT[]>([]);\n  const [expanded, setExpanded] = React.useState(false);\n  const [interacting, setInteracting] = React.useState(false);\n  const [actualTheme, setActualTheme] = React.useState(\n    theme !== 'system'\n      ? theme\n      : typeof window !== 'undefined'\n      ? window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches\n        ? 'dark'\n        : 'light'\n      : 'light',\n  );\n\n  const listRef = React.useRef<HTMLOListElement>(null);\n  const hotkeyLabel = hotkey.join('+').replace(/Key/g, '').replace(/Digit/g, '');\n  const lastFocusedElementRef = React.useRef<HTMLElement>(null);\n  const isFocusWithinRef = React.useRef(false);\n\n  const removeToast = React.useCallback((toastToRemove: ToastT) => {\n    setToasts((toasts) => {\n      if (!toasts.find((toast) => toast.id === toastToRemove.id)?.delete) {\n        ToastState.dismiss(toastToRemove.id);\n      }\n\n      return toasts.filter(({ id }) => id !== toastToRemove.id);\n    });\n  }, []);\n\n  React.useEffect(() => {\n    return ToastState.subscribe((toast) => {\n      if ((toast as ToastToDismiss).dismiss) {\n        setToasts((toasts) => toasts.map((t) => (t.id === toast.id ? { ...t, delete: true } : t)));\n        return;\n      }\n\n      // Prevent batching, temp solution.\n      setTimeout(() => {\n        ReactDOM.flushSync(() => {\n          setToasts((toasts) => {\n            const indexOfExistingToast = toasts.findIndex((t) => t.id === toast.id);\n\n            // Update the toast if it already exists\n            if (indexOfExistingToast !== -1) {\n              return [\n                ...toasts.slice(0, indexOfExistingToast),\n                { ...toasts[indexOfExistingToast], ...toast },\n                ...toasts.slice(indexOfExistingToast + 1),\n              ];\n            }\n\n            return [toast, ...toasts];\n          });\n        });\n      });\n    });\n  }, []);\n\n  React.useEffect(() => {\n    if (theme !== 'system') {\n      setActualTheme(theme);\n      return;\n    }\n\n    if (theme === 'system') {\n      // check if current preference is dark\n      if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {\n        // it's currently dark\n        setActualTheme('dark');\n      } else {\n        // it's not dark\n        setActualTheme('light');\n      }\n    }\n\n    if (typeof window === 'undefined') return;\n    const darkMediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n\n    try {\n      // Chrome & Firefox\n      darkMediaQuery.addEventListener('change', ({ matches }) => {\n        if (matches) {\n          setActualTheme('dark');\n        } else {\n          setActualTheme('light');\n        }\n      });\n    } catch (error) {\n      // Safari < 14\n      darkMediaQuery.addListener(({ matches }) => {\n        try {\n          if (matches) {\n            setActualTheme('dark');\n          } else {\n            setActualTheme('light');\n          }\n        } catch (e) {\n          console.error(e);\n        }\n      });\n    }\n  }, [theme]);\n\n  React.useEffect(() => {\n    // Ensure expanded is always false when no toasts are present / only one left\n    if (toasts.length <= 1) {\n      setExpanded(false);\n    }\n  }, [toasts]);\n\n  React.useEffect(() => {\n    const handleKeyDown = (event: KeyboardEvent) => {\n      const isHotkeyPressed = hotkey.every((key) => (event as any)[key] || event.code === key);\n\n      if (isHotkeyPressed) {\n        setExpanded(true);\n        listRef.current?.focus();\n      }\n\n      if (\n        event.code === 'Escape' &&\n        (document.activeElement === listRef.current || listRef.current?.contains(document.activeElement))\n      ) {\n        setExpanded(false);\n      }\n    };\n    document.addEventListener('keydown', handleKeyDown);\n\n    return () => document.removeEventListener('keydown', handleKeyDown);\n  }, [hotkey]);\n\n  React.useEffect(() => {\n    if (listRef.current) {\n      return () => {\n        if (lastFocusedElementRef.current) {\n          lastFocusedElementRef.current.focus({ preventScroll: true });\n          lastFocusedElementRef.current = null;\n          isFocusWithinRef.current = false;\n        }\n      };\n    }\n  }, [listRef.current]);\n\n  return (\n    // Remove item from normal navigation flow, only available via hotkey\n    <section\n      ref={ref}\n      aria-label={`${containerAriaLabel} ${hotkeyLabel}`}\n      tabIndex={-1}\n      aria-live=\"polite\"\n      aria-relevant=\"additions text\"\n      aria-atomic=\"false\"\n      suppressHydrationWarning\n    >\n      {possiblePositions.map((position, index) => {\n        const [y, x] = position.split('-');\n\n        if (!toasts.length) return null;\n\n        return (\n          <ol\n            key={position}\n            dir={dir === 'auto' ? getDocumentDirection() : dir}\n            tabIndex={-1}\n            ref={listRef}\n            className={className}\n            data-sonner-toaster\n            data-theme={actualTheme}\n            data-y-position={y}\n            data-lifted={expanded && toasts.length > 1 && !expand}\n            data-x-position={x}\n            style={\n              {\n                '--front-toast-height': `${heights[0]?.height || 0}px`,\n                '--width': `${TOAST_WIDTH}px`,\n                '--gap': `${gap}px`,\n                ...style,\n                ...assignOffset(offset, mobileOffset),\n              } as React.CSSProperties\n            }\n            onBlur={(event) => {\n              if (isFocusWithinRef.current && !event.currentTarget.contains(event.relatedTarget)) {\n                isFocusWithinRef.current = false;\n                if (lastFocusedElementRef.current) {\n                  lastFocusedElementRef.current.focus({ preventScroll: true });\n                  lastFocusedElementRef.current = null;\n                }\n              }\n            }}\n            onFocus={(event) => {\n              const isNotDismissible =\n                event.target instanceof HTMLElement && event.target.dataset.dismissible === 'false';\n\n              if (isNotDismissible) return;\n\n              if (!isFocusWithinRef.current) {\n                isFocusWithinRef.current = true;\n                lastFocusedElementRef.current = event.relatedTarget as HTMLElement;\n              }\n            }}\n            onMouseEnter={() => setExpanded(true)}\n            onMouseMove={() => setExpanded(true)}\n            onMouseLeave={() => {\n              // Avoid setting expanded to false when interacting with a toast, e.g. swiping\n              if (!interacting) {\n                setExpanded(false);\n              }\n            }}\n            onDragEnd={() => setExpanded(false)}\n            onPointerDown={(event) => {\n              const isNotDismissible =\n                event.target instanceof HTMLElement && event.target.dataset.dismissible === 'false';\n\n              if (isNotDismissible) return;\n              setInteracting(true);\n            }}\n            onPointerUp={() => setInteracting(false)}\n          >\n            {toasts\n              .filter((toast) => (!toast.position && index === 0) || toast.position === position)\n              .map((toast, index) => (\n                <Toast\n                  key={toast.id}\n                  icons={icons}\n                  index={index}\n                  toast={toast}\n                  defaultRichColors={richColors}\n                  duration={toastOptions?.duration ?? duration}\n                  className={toastOptions?.className}\n                  descriptionClassName={toastOptions?.descriptionClassName}\n                  invert={invert}\n                  visibleToasts={visibleToasts}\n                  closeButton={toastOptions?.closeButton ?? closeButton}\n                  interacting={interacting}\n                  position={position}\n                  style={toastOptions?.style}\n                  unstyled={toastOptions?.unstyled}\n                  classNames={toastOptions?.classNames}\n                  cancelButtonStyle={toastOptions?.cancelButtonStyle}\n                  actionButtonStyle={toastOptions?.actionButtonStyle}\n                  removeToast={removeToast}\n                  toasts={toasts.filter((t) => t.position == toast.position)}\n                  heights={heights.filter((h) => h.position == toast.position)}\n                  setHeights={setHeights}\n                  expandByDefault={expand}\n                  gap={gap}\n                  loadingIcon={loadingIcon}\n                  expanded={expanded}\n                  pauseWhenPageIsHidden={pauseWhenPageIsHidden}\n                  swipeDirections={props.swipeDirections}\n                />\n              ))}\n          </ol>\n        );\n      })}\n    </section>\n  );\n});\nexport { toast, Toaster, type ExternalToast, type ToastT, type ToasterProps, useSonner };\nexport { type ToastClassnames, type ToastToDismiss, type Action } from './types';\n", "'use client';\nimport React from 'react';\nimport type { ToastTypes } from './types';\n\nexport const getAsset = (type: ToastTypes): JSX.Element | null => {\n  switch (type) {\n    case 'success':\n      return SuccessIcon;\n\n    case 'info':\n      return InfoIcon;\n\n    case 'warning':\n      return WarningIcon;\n\n    case 'error':\n      return ErrorIcon;\n\n    default:\n      return null;\n  }\n};\n\nconst bars = Array(12).fill(0);\n\nexport const Loader = ({ visible, className }: { visible: boolean, className?: string }) => {\n  return (\n    <div className={['sonner-loading-wrapper', className].filter(Boolean).join(' ')} data-visible={visible}>\n      <div className=\"sonner-spinner\">\n        {bars.map((_, i) => (\n          <div className=\"sonner-loading-bar\" key={`spinner-bar-${i}`} />\n        ))}\n      </div>\n    </div>\n  );\n};\n\nconst SuccessIcon = (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 20 20\" fill=\"currentColor\" height=\"20\" width=\"20\">\n    <path\n      fillRule=\"evenodd\"\n      d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z\"\n      clipRule=\"evenodd\"\n    />\n  </svg>\n);\n\nconst WarningIcon = (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"currentColor\" height=\"20\" width=\"20\">\n    <path\n      fillRule=\"evenodd\"\n      d=\"M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z\"\n      clipRule=\"evenodd\"\n    />\n  </svg>\n);\n\nconst InfoIcon = (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 20 20\" fill=\"currentColor\" height=\"20\" width=\"20\">\n    <path\n      fillRule=\"evenodd\"\n      d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z\"\n      clipRule=\"evenodd\"\n    />\n  </svg>\n);\n\nconst ErrorIcon = (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 20 20\" fill=\"currentColor\" height=\"20\" width=\"20\">\n    <path\n      fillRule=\"evenodd\"\n      d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z\"\n      clipRule=\"evenodd\"\n    />\n  </svg>\n);\n\nexport const CloseIcon = (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    width=\"12\"\n    height=\"12\"\n    viewBox=\"0 0 24 24\"\n    fill=\"none\"\n    stroke=\"currentColor\"\n    strokeWidth=\"1.5\"\n    strokeLinecap=\"round\"\n    strokeLinejoin=\"round\"\n  >\n    <line x1=\"18\" y1=\"6\" x2=\"6\" y2=\"18\"></line>\n    <line x1=\"6\" y1=\"6\" x2=\"18\" y2=\"18\"></line>\n  </svg>\n);\n", "import React from 'react';\n\nexport const useIsDocumentHidden = () => {\n  const [isDocumentHidden, setIsDocumentHidden] = React.useState(document.hidden);\n\n  React.useEffect(() => {\n    const callback = () => {\n      setIsDocumentHidden(document.hidden);\n    };\n    document.addEventListener('visibilitychange', callback);\n    return () => window.removeEventListener('visibilitychange', callback);\n  }, []);\n\n  return isDocumentHidden;\n};\n", "import type { ExternalToast, PromiseD<PERSON>, PromiseT, ToastT, ToastToDismiss, ToastTypes } from './types';\n\nimport React from 'react';\n\nlet toastsCounter = 1;\n\ntype titleT = (() => React.ReactNode) | React.ReactNode;\n\nclass Observer {\n  subscribers: Array<(toast: ExternalToast | ToastToDismiss) => void>;\n  toasts: Array<ToastT | ToastToDismiss>;\n  dismissedToasts: Set<string | number>;\n\n  constructor() {\n    this.subscribers = [];\n    this.toasts = [];\n    this.dismissedToasts = new Set();\n  }\n\n  // We use arrow functions to maintain the correct `this` reference\n  subscribe = (subscriber: (toast: ToastT | ToastToDismiss) => void) => {\n    this.subscribers.push(subscriber);\n\n    return () => {\n      const index = this.subscribers.indexOf(subscriber);\n      this.subscribers.splice(index, 1);\n    };\n  };\n\n  publish = (data: ToastT) => {\n    this.subscribers.forEach((subscriber) => subscriber(data));\n  };\n\n  addToast = (data: ToastT) => {\n    this.publish(data);\n    this.toasts = [...this.toasts, data];\n  };\n\n  create = (\n    data: ExternalToast & {\n      message?: titleT;\n      type?: ToastTypes;\n      promise?: PromiseT;\n      jsx?: React.ReactElement;\n    },\n  ) => {\n    const { message, ...rest } = data;\n    const id = typeof data?.id === 'number' || data.id?.length > 0 ? data.id : toastsCounter++;\n    const alreadyExists = this.toasts.find((toast) => {\n      return toast.id === id;\n    });\n    const dismissible = data.dismissible === undefined ? true : data.dismissible;\n\n    if (this.dismissedToasts.has(id)) {\n      this.dismissedToasts.delete(id);\n    }\n\n    if (alreadyExists) {\n      this.toasts = this.toasts.map((toast) => {\n        if (toast.id === id) {\n          this.publish({ ...toast, ...data, id, title: message });\n          return {\n            ...toast,\n            ...data,\n            id,\n            dismissible,\n            title: message,\n          };\n        }\n\n        return toast;\n      });\n    } else {\n      this.addToast({ title: message, ...rest, dismissible, id });\n    }\n\n    return id;\n  };\n\n  dismiss = (id?: number | string) => {\n    this.dismissedToasts.add(id);\n\n    if (!id) {\n      this.toasts.forEach((toast) => {\n        this.subscribers.forEach((subscriber) => subscriber({ id: toast.id, dismiss: true }));\n      });\n    }\n    this.subscribers.forEach((subscriber) => subscriber({ id, dismiss: true }));\n    return id;\n  };\n\n  message = (message: titleT | React.ReactNode, data?: ExternalToast) => {\n    return this.create({ ...data, message });\n  };\n\n  error = (message: titleT | React.ReactNode, data?: ExternalToast) => {\n    return this.create({ ...data, message, type: 'error' });\n  };\n\n  success = (message: titleT | React.ReactNode, data?: ExternalToast) => {\n    return this.create({ ...data, type: 'success', message });\n  };\n\n  info = (message: titleT | React.ReactNode, data?: ExternalToast) => {\n    return this.create({ ...data, type: 'info', message });\n  };\n\n  warning = (message: titleT | React.ReactNode, data?: ExternalToast) => {\n    return this.create({ ...data, type: 'warning', message });\n  };\n\n  loading = (message: titleT | React.ReactNode, data?: ExternalToast) => {\n    return this.create({ ...data, type: 'loading', message });\n  };\n\n  promise = <ToastData>(promise: PromiseT<ToastData>, data?: PromiseData<ToastData>) => {\n    if (!data) {\n      // Nothing to show\n      return;\n    }\n\n    let id: string | number | undefined = undefined;\n    if (data.loading !== undefined) {\n      id = this.create({\n        ...data,\n        promise,\n        type: 'loading',\n        message: data.loading,\n        description: typeof data.description !== 'function' ? data.description : undefined,\n      });\n    }\n\n    const p = promise instanceof Promise ? promise : promise();\n\n    let shouldDismiss = id !== undefined;\n    let result: ['resolve', ToastData] | ['reject', unknown];\n\n    const originalPromise = p\n      .then(async (response) => {\n        result = ['resolve', response];\n        const isReactElementResponse = React.isValidElement(response);\n        if (isReactElementResponse) {\n          shouldDismiss = false;\n          this.create({ id, type: 'default', message: response });\n        } else if (isHttpResponse(response) && !response.ok) {\n          shouldDismiss = false;\n          const message =\n            typeof data.error === 'function' ? await data.error(`HTTP error! status: ${response.status}`) : data.error;\n          const description =\n            typeof data.description === 'function'\n              ? await data.description(`HTTP error! status: ${response.status}`)\n              : data.description;\n          this.create({ id, type: 'error', message, description });\n        } else if (data.success !== undefined) {\n          shouldDismiss = false;\n          const message = typeof data.success === 'function' ? await data.success(response) : data.success;\n          const description =\n            typeof data.description === 'function' ? await data.description(response) : data.description;\n          this.create({ id, type: 'success', message, description });\n        }\n      })\n      .catch(async (error) => {\n        result = ['reject', error];\n        if (data.error !== undefined) {\n          shouldDismiss = false;\n          const message = typeof data.error === 'function' ? await data.error(error) : data.error;\n          const description = typeof data.description === 'function' ? await data.description(error) : data.description;\n          this.create({ id, type: 'error', message, description });\n        }\n      })\n      .finally(() => {\n        if (shouldDismiss) {\n          // Toast is still in load state (and will be indefinitely — dismiss it)\n          this.dismiss(id);\n          id = undefined;\n        }\n\n        data.finally?.();\n      });\n\n    const unwrap = () =>\n      new Promise<ToastData>((resolve, reject) =>\n        originalPromise.then(() => (result[0] === 'reject' ? reject(result[1]) : resolve(result[1]))).catch(reject),\n      );\n\n    if (typeof id !== 'string' && typeof id !== 'number') {\n      // cannot Object.assign on undefined\n      return { unwrap };\n    } else {\n      return Object.assign(id, { unwrap });\n    }\n  };\n\n  custom = (jsx: (id: number | string) => React.ReactElement, data?: ExternalToast) => {\n    const id = data?.id || toastsCounter++;\n    this.create({ jsx: jsx(id), id, ...data });\n    return id;\n  };\n\n  getActiveToasts = () => {\n    return this.toasts.filter((toast) => !this.dismissedToasts.has(toast.id));\n  };\n}\n\nexport const ToastState = new Observer();\n\n// bind this to the toast function\nconst toastFunction = (message: titleT, data?: ExternalToast) => {\n  const id = data?.id || toastsCounter++;\n\n  ToastState.addToast({\n    title: message,\n    ...data,\n    id,\n  });\n  return id;\n};\n\nconst isHttpResponse = (data: any): data is Response => {\n  return (\n    data &&\n    typeof data === 'object' &&\n    'ok' in data &&\n    typeof data.ok === 'boolean' &&\n    'status' in data &&\n    typeof data.status === 'number'\n  );\n};\n\nconst basicToast = toastFunction;\n\nconst getHistory = () => ToastState.toasts;\nconst getToasts = () => ToastState.getActiveToasts();\n\n// We use `Object.assign` to maintain the correct types as we would lose them otherwise\nexport const toast = Object.assign(\n  basicToast,\n  {\n    success: ToastState.success,\n    info: ToastState.info,\n    warning: ToastState.warning,\n    error: ToastState.error,\n    custom: ToastState.custom,\n    message: ToastState.message,\n    promise: ToastState.promise,\n    dismiss: ToastState.dismiss,\n    loading: ToastState.loading,\n  },\n  { getHistory, getToasts },\n);\n", "\n          export default function styleInject(css, { insertAt } = {}) {\n            if (!css || typeof document === 'undefined') return\n          \n            const head = document.head || document.getElementsByTagName('head')[0]\n            const style = document.createElement('style')\n            style.type = 'text/css'\n          \n            if (insertAt === 'top') {\n              if (head.firstChild) {\n                head.insertBefore(style, head.firstChild)\n              } else {\n                head.appendChild(style)\n              }\n            } else {\n              head.appendChild(style)\n            }\n          \n            if (style.styleSheet) {\n              style.styleSheet.cssText = css\n            } else {\n              style.appendChild(document.createTextNode(css))\n            }\n          }\n          ", "import styleInject from '#style-inject';styleInject(\":where(html[dir=\\\"ltr\\\"]),:where([data-sonner-toaster][dir=\\\"ltr\\\"]){--toast-icon-margin-start: -3px;--toast-icon-margin-end: 4px;--toast-svg-margin-start: -1px;--toast-svg-margin-end: 0px;--toast-button-margin-start: auto;--toast-button-margin-end: 0;--toast-close-button-start: 0;--toast-close-button-end: unset;--toast-close-button-transform: translate(-35%, -35%)}:where(html[dir=\\\"rtl\\\"]),:where([data-sonner-toaster][dir=\\\"rtl\\\"]){--toast-icon-margin-start: 4px;--toast-icon-margin-end: -3px;--toast-svg-margin-start: 0px;--toast-svg-margin-end: -1px;--toast-button-margin-start: 0;--toast-button-margin-end: auto;--toast-close-button-start: unset;--toast-close-button-end: 0;--toast-close-button-transform: translate(35%, -35%)}:where([data-sonner-toaster]){position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1: hsl(0, 0%, 99%);--gray2: hsl(0, 0%, 97.3%);--gray3: hsl(0, 0%, 95.1%);--gray4: hsl(0, 0%, 93%);--gray5: hsl(0, 0%, 90.9%);--gray6: hsl(0, 0%, 88.7%);--gray7: hsl(0, 0%, 85.8%);--gray8: hsl(0, 0%, 78%);--gray9: hsl(0, 0%, 56.1%);--gray10: hsl(0, 0%, 52.3%);--gray11: hsl(0, 0%, 43.5%);--gray12: hsl(0, 0%, 9%);--border-radius: 8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:none;z-index:999999999;transition:transform .4s ease}:where([data-sonner-toaster][data-lifted=\\\"true\\\"]){transform:translateY(-10px)}@media (hover: none) and (pointer: coarse){:where([data-sonner-toaster][data-lifted=\\\"true\\\"]){transform:none}}:where([data-sonner-toaster][data-x-position=\\\"right\\\"]){right:var(--offset-right)}:where([data-sonner-toaster][data-x-position=\\\"left\\\"]){left:var(--offset-left)}:where([data-sonner-toaster][data-x-position=\\\"center\\\"]){left:50%;transform:translate(-50%)}:where([data-sonner-toaster][data-y-position=\\\"top\\\"]){top:var(--offset-top)}:where([data-sonner-toaster][data-y-position=\\\"bottom\\\"]){bottom:var(--offset-bottom)}:where([data-sonner-toast]){--y: translateY(100%);--lift-amount: calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);filter:blur(0);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:none;overflow-wrap:anywhere}:where([data-sonner-toast][data-styled=\\\"true\\\"]){padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px #0000001a;width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}:where([data-sonner-toast]:focus-visible){box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast][data-y-position=\\\"top\\\"]){top:0;--y: translateY(-100%);--lift: 1;--lift-amount: calc(1 * var(--gap))}:where([data-sonner-toast][data-y-position=\\\"bottom\\\"]){bottom:0;--y: translateY(100%);--lift: -1;--lift-amount: calc(var(--lift) * var(--gap))}:where([data-sonner-toast]) :where([data-description]){font-weight:400;line-height:1.4;color:inherit}:where([data-sonner-toast]) :where([data-title]){font-weight:500;line-height:1.5;color:inherit}:where([data-sonner-toast]) :where([data-icon]){display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}:where([data-sonner-toast][data-promise=\\\"true\\\"]) :where([data-icon])>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}:where([data-sonner-toast]) :where([data-icon])>*{flex-shrink:0}:where([data-sonner-toast]) :where([data-icon]) svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}:where([data-sonner-toast]) :where([data-content]){display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;cursor:pointer;outline:none;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}:where([data-sonner-toast]) :where([data-button]):focus-visible{box-shadow:0 0 0 2px #0006}:where([data-sonner-toast]) :where([data-button]):first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}:where([data-sonner-toast]) :where([data-cancel]){color:var(--normal-text);background:rgba(0,0,0,.08)}:where([data-sonner-toast][data-theme=\\\"dark\\\"]) :where([data-cancel]){background:rgba(255,255,255,.3)}:where([data-sonner-toast]) :where([data-close-button]){position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;color:var(--gray12);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}[data-sonner-toast] [data-close-button]{background:var(--gray1)}:where([data-sonner-toast]) :where([data-close-button]):focus-visible{box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast]) :where([data-disabled=\\\"true\\\"]){cursor:not-allowed}:where([data-sonner-toast]):hover :where([data-close-button]):hover{background:var(--gray2);border-color:var(--gray5)}:where([data-sonner-toast][data-swiping=\\\"true\\\"]):before{content:\\\"\\\";position:absolute;left:-50%;right:-50%;height:100%;z-index:-1}:where([data-sonner-toast][data-y-position=\\\"top\\\"][data-swiping=\\\"true\\\"]):before{bottom:50%;transform:scaleY(3) translateY(50%)}:where([data-sonner-toast][data-y-position=\\\"bottom\\\"][data-swiping=\\\"true\\\"]):before{top:50%;transform:scaleY(3) translateY(-50%)}:where([data-sonner-toast][data-swiping=\\\"false\\\"][data-removed=\\\"true\\\"]):before{content:\\\"\\\";position:absolute;inset:0;transform:scaleY(2)}:where([data-sonner-toast]):after{content:\\\"\\\";position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}:where([data-sonner-toast][data-mounted=\\\"true\\\"]){--y: translateY(0);opacity:1}:where([data-sonner-toast][data-expanded=\\\"false\\\"][data-front=\\\"false\\\"]){--scale: var(--toasts-before) * .05 + 1;--y: translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}:where([data-sonner-toast])>*{transition:opacity .4s}:where([data-sonner-toast][data-expanded=\\\"false\\\"][data-front=\\\"false\\\"][data-styled=\\\"true\\\"])>*{opacity:0}:where([data-sonner-toast][data-visible=\\\"false\\\"]){opacity:0;pointer-events:none}:where([data-sonner-toast][data-mounted=\\\"true\\\"][data-expanded=\\\"true\\\"]){--y: translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}:where([data-sonner-toast][data-removed=\\\"true\\\"][data-front=\\\"true\\\"][data-swipe-out=\\\"false\\\"]){--y: translateY(calc(var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed=\\\"true\\\"][data-front=\\\"false\\\"][data-swipe-out=\\\"false\\\"][data-expanded=\\\"true\\\"]){--y: translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed=\\\"true\\\"][data-front=\\\"false\\\"][data-swipe-out=\\\"false\\\"][data-expanded=\\\"false\\\"]){--y: translateY(40%);opacity:0;transition:transform .5s,opacity .2s}:where([data-sonner-toast][data-removed=\\\"true\\\"][data-front=\\\"false\\\"]):before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount-y, 0px)) translate(var(--swipe-amount-x, 0px));transition:none}[data-sonner-toast][data-swiped=true]{user-select:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation-duration:.2s;animation-timing-function:ease-out;animation-fill-mode:forwards}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=left]{animation-name:swipe-out-left}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=right]{animation-name:swipe-out-right}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=up]{animation-name:swipe-out-up}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=down]{animation-name:swipe-out-down}@keyframes swipe-out-left{0%{transform:var(--y) translate(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translate(calc(var(--swipe-amount-x) - 100%));opacity:0}}@keyframes swipe-out-right{0%{transform:var(--y) translate(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translate(calc(var(--swipe-amount-x) + 100%));opacity:0}}@keyframes swipe-out-up{0%{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) - 100%));opacity:0}}@keyframes swipe-out-down{0%{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) + 100%));opacity:0}}@media (max-width: 600px){[data-sonner-toaster]{position:fixed;right:var(--mobile-offset-right);left:var(--mobile-offset-left);width:100%}[data-sonner-toaster][dir=rtl]{left:calc(var(--mobile-offset-left) * -1)}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset-left) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset-left)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--mobile-offset-bottom)}[data-sonner-toaster][data-y-position=top]{top:var(--mobile-offset-top)}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset-left);right:var(--mobile-offset-right);transform:none}}[data-sonner-toaster][data-theme=light]{--normal-bg: #fff;--normal-border: var(--gray4);--normal-text: var(--gray12);--success-bg: hsl(143, 85%, 96%);--success-border: hsl(145, 92%, 91%);--success-text: hsl(140, 100%, 27%);--info-bg: hsl(208, 100%, 97%);--info-border: hsl(221, 91%, 91%);--info-text: hsl(210, 92%, 45%);--warning-bg: hsl(49, 100%, 97%);--warning-border: hsl(49, 91%, 91%);--warning-text: hsl(31, 92%, 45%);--error-bg: hsl(359, 100%, 97%);--error-border: hsl(359, 100%, 94%);--error-text: hsl(360, 100%, 45%)}[data-sonner-toaster][data-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg: #000;--normal-border: hsl(0, 0%, 20%);--normal-text: var(--gray1)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg: #fff;--normal-border: var(--gray3);--normal-text: var(--gray12)}[data-sonner-toaster][data-theme=dark]{--normal-bg: #000;--normal-bg-hover: hsl(0, 0%, 12%);--normal-border: hsl(0, 0%, 20%);--normal-border-hover: hsl(0, 0%, 25%);--normal-text: var(--gray1);--success-bg: hsl(150, 100%, 6%);--success-border: hsl(147, 100%, 12%);--success-text: hsl(150, 86%, 65%);--info-bg: hsl(215, 100%, 6%);--info-border: hsl(223, 100%, 12%);--info-text: hsl(216, 87%, 65%);--warning-bg: hsl(64, 100%, 6%);--warning-border: hsl(60, 100%, 12%);--warning-text: hsl(46, 87%, 65%);--error-bg: hsl(358, 76%, 10%);--error-border: hsl(357, 89%, 16%);--error-text: hsl(358, 100%, 81%)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast] [data-close-button]{background:var(--normal-bg);border-color:var(--normal-border);color:var(--normal-text)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast] [data-close-button]:hover{background:var(--normal-bg-hover);border-color:var(--normal-border-hover)}[data-rich-colors=true][data-sonner-toast][data-type=success],[data-rich-colors=true][data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=info],[data-rich-colors=true][data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning],[data-rich-colors=true][data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=error],[data-rich-colors=true][data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size: 16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:nth-child(1){animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}to{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}to{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}to{opacity:.15}}@media (prefers-reduced-motion){[data-sonner-toast],[data-sonner-toast]>*,.sonner-loading-bar{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}\\n\")", "import React from 'react';\n\nexport type ToastTypes = 'normal' | 'action' | 'success' | 'info' | 'warning' | 'error' | 'loading' | 'default';\n\nexport type PromiseT<Data = any> = Promise<Data> | (() => Promise<Data>);\n\nexport type PromiseTResult<Data = any> =\n  | string\n  | React.ReactNode\n  | ((data: Data) => React.ReactNode | string | Promise<React.ReactNode | string>);\n\nexport type PromiseExternalToast = Omit<ExternalToast, 'description'>;\n\nexport type PromiseData<ToastData = any> = PromiseExternalToast & {\n  loading?: string | React.ReactNode;\n  success?: PromiseTResult<ToastData>;\n  error?: PromiseTResult;\n  description?: PromiseTResult;\n  finally?: () => void | Promise<void>;\n};\n\nexport interface ToastClassnames {\n  toast?: string;\n  title?: string;\n  description?: string;\n  loader?: string;\n  closeButton?: string;\n  cancelButton?: string;\n  actionButton?: string;\n  success?: string;\n  error?: string;\n  info?: string;\n  warning?: string;\n  loading?: string;\n  default?: string;\n  content?: string;\n  icon?: string;\n}\n\nexport interface ToastIcons {\n  success?: React.ReactNode;\n  info?: React.ReactNode;\n  warning?: React.ReactNode;\n  error?: React.ReactNode;\n  loading?: React.ReactNode;\n  close?: React.ReactNode;\n}\n\nexport interface Action {\n  label: React.ReactNode;\n  onClick: (event: React.MouseEvent<HTMLButtonElement, MouseEvent>) => void;\n  actionButtonStyle?: React.CSSProperties;\n}\n\nexport interface ToastT {\n  id: number | string;\n  title?: (() => React.ReactNode) | React.ReactNode;\n  type?: ToastTypes;\n  icon?: React.ReactNode;\n  jsx?: React.ReactNode;\n  richColors?: boolean;\n  invert?: boolean;\n  closeButton?: boolean;\n  dismissible?: boolean;\n  description?: (() => React.ReactNode) | React.ReactNode;\n  duration?: number;\n  delete?: boolean;\n  action?: Action | React.ReactNode;\n  cancel?: Action | React.ReactNode;\n  onDismiss?: (toast: ToastT) => void;\n  onAutoClose?: (toast: ToastT) => void;\n  promise?: PromiseT;\n  cancelButtonStyle?: React.CSSProperties;\n  actionButtonStyle?: React.CSSProperties;\n  style?: React.CSSProperties;\n  unstyled?: boolean;\n  className?: string;\n  classNames?: ToastClassnames;\n  descriptionClassName?: string;\n  position?: Position;\n}\n\nexport function isAction(action: Action | React.ReactNode): action is Action {\n  return (action as Action).label !== undefined;\n}\n\nexport type Position = 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right' | 'top-center' | 'bottom-center';\nexport interface HeightT {\n  height: number;\n  toastId: number | string;\n  position: Position;\n}\n\ninterface ToastOptions {\n  className?: string;\n  closeButton?: boolean;\n  descriptionClassName?: string;\n  style?: React.CSSProperties;\n  cancelButtonStyle?: React.CSSProperties;\n  actionButtonStyle?: React.CSSProperties;\n  duration?: number;\n  unstyled?: boolean;\n  classNames?: ToastClassnames;\n}\n\ntype Offset =\n  | {\n      top?: string | number;\n      right?: string | number;\n      bottom?: string | number;\n      left?: string | number;\n    }\n  | string\n  | number;\n\nexport interface ToasterProps {\n  invert?: boolean;\n  theme?: 'light' | 'dark' | 'system';\n  position?: Position;\n  hotkey?: string[];\n  richColors?: boolean;\n  expand?: boolean;\n  duration?: number;\n  gap?: number;\n  visibleToasts?: number;\n  closeButton?: boolean;\n  toastOptions?: ToastOptions;\n  className?: string;\n  style?: React.CSSProperties;\n  offset?: Offset;\n  mobileOffset?: Offset;\n  dir?: 'rtl' | 'ltr' | 'auto';\n  swipeDirections?: SwipeDirection[];\n  /**\n   * @deprecated Please use the `icons` prop instead:\n   * ```jsx\n   * <Toaster\n   *   icons={{ loading: <LoadingIcon /> }}\n   * />\n   * ```\n   */\n  loadingIcon?: React.ReactNode;\n  icons?: ToastIcons;\n  containerAriaLabel?: string;\n  pauseWhenPageIsHidden?: boolean;\n}\n\nexport type SwipeDirection = 'top' | 'right' | 'bottom' | 'left';\n\nexport interface ToastProps {\n  toast: ToastT;\n  toasts: ToastT[];\n  index: number;\n  swipeDirections?: SwipeDirection[];\n  expanded: boolean;\n  invert: boolean;\n  heights: HeightT[];\n  setHeights: React.Dispatch<React.SetStateAction<HeightT[]>>;\n  removeToast: (toast: ToastT) => void;\n  gap?: number;\n  position: Position;\n  visibleToasts: number;\n  expandByDefault: boolean;\n  closeButton: boolean;\n  interacting: boolean;\n  style?: React.CSSProperties;\n  cancelButtonStyle?: React.CSSProperties;\n  actionButtonStyle?: React.CSSProperties;\n  duration?: number;\n  className?: string;\n  unstyled?: boolean;\n  descriptionClassName?: string;\n  loadingIcon?: React.ReactNode;\n  classNames?: ToastClassnames;\n  icons?: ToastIcons;\n  closeButtonAriaLabel?: string;\n  pauseWhenPageIsHidden: boolean;\n  defaultRichColors?: boolean;\n}\n\nexport enum SwipeStateTypes {\n  SwipedOut = 'SwipedOut',\n  SwipedBack = 'SwipedBack',\n  NotSwiped = 'NotSwiped',\n}\n\nexport type Theme = 'light' | 'dark';\n\nexport interface ToastToDismiss {\n  id: number | string;\n  dismiss: boolean;\n}\n\nexport type ExternalToast = Omit<ToastT, 'id' | 'type' | 'title' | 'jsx' | 'delete' | 'promise'> & {\n  id?: number | string;\n};\n"], "names": ["React", "forwardRef", "isValidElement", "ReactDOM", "React", "getAsset", "type", "SuccessIcon", "InfoIcon", "WarningIcon", "ErrorIcon", "bars", "Loader", "visible", "className", "_", "i", "CloseIcon", "React", "useIsDocumentHidden", "isDocumentHidden", "setIsDocumentHidden", "callback", "React", "toastsCounter", "Observer", "subscriber", "index", "data", "_a", "message", "rest", "id", "alreadyExists", "toast", "dismissible", "promise", "p", "<PERSON><PERSON><PERSON><PERSON>", "result", "originalPromise", "response", "isHttpResponse", "description", "error", "unwrap", "resolve", "reject", "jsx", "ToastState", "toastFunction", "basicToast", "getHistory", "getToasts", "styleInject", "css", "insertAt", "head", "style", "styleInject", "isAction", "action", "VISIBLE_TOASTS_AMOUNT", "VIEWPORT_OFFSET", "MOBILE_VIEWPORT_OFFSET", "TOAST_LIFETIME", "TOAST_WIDTH", "GAP", "SWIPE_THRESHOLD", "TIME_BEFORE_UNMOUNT", "cn", "classes", "getDefaultSwipeDirections", "position", "y", "x", "directions", "Toast", "props", "_a", "_b", "_c", "_d", "_e", "_f", "_g", "_h", "_i", "_j", "_k", "ToasterInvert", "toast", "unstyled", "interacting", "setHeights", "visibleToasts", "heights", "index", "toasts", "expanded", "removeToast", "defaultRichColors", "closeButtonFromToaster", "style", "cancelButtonStyle", "actionButtonStyle", "className", "descriptionClassName", "durationFromToaster", "gap", "loadingIconProp", "expandByDefault", "classNames", "icons", "closeButtonAriaLabel", "pauseWhenPageIsHidden", "swipeDirection", "setSwipeDirection", "React", "swipeOutDirection", "setSwipeOutDirection", "mounted", "setMounted", "removed", "setRemoved", "swiping", "setSwiping", "swipeOut", "setSwipeOut", "isSwiped", "setIsSwiped", "offsetBeforeRemove", "setOffsetBeforeRemove", "initialHeight", "setInitialHeight", "remainingTime", "dragStartTime", "toastRef", "isFront", "isVisible", "toastType", "dismissible", "toastClassname", "toastDescriptionClassname", "heightIndex", "height", "closeButton", "duration", "closeTimerStartTimeRef", "offset", "lastCloseTimerStartTimeRef", "pointerStartRef", "toastsHeightBefore", "prev", "curr", "reducerIndex", "isDocumentHidden", "useIsDocumentHidden", "invert", "disabled", "toastNode", "h", "originalHeight", "newHeight", "deleteToast", "timeoutId", "elapsedTime", "getLoadingIcon", "Loader", "event", "swipeAmountX", "swipeAmountY", "timeTaken", "swipeAmount", "velocity", "y<PERSON><PERSON><PERSON>", "xDelta", "swipeDirections", "CloseIcon", "isValidElement", "getAsset", "isAction", "getDocumentDirection", "dirAttribute", "assignOffset", "defaultOffset", "mobileOffset", "styles", "isMobile", "prefix", "defaultValue", "assignAll", "key", "useSonner", "activeToasts", "setActiveToasts", "ToastState", "ReactDOM", "t", "indexOfExistingToast", "Toaster", "forwardRef", "ref", "hotkey", "expand", "theme", "richColors", "toastOptions", "dir", "loadingIcon", "containerAriaLabel", "setToasts", "possiblePositions", "setExpanded", "setInteracting", "actualTheme", "setActualTheme", "listRef", "hotkeyLabel", "lastFocusedElementRef", "isFocusWithinRef", "toast<PERSON>oRemove", "id", "darkMediaQuery", "matches", "error", "e", "handleKeyDown"], "mappings": "", "ignoreList": [0, 1, 2, 3, 4, 5, 6]}}, {"offset": {"line": 146, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 152, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/clsx/dist/clsx.mjs"], "sourcesContent": ["function r(e){var t,f,n=\"\";if(\"string\"==typeof e||\"number\"==typeof e)n+=e;else if(\"object\"==typeof e)if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(f=r(e[t]))&&(n&&(n+=\" \"),n+=f)}else for(f in e)e[f]&&(n&&(n+=\" \"),n+=f);return n}export function clsx(){for(var e,t,f=0,n=\"\",o=arguments.length;f<o;f++)(e=arguments[f])&&(t=r(e))&&(n&&(n+=\" \"),n+=t);return n}export default clsx;"], "names": [], "mappings": ";;;;AAAA,SAAS,EAAE,CAAC;IAAE,IAAI,GAAE,GAAE,IAAE;IAAG,IAAG,YAAU,OAAO,KAAG,YAAU,OAAO,GAAE,KAAG;SAAO,IAAG,YAAU,OAAO,GAAE,IAAG,MAAM,OAAO,CAAC,IAAG;QAAC,IAAI,IAAE,EAAE,MAAM;QAAC,IAAI,IAAE,GAAE,IAAE,GAAE,IAAI,CAAC,CAAC,EAAE,IAAE,CAAC,IAAE,EAAE,CAAC,CAAC,EAAE,CAAC,KAAG,CAAC,KAAG,CAAC,KAAG,GAAG,GAAE,KAAG,CAAC;IAAC,OAAM,IAAI,KAAK,EAAE,CAAC,CAAC,EAAE,IAAE,CAAC,KAAG,CAAC,KAAG,GAAG,GAAE,KAAG,CAAC;IAAE,OAAO;AAAC;AAAQ,SAAS;IAAO,IAAI,IAAI,GAAE,GAAE,IAAE,GAAE,IAAE,IAAG,IAAE,UAAU,MAAM,EAAC,IAAE,GAAE,IAAI,CAAC,IAAE,SAAS,CAAC,EAAE,KAAG,CAAC,IAAE,EAAE,EAAE,KAAG,CAAC,KAAG,CAAC,KAAG,GAAG,GAAE,KAAG,CAAC;IAAE,OAAO;AAAC;uCAAgB", "ignoreList": [0]}}, {"offset": {"line": 170, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 176, "column": 0}, "map": {"version": 3, "file": "bundle-mjs.mjs", "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/tailwind-merge/src/lib/class-group-utils.ts", "file:///home/<USER>/Desktop/templgen/node_modules/tailwind-merge/src/lib/lru-cache.ts", "file:///home/<USER>/Desktop/templgen/node_modules/tailwind-merge/src/lib/parse-class-name.ts", "file:///home/<USER>/Desktop/templgen/node_modules/tailwind-merge/src/lib/config-utils.ts", "file:///home/<USER>/Desktop/templgen/node_modules/tailwind-merge/src/lib/merge-classlist.ts", "file:///home/<USER>/Desktop/templgen/node_modules/tailwind-merge/src/lib/tw-join.ts", "file:///home/<USER>/Desktop/templgen/node_modules/tailwind-merge/src/lib/create-tailwind-merge.ts", "file:///home/<USER>/Desktop/templgen/node_modules/tailwind-merge/src/lib/from-theme.ts", "file:///home/<USER>/Desktop/templgen/node_modules/tailwind-merge/src/lib/validators.ts", "file:///home/<USER>/Desktop/templgen/node_modules/tailwind-merge/src/lib/default-config.ts", "file:///home/<USER>/Desktop/templgen/node_modules/tailwind-merge/src/lib/merge-configs.ts", "file:///home/<USER>/Desktop/templgen/node_modules/tailwind-merge/src/lib/extend-tailwind-merge.ts", "file:///home/<USER>/Desktop/templgen/node_modules/tailwind-merge/src/lib/tw-merge.ts"], "sourcesContent": ["import {\n    AnyClassGroupIds,\n    AnyConfig,\n    AnyThemeGroupIds,\n    ClassGroup,\n    ClassValidator,\n    Config,\n    ThemeGetter,\n    ThemeObject,\n} from './types'\n\nexport interface ClassPartObject {\n    nextPart: Map<string, ClassPartObject>\n    validators: ClassValidatorObject[]\n    classGroupId?: AnyClassGroupIds\n}\n\ninterface ClassValidatorObject {\n    classGroupId: AnyClassGroupIds\n    validator: ClassValidator\n}\n\nconst CLASS_PART_SEPARATOR = '-'\n\nexport const createClassGroupUtils = (config: AnyConfig) => {\n    const classMap = createClassMap(config)\n    const { conflictingClassGroups, conflictingClassGroupModifiers } = config\n\n    const getClassGroupId = (className: string) => {\n        const classParts = className.split(CLASS_PART_SEPARATOR)\n\n        // Classes like `-inset-1` produce an empty string as first classPart. We assume that classes for negative values are used correctly and remove it from classParts.\n        if (classParts[0] === '' && classParts.length !== 1) {\n            classParts.shift()\n        }\n\n        return getGroupRecursive(classParts, classMap) || getGroupIdForArbitraryProperty(className)\n    }\n\n    const getConflictingClassGroupIds = (\n        classGroupId: AnyClassGroupIds,\n        hasPostfixModifier: boolean,\n    ) => {\n        const conflicts = conflictingClassGroups[classGroupId] || []\n\n        if (hasPostfixModifier && conflictingClassGroupModifiers[classGroupId]) {\n            return [...conflicts, ...conflictingClassGroupModifiers[classGroupId]!]\n        }\n\n        return conflicts\n    }\n\n    return {\n        getClassGroupId,\n        getConflictingClassGroupIds,\n    }\n}\n\nconst getGroupRecursive = (\n    classParts: string[],\n    classPartObject: ClassPartObject,\n): AnyClassGroupIds | undefined => {\n    if (classParts.length === 0) {\n        return classPartObject.classGroupId\n    }\n\n    const currentClassPart = classParts[0]!\n    const nextClassPartObject = classPartObject.nextPart.get(currentClassPart)\n    const classGroupFromNextClassPart = nextClassPartObject\n        ? getGroupRecursive(classParts.slice(1), nextClassPartObject)\n        : undefined\n\n    if (classGroupFromNextClassPart) {\n        return classGroupFromNextClassPart\n    }\n\n    if (classPartObject.validators.length === 0) {\n        return undefined\n    }\n\n    const classRest = classParts.join(CLASS_PART_SEPARATOR)\n\n    return classPartObject.validators.find(({ validator }) => validator(classRest))?.classGroupId\n}\n\nconst arbitraryPropertyRegex = /^\\[(.+)\\]$/\n\nconst getGroupIdForArbitraryProperty = (className: string) => {\n    if (arbitraryPropertyRegex.test(className)) {\n        const arbitraryPropertyClassName = arbitraryPropertyRegex.exec(className)![1]\n        const property = arbitraryPropertyClassName?.substring(\n            0,\n            arbitraryPropertyClassName.indexOf(':'),\n        )\n\n        if (property) {\n            // I use two dots here because one dot is used as prefix for class groups in plugins\n            return 'arbitrary..' + property\n        }\n    }\n}\n\n/**\n * Exported for testing only\n */\nexport const createClassMap = (config: Config<AnyClassGroupIds, AnyThemeGroupIds>) => {\n    const { theme, prefix } = config\n    const classMap: ClassPartObject = {\n        nextPart: new Map<string, ClassPartObject>(),\n        validators: [],\n    }\n\n    const prefixedClassGroupEntries = getPrefixedClassGroupEntries(\n        Object.entries(config.classGroups),\n        prefix,\n    )\n\n    prefixedClassGroupEntries.forEach(([classGroupId, classGroup]) => {\n        processClassesRecursively(classGroup, classMap, classGroupId, theme)\n    })\n\n    return classMap\n}\n\nconst processClassesRecursively = (\n    classGroup: ClassGroup<AnyThemeGroupIds>,\n    classPartObject: ClassPartObject,\n    classGroupId: AnyClassGroupIds,\n    theme: ThemeObject<AnyThemeGroupIds>,\n) => {\n    classGroup.forEach((classDefinition) => {\n        if (typeof classDefinition === 'string') {\n            const classPartObjectToEdit =\n                classDefinition === '' ? classPartObject : getPart(classPartObject, classDefinition)\n            classPartObjectToEdit.classGroupId = classGroupId\n            return\n        }\n\n        if (typeof classDefinition === 'function') {\n            if (isThemeGetter(classDefinition)) {\n                processClassesRecursively(\n                    classDefinition(theme),\n                    classPartObject,\n                    classGroupId,\n                    theme,\n                )\n                return\n            }\n\n            classPartObject.validators.push({\n                validator: classDefinition,\n                classGroupId,\n            })\n\n            return\n        }\n\n        Object.entries(classDefinition).forEach(([key, classGroup]) => {\n            processClassesRecursively(\n                classGroup,\n                getPart(classPartObject, key),\n                classGroupId,\n                theme,\n            )\n        })\n    })\n}\n\nconst getPart = (classPartObject: ClassPartObject, path: string) => {\n    let currentClassPartObject = classPartObject\n\n    path.split(CLASS_PART_SEPARATOR).forEach((pathPart) => {\n        if (!currentClassPartObject.nextPart.has(pathPart)) {\n            currentClassPartObject.nextPart.set(pathPart, {\n                nextPart: new Map(),\n                validators: [],\n            })\n        }\n\n        currentClassPartObject = currentClassPartObject.nextPart.get(pathPart)!\n    })\n\n    return currentClassPartObject\n}\n\nconst isThemeGetter = (func: ClassValidator | ThemeGetter): func is ThemeGetter =>\n    (func as ThemeGetter).isThemeGetter\n\nconst getPrefixedClassGroupEntries = (\n    classGroupEntries: Array<[classGroupId: string, classGroup: ClassGroup<AnyThemeGroupIds>]>,\n    prefix: string | undefined,\n): Array<[classGroupId: string, classGroup: ClassGroup<AnyThemeGroupIds>]> => {\n    if (!prefix) {\n        return classGroupEntries\n    }\n\n    return classGroupEntries.map(([classGroupId, classGroup]) => {\n        const prefixedClassGroup = classGroup.map((classDefinition) => {\n            if (typeof classDefinition === 'string') {\n                return prefix + classDefinition\n            }\n\n            if (typeof classDefinition === 'object') {\n                return Object.fromEntries(\n                    Object.entries(classDefinition).map(([key, value]) => [prefix + key, value]),\n                )\n            }\n\n            return classDefinition\n        })\n\n        return [classGroupId, prefixedClassGroup]\n    })\n}\n", "// Export is needed because TypeScript complains about an error otherwise:\n// Error: …/tailwind-merge/src/config-utils.ts(8,17): semantic error TS4058: Return type of exported function has or is using name 'LruCache' from external module \"…/tailwind-merge/src/lru-cache\" but cannot be named.\nexport interface LruCache<Key, Value> {\n    get(key: Key): Value | undefined\n    set(key: Key, value: Value): void\n}\n\n// LRU cache inspired from hashlru (https://github.com/dominictarr/hashlru/blob/v1.0.4/index.js) but object replaced with Map to improve performance\nexport const createLruCache = <Key, Value>(maxCacheSize: number): LruCache<Key, Value> => {\n    if (maxCacheSize < 1) {\n        return {\n            get: () => undefined,\n            set: () => {},\n        }\n    }\n\n    let cacheSize = 0\n    let cache = new Map<Key, Value>()\n    let previousCache = new Map<Key, Value>()\n\n    const update = (key: Key, value: Value) => {\n        cache.set(key, value)\n        cacheSize++\n\n        if (cacheSize > maxCacheSize) {\n            cacheSize = 0\n            previousCache = cache\n            cache = new Map()\n        }\n    }\n\n    return {\n        get(key) {\n            let value = cache.get(key)\n\n            if (value !== undefined) {\n                return value\n            }\n            if ((value = previousCache.get(key)) !== undefined) {\n                update(key, value)\n                return value\n            }\n        },\n        set(key, value) {\n            if (cache.has(key)) {\n                cache.set(key, value)\n            } else {\n                update(key, value)\n            }\n        },\n    }\n}\n", "import { AnyConfig } from './types'\n\nexport const IMPORTANT_MODIFIER = '!'\n\nexport const createParseClassName = (config: AnyConfig) => {\n    const { separator, experimentalParseClassName } = config\n    const isSeparatorSingleCharacter = separator.length === 1\n    const firstSeparatorCharacter = separator[0]\n    const separatorLength = separator.length\n\n    // parseClassName inspired by https://github.com/tailwindlabs/tailwindcss/blob/v3.2.2/src/util/splitAtTopLevelOnly.js\n    const parseClassName = (className: string) => {\n        const modifiers = []\n\n        let bracketDepth = 0\n        let modifierStart = 0\n        let postfixModifierPosition: number | undefined\n\n        for (let index = 0; index < className.length; index++) {\n            let currentCharacter = className[index]\n\n            if (bracketDepth === 0) {\n                if (\n                    currentCharacter === firstSeparatorCharacter &&\n                    (isSeparatorSingleCharacter ||\n                        className.slice(index, index + separatorLength) === separator)\n                ) {\n                    modifiers.push(className.slice(modifierStart, index))\n                    modifierStart = index + separatorLength\n                    continue\n                }\n\n                if (currentCharacter === '/') {\n                    postfixModifierPosition = index\n                    continue\n                }\n            }\n\n            if (currentCharacter === '[') {\n                bracketDepth++\n            } else if (currentCharacter === ']') {\n                bracketDepth--\n            }\n        }\n\n        const baseClassNameWithImportantModifier =\n            modifiers.length === 0 ? className : className.substring(modifierStart)\n        const hasImportantModifier =\n            baseClassNameWithImportantModifier.startsWith(IMPORTANT_MODIFIER)\n        const baseClassName = hasImportantModifier\n            ? baseClassNameWithImportantModifier.substring(1)\n            : baseClassNameWithImportantModifier\n\n        const maybePostfixModifierPosition =\n            postfixModifierPosition && postfixModifierPosition > modifierStart\n                ? postfixModifierPosition - modifierStart\n                : undefined\n\n        return {\n            modifiers,\n            hasImportantModifier,\n            baseClassName,\n            maybePostfixModifierPosition,\n        }\n    }\n\n    if (experimentalParseClassName) {\n        return (className: string) => experimentalParseClassName({ className, parseClassName })\n    }\n\n    return parseClassName\n}\n\n/**\n * Sorts modifiers according to following schema:\n * - Predefined modifiers are sorted alphabetically\n * - When an arbitrary variant appears, it must be preserved which modifiers are before and after it\n */\nexport const sortModifiers = (modifiers: string[]) => {\n    if (modifiers.length <= 1) {\n        return modifiers\n    }\n\n    const sortedModifiers: string[] = []\n    let unsortedModifiers: string[] = []\n\n    modifiers.forEach((modifier) => {\n        const isArbitraryVariant = modifier[0] === '['\n\n        if (isArbitraryVariant) {\n            sortedModifiers.push(...unsortedModifiers.sort(), modifier)\n            unsortedModifiers = []\n        } else {\n            unsortedModifiers.push(modifier)\n        }\n    })\n\n    sortedModifiers.push(...unsortedModifiers.sort())\n\n    return sortedModifiers\n}\n", "import { createClassGroupUtils } from './class-group-utils'\nimport { createLruCache } from './lru-cache'\nimport { createParseClassName } from './parse-class-name'\nimport { AnyConfig } from './types'\n\nexport type ConfigUtils = ReturnType<typeof createConfigUtils>\n\nexport const createConfigUtils = (config: AnyConfig) => ({\n    cache: createLruCache<string, string>(config.cacheSize),\n    parseClassName: createParseClassName(config),\n    ...createClassGroupUtils(config),\n})\n", "import { ConfigUtils } from './config-utils'\nimport { IMPORTANT_MODIFIER, sortModifiers } from './parse-class-name'\n\nconst SPLIT_CLASSES_REGEX = /\\s+/\n\nexport const mergeClassList = (classList: string, configUtils: ConfigUtils) => {\n    const { parseClassName, getClassGroupId, getConflictingClassGroupIds } = configUtils\n\n    /**\n     * Set of classGroupIds in following format:\n     * `{importantModifier}{variantModifiers}{classGroupId}`\n     * @example 'float'\n     * @example 'hover:focus:bg-color'\n     * @example 'md:!pr'\n     */\n    const classGroupsInConflict: string[] = []\n    const classNames = classList.trim().split(SPLIT_CLASSES_REGEX)\n\n    let result = ''\n\n    for (let index = classNames.length - 1; index >= 0; index -= 1) {\n        const originalClassName = classNames[index]!\n\n        const { modifiers, hasImportantModifier, baseClassName, maybePostfixModifierPosition } =\n            parseClassName(originalClassName)\n\n        let hasPostfixModifier = Boolean(maybePostfixModifierPosition)\n        let classGroupId = getClassGroupId(\n            hasPostfixModifier\n                ? baseClassName.substring(0, maybePostfixModifierPosition)\n                : baseClassName,\n        )\n\n        if (!classGroupId) {\n            if (!hasPostfixModifier) {\n                // Not a Tailwind class\n                result = originalClassName + (result.length > 0 ? ' ' + result : result)\n                continue\n            }\n\n            classGroupId = getClassGroupId(baseClassName)\n\n            if (!classGroupId) {\n                // Not a Tailwind class\n                result = originalClassName + (result.length > 0 ? ' ' + result : result)\n                continue\n            }\n\n            hasPostfixModifier = false\n        }\n\n        const variantModifier = sortModifiers(modifiers).join(':')\n\n        const modifierId = hasImportantModifier\n            ? variantModifier + IMPORTANT_MODIFIER\n            : variantModifier\n\n        const classId = modifierId + classGroupId\n\n        if (classGroupsInConflict.includes(classId)) {\n            // Tailwind class omitted due to conflict\n            continue\n        }\n\n        classGroupsInConflict.push(classId)\n\n        const conflictGroups = getConflictingClassGroupIds(classGroupId, hasPostfixModifier)\n        for (let i = 0; i < conflictGroups.length; ++i) {\n            const group = conflictGroups[i]!\n            classGroupsInConflict.push(modifierId + group)\n        }\n\n        // Tailwind class not in conflict\n        result = originalClassName + (result.length > 0 ? ' ' + result : result)\n    }\n\n    return result\n}\n", "/**\n * The code in this file is copied from https://github.com/lukeed/clsx and modified to suit the needs of tailwind-merge better.\n *\n * Specifically:\n * - Runtime code from https://github.com/lukeed/clsx/blob/v1.2.1/src/index.js\n * - TypeScript types from https://github.com/lukeed/clsx/blob/v1.2.1/clsx.d.ts\n *\n * Original code has MIT license: Copyright (c) <PERSON> <<EMAIL>> (lukeed.com)\n */\n\nexport type ClassNameValue = ClassNameArray | string | null | undefined | 0 | 0n | false\ntype ClassNameArray = ClassNameValue[]\n\nexport function twJoin(...classLists: ClassNameValue[]): string\nexport function twJoin() {\n    let index = 0\n    let argument: ClassNameValue\n    let resolvedValue: string\n    let string = ''\n\n    while (index < arguments.length) {\n        if ((argument = arguments[index++])) {\n            if ((resolvedValue = toValue(argument))) {\n                string && (string += ' ')\n                string += resolvedValue\n            }\n        }\n    }\n    return string\n}\n\nconst toValue = (mix: ClassNameArray | string) => {\n    if (typeof mix === 'string') {\n        return mix\n    }\n\n    let resolvedValue: string\n    let string = ''\n\n    for (let k = 0; k < mix.length; k++) {\n        if (mix[k]) {\n            if ((resolvedValue = toValue(mix[k] as ClassNameArray | string))) {\n                string && (string += ' ')\n                string += resolvedValue\n            }\n        }\n    }\n\n    return string\n}\n", "import { createConfigUtils } from './config-utils'\nimport { mergeClassList } from './merge-classlist'\nimport { ClassNameValue, twJoin } from './tw-join'\nimport { AnyConfig } from './types'\n\ntype CreateConfigFirst = () => AnyConfig\ntype CreateConfigSubsequent = (config: AnyConfig) => AnyConfig\ntype TailwindMerge = (...classLists: ClassNameValue[]) => string\ntype ConfigUtils = ReturnType<typeof createConfigUtils>\n\nexport function createTailwindMerge(\n    createConfigFirst: CreateConfigFirst,\n    ...createConfigRest: CreateConfigSubsequent[]\n): TailwindMerge {\n    let configUtils: ConfigUtils\n    let cacheGet: ConfigUtils['cache']['get']\n    let cacheSet: ConfigUtils['cache']['set']\n    let functionToCall = initTailwindMerge\n\n    function initTailwindMerge(classList: string) {\n        const config = createConfigRest.reduce(\n            (previousConfig, createConfigCurrent) => createConfigCurrent(previousConfig),\n            createConfigFirst() as AnyConfig,\n        )\n\n        configUtils = createConfigUtils(config)\n        cacheGet = configUtils.cache.get\n        cacheSet = configUtils.cache.set\n        functionToCall = tailwindMerge\n\n        return tailwindMerge(classList)\n    }\n\n    function tailwindMerge(classList: string) {\n        const cachedResult = cacheGet(classList)\n\n        if (cachedResult) {\n            return cachedResult\n        }\n\n        const result = mergeClassList(classList, configUtils)\n        cacheSet(classList, result)\n\n        return result\n    }\n\n    return function callTailwindMerge() {\n        return functionToCall(twJoin.apply(null, arguments as any))\n    }\n}\n", "import { DefaultThemeGroupIds, <PERSON><PERSON><PERSON><PERSON>, ThemeGetter, ThemeObject } from './types'\n\nexport const fromTheme = <\n    AdditionalThemeGroupIds extends string = never,\n    DefaultThemeGroupIdsInner extends string = DefaultThemeGroupIds,\n>(key: NoInfer<DefaultThemeGroupIdsInner | AdditionalThemeGroupIds>): ThemeGetter => {\n    const themeGetter = (theme: ThemeObject<DefaultThemeGroupIdsInner | AdditionalThemeGroupIds>) =>\n        theme[key] || []\n\n    themeGetter.isThemeGetter = true as const\n\n    return themeGetter\n}\n", "const arbitraryValueRegex = /^\\[(?:([a-z-]+):)?(.+)\\]$/i\nconst fractionRegex = /^\\d+\\/\\d+$/\nconst stringLengths = new Set(['px', 'full', 'screen'])\nconst tshirtUnitRegex = /^(\\d+(\\.\\d+)?)?(xs|sm|md|lg|xl)$/\nconst lengthUnitRegex =\n    /\\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\\b(calc|min|max|clamp)\\(.+\\)|^0$/\nconst colorFunctionRegex = /^(rgba?|hsla?|hwb|(ok)?(lab|lch))\\(.+\\)$/\n// Shadow always begins with x and y offset separated by underscore optionally prepended by inset\nconst shadowRegex = /^(inset_)?-?((\\d+)?\\.?(\\d+)[a-z]+|0)_-?((\\d+)?\\.?(\\d+)[a-z]+|0)/\nconst imageRegex =\n    /^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\\(.+\\)$/\n\nexport const isLength = (value: string) =>\n    isNumber(value) || stringLengths.has(value) || fractionRegex.test(value)\n\nexport const isArbitraryLength = (value: string) =>\n    getIsArbitraryValue(value, 'length', isLengthOnly)\n\nexport const isNumber = (value: string) => Boolean(value) && !Number.isNaN(Number(value))\n\nexport const isArbitraryNumber = (value: string) => getIsArbitraryValue(value, 'number', isNumber)\n\nexport const isInteger = (value: string) => Boolean(value) && Number.isInteger(Number(value))\n\nexport const isPercent = (value: string) => value.endsWith('%') && isNumber(value.slice(0, -1))\n\nexport const isArbitraryValue = (value: string) => arbitraryValueRegex.test(value)\n\nexport const isTshirtSize = (value: string) => tshirtUnitRegex.test(value)\n\nconst sizeLabels = new Set(['length', 'size', 'percentage'])\n\nexport const isArbitrarySize = (value: string) => getIsArbitraryValue(value, sizeLabels, isNever)\n\nexport const isArbitraryPosition = (value: string) =>\n    getIsArbitraryValue(value, 'position', isNever)\n\nconst imageLabels = new Set(['image', 'url'])\n\nexport const isArbitraryImage = (value: string) => getIsArbitraryValue(value, imageLabels, isImage)\n\nexport const isArbitraryShadow = (value: string) => getIsArbitraryValue(value, '', isShadow)\n\nexport const isAny = () => true\n\nconst getIsArbitraryValue = (\n    value: string,\n    label: string | Set<string>,\n    testValue: (value: string) => boolean,\n) => {\n    const result = arbitraryValueRegex.exec(value)\n\n    if (result) {\n        if (result[1]) {\n            return typeof label === 'string' ? result[1] === label : label.has(result[1])\n        }\n\n        return testValue(result[2]!)\n    }\n\n    return false\n}\n\nconst isLengthOnly = (value: string) =>\n    // `colorFunctionRegex` check is necessary because color functions can have percentages in them which which would be incorrectly classified as lengths.\n    // For example, `hsl(0 0% 0%)` would be classified as a length without this check.\n    // I could also use lookbehind assertion in `lengthUnitRegex` but that isn't supported widely enough.\n    lengthUnitRegex.test(value) && !colorFunctionRegex.test(value)\n\nconst isNever = () => false\n\nconst isShadow = (value: string) => shadowRegex.test(value)\n\nconst isImage = (value: string) => imageRegex.test(value)\n", "import { fromTheme } from './from-theme'\nimport { Config, DefaultClassGroupIds, DefaultThemeGroupIds } from './types'\nimport {\n    isAny,\n    isArbitraryImage,\n    isArbitraryLength,\n    isArbitraryNumber,\n    isArbitraryPosition,\n    isArbitraryShadow,\n    isArbitrarySize,\n    isArbitraryValue,\n    isInteger,\n    isLength,\n    isNumber,\n    isPercent,\n    isTshirtSize,\n} from './validators'\n\nexport const getDefaultConfig = () => {\n    const colors = fromTheme('colors')\n    const spacing = fromTheme('spacing')\n    const blur = fromTheme('blur')\n    const brightness = fromTheme('brightness')\n    const borderColor = fromTheme('borderColor')\n    const borderRadius = fromTheme('borderRadius')\n    const borderSpacing = fromTheme('borderSpacing')\n    const borderWidth = fromTheme('borderWidth')\n    const contrast = fromTheme('contrast')\n    const grayscale = fromTheme('grayscale')\n    const hueRotate = fromTheme('hueRotate')\n    const invert = fromTheme('invert')\n    const gap = fromTheme('gap')\n    const gradientColorStops = fromTheme('gradientColorStops')\n    const gradientColorStopPositions = fromTheme('gradientColorStopPositions')\n    const inset = fromTheme('inset')\n    const margin = fromTheme('margin')\n    const opacity = fromTheme('opacity')\n    const padding = fromTheme('padding')\n    const saturate = fromTheme('saturate')\n    const scale = fromTheme('scale')\n    const sepia = fromTheme('sepia')\n    const skew = fromTheme('skew')\n    const space = fromTheme('space')\n    const translate = fromTheme('translate')\n\n    const getOverscroll = () => ['auto', 'contain', 'none'] as const\n    const getOverflow = () => ['auto', 'hidden', 'clip', 'visible', 'scroll'] as const\n    const getSpacingWithAutoAndArbitrary = () => ['auto', isArbitraryValue, spacing] as const\n    const getSpacingWithArbitrary = () => [isArbitraryValue, spacing] as const\n    const getLengthWithEmptyAndArbitrary = () => ['', isLength, isArbitraryLength] as const\n    const getNumberWithAutoAndArbitrary = () => ['auto', isNumber, isArbitraryValue] as const\n    const getPositions = () =>\n        [\n            'bottom',\n            'center',\n            'left',\n            'left-bottom',\n            'left-top',\n            'right',\n            'right-bottom',\n            'right-top',\n            'top',\n        ] as const\n    const getLineStyles = () => ['solid', 'dashed', 'dotted', 'double', 'none'] as const\n    const getBlendModes = () =>\n        [\n            'normal',\n            'multiply',\n            'screen',\n            'overlay',\n            'darken',\n            'lighten',\n            'color-dodge',\n            'color-burn',\n            'hard-light',\n            'soft-light',\n            'difference',\n            'exclusion',\n            'hue',\n            'saturation',\n            'color',\n            'luminosity',\n        ] as const\n    const getAlign = () =>\n        ['start', 'end', 'center', 'between', 'around', 'evenly', 'stretch'] as const\n    const getZeroAndEmpty = () => ['', '0', isArbitraryValue] as const\n    const getBreaks = () =>\n        ['auto', 'avoid', 'all', 'avoid-page', 'page', 'left', 'right', 'column'] as const\n    const getNumberAndArbitrary = () => [isNumber, isArbitraryValue]\n\n    return {\n        cacheSize: 500,\n        separator: ':',\n        theme: {\n            colors: [isAny],\n            spacing: [isLength, isArbitraryLength],\n            blur: ['none', '', isTshirtSize, isArbitraryValue],\n            brightness: getNumberAndArbitrary(),\n            borderColor: [colors],\n            borderRadius: ['none', '', 'full', isTshirtSize, isArbitraryValue],\n            borderSpacing: getSpacingWithArbitrary(),\n            borderWidth: getLengthWithEmptyAndArbitrary(),\n            contrast: getNumberAndArbitrary(),\n            grayscale: getZeroAndEmpty(),\n            hueRotate: getNumberAndArbitrary(),\n            invert: getZeroAndEmpty(),\n            gap: getSpacingWithArbitrary(),\n            gradientColorStops: [colors],\n            gradientColorStopPositions: [isPercent, isArbitraryLength],\n            inset: getSpacingWithAutoAndArbitrary(),\n            margin: getSpacingWithAutoAndArbitrary(),\n            opacity: getNumberAndArbitrary(),\n            padding: getSpacingWithArbitrary(),\n            saturate: getNumberAndArbitrary(),\n            scale: getNumberAndArbitrary(),\n            sepia: getZeroAndEmpty(),\n            skew: getNumberAndArbitrary(),\n            space: getSpacingWithArbitrary(),\n            translate: getSpacingWithArbitrary(),\n        },\n        classGroups: {\n            // Layout\n            /**\n             * Aspect Ratio\n             * @see https://tailwindcss.com/docs/aspect-ratio\n             */\n            aspect: [{ aspect: ['auto', 'square', 'video', isArbitraryValue] }],\n            /**\n             * Container\n             * @see https://tailwindcss.com/docs/container\n             */\n            container: ['container'],\n            /**\n             * Columns\n             * @see https://tailwindcss.com/docs/columns\n             */\n            columns: [{ columns: [isTshirtSize] }],\n            /**\n             * Break After\n             * @see https://tailwindcss.com/docs/break-after\n             */\n            'break-after': [{ 'break-after': getBreaks() }],\n            /**\n             * Break Before\n             * @see https://tailwindcss.com/docs/break-before\n             */\n            'break-before': [{ 'break-before': getBreaks() }],\n            /**\n             * Break Inside\n             * @see https://tailwindcss.com/docs/break-inside\n             */\n            'break-inside': [{ 'break-inside': ['auto', 'avoid', 'avoid-page', 'avoid-column'] }],\n            /**\n             * Box Decoration Break\n             * @see https://tailwindcss.com/docs/box-decoration-break\n             */\n            'box-decoration': [{ 'box-decoration': ['slice', 'clone'] }],\n            /**\n             * Box Sizing\n             * @see https://tailwindcss.com/docs/box-sizing\n             */\n            box: [{ box: ['border', 'content'] }],\n            /**\n             * Display\n             * @see https://tailwindcss.com/docs/display\n             */\n            display: [\n                'block',\n                'inline-block',\n                'inline',\n                'flex',\n                'inline-flex',\n                'table',\n                'inline-table',\n                'table-caption',\n                'table-cell',\n                'table-column',\n                'table-column-group',\n                'table-footer-group',\n                'table-header-group',\n                'table-row-group',\n                'table-row',\n                'flow-root',\n                'grid',\n                'inline-grid',\n                'contents',\n                'list-item',\n                'hidden',\n            ],\n            /**\n             * Floats\n             * @see https://tailwindcss.com/docs/float\n             */\n            float: [{ float: ['right', 'left', 'none', 'start', 'end'] }],\n            /**\n             * Clear\n             * @see https://tailwindcss.com/docs/clear\n             */\n            clear: [{ clear: ['left', 'right', 'both', 'none', 'start', 'end'] }],\n            /**\n             * Isolation\n             * @see https://tailwindcss.com/docs/isolation\n             */\n            isolation: ['isolate', 'isolation-auto'],\n            /**\n             * Object Fit\n             * @see https://tailwindcss.com/docs/object-fit\n             */\n            'object-fit': [{ object: ['contain', 'cover', 'fill', 'none', 'scale-down'] }],\n            /**\n             * Object Position\n             * @see https://tailwindcss.com/docs/object-position\n             */\n            'object-position': [{ object: [...getPositions(), isArbitraryValue] }],\n            /**\n             * Overflow\n             * @see https://tailwindcss.com/docs/overflow\n             */\n            overflow: [{ overflow: getOverflow() }],\n            /**\n             * Overflow X\n             * @see https://tailwindcss.com/docs/overflow\n             */\n            'overflow-x': [{ 'overflow-x': getOverflow() }],\n            /**\n             * Overflow Y\n             * @see https://tailwindcss.com/docs/overflow\n             */\n            'overflow-y': [{ 'overflow-y': getOverflow() }],\n            /**\n             * Overscroll Behavior\n             * @see https://tailwindcss.com/docs/overscroll-behavior\n             */\n            overscroll: [{ overscroll: getOverscroll() }],\n            /**\n             * Overscroll Behavior X\n             * @see https://tailwindcss.com/docs/overscroll-behavior\n             */\n            'overscroll-x': [{ 'overscroll-x': getOverscroll() }],\n            /**\n             * Overscroll Behavior Y\n             * @see https://tailwindcss.com/docs/overscroll-behavior\n             */\n            'overscroll-y': [{ 'overscroll-y': getOverscroll() }],\n            /**\n             * Position\n             * @see https://tailwindcss.com/docs/position\n             */\n            position: ['static', 'fixed', 'absolute', 'relative', 'sticky'],\n            /**\n             * Top / Right / Bottom / Left\n             * @see https://tailwindcss.com/docs/top-right-bottom-left\n             */\n            inset: [{ inset: [inset] }],\n            /**\n             * Right / Left\n             * @see https://tailwindcss.com/docs/top-right-bottom-left\n             */\n            'inset-x': [{ 'inset-x': [inset] }],\n            /**\n             * Top / Bottom\n             * @see https://tailwindcss.com/docs/top-right-bottom-left\n             */\n            'inset-y': [{ 'inset-y': [inset] }],\n            /**\n             * Start\n             * @see https://tailwindcss.com/docs/top-right-bottom-left\n             */\n            start: [{ start: [inset] }],\n            /**\n             * End\n             * @see https://tailwindcss.com/docs/top-right-bottom-left\n             */\n            end: [{ end: [inset] }],\n            /**\n             * Top\n             * @see https://tailwindcss.com/docs/top-right-bottom-left\n             */\n            top: [{ top: [inset] }],\n            /**\n             * Right\n             * @see https://tailwindcss.com/docs/top-right-bottom-left\n             */\n            right: [{ right: [inset] }],\n            /**\n             * Bottom\n             * @see https://tailwindcss.com/docs/top-right-bottom-left\n             */\n            bottom: [{ bottom: [inset] }],\n            /**\n             * Left\n             * @see https://tailwindcss.com/docs/top-right-bottom-left\n             */\n            left: [{ left: [inset] }],\n            /**\n             * Visibility\n             * @see https://tailwindcss.com/docs/visibility\n             */\n            visibility: ['visible', 'invisible', 'collapse'],\n            /**\n             * Z-Index\n             * @see https://tailwindcss.com/docs/z-index\n             */\n            z: [{ z: ['auto', isInteger, isArbitraryValue] }],\n            // Flexbox and Grid\n            /**\n             * Flex Basis\n             * @see https://tailwindcss.com/docs/flex-basis\n             */\n            basis: [{ basis: getSpacingWithAutoAndArbitrary() }],\n            /**\n             * Flex Direction\n             * @see https://tailwindcss.com/docs/flex-direction\n             */\n            'flex-direction': [{ flex: ['row', 'row-reverse', 'col', 'col-reverse'] }],\n            /**\n             * Flex Wrap\n             * @see https://tailwindcss.com/docs/flex-wrap\n             */\n            'flex-wrap': [{ flex: ['wrap', 'wrap-reverse', 'nowrap'] }],\n            /**\n             * Flex\n             * @see https://tailwindcss.com/docs/flex\n             */\n            flex: [{ flex: ['1', 'auto', 'initial', 'none', isArbitraryValue] }],\n            /**\n             * Flex Grow\n             * @see https://tailwindcss.com/docs/flex-grow\n             */\n            grow: [{ grow: getZeroAndEmpty() }],\n            /**\n             * Flex Shrink\n             * @see https://tailwindcss.com/docs/flex-shrink\n             */\n            shrink: [{ shrink: getZeroAndEmpty() }],\n            /**\n             * Order\n             * @see https://tailwindcss.com/docs/order\n             */\n            order: [{ order: ['first', 'last', 'none', isInteger, isArbitraryValue] }],\n            /**\n             * Grid Template Columns\n             * @see https://tailwindcss.com/docs/grid-template-columns\n             */\n            'grid-cols': [{ 'grid-cols': [isAny] }],\n            /**\n             * Grid Column Start / End\n             * @see https://tailwindcss.com/docs/grid-column\n             */\n            'col-start-end': [\n                {\n                    col: [\n                        'auto',\n                        { span: ['full', isInteger, isArbitraryValue] },\n                        isArbitraryValue,\n                    ],\n                },\n            ],\n            /**\n             * Grid Column Start\n             * @see https://tailwindcss.com/docs/grid-column\n             */\n            'col-start': [{ 'col-start': getNumberWithAutoAndArbitrary() }],\n            /**\n             * Grid Column End\n             * @see https://tailwindcss.com/docs/grid-column\n             */\n            'col-end': [{ 'col-end': getNumberWithAutoAndArbitrary() }],\n            /**\n             * Grid Template Rows\n             * @see https://tailwindcss.com/docs/grid-template-rows\n             */\n            'grid-rows': [{ 'grid-rows': [isAny] }],\n            /**\n             * Grid Row Start / End\n             * @see https://tailwindcss.com/docs/grid-row\n             */\n            'row-start-end': [\n                { row: ['auto', { span: [isInteger, isArbitraryValue] }, isArbitraryValue] },\n            ],\n            /**\n             * Grid Row Start\n             * @see https://tailwindcss.com/docs/grid-row\n             */\n            'row-start': [{ 'row-start': getNumberWithAutoAndArbitrary() }],\n            /**\n             * Grid Row End\n             * @see https://tailwindcss.com/docs/grid-row\n             */\n            'row-end': [{ 'row-end': getNumberWithAutoAndArbitrary() }],\n            /**\n             * Grid Auto Flow\n             * @see https://tailwindcss.com/docs/grid-auto-flow\n             */\n            'grid-flow': [{ 'grid-flow': ['row', 'col', 'dense', 'row-dense', 'col-dense'] }],\n            /**\n             * Grid Auto Columns\n             * @see https://tailwindcss.com/docs/grid-auto-columns\n             */\n            'auto-cols': [{ 'auto-cols': ['auto', 'min', 'max', 'fr', isArbitraryValue] }],\n            /**\n             * Grid Auto Rows\n             * @see https://tailwindcss.com/docs/grid-auto-rows\n             */\n            'auto-rows': [{ 'auto-rows': ['auto', 'min', 'max', 'fr', isArbitraryValue] }],\n            /**\n             * Gap\n             * @see https://tailwindcss.com/docs/gap\n             */\n            gap: [{ gap: [gap] }],\n            /**\n             * Gap X\n             * @see https://tailwindcss.com/docs/gap\n             */\n            'gap-x': [{ 'gap-x': [gap] }],\n            /**\n             * Gap Y\n             * @see https://tailwindcss.com/docs/gap\n             */\n            'gap-y': [{ 'gap-y': [gap] }],\n            /**\n             * Justify Content\n             * @see https://tailwindcss.com/docs/justify-content\n             */\n            'justify-content': [{ justify: ['normal', ...getAlign()] }],\n            /**\n             * Justify Items\n             * @see https://tailwindcss.com/docs/justify-items\n             */\n            'justify-items': [{ 'justify-items': ['start', 'end', 'center', 'stretch'] }],\n            /**\n             * Justify Self\n             * @see https://tailwindcss.com/docs/justify-self\n             */\n            'justify-self': [{ 'justify-self': ['auto', 'start', 'end', 'center', 'stretch'] }],\n            /**\n             * Align Content\n             * @see https://tailwindcss.com/docs/align-content\n             */\n            'align-content': [{ content: ['normal', ...getAlign(), 'baseline'] }],\n            /**\n             * Align Items\n             * @see https://tailwindcss.com/docs/align-items\n             */\n            'align-items': [{ items: ['start', 'end', 'center', 'baseline', 'stretch'] }],\n            /**\n             * Align Self\n             * @see https://tailwindcss.com/docs/align-self\n             */\n            'align-self': [{ self: ['auto', 'start', 'end', 'center', 'stretch', 'baseline'] }],\n            /**\n             * Place Content\n             * @see https://tailwindcss.com/docs/place-content\n             */\n            'place-content': [{ 'place-content': [...getAlign(), 'baseline'] }],\n            /**\n             * Place Items\n             * @see https://tailwindcss.com/docs/place-items\n             */\n            'place-items': [{ 'place-items': ['start', 'end', 'center', 'baseline', 'stretch'] }],\n            /**\n             * Place Self\n             * @see https://tailwindcss.com/docs/place-self\n             */\n            'place-self': [{ 'place-self': ['auto', 'start', 'end', 'center', 'stretch'] }],\n            // Spacing\n            /**\n             * Padding\n             * @see https://tailwindcss.com/docs/padding\n             */\n            p: [{ p: [padding] }],\n            /**\n             * Padding X\n             * @see https://tailwindcss.com/docs/padding\n             */\n            px: [{ px: [padding] }],\n            /**\n             * Padding Y\n             * @see https://tailwindcss.com/docs/padding\n             */\n            py: [{ py: [padding] }],\n            /**\n             * Padding Start\n             * @see https://tailwindcss.com/docs/padding\n             */\n            ps: [{ ps: [padding] }],\n            /**\n             * Padding End\n             * @see https://tailwindcss.com/docs/padding\n             */\n            pe: [{ pe: [padding] }],\n            /**\n             * Padding Top\n             * @see https://tailwindcss.com/docs/padding\n             */\n            pt: [{ pt: [padding] }],\n            /**\n             * Padding Right\n             * @see https://tailwindcss.com/docs/padding\n             */\n            pr: [{ pr: [padding] }],\n            /**\n             * Padding Bottom\n             * @see https://tailwindcss.com/docs/padding\n             */\n            pb: [{ pb: [padding] }],\n            /**\n             * Padding Left\n             * @see https://tailwindcss.com/docs/padding\n             */\n            pl: [{ pl: [padding] }],\n            /**\n             * Margin\n             * @see https://tailwindcss.com/docs/margin\n             */\n            m: [{ m: [margin] }],\n            /**\n             * Margin X\n             * @see https://tailwindcss.com/docs/margin\n             */\n            mx: [{ mx: [margin] }],\n            /**\n             * Margin Y\n             * @see https://tailwindcss.com/docs/margin\n             */\n            my: [{ my: [margin] }],\n            /**\n             * Margin Start\n             * @see https://tailwindcss.com/docs/margin\n             */\n            ms: [{ ms: [margin] }],\n            /**\n             * Margin End\n             * @see https://tailwindcss.com/docs/margin\n             */\n            me: [{ me: [margin] }],\n            /**\n             * Margin Top\n             * @see https://tailwindcss.com/docs/margin\n             */\n            mt: [{ mt: [margin] }],\n            /**\n             * Margin Right\n             * @see https://tailwindcss.com/docs/margin\n             */\n            mr: [{ mr: [margin] }],\n            /**\n             * Margin Bottom\n             * @see https://tailwindcss.com/docs/margin\n             */\n            mb: [{ mb: [margin] }],\n            /**\n             * Margin Left\n             * @see https://tailwindcss.com/docs/margin\n             */\n            ml: [{ ml: [margin] }],\n            /**\n             * Space Between X\n             * @see https://tailwindcss.com/docs/space\n             */\n            'space-x': [{ 'space-x': [space] }],\n            /**\n             * Space Between X Reverse\n             * @see https://tailwindcss.com/docs/space\n             */\n            'space-x-reverse': ['space-x-reverse'],\n            /**\n             * Space Between Y\n             * @see https://tailwindcss.com/docs/space\n             */\n            'space-y': [{ 'space-y': [space] }],\n            /**\n             * Space Between Y Reverse\n             * @see https://tailwindcss.com/docs/space\n             */\n            'space-y-reverse': ['space-y-reverse'],\n            // Sizing\n            /**\n             * Width\n             * @see https://tailwindcss.com/docs/width\n             */\n            w: [\n                {\n                    w: [\n                        'auto',\n                        'min',\n                        'max',\n                        'fit',\n                        'svw',\n                        'lvw',\n                        'dvw',\n                        isArbitraryValue,\n                        spacing,\n                    ],\n                },\n            ],\n            /**\n             * Min-Width\n             * @see https://tailwindcss.com/docs/min-width\n             */\n            'min-w': [{ 'min-w': [isArbitraryValue, spacing, 'min', 'max', 'fit'] }],\n            /**\n             * Max-Width\n             * @see https://tailwindcss.com/docs/max-width\n             */\n            'max-w': [\n                {\n                    'max-w': [\n                        isArbitraryValue,\n                        spacing,\n                        'none',\n                        'full',\n                        'min',\n                        'max',\n                        'fit',\n                        'prose',\n                        { screen: [isTshirtSize] },\n                        isTshirtSize,\n                    ],\n                },\n            ],\n            /**\n             * Height\n             * @see https://tailwindcss.com/docs/height\n             */\n            h: [\n                {\n                    h: [\n                        isArbitraryValue,\n                        spacing,\n                        'auto',\n                        'min',\n                        'max',\n                        'fit',\n                        'svh',\n                        'lvh',\n                        'dvh',\n                    ],\n                },\n            ],\n            /**\n             * Min-Height\n             * @see https://tailwindcss.com/docs/min-height\n             */\n            'min-h': [\n                { 'min-h': [isArbitraryValue, spacing, 'min', 'max', 'fit', 'svh', 'lvh', 'dvh'] },\n            ],\n            /**\n             * Max-Height\n             * @see https://tailwindcss.com/docs/max-height\n             */\n            'max-h': [\n                { 'max-h': [isArbitraryValue, spacing, 'min', 'max', 'fit', 'svh', 'lvh', 'dvh'] },\n            ],\n            /**\n             * Size\n             * @see https://tailwindcss.com/docs/size\n             */\n            size: [{ size: [isArbitraryValue, spacing, 'auto', 'min', 'max', 'fit'] }],\n            // Typography\n            /**\n             * Font Size\n             * @see https://tailwindcss.com/docs/font-size\n             */\n            'font-size': [{ text: ['base', isTshirtSize, isArbitraryLength] }],\n            /**\n             * Font Smoothing\n             * @see https://tailwindcss.com/docs/font-smoothing\n             */\n            'font-smoothing': ['antialiased', 'subpixel-antialiased'],\n            /**\n             * Font Style\n             * @see https://tailwindcss.com/docs/font-style\n             */\n            'font-style': ['italic', 'not-italic'],\n            /**\n             * Font Weight\n             * @see https://tailwindcss.com/docs/font-weight\n             */\n            'font-weight': [\n                {\n                    font: [\n                        'thin',\n                        'extralight',\n                        'light',\n                        'normal',\n                        'medium',\n                        'semibold',\n                        'bold',\n                        'extrabold',\n                        'black',\n                        isArbitraryNumber,\n                    ],\n                },\n            ],\n            /**\n             * Font Family\n             * @see https://tailwindcss.com/docs/font-family\n             */\n            'font-family': [{ font: [isAny] }],\n            /**\n             * Font Variant Numeric\n             * @see https://tailwindcss.com/docs/font-variant-numeric\n             */\n            'fvn-normal': ['normal-nums'],\n            /**\n             * Font Variant Numeric\n             * @see https://tailwindcss.com/docs/font-variant-numeric\n             */\n            'fvn-ordinal': ['ordinal'],\n            /**\n             * Font Variant Numeric\n             * @see https://tailwindcss.com/docs/font-variant-numeric\n             */\n            'fvn-slashed-zero': ['slashed-zero'],\n            /**\n             * Font Variant Numeric\n             * @see https://tailwindcss.com/docs/font-variant-numeric\n             */\n            'fvn-figure': ['lining-nums', 'oldstyle-nums'],\n            /**\n             * Font Variant Numeric\n             * @see https://tailwindcss.com/docs/font-variant-numeric\n             */\n            'fvn-spacing': ['proportional-nums', 'tabular-nums'],\n            /**\n             * Font Variant Numeric\n             * @see https://tailwindcss.com/docs/font-variant-numeric\n             */\n            'fvn-fraction': ['diagonal-fractions', 'stacked-fractions'],\n            /**\n             * Letter Spacing\n             * @see https://tailwindcss.com/docs/letter-spacing\n             */\n            tracking: [\n                {\n                    tracking: [\n                        'tighter',\n                        'tight',\n                        'normal',\n                        'wide',\n                        'wider',\n                        'widest',\n                        isArbitraryValue,\n                    ],\n                },\n            ],\n            /**\n             * Line Clamp\n             * @see https://tailwindcss.com/docs/line-clamp\n             */\n            'line-clamp': [{ 'line-clamp': ['none', isNumber, isArbitraryNumber] }],\n            /**\n             * Line Height\n             * @see https://tailwindcss.com/docs/line-height\n             */\n            leading: [\n                {\n                    leading: [\n                        'none',\n                        'tight',\n                        'snug',\n                        'normal',\n                        'relaxed',\n                        'loose',\n                        isLength,\n                        isArbitraryValue,\n                    ],\n                },\n            ],\n            /**\n             * List Style Image\n             * @see https://tailwindcss.com/docs/list-style-image\n             */\n            'list-image': [{ 'list-image': ['none', isArbitraryValue] }],\n            /**\n             * List Style Type\n             * @see https://tailwindcss.com/docs/list-style-type\n             */\n            'list-style-type': [{ list: ['none', 'disc', 'decimal', isArbitraryValue] }],\n            /**\n             * List Style Position\n             * @see https://tailwindcss.com/docs/list-style-position\n             */\n            'list-style-position': [{ list: ['inside', 'outside'] }],\n            /**\n             * Placeholder Color\n             * @deprecated since Tailwind CSS v3.0.0\n             * @see https://tailwindcss.com/docs/placeholder-color\n             */\n            'placeholder-color': [{ placeholder: [colors] }],\n            /**\n             * Placeholder Opacity\n             * @see https://tailwindcss.com/docs/placeholder-opacity\n             */\n            'placeholder-opacity': [{ 'placeholder-opacity': [opacity] }],\n            /**\n             * Text Alignment\n             * @see https://tailwindcss.com/docs/text-align\n             */\n            'text-alignment': [{ text: ['left', 'center', 'right', 'justify', 'start', 'end'] }],\n            /**\n             * Text Color\n             * @see https://tailwindcss.com/docs/text-color\n             */\n            'text-color': [{ text: [colors] }],\n            /**\n             * Text Opacity\n             * @see https://tailwindcss.com/docs/text-opacity\n             */\n            'text-opacity': [{ 'text-opacity': [opacity] }],\n            /**\n             * Text Decoration\n             * @see https://tailwindcss.com/docs/text-decoration\n             */\n            'text-decoration': ['underline', 'overline', 'line-through', 'no-underline'],\n            /**\n             * Text Decoration Style\n             * @see https://tailwindcss.com/docs/text-decoration-style\n             */\n            'text-decoration-style': [{ decoration: [...getLineStyles(), 'wavy'] }],\n            /**\n             * Text Decoration Thickness\n             * @see https://tailwindcss.com/docs/text-decoration-thickness\n             */\n            'text-decoration-thickness': [\n                { decoration: ['auto', 'from-font', isLength, isArbitraryLength] },\n            ],\n            /**\n             * Text Underline Offset\n             * @see https://tailwindcss.com/docs/text-underline-offset\n             */\n            'underline-offset': [{ 'underline-offset': ['auto', isLength, isArbitraryValue] }],\n            /**\n             * Text Decoration Color\n             * @see https://tailwindcss.com/docs/text-decoration-color\n             */\n            'text-decoration-color': [{ decoration: [colors] }],\n            /**\n             * Text Transform\n             * @see https://tailwindcss.com/docs/text-transform\n             */\n            'text-transform': ['uppercase', 'lowercase', 'capitalize', 'normal-case'],\n            /**\n             * Text Overflow\n             * @see https://tailwindcss.com/docs/text-overflow\n             */\n            'text-overflow': ['truncate', 'text-ellipsis', 'text-clip'],\n            /**\n             * Text Wrap\n             * @see https://tailwindcss.com/docs/text-wrap\n             */\n            'text-wrap': [{ text: ['wrap', 'nowrap', 'balance', 'pretty'] }],\n            /**\n             * Text Indent\n             * @see https://tailwindcss.com/docs/text-indent\n             */\n            indent: [{ indent: getSpacingWithArbitrary() }],\n            /**\n             * Vertical Alignment\n             * @see https://tailwindcss.com/docs/vertical-align\n             */\n            'vertical-align': [\n                {\n                    align: [\n                        'baseline',\n                        'top',\n                        'middle',\n                        'bottom',\n                        'text-top',\n                        'text-bottom',\n                        'sub',\n                        'super',\n                        isArbitraryValue,\n                    ],\n                },\n            ],\n            /**\n             * Whitespace\n             * @see https://tailwindcss.com/docs/whitespace\n             */\n            whitespace: [\n                { whitespace: ['normal', 'nowrap', 'pre', 'pre-line', 'pre-wrap', 'break-spaces'] },\n            ],\n            /**\n             * Word Break\n             * @see https://tailwindcss.com/docs/word-break\n             */\n            break: [{ break: ['normal', 'words', 'all', 'keep'] }],\n            /**\n             * Hyphens\n             * @see https://tailwindcss.com/docs/hyphens\n             */\n            hyphens: [{ hyphens: ['none', 'manual', 'auto'] }],\n            /**\n             * Content\n             * @see https://tailwindcss.com/docs/content\n             */\n            content: [{ content: ['none', isArbitraryValue] }],\n            // Backgrounds\n            /**\n             * Background Attachment\n             * @see https://tailwindcss.com/docs/background-attachment\n             */\n            'bg-attachment': [{ bg: ['fixed', 'local', 'scroll'] }],\n            /**\n             * Background Clip\n             * @see https://tailwindcss.com/docs/background-clip\n             */\n            'bg-clip': [{ 'bg-clip': ['border', 'padding', 'content', 'text'] }],\n            /**\n             * Background Opacity\n             * @deprecated since Tailwind CSS v3.0.0\n             * @see https://tailwindcss.com/docs/background-opacity\n             */\n            'bg-opacity': [{ 'bg-opacity': [opacity] }],\n            /**\n             * Background Origin\n             * @see https://tailwindcss.com/docs/background-origin\n             */\n            'bg-origin': [{ 'bg-origin': ['border', 'padding', 'content'] }],\n            /**\n             * Background Position\n             * @see https://tailwindcss.com/docs/background-position\n             */\n            'bg-position': [{ bg: [...getPositions(), isArbitraryPosition] }],\n            /**\n             * Background Repeat\n             * @see https://tailwindcss.com/docs/background-repeat\n             */\n            'bg-repeat': [{ bg: ['no-repeat', { repeat: ['', 'x', 'y', 'round', 'space'] }] }],\n            /**\n             * Background Size\n             * @see https://tailwindcss.com/docs/background-size\n             */\n            'bg-size': [{ bg: ['auto', 'cover', 'contain', isArbitrarySize] }],\n            /**\n             * Background Image\n             * @see https://tailwindcss.com/docs/background-image\n             */\n            'bg-image': [\n                {\n                    bg: [\n                        'none',\n                        { 'gradient-to': ['t', 'tr', 'r', 'br', 'b', 'bl', 'l', 'tl'] },\n                        isArbitraryImage,\n                    ],\n                },\n            ],\n            /**\n             * Background Color\n             * @see https://tailwindcss.com/docs/background-color\n             */\n            'bg-color': [{ bg: [colors] }],\n            /**\n             * Gradient Color Stops From Position\n             * @see https://tailwindcss.com/docs/gradient-color-stops\n             */\n            'gradient-from-pos': [{ from: [gradientColorStopPositions] }],\n            /**\n             * Gradient Color Stops Via Position\n             * @see https://tailwindcss.com/docs/gradient-color-stops\n             */\n            'gradient-via-pos': [{ via: [gradientColorStopPositions] }],\n            /**\n             * Gradient Color Stops To Position\n             * @see https://tailwindcss.com/docs/gradient-color-stops\n             */\n            'gradient-to-pos': [{ to: [gradientColorStopPositions] }],\n            /**\n             * Gradient Color Stops From\n             * @see https://tailwindcss.com/docs/gradient-color-stops\n             */\n            'gradient-from': [{ from: [gradientColorStops] }],\n            /**\n             * Gradient Color Stops Via\n             * @see https://tailwindcss.com/docs/gradient-color-stops\n             */\n            'gradient-via': [{ via: [gradientColorStops] }],\n            /**\n             * Gradient Color Stops To\n             * @see https://tailwindcss.com/docs/gradient-color-stops\n             */\n            'gradient-to': [{ to: [gradientColorStops] }],\n            // Borders\n            /**\n             * Border Radius\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            rounded: [{ rounded: [borderRadius] }],\n            /**\n             * Border Radius Start\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-s': [{ 'rounded-s': [borderRadius] }],\n            /**\n             * Border Radius End\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-e': [{ 'rounded-e': [borderRadius] }],\n            /**\n             * Border Radius Top\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-t': [{ 'rounded-t': [borderRadius] }],\n            /**\n             * Border Radius Right\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-r': [{ 'rounded-r': [borderRadius] }],\n            /**\n             * Border Radius Bottom\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-b': [{ 'rounded-b': [borderRadius] }],\n            /**\n             * Border Radius Left\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-l': [{ 'rounded-l': [borderRadius] }],\n            /**\n             * Border Radius Start Start\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-ss': [{ 'rounded-ss': [borderRadius] }],\n            /**\n             * Border Radius Start End\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-se': [{ 'rounded-se': [borderRadius] }],\n            /**\n             * Border Radius End End\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-ee': [{ 'rounded-ee': [borderRadius] }],\n            /**\n             * Border Radius End Start\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-es': [{ 'rounded-es': [borderRadius] }],\n            /**\n             * Border Radius Top Left\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-tl': [{ 'rounded-tl': [borderRadius] }],\n            /**\n             * Border Radius Top Right\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-tr': [{ 'rounded-tr': [borderRadius] }],\n            /**\n             * Border Radius Bottom Right\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-br': [{ 'rounded-br': [borderRadius] }],\n            /**\n             * Border Radius Bottom Left\n             * @see https://tailwindcss.com/docs/border-radius\n             */\n            'rounded-bl': [{ 'rounded-bl': [borderRadius] }],\n            /**\n             * Border Width\n             * @see https://tailwindcss.com/docs/border-width\n             */\n            'border-w': [{ border: [borderWidth] }],\n            /**\n             * Border Width X\n             * @see https://tailwindcss.com/docs/border-width\n             */\n            'border-w-x': [{ 'border-x': [borderWidth] }],\n            /**\n             * Border Width Y\n             * @see https://tailwindcss.com/docs/border-width\n             */\n            'border-w-y': [{ 'border-y': [borderWidth] }],\n            /**\n             * Border Width Start\n             * @see https://tailwindcss.com/docs/border-width\n             */\n            'border-w-s': [{ 'border-s': [borderWidth] }],\n            /**\n             * Border Width End\n             * @see https://tailwindcss.com/docs/border-width\n             */\n            'border-w-e': [{ 'border-e': [borderWidth] }],\n            /**\n             * Border Width Top\n             * @see https://tailwindcss.com/docs/border-width\n             */\n            'border-w-t': [{ 'border-t': [borderWidth] }],\n            /**\n             * Border Width Right\n             * @see https://tailwindcss.com/docs/border-width\n             */\n            'border-w-r': [{ 'border-r': [borderWidth] }],\n            /**\n             * Border Width Bottom\n             * @see https://tailwindcss.com/docs/border-width\n             */\n            'border-w-b': [{ 'border-b': [borderWidth] }],\n            /**\n             * Border Width Left\n             * @see https://tailwindcss.com/docs/border-width\n             */\n            'border-w-l': [{ 'border-l': [borderWidth] }],\n            /**\n             * Border Opacity\n             * @see https://tailwindcss.com/docs/border-opacity\n             */\n            'border-opacity': [{ 'border-opacity': [opacity] }],\n            /**\n             * Border Style\n             * @see https://tailwindcss.com/docs/border-style\n             */\n            'border-style': [{ border: [...getLineStyles(), 'hidden'] }],\n            /**\n             * Divide Width X\n             * @see https://tailwindcss.com/docs/divide-width\n             */\n            'divide-x': [{ 'divide-x': [borderWidth] }],\n            /**\n             * Divide Width X Reverse\n             * @see https://tailwindcss.com/docs/divide-width\n             */\n            'divide-x-reverse': ['divide-x-reverse'],\n            /**\n             * Divide Width Y\n             * @see https://tailwindcss.com/docs/divide-width\n             */\n            'divide-y': [{ 'divide-y': [borderWidth] }],\n            /**\n             * Divide Width Y Reverse\n             * @see https://tailwindcss.com/docs/divide-width\n             */\n            'divide-y-reverse': ['divide-y-reverse'],\n            /**\n             * Divide Opacity\n             * @see https://tailwindcss.com/docs/divide-opacity\n             */\n            'divide-opacity': [{ 'divide-opacity': [opacity] }],\n            /**\n             * Divide Style\n             * @see https://tailwindcss.com/docs/divide-style\n             */\n            'divide-style': [{ divide: getLineStyles() }],\n            /**\n             * Border Color\n             * @see https://tailwindcss.com/docs/border-color\n             */\n            'border-color': [{ border: [borderColor] }],\n            /**\n             * Border Color X\n             * @see https://tailwindcss.com/docs/border-color\n             */\n            'border-color-x': [{ 'border-x': [borderColor] }],\n            /**\n             * Border Color Y\n             * @see https://tailwindcss.com/docs/border-color\n             */\n            'border-color-y': [{ 'border-y': [borderColor] }],\n            /**\n             * Border Color S\n             * @see https://tailwindcss.com/docs/border-color\n             */\n            'border-color-s': [{ 'border-s': [borderColor] }],\n            /**\n             * Border Color E\n             * @see https://tailwindcss.com/docs/border-color\n             */\n            'border-color-e': [{ 'border-e': [borderColor] }],\n            /**\n             * Border Color Top\n             * @see https://tailwindcss.com/docs/border-color\n             */\n            'border-color-t': [{ 'border-t': [borderColor] }],\n            /**\n             * Border Color Right\n             * @see https://tailwindcss.com/docs/border-color\n             */\n            'border-color-r': [{ 'border-r': [borderColor] }],\n            /**\n             * Border Color Bottom\n             * @see https://tailwindcss.com/docs/border-color\n             */\n            'border-color-b': [{ 'border-b': [borderColor] }],\n            /**\n             * Border Color Left\n             * @see https://tailwindcss.com/docs/border-color\n             */\n            'border-color-l': [{ 'border-l': [borderColor] }],\n            /**\n             * Divide Color\n             * @see https://tailwindcss.com/docs/divide-color\n             */\n            'divide-color': [{ divide: [borderColor] }],\n            /**\n             * Outline Style\n             * @see https://tailwindcss.com/docs/outline-style\n             */\n            'outline-style': [{ outline: ['', ...getLineStyles()] }],\n            /**\n             * Outline Offset\n             * @see https://tailwindcss.com/docs/outline-offset\n             */\n            'outline-offset': [{ 'outline-offset': [isLength, isArbitraryValue] }],\n            /**\n             * Outline Width\n             * @see https://tailwindcss.com/docs/outline-width\n             */\n            'outline-w': [{ outline: [isLength, isArbitraryLength] }],\n            /**\n             * Outline Color\n             * @see https://tailwindcss.com/docs/outline-color\n             */\n            'outline-color': [{ outline: [colors] }],\n            /**\n             * Ring Width\n             * @see https://tailwindcss.com/docs/ring-width\n             */\n            'ring-w': [{ ring: getLengthWithEmptyAndArbitrary() }],\n            /**\n             * Ring Width Inset\n             * @see https://tailwindcss.com/docs/ring-width\n             */\n            'ring-w-inset': ['ring-inset'],\n            /**\n             * Ring Color\n             * @see https://tailwindcss.com/docs/ring-color\n             */\n            'ring-color': [{ ring: [colors] }],\n            /**\n             * Ring Opacity\n             * @see https://tailwindcss.com/docs/ring-opacity\n             */\n            'ring-opacity': [{ 'ring-opacity': [opacity] }],\n            /**\n             * Ring Offset Width\n             * @see https://tailwindcss.com/docs/ring-offset-width\n             */\n            'ring-offset-w': [{ 'ring-offset': [isLength, isArbitraryLength] }],\n            /**\n             * Ring Offset Color\n             * @see https://tailwindcss.com/docs/ring-offset-color\n             */\n            'ring-offset-color': [{ 'ring-offset': [colors] }],\n            // Effects\n            /**\n             * Box Shadow\n             * @see https://tailwindcss.com/docs/box-shadow\n             */\n            shadow: [{ shadow: ['', 'inner', 'none', isTshirtSize, isArbitraryShadow] }],\n            /**\n             * Box Shadow Color\n             * @see https://tailwindcss.com/docs/box-shadow-color\n             */\n            'shadow-color': [{ shadow: [isAny] }],\n            /**\n             * Opacity\n             * @see https://tailwindcss.com/docs/opacity\n             */\n            opacity: [{ opacity: [opacity] }],\n            /**\n             * Mix Blend Mode\n             * @see https://tailwindcss.com/docs/mix-blend-mode\n             */\n            'mix-blend': [{ 'mix-blend': [...getBlendModes(), 'plus-lighter', 'plus-darker'] }],\n            /**\n             * Background Blend Mode\n             * @see https://tailwindcss.com/docs/background-blend-mode\n             */\n            'bg-blend': [{ 'bg-blend': getBlendModes() }],\n            // Filters\n            /**\n             * Filter\n             * @deprecated since Tailwind CSS v3.0.0\n             * @see https://tailwindcss.com/docs/filter\n             */\n            filter: [{ filter: ['', 'none'] }],\n            /**\n             * Blur\n             * @see https://tailwindcss.com/docs/blur\n             */\n            blur: [{ blur: [blur] }],\n            /**\n             * Brightness\n             * @see https://tailwindcss.com/docs/brightness\n             */\n            brightness: [{ brightness: [brightness] }],\n            /**\n             * Contrast\n             * @see https://tailwindcss.com/docs/contrast\n             */\n            contrast: [{ contrast: [contrast] }],\n            /**\n             * Drop Shadow\n             * @see https://tailwindcss.com/docs/drop-shadow\n             */\n            'drop-shadow': [{ 'drop-shadow': ['', 'none', isTshirtSize, isArbitraryValue] }],\n            /**\n             * Grayscale\n             * @see https://tailwindcss.com/docs/grayscale\n             */\n            grayscale: [{ grayscale: [grayscale] }],\n            /**\n             * Hue Rotate\n             * @see https://tailwindcss.com/docs/hue-rotate\n             */\n            'hue-rotate': [{ 'hue-rotate': [hueRotate] }],\n            /**\n             * Invert\n             * @see https://tailwindcss.com/docs/invert\n             */\n            invert: [{ invert: [invert] }],\n            /**\n             * Saturate\n             * @see https://tailwindcss.com/docs/saturate\n             */\n            saturate: [{ saturate: [saturate] }],\n            /**\n             * Sepia\n             * @see https://tailwindcss.com/docs/sepia\n             */\n            sepia: [{ sepia: [sepia] }],\n            /**\n             * Backdrop Filter\n             * @deprecated since Tailwind CSS v3.0.0\n             * @see https://tailwindcss.com/docs/backdrop-filter\n             */\n            'backdrop-filter': [{ 'backdrop-filter': ['', 'none'] }],\n            /**\n             * Backdrop Blur\n             * @see https://tailwindcss.com/docs/backdrop-blur\n             */\n            'backdrop-blur': [{ 'backdrop-blur': [blur] }],\n            /**\n             * Backdrop Brightness\n             * @see https://tailwindcss.com/docs/backdrop-brightness\n             */\n            'backdrop-brightness': [{ 'backdrop-brightness': [brightness] }],\n            /**\n             * Backdrop Contrast\n             * @see https://tailwindcss.com/docs/backdrop-contrast\n             */\n            'backdrop-contrast': [{ 'backdrop-contrast': [contrast] }],\n            /**\n             * Backdrop Grayscale\n             * @see https://tailwindcss.com/docs/backdrop-grayscale\n             */\n            'backdrop-grayscale': [{ 'backdrop-grayscale': [grayscale] }],\n            /**\n             * Backdrop Hue Rotate\n             * @see https://tailwindcss.com/docs/backdrop-hue-rotate\n             */\n            'backdrop-hue-rotate': [{ 'backdrop-hue-rotate': [hueRotate] }],\n            /**\n             * Backdrop Invert\n             * @see https://tailwindcss.com/docs/backdrop-invert\n             */\n            'backdrop-invert': [{ 'backdrop-invert': [invert] }],\n            /**\n             * Backdrop Opacity\n             * @see https://tailwindcss.com/docs/backdrop-opacity\n             */\n            'backdrop-opacity': [{ 'backdrop-opacity': [opacity] }],\n            /**\n             * Backdrop Saturate\n             * @see https://tailwindcss.com/docs/backdrop-saturate\n             */\n            'backdrop-saturate': [{ 'backdrop-saturate': [saturate] }],\n            /**\n             * Backdrop Sepia\n             * @see https://tailwindcss.com/docs/backdrop-sepia\n             */\n            'backdrop-sepia': [{ 'backdrop-sepia': [sepia] }],\n            // Tables\n            /**\n             * Border Collapse\n             * @see https://tailwindcss.com/docs/border-collapse\n             */\n            'border-collapse': [{ border: ['collapse', 'separate'] }],\n            /**\n             * Border Spacing\n             * @see https://tailwindcss.com/docs/border-spacing\n             */\n            'border-spacing': [{ 'border-spacing': [borderSpacing] }],\n            /**\n             * Border Spacing X\n             * @see https://tailwindcss.com/docs/border-spacing\n             */\n            'border-spacing-x': [{ 'border-spacing-x': [borderSpacing] }],\n            /**\n             * Border Spacing Y\n             * @see https://tailwindcss.com/docs/border-spacing\n             */\n            'border-spacing-y': [{ 'border-spacing-y': [borderSpacing] }],\n            /**\n             * Table Layout\n             * @see https://tailwindcss.com/docs/table-layout\n             */\n            'table-layout': [{ table: ['auto', 'fixed'] }],\n            /**\n             * Caption Side\n             * @see https://tailwindcss.com/docs/caption-side\n             */\n            caption: [{ caption: ['top', 'bottom'] }],\n            // Transitions and Animation\n            /**\n             * Tranisition Property\n             * @see https://tailwindcss.com/docs/transition-property\n             */\n            transition: [\n                {\n                    transition: [\n                        'none',\n                        'all',\n                        '',\n                        'colors',\n                        'opacity',\n                        'shadow',\n                        'transform',\n                        isArbitraryValue,\n                    ],\n                },\n            ],\n            /**\n             * Transition Duration\n             * @see https://tailwindcss.com/docs/transition-duration\n             */\n            duration: [{ duration: getNumberAndArbitrary() }],\n            /**\n             * Transition Timing Function\n             * @see https://tailwindcss.com/docs/transition-timing-function\n             */\n            ease: [{ ease: ['linear', 'in', 'out', 'in-out', isArbitraryValue] }],\n            /**\n             * Transition Delay\n             * @see https://tailwindcss.com/docs/transition-delay\n             */\n            delay: [{ delay: getNumberAndArbitrary() }],\n            /**\n             * Animation\n             * @see https://tailwindcss.com/docs/animation\n             */\n            animate: [{ animate: ['none', 'spin', 'ping', 'pulse', 'bounce', isArbitraryValue] }],\n            // Transforms\n            /**\n             * Transform\n             * @see https://tailwindcss.com/docs/transform\n             */\n            transform: [{ transform: ['', 'gpu', 'none'] }],\n            /**\n             * Scale\n             * @see https://tailwindcss.com/docs/scale\n             */\n            scale: [{ scale: [scale] }],\n            /**\n             * Scale X\n             * @see https://tailwindcss.com/docs/scale\n             */\n            'scale-x': [{ 'scale-x': [scale] }],\n            /**\n             * Scale Y\n             * @see https://tailwindcss.com/docs/scale\n             */\n            'scale-y': [{ 'scale-y': [scale] }],\n            /**\n             * Rotate\n             * @see https://tailwindcss.com/docs/rotate\n             */\n            rotate: [{ rotate: [isInteger, isArbitraryValue] }],\n            /**\n             * Translate X\n             * @see https://tailwindcss.com/docs/translate\n             */\n            'translate-x': [{ 'translate-x': [translate] }],\n            /**\n             * Translate Y\n             * @see https://tailwindcss.com/docs/translate\n             */\n            'translate-y': [{ 'translate-y': [translate] }],\n            /**\n             * Skew X\n             * @see https://tailwindcss.com/docs/skew\n             */\n            'skew-x': [{ 'skew-x': [skew] }],\n            /**\n             * Skew Y\n             * @see https://tailwindcss.com/docs/skew\n             */\n            'skew-y': [{ 'skew-y': [skew] }],\n            /**\n             * Transform Origin\n             * @see https://tailwindcss.com/docs/transform-origin\n             */\n            'transform-origin': [\n                {\n                    origin: [\n                        'center',\n                        'top',\n                        'top-right',\n                        'right',\n                        'bottom-right',\n                        'bottom',\n                        'bottom-left',\n                        'left',\n                        'top-left',\n                        isArbitraryValue,\n                    ],\n                },\n            ],\n            // Interactivity\n            /**\n             * Accent Color\n             * @see https://tailwindcss.com/docs/accent-color\n             */\n            accent: [{ accent: ['auto', colors] }],\n            /**\n             * Appearance\n             * @see https://tailwindcss.com/docs/appearance\n             */\n            appearance: [{ appearance: ['none', 'auto'] }],\n            /**\n             * Cursor\n             * @see https://tailwindcss.com/docs/cursor\n             */\n            cursor: [\n                {\n                    cursor: [\n                        'auto',\n                        'default',\n                        'pointer',\n                        'wait',\n                        'text',\n                        'move',\n                        'help',\n                        'not-allowed',\n                        'none',\n                        'context-menu',\n                        'progress',\n                        'cell',\n                        'crosshair',\n                        'vertical-text',\n                        'alias',\n                        'copy',\n                        'no-drop',\n                        'grab',\n                        'grabbing',\n                        'all-scroll',\n                        'col-resize',\n                        'row-resize',\n                        'n-resize',\n                        'e-resize',\n                        's-resize',\n                        'w-resize',\n                        'ne-resize',\n                        'nw-resize',\n                        'se-resize',\n                        'sw-resize',\n                        'ew-resize',\n                        'ns-resize',\n                        'nesw-resize',\n                        'nwse-resize',\n                        'zoom-in',\n                        'zoom-out',\n                        isArbitraryValue,\n                    ],\n                },\n            ],\n            /**\n             * Caret Color\n             * @see https://tailwindcss.com/docs/just-in-time-mode#caret-color-utilities\n             */\n            'caret-color': [{ caret: [colors] }],\n            /**\n             * Pointer Events\n             * @see https://tailwindcss.com/docs/pointer-events\n             */\n            'pointer-events': [{ 'pointer-events': ['none', 'auto'] }],\n            /**\n             * Resize\n             * @see https://tailwindcss.com/docs/resize\n             */\n            resize: [{ resize: ['none', 'y', 'x', ''] }],\n            /**\n             * Scroll Behavior\n             * @see https://tailwindcss.com/docs/scroll-behavior\n             */\n            'scroll-behavior': [{ scroll: ['auto', 'smooth'] }],\n            /**\n             * Scroll Margin\n             * @see https://tailwindcss.com/docs/scroll-margin\n             */\n            'scroll-m': [{ 'scroll-m': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Margin X\n             * @see https://tailwindcss.com/docs/scroll-margin\n             */\n            'scroll-mx': [{ 'scroll-mx': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Margin Y\n             * @see https://tailwindcss.com/docs/scroll-margin\n             */\n            'scroll-my': [{ 'scroll-my': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Margin Start\n             * @see https://tailwindcss.com/docs/scroll-margin\n             */\n            'scroll-ms': [{ 'scroll-ms': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Margin End\n             * @see https://tailwindcss.com/docs/scroll-margin\n             */\n            'scroll-me': [{ 'scroll-me': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Margin Top\n             * @see https://tailwindcss.com/docs/scroll-margin\n             */\n            'scroll-mt': [{ 'scroll-mt': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Margin Right\n             * @see https://tailwindcss.com/docs/scroll-margin\n             */\n            'scroll-mr': [{ 'scroll-mr': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Margin Bottom\n             * @see https://tailwindcss.com/docs/scroll-margin\n             */\n            'scroll-mb': [{ 'scroll-mb': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Margin Left\n             * @see https://tailwindcss.com/docs/scroll-margin\n             */\n            'scroll-ml': [{ 'scroll-ml': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Padding\n             * @see https://tailwindcss.com/docs/scroll-padding\n             */\n            'scroll-p': [{ 'scroll-p': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Padding X\n             * @see https://tailwindcss.com/docs/scroll-padding\n             */\n            'scroll-px': [{ 'scroll-px': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Padding Y\n             * @see https://tailwindcss.com/docs/scroll-padding\n             */\n            'scroll-py': [{ 'scroll-py': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Padding Start\n             * @see https://tailwindcss.com/docs/scroll-padding\n             */\n            'scroll-ps': [{ 'scroll-ps': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Padding End\n             * @see https://tailwindcss.com/docs/scroll-padding\n             */\n            'scroll-pe': [{ 'scroll-pe': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Padding Top\n             * @see https://tailwindcss.com/docs/scroll-padding\n             */\n            'scroll-pt': [{ 'scroll-pt': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Padding Right\n             * @see https://tailwindcss.com/docs/scroll-padding\n             */\n            'scroll-pr': [{ 'scroll-pr': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Padding Bottom\n             * @see https://tailwindcss.com/docs/scroll-padding\n             */\n            'scroll-pb': [{ 'scroll-pb': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Padding Left\n             * @see https://tailwindcss.com/docs/scroll-padding\n             */\n            'scroll-pl': [{ 'scroll-pl': getSpacingWithArbitrary() }],\n            /**\n             * Scroll Snap Align\n             * @see https://tailwindcss.com/docs/scroll-snap-align\n             */\n            'snap-align': [{ snap: ['start', 'end', 'center', 'align-none'] }],\n            /**\n             * Scroll Snap Stop\n             * @see https://tailwindcss.com/docs/scroll-snap-stop\n             */\n            'snap-stop': [{ snap: ['normal', 'always'] }],\n            /**\n             * Scroll Snap Type\n             * @see https://tailwindcss.com/docs/scroll-snap-type\n             */\n            'snap-type': [{ snap: ['none', 'x', 'y', 'both'] }],\n            /**\n             * Scroll Snap Type Strictness\n             * @see https://tailwindcss.com/docs/scroll-snap-type\n             */\n            'snap-strictness': [{ snap: ['mandatory', 'proximity'] }],\n            /**\n             * Touch Action\n             * @see https://tailwindcss.com/docs/touch-action\n             */\n            touch: [\n                {\n                    touch: ['auto', 'none', 'manipulation'],\n                },\n            ],\n            /**\n             * Touch Action X\n             * @see https://tailwindcss.com/docs/touch-action\n             */\n            'touch-x': [\n                {\n                    'touch-pan': ['x', 'left', 'right'],\n                },\n            ],\n            /**\n             * Touch Action Y\n             * @see https://tailwindcss.com/docs/touch-action\n             */\n            'touch-y': [\n                {\n                    'touch-pan': ['y', 'up', 'down'],\n                },\n            ],\n            /**\n             * Touch Action Pinch Zoom\n             * @see https://tailwindcss.com/docs/touch-action\n             */\n            'touch-pz': ['touch-pinch-zoom'],\n            /**\n             * User Select\n             * @see https://tailwindcss.com/docs/user-select\n             */\n            select: [{ select: ['none', 'text', 'all', 'auto'] }],\n            /**\n             * Will Change\n             * @see https://tailwindcss.com/docs/will-change\n             */\n            'will-change': [\n                { 'will-change': ['auto', 'scroll', 'contents', 'transform', isArbitraryValue] },\n            ],\n            // SVG\n            /**\n             * Fill\n             * @see https://tailwindcss.com/docs/fill\n             */\n            fill: [{ fill: [colors, 'none'] }],\n            /**\n             * Stroke Width\n             * @see https://tailwindcss.com/docs/stroke-width\n             */\n            'stroke-w': [{ stroke: [isLength, isArbitraryLength, isArbitraryNumber] }],\n            /**\n             * Stroke\n             * @see https://tailwindcss.com/docs/stroke\n             */\n            stroke: [{ stroke: [colors, 'none'] }],\n            // Accessibility\n            /**\n             * Screen Readers\n             * @see https://tailwindcss.com/docs/screen-readers\n             */\n            sr: ['sr-only', 'not-sr-only'],\n            /**\n             * Forced Color Adjust\n             * @see https://tailwindcss.com/docs/forced-color-adjust\n             */\n            'forced-color-adjust': [{ 'forced-color-adjust': ['auto', 'none'] }],\n        },\n        conflictingClassGroups: {\n            overflow: ['overflow-x', 'overflow-y'],\n            overscroll: ['overscroll-x', 'overscroll-y'],\n            inset: ['inset-x', 'inset-y', 'start', 'end', 'top', 'right', 'bottom', 'left'],\n            'inset-x': ['right', 'left'],\n            'inset-y': ['top', 'bottom'],\n            flex: ['basis', 'grow', 'shrink'],\n            gap: ['gap-x', 'gap-y'],\n            p: ['px', 'py', 'ps', 'pe', 'pt', 'pr', 'pb', 'pl'],\n            px: ['pr', 'pl'],\n            py: ['pt', 'pb'],\n            m: ['mx', 'my', 'ms', 'me', 'mt', 'mr', 'mb', 'ml'],\n            mx: ['mr', 'ml'],\n            my: ['mt', 'mb'],\n            size: ['w', 'h'],\n            'font-size': ['leading'],\n            'fvn-normal': [\n                'fvn-ordinal',\n                'fvn-slashed-zero',\n                'fvn-figure',\n                'fvn-spacing',\n                'fvn-fraction',\n            ],\n            'fvn-ordinal': ['fvn-normal'],\n            'fvn-slashed-zero': ['fvn-normal'],\n            'fvn-figure': ['fvn-normal'],\n            'fvn-spacing': ['fvn-normal'],\n            'fvn-fraction': ['fvn-normal'],\n            'line-clamp': ['display', 'overflow'],\n            rounded: [\n                'rounded-s',\n                'rounded-e',\n                'rounded-t',\n                'rounded-r',\n                'rounded-b',\n                'rounded-l',\n                'rounded-ss',\n                'rounded-se',\n                'rounded-ee',\n                'rounded-es',\n                'rounded-tl',\n                'rounded-tr',\n                'rounded-br',\n                'rounded-bl',\n            ],\n            'rounded-s': ['rounded-ss', 'rounded-es'],\n            'rounded-e': ['rounded-se', 'rounded-ee'],\n            'rounded-t': ['rounded-tl', 'rounded-tr'],\n            'rounded-r': ['rounded-tr', 'rounded-br'],\n            'rounded-b': ['rounded-br', 'rounded-bl'],\n            'rounded-l': ['rounded-tl', 'rounded-bl'],\n            'border-spacing': ['border-spacing-x', 'border-spacing-y'],\n            'border-w': [\n                'border-w-s',\n                'border-w-e',\n                'border-w-t',\n                'border-w-r',\n                'border-w-b',\n                'border-w-l',\n            ],\n            'border-w-x': ['border-w-r', 'border-w-l'],\n            'border-w-y': ['border-w-t', 'border-w-b'],\n            'border-color': [\n                'border-color-s',\n                'border-color-e',\n                'border-color-t',\n                'border-color-r',\n                'border-color-b',\n                'border-color-l',\n            ],\n            'border-color-x': ['border-color-r', 'border-color-l'],\n            'border-color-y': ['border-color-t', 'border-color-b'],\n            'scroll-m': [\n                'scroll-mx',\n                'scroll-my',\n                'scroll-ms',\n                'scroll-me',\n                'scroll-mt',\n                'scroll-mr',\n                'scroll-mb',\n                'scroll-ml',\n            ],\n            'scroll-mx': ['scroll-mr', 'scroll-ml'],\n            'scroll-my': ['scroll-mt', 'scroll-mb'],\n            'scroll-p': [\n                'scroll-px',\n                'scroll-py',\n                'scroll-ps',\n                'scroll-pe',\n                'scroll-pt',\n                'scroll-pr',\n                'scroll-pb',\n                'scroll-pl',\n            ],\n            'scroll-px': ['scroll-pr', 'scroll-pl'],\n            'scroll-py': ['scroll-pt', 'scroll-pb'],\n            touch: ['touch-x', 'touch-y', 'touch-pz'],\n            'touch-x': ['touch'],\n            'touch-y': ['touch'],\n            'touch-pz': ['touch'],\n        },\n        conflictingClassGroupModifiers: {\n            'font-size': ['leading'],\n        },\n    } as const satisfies Config<DefaultClassGroupIds, DefaultThemeGroupIds>\n}\n", "import { AnyConfig, ConfigExtension } from './types'\n\n/**\n * @param baseConfig Config where other config will be merged into. This object will be mutated.\n * @param configExtension Partial config to merge into the `baseConfig`.\n */\nexport const mergeConfigs = <ClassGroupIds extends string, ThemeGroupIds extends string = never>(\n    baseConfig: AnyConfig,\n    {\n        cacheSize,\n        prefix,\n        separator,\n        experimentalParseClassName,\n        extend = {},\n        override = {},\n    }: ConfigExtension<ClassGroupIds, ThemeGroupIds>,\n) => {\n    overrideProperty(baseConfig, 'cacheSize', cacheSize)\n    overrideProperty(baseConfig, 'prefix', prefix)\n    overrideProperty(baseConfig, 'separator', separator)\n    overrideProperty(baseConfig, 'experimentalParseClassName', experimentalParseClassName)\n\n    for (const configKey in override) {\n        overrideConfigProperties(\n            baseConfig[configKey as keyof typeof override],\n            override[configKey as keyof typeof override],\n        )\n    }\n\n    for (const key in extend) {\n        mergeConfigProperties(\n            baseConfig[key as keyof typeof extend],\n            extend[key as keyof typeof extend],\n        )\n    }\n\n    return baseConfig\n}\n\nconst overrideProperty = <T extends object, K extends keyof T>(\n    baseObject: T,\n    overrideKey: K,\n    overrideValue: T[K] | undefined,\n) => {\n    if (overrideValue !== undefined) {\n        baseObject[overrideKey] = overrideValue\n    }\n}\n\nconst overrideConfigProperties = (\n    baseObject: Partial<Record<string, readonly unknown[]>>,\n    overrideObject: Partial<Record<string, readonly unknown[]>> | undefined,\n) => {\n    if (overrideObject) {\n        for (const key in overrideObject) {\n            overrideProperty(baseObject, key, overrideObject[key])\n        }\n    }\n}\n\nconst mergeConfigProperties = (\n    baseObject: Partial<Record<string, readonly unknown[]>>,\n    mergeObject: Partial<Record<string, readonly unknown[]>> | undefined,\n) => {\n    if (mergeObject) {\n        for (const key in mergeObject) {\n            const mergeValue = mergeObject[key]\n\n            if (mergeValue !== undefined) {\n                baseObject[key] = (baseObject[key] || []).concat(mergeValue)\n            }\n        }\n    }\n}\n", "import { createTailwindMerge } from './create-tailwind-merge'\nimport { getDefaultConfig } from './default-config'\nimport { mergeConfigs } from './merge-configs'\nimport { AnyConfig, ConfigExtension, DefaultClassGroupIds, DefaultThemeGroupIds } from './types'\n\ntype CreateConfigSubsequent = (config: AnyConfig) => AnyConfig\n\nexport const extendTailwindMerge = <\n    AdditionalClassGroupIds extends string = never,\n    AdditionalThemeGroupIds extends string = never,\n>(\n    configExtension:\n        | ConfigExtension<\n              DefaultClassGroupIds | AdditionalClassGroupIds,\n              DefaultThemeGroupIds | AdditionalThemeGroupIds\n          >\n        | CreateConfigSubsequent,\n    ...createConfig: CreateConfigSubsequent[]\n) =>\n    typeof configExtension === 'function'\n        ? createTailwindMerge(getDefaultConfig, configExtension, ...createConfig)\n        : createTailwindMerge(\n              () => mergeConfigs(getDefaultConfig(), configExtension),\n              ...createConfig,\n          )\n", "import { createTailwindMerge } from './create-tailwind-merge'\nimport { getDefaultConfig } from './default-config'\n\nexport const twMerge = createTailwindMerge(getDefaultConfig)\n"], "names": ["CLASS_PART_SEPARATOR", "createClassGroupUtils", "config", "classMap", "createClassMap", "conflictingClassGroups", "conflictingClassGroupModifiers", "getClassGroupId", "className", "classParts", "split", "length", "shift", "getGroupRecursive", "getGroupIdForArbitraryProperty", "getConflictingClassGroupIds", "classGroupId", "hasPostfixModifier", "conflicts", "classPartObject", "currentClassPart", "nextClassPartObject", "nextPart", "get", "classGroupFromNextClassPart", "slice", "undefined", "validators", "classRest", "join", "find", "validator", "arbitraryPropertyRegex", "test", "arbitraryPropertyClassName", "exec", "property", "substring", "indexOf", "theme", "prefix", "Map", "prefixedClassGroupEntries", "getPrefixedClassGroupEntries", "Object", "entries", "classGroups", "for<PERSON>ach", "classGroup", "processClassesRecursively", "classDefinition", "classPartObjectToEdit", "get<PERSON>art", "isThemeGetter", "push", "key", "path", "currentClassPartObject", "pathPart", "has", "set", "func", "classGroupEntries", "map", "prefixedClassGroup", "fromEntries", "value", "createLruCache", "maxCacheSize", "cacheSize", "cache", "previousCache", "update", "IMPORTANT_MODIFIER", "createParseClassName", "separator", "experimentalParseClassName", "isSeparatorSingleCharacter", "firstSeparatorCharacter", "separator<PERSON><PERSON><PERSON>", "parseClassName", "modifiers", "<PERSON><PERSON><PERSON><PERSON>", "modifierStart", "postfixModifierPosition", "index", "currentCharacter", "baseClassNameWithImportantModifier", "hasImportantModifier", "startsWith", "baseClassName", "maybePostfixModifierPosition", "sortModifiers", "sortedModifiers", "unsortedModifiers", "modifier", "isArbitraryVariant", "sort", "createConfigUtils", "SPLIT_CLASSES_REGEX", "mergeClassList", "classList", "configUtils", "classGroupsInConflict", "classNames", "trim", "result", "originalClassName", "Boolean", "variantModifier", "modifierId", "classId", "includes", "conflictGroups", "i", "group", "twJoin", "argument", "resolvedValue", "string", "arguments", "toValue", "mix", "k", "createTailwindMerge", "createConfigFirst", "createConfigRest", "cacheGet", "cacheSet", "functionToCall", "initTailwindMerge", "reduce", "previousConfig", "createConfigCurrent", "tailwindMerge", "cachedResult", "callTailwindMerge", "apply", "fromTheme", "themeGetter", "arbitraryValueRegex", "fractionRegex", "stringLengths", "Set", "tshirtUnitRegex", "lengthUnitRegex", "colorFunctionRegex", "shadowRegex", "imageRegex", "<PERSON><PERSON><PERSON><PERSON>", "isNumber", "isArbitraryLength", "getIsArbitraryValue", "is<PERSON>engthOnly", "Number", "isNaN", "isArbitraryNumber", "isInteger", "isPercent", "endsWith", "isArbitraryValue", "isTshirtSize", "sizeLabels", "isArbitrarySize", "isNever", "isArbitraryPosition", "imageLabels", "isArbitraryImage", "isImage", "isArbitraryShadow", "is<PERSON><PERSON>ow", "isAny", "label", "testValue", "getDefaultConfig", "colors", "spacing", "blur", "brightness", "borderColor", "borderRadius", "borderSpacing", "borderWidth", "contrast", "grayscale", "hueRotate", "invert", "gap", "gradientColorStops", "gradientColorStopPositions", "inset", "margin", "opacity", "padding", "saturate", "scale", "sepia", "skew", "space", "translate", "getOverscroll", "getOverflow", "getSpacingWithAutoAndArbitrary", "getSpacingWithArbitrary", "getLengthWithEmptyAndArbitrary", "getNumberWithAutoAndArbitrary", "getPositions", "getLineStyles", "getBlendModes", "getAlign", "getZeroAndEmpty", "getBreaks", "getNumberAndArbitrary", "aspect", "container", "columns", "box", "display", "float", "clear", "isolation", "object", "overflow", "overscroll", "position", "start", "end", "top", "right", "bottom", "left", "visibility", "z", "basis", "flex", "grow", "shrink", "order", "col", "span", "row", "justify", "content", "items", "self", "p", "px", "py", "ps", "pe", "pt", "pr", "pb", "pl", "m", "mx", "my", "ms", "me", "mt", "mr", "mb", "ml", "w", "screen", "h", "size", "text", "font", "tracking", "leading", "list", "placeholder", "decoration", "indent", "align", "whitespace", "break", "hyphens", "bg", "repeat", "from", "via", "to", "rounded", "border", "divide", "outline", "ring", "shadow", "filter", "table", "caption", "transition", "duration", "ease", "delay", "animate", "transform", "rotate", "origin", "accent", "appearance", "cursor", "caret", "resize", "scroll", "snap", "touch", "select", "fill", "stroke", "sr", "mergeConfigs", "baseConfig", "extend", "override", "overrideProperty", "config<PERSON><PERSON>", "overrideConfigProperties", "mergeConfigProperties", "baseObject", "override<PERSON><PERSON>", "overrideValue", "overrideObject", "mergeObject", "mergeValue", "concat", "extendTailwindMerge", "configExtension", "createConfig", "twMerge"], "mappings": ";;;;;;;;;;AAsBA,MAAMA,oBAAoB,GAAG,GAAG;AAEzB,MAAMC,qBAAqB,IAAIC,MAAiB,IAAI;IACvD,MAAMC,QAAQ,GAAGC,cAAc,CAACF,MAAM,CAAC;IACvC,MAAM,EAAEG,sBAAsB,EAAEC,8BAAAA,EAAgC,GAAGJ,MAAM;IAEzE,MAAMK,eAAe,IAAIC,SAAiB,IAAI;QAC1C,MAAMC,UAAU,GAAGD,SAAS,CAACE,KAAK,CAACV,oBAAoB,CAAC;;QAGxD,IAAIS,UAAU,CAAC,CAAC,CAAC,KAAK,EAAE,IAAIA,UAAU,CAACE,MAAM,KAAK,CAAC,EAAE;YACjDF,UAAU,CAACG,KAAK,CAAE,CAAA;;QAGtB,OAAOC,iBAAiB,CAACJ,UAAU,EAAEN,QAAQ,CAAC,IAAIW,8BAA8B,CAACN,SAAS,CAAC;IAC9F,CAAA;IAED,MAAMO,2BAA2B,GAAGA,CAChCC,YAA8B,EAC9BC,kBAA2B,KAC3B;QACA,MAAMC,SAAS,GAAGb,sBAAsB,CAACW,YAAY,CAAC,IAAI,EAAE;QAE5D,IAAIC,kBAAkB,IAAIX,8BAA8B,CAACU,YAAY,CAAC,EAAE;YACpE,OAAO,CAAC;mBAAGE,SAAS,EAAE;mBAAGZ,8BAA8B,CAACU,YAAY,CAAE;aAAC;;QAG3E,OAAOE,SAAS;IACnB,CAAA;IAED,OAAO;QACHX,eAAe;QACfQ;IACH,CAAA;AACL,CAAC;AAED,MAAMF,iBAAiB,GAAGA,CACtBJ,UAAoB,EACpBU,eAAgC,KACF;IAC9B,IAAIV,UAAU,CAACE,MAAM,KAAK,CAAC,EAAE;QACzB,OAAOQ,eAAe,CAACH,YAAY;;IAGvC,MAAMI,gBAAgB,GAAGX,UAAU,CAAC,CAAC,CAAE;IACvC,MAAMY,mBAAmB,GAAGF,eAAe,CAACG,QAAQ,CAACC,GAAG,CAACH,gBAAgB,CAAC;IAC1E,MAAMI,2BAA2B,GAAGH,mBAAA,GAC9BR,iBAAiB,CAACJ,UAAU,CAACgB,KAAK,CAAC,CAAC,CAAC,EAAEJ,mBAAmB,CAAA,GAC1DK,SAAS;IAEf,IAAIF,2BAA2B,EAAE;QAC7B,OAAOA,2BAA2B;;IAGtC,IAAIL,eAAe,CAACQ,UAAU,CAAChB,MAAM,KAAK,CAAC,EAAE;QACzC,OAAOe,SAAS;;IAGpB,MAAME,SAAS,GAAGnB,UAAU,CAACoB,IAAI,CAAC7B,oBAAoB,CAAC;IAEvD,OAAOmB,eAAe,CAACQ,UAAU,CAACG,IAAI,CAAC,CAAC,EAAEC,SAAAA,EAAW,GAAKA,SAAS,CAACH,SAAS,CAAC,CAAC,EAAEZ,YAAY;AACjG,CAAC;AAED,MAAMgB,sBAAsB,GAAG,YAAY;AAE3C,MAAMlB,8BAA8B,IAAIN,SAAiB,IAAI;IACzD,IAAIwB,sBAAsB,CAACC,IAAI,CAACzB,SAAS,CAAC,EAAE;QACxC,MAAM0B,0BAA0B,GAAGF,sBAAsB,CAACG,IAAI,CAAC3B,SAAS,CAAE,CAAC,CAAC,CAAC;QAC7E,MAAM4B,QAAQ,GAAGF,0BAA0B,EAAEG,SAAS,CAClD,CAAC,EACDH,0BAA0B,CAACI,OAAO,CAAC,GAAG,CAAC,CAC1C;QAED,IAAIF,QAAQ,EAAE;;YAEV,OAAO,aAAa,GAAGA,QAAQ;;;AAG3C,CAAC;AAED;;CAEG,GACI,MAAMhC,cAAc,IAAIF,MAAkD,IAAI;IACjF,MAAM,EAAEqC,KAAK,EAAEC,MAAAA,EAAQ,GAAGtC,MAAM;IAChC,MAAMC,QAAQ,GAAoB;QAC9BmB,QAAQ,EAAE,IAAImB,GAAG,CAA2B,CAAA;QAC5Cd,UAAU,EAAE,EAAA;IACf,CAAA;IAED,MAAMe,yBAAyB,GAAGC,4BAA4B,CAC1DC,MAAM,CAACC,OAAO,CAAC3C,MAAM,CAAC4C,WAAW,CAAC,EAClCN,MAAM,CACT;IAEDE,yBAAyB,CAACK,OAAO,CAAC,CAAC,CAAC/B,YAAY,EAAEgC,UAAU,CAAC,KAAI;QAC7DC,yBAAyB,CAACD,UAAU,EAAE7C,QAAQ,EAAEa,YAAY,EAAEuB,KAAK,CAAC;IACxE,CAAC,CAAC;IAEF,OAAOpC,QAAQ;AACnB,CAAC;AAED,MAAM8C,yBAAyB,GAAGA,CAC9BD,UAAwC,EACxC7B,eAAgC,EAChCH,YAA8B,EAC9BuB,KAAoC,KACpC;IACAS,UAAU,CAACD,OAAO,EAAEG,eAAe,IAAI;QACnC,IAAI,OAAOA,eAAe,KAAK,QAAQ,EAAE;YACrC,MAAMC,qBAAqB,GACvBD,eAAe,KAAK,EAAE,GAAG/B,eAAe,GAAGiC,OAAO,CAACjC,eAAe,EAAE+B,eAAe,CAAC;YACxFC,qBAAqB,CAACnC,YAAY,GAAGA,YAAY;YACjD;;QAGJ,IAAI,OAAOkC,eAAe,KAAK,UAAU,EAAE;YACvC,IAAIG,aAAa,CAACH,eAAe,CAAC,EAAE;gBAChCD,yBAAyB,CACrBC,eAAe,CAACX,KAAK,CAAC,EACtBpB,eAAe,EACfH,YAAY,EACZuB,KAAK,CACR;gBACD;;YAGJpB,eAAe,CAACQ,UAAU,CAAC2B,IAAI,CAAC;gBAC5BvB,SAAS,EAAEmB,eAAe;gBAC1BlC;YACH,CAAA,CAAC;YAEF;;QAGJ4B,MAAM,CAACC,OAAO,CAACK,eAAe,CAAC,CAACH,OAAO,CAAC,CAAC,CAACQ,GAAG,EAAEP,UAAU,CAAC,KAAI;YAC1DC,yBAAyB,CACrBD,UAAU,EACVI,OAAO,CAACjC,eAAe,EAAEoC,GAAG,CAAC,EAC7BvC,YAAY,EACZuB,KAAK,CACR;QACL,CAAC,CAAC;IACN,CAAC,CAAC;AACN,CAAC;AAED,MAAMa,OAAO,GAAGA,CAACjC,eAAgC,EAAEqC,IAAY,KAAI;IAC/D,IAAIC,sBAAsB,GAAGtC,eAAe;IAE5CqC,IAAI,CAAC9C,KAAK,CAACV,oBAAoB,CAAC,CAAC+C,OAAO,EAAEW,QAAQ,IAAI;QAClD,IAAI,CAACD,sBAAsB,CAACnC,QAAQ,CAACqC,GAAG,CAACD,QAAQ,CAAC,EAAE;YAChDD,sBAAsB,CAACnC,QAAQ,CAACsC,GAAG,CAACF,QAAQ,EAAE;gBAC1CpC,QAAQ,EAAE,IAAImB,GAAG,CAAE,CAAA;gBACnBd,UAAU,EAAE,EAAA;YACf,CAAA,CAAC;;QAGN8B,sBAAsB,GAAGA,sBAAsB,CAACnC,QAAQ,CAACC,GAAG,CAACmC,QAAQ,CAAE;IAC3E,CAAC,CAAC;IAEF,OAAOD,sBAAsB;AACjC,CAAC;AAED,MAAMJ,aAAa,IAAIQ,IAAkC,GACpDA,IAAoB,CAACR,aAAa;AAEvC,MAAMV,4BAA4B,GAAGA,CACjCmB,iBAA0F,EAC1FtB,MAA0B,KAC+C;IACzE,IAAI,CAACA,MAAM,EAAE;QACT,OAAOsB,iBAAiB;;IAG5B,OAAOA,iBAAiB,CAACC,GAAG,CAAC,CAAC,CAAC/C,YAAY,EAAEgC,UAAU,CAAC,KAAI;QACxD,MAAMgB,kBAAkB,GAAGhB,UAAU,CAACe,GAAG,EAAEb,eAAe,IAAI;YAC1D,IAAI,OAAOA,eAAe,KAAK,QAAQ,EAAE;gBACrC,OAAOV,MAAM,GAAGU,eAAe;;YAGnC,IAAI,OAAOA,eAAe,KAAK,QAAQ,EAAE;gBACrC,OAAON,MAAM,CAACqB,WAAW,CACrBrB,MAAM,CAACC,OAAO,CAACK,eAAe,CAAC,CAACa,GAAG,CAAC,CAAC,CAACR,GAAG,EAAEW,KAAK,CAAC,GAAK;wBAAC1B,MAAM,GAAGe,GAAG;wBAAEW,KAAK;qBAAC,CAAC,CAC/E;;YAGL,OAAOhB,eAAe;QAC1B,CAAC,CAAC;QAEF,OAAO;YAAClC,YAAY;YAAEgD,kBAAkB;SAAC;IAC7C,CAAC,CAAC;AACN,CAAC;AC9MD,oJAAA;AACO,MAAMG,cAAc,IAAgBC,YAAoB,IAA0B;IACrF,IAAIA,YAAY,GAAG,CAAC,EAAE;QAClB,OAAO;YACH7C,GAAG,EAAEA,CAAA,GAAMG,SAAS;YACpBkC,GAAG,EAAEA,CAAA,IAAQ,CAAH;QACb,CAAA;;IAGL,IAAIS,SAAS,GAAG,CAAC;IACjB,IAAIC,KAAK,GAAG,IAAI7B,GAAG,CAAc,CAAA;IACjC,IAAI8B,aAAa,GAAG,IAAI9B,GAAG,CAAc,CAAA;IAEzC,MAAM+B,MAAM,GAAGA,CAACjB,GAAQ,EAAEW,KAAY,KAAI;QACtCI,KAAK,CAACV,GAAG,CAACL,GAAG,EAAEW,KAAK,CAAC;QACrBG,SAAS,EAAE;QAEX,IAAIA,SAAS,GAAGD,YAAY,EAAE;YAC1BC,SAAS,GAAG,CAAC;YACbE,aAAa,GAAGD,KAAK;YACrBA,KAAK,GAAG,IAAI7B,GAAG,CAAE,CAAA;;IAExB,CAAA;IAED,OAAO;QACHlB,GAAGA,EAACgC,GAAG,EAAA;YACH,IAAIW,KAAK,GAAGI,KAAK,CAAC/C,GAAG,CAACgC,GAAG,CAAC;YAE1B,IAAIW,KAAK,KAAKxC,SAAS,EAAE;gBACrB,OAAOwC,KAAK;;YAEhB,IAAI,CAACA,KAAK,GAAGK,aAAa,CAAChD,GAAG,CAACgC,GAAG,CAAC,MAAM7B,SAAS,EAAE;gBAChD8C,MAAM,CAACjB,GAAG,EAAEW,KAAK,CAAC;gBAClB,OAAOA,KAAK;;QAEnB,CAAA;QACDN,GAAGA,EAACL,GAAG,EAAEW,KAAK,EAAA;YACV,IAAII,KAAK,CAACX,GAAG,CAACJ,GAAG,CAAC,EAAE;gBAChBe,KAAK,CAACV,GAAG,CAACL,GAAG,EAAEW,KAAK,CAAC;mBAClB;gBACHM,MAAM,CAACjB,GAAG,EAAEW,KAAK,CAAC;;QAEzB;IACJ,CAAA;AACL,CAAC;ACjDM,MAAMO,kBAAkB,GAAG,GAAG;AAE9B,MAAMC,oBAAoB,IAAIxE,MAAiB,IAAI;IACtD,MAAM,EAAEyE,SAAS,EAAEC,0BAAAA,EAA4B,GAAG1E,MAAM;IACxD,MAAM2E,0BAA0B,GAAGF,SAAS,CAAChE,MAAM,KAAK,CAAC;IACzD,MAAMmE,uBAAuB,GAAGH,SAAS,CAAC,CAAC,CAAC;IAC5C,MAAMI,eAAe,GAAGJ,SAAS,CAAChE,MAAM;;IAGxC,MAAMqE,cAAc,IAAIxE,SAAiB,IAAI;QACzC,MAAMyE,SAAS,GAAG,EAAE;QAEpB,IAAIC,YAAY,GAAG,CAAC;QACpB,IAAIC,aAAa,GAAG,CAAC;QACrB,IAAIC,uBAA2C;QAE/C,IAAK,IAAIC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAG7E,SAAS,CAACG,MAAM,EAAE0E,KAAK,EAAE,CAAE;YACnD,IAAIC,gBAAgB,GAAG9E,SAAS,CAAC6E,KAAK,CAAC;YAEvC,IAAIH,YAAY,KAAK,CAAC,EAAE;gBACpB,IACII,gBAAgB,KAAKR,uBAAuB,IAAA,CAC3CD,0BAA0B,IACvBrE,SAAS,CAACiB,KAAK,CAAC4D,KAAK,EAAEA,KAAK,GAAGN,eAAe,CAAC,KAAKJ,SAAS,CAAC,EACpE;oBACEM,SAAS,CAAC3B,IAAI,CAAC9C,SAAS,CAACiB,KAAK,CAAC0D,aAAa,EAAEE,KAAK,CAAC,CAAC;oBACrDF,aAAa,GAAGE,KAAK,GAAGN,eAAe;oBACvC;;gBAGJ,IAAIO,gBAAgB,KAAK,GAAG,EAAE;oBAC1BF,uBAAuB,GAAGC,KAAK;oBAC/B;;;YAIR,IAAIC,gBAAgB,KAAK,GAAG,EAAE;gBAC1BJ,YAAY,EAAE;mBACX,IAAII,gBAAgB,KAAK,GAAG,EAAE;gBACjCJ,YAAY,EAAE;;;QAItB,MAAMK,kCAAkC,GACpCN,SAAS,CAACtE,MAAM,KAAK,CAAC,GAAGH,SAAS,GAAGA,SAAS,CAAC6B,SAAS,CAAC8C,aAAa,CAAC;QAC3E,MAAMK,oBAAoB,GACtBD,kCAAkC,CAACE,UAAU,CAAChB,kBAAkB,CAAC;QACrE,MAAMiB,aAAa,GAAGF,oBAAA,GAChBD,kCAAkC,CAAClD,SAAS,CAAC,CAAC,CAAA,GAC9CkD,kCAAkC;QAExC,MAAMI,4BAA4B,GAC9BP,uBAAuB,IAAIA,uBAAuB,GAAGD,aAAA,GAC/CC,uBAAuB,GAAGD,aAAA,GAC1BzD,SAAS;QAEnB,OAAO;YACHuD,SAAS;YACTO,oBAAoB;YACpBE,aAAa;YACbC;QACH,CAAA;IACJ,CAAA;IAED,IAAIf,0BAA0B,EAAE;QAC5B,QAAQpE,SAAiB,GAAKoE,0BAA0B,CAAC;gBAAEpE,SAAS;gBAAEwE;YAAc,CAAE,CAAC;;IAG3F,OAAOA,cAAc;AACzB,CAAC;AAED;;;;CAIG,GACI,MAAMY,aAAa,IAAIX,SAAmB,IAAI;IACjD,IAAIA,SAAS,CAACtE,MAAM,IAAI,CAAC,EAAE;QACvB,OAAOsE,SAAS;;IAGpB,MAAMY,eAAe,GAAa,EAAE;IACpC,IAAIC,iBAAiB,GAAa,EAAE;IAEpCb,SAAS,CAAClC,OAAO,EAAEgD,QAAQ,IAAI;QAC3B,MAAMC,kBAAkB,GAAGD,QAAQ,CAAC,CAAC,CAAC,KAAK,GAAG;QAE9C,IAAIC,kBAAkB,EAAE;YACpBH,eAAe,CAACvC,IAAI,CAAC,GAAGwC,iBAAiB,CAACG,IAAI,CAAA,CAAE,EAAEF,QAAQ,CAAC;YAC3DD,iBAAiB,GAAG,EAAE;eACnB;YACHA,iBAAiB,CAACxC,IAAI,CAACyC,QAAQ,CAAC;;IAExC,CAAC,CAAC;IAEFF,eAAe,CAACvC,IAAI,CAAC,GAAGwC,iBAAiB,CAACG,IAAI,CAAA,CAAE,CAAC;IAEjD,OAAOJ,eAAe;AAC1B,CAAC;AC7FM,MAAMK,iBAAiB,IAAIhG,MAAiB,GAAA,CAAM;QACrDoE,KAAK,EAAEH,cAAc,CAAiBjE,MAAM,CAACmE,SAAS,CAAC;QACvDW,cAAc,EAAEN,oBAAoB,CAACxE,MAAM,CAAC;QAC5C,GAAGD,qBAAqB,CAACC,MAAM,CAAA;IAClC,CAAA,CAAC;ACRF,MAAMiG,mBAAmB,GAAG,KAAK;AAE1B,MAAMC,cAAc,GAAGA,CAACC,SAAiB,EAAEC,WAAwB,KAAI;IAC1E,MAAM,EAAEtB,cAAc,EAAEzE,eAAe,EAAEQ,2BAAAA,EAA6B,GAAGuF,WAAW;IAEpF;;;;;;GAMG,GACH,MAAMC,qBAAqB,GAAa,EAAE;IAC1C,MAAMC,UAAU,GAAGH,SAAS,CAACI,IAAI,CAAA,CAAE,CAAC/F,KAAK,CAACyF,mBAAmB,CAAC;IAE9D,IAAIO,MAAM,GAAG,EAAE;IAEf,IAAK,IAAIrB,KAAK,GAAGmB,UAAU,CAAC7F,MAAM,GAAG,CAAC,EAAE0E,KAAK,IAAI,CAAC,EAAEA,KAAK,IAAI,CAAC,CAAE;QAC5D,MAAMsB,iBAAiB,GAAGH,UAAU,CAACnB,KAAK,CAAE;QAE5C,MAAM,EAAEJ,SAAS,EAAEO,oBAAoB,EAAEE,aAAa,EAAEC,4BAAAA,EAA8B,GAClFX,cAAc,CAAC2B,iBAAiB,CAAC;QAErC,IAAI1F,kBAAkB,GAAG2F,OAAO,CAACjB,4BAA4B,CAAC;QAC9D,IAAI3E,YAAY,GAAGT,eAAe,CAC9BU,kBAAA,GACMyE,aAAa,CAACrD,SAAS,CAAC,CAAC,EAAEsD,4BAA4B,CAAA,GACvDD,aAAa,CACtB;QAED,IAAI,CAAC1E,YAAY,EAAE;YACf,IAAI,CAACC,kBAAkB,EAAE;;gBAErByF,MAAM,GAAGC,iBAAiB,GAAA,CAAID,MAAM,CAAC/F,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG+F,MAAM,GAAGA,MAAM,CAAC;gBACxE;;YAGJ1F,YAAY,GAAGT,eAAe,CAACmF,aAAa,CAAC;YAE7C,IAAI,CAAC1E,YAAY,EAAE;;gBAEf0F,MAAM,GAAGC,iBAAiB,GAAA,CAAID,MAAM,CAAC/F,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG+F,MAAM,GAAGA,MAAM,CAAC;gBACxE;;YAGJzF,kBAAkB,GAAG,KAAK;;QAG9B,MAAM4F,eAAe,GAAGjB,aAAa,CAACX,SAAS,CAAC,CAACpD,IAAI,CAAC,GAAG,CAAC;QAE1D,MAAMiF,UAAU,GAAGtB,oBAAA,GACbqB,eAAe,GAAGpC,kBAAA,GAClBoC,eAAe;QAErB,MAAME,OAAO,GAAGD,UAAU,GAAG9F,YAAY;QAEzC,IAAIuF,qBAAqB,CAACS,QAAQ,CAACD,OAAO,CAAC,EAAE;YAEzC;;QAGJR,qBAAqB,CAACjD,IAAI,CAACyD,OAAO,CAAC;QAEnC,MAAME,cAAc,GAAGlG,2BAA2B,CAACC,YAAY,EAAEC,kBAAkB,CAAC;QACpF,IAAK,IAAIiG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,cAAc,CAACtG,MAAM,EAAE,EAAEuG,CAAC,CAAE;YAC5C,MAAMC,KAAK,GAAGF,cAAc,CAACC,CAAC,CAAE;YAChCX,qBAAqB,CAACjD,IAAI,CAACwD,UAAU,GAAGK,KAAK,CAAC;;;QAIlDT,MAAM,GAAGC,iBAAiB,GAAA,CAAID,MAAM,CAAC/F,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG+F,MAAM,GAAGA,MAAM,CAAC;;IAG5E,OAAOA,MAAM;AACjB,CAAC;AC7ED;;;;;;;;CAQG,YAMaU,MAAMA,CAAA,EAAA;IAClB,IAAI/B,KAAK,GAAG,CAAC;IACb,IAAIgC,QAAwB;IAC5B,IAAIC,aAAqB;IACzB,IAAIC,MAAM,GAAG,EAAE;IAEf,MAAOlC,KAAK,GAAGmC,SAAS,CAAC7G,MAAM,CAAE;QAC7B,IAAK0G,QAAQ,GAAGG,SAAS,CAACnC,KAAK,EAAE,CAAC,EAAG;YACjC,IAAKiC,aAAa,GAAGG,OAAO,CAACJ,QAAQ,CAAC,EAAG;gBACrCE,MAAM,IAAA,CAAKA,MAAM,IAAI,GAAG,CAAC;gBACzBA,MAAM,IAAID,aAAa;;;;IAInC,OAAOC,MAAM;AACjB;AAEA,MAAME,OAAO,IAAIC,GAA4B,IAAI;IAC7C,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;QACzB,OAAOA,GAAG;;IAGd,IAAIJ,aAAqB;IACzB,IAAIC,MAAM,GAAG,EAAE;IAEf,IAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,GAAG,CAAC/G,MAAM,EAAEgH,CAAC,EAAE,CAAE;QACjC,IAAID,GAAG,CAACC,CAAC,CAAC,EAAE;YACR,IAAKL,aAAa,GAAGG,OAAO,CAACC,GAAG,CAACC,CAAC,CAA4B,CAAC,EAAG;gBAC9DJ,MAAM,IAAA,CAAKA,MAAM,IAAI,GAAG,CAAC;gBACzBA,MAAM,IAAID,aAAa;;;;IAKnC,OAAOC,MAAM;AACjB,CAAC;SCvCeK,mBAAmBA,CAC/BC,iBAAoC,EACpC,GAAGC,gBAA0C,EAAA;IAE7C,IAAIxB,WAAwB;IAC5B,IAAIyB,QAAqC;IACzC,IAAIC,QAAqC;IACzC,IAAIC,cAAc,GAAGC,iBAAiB;IAEtC,SAASA,iBAAiBA,CAAC7B,SAAiB,EAAA;QACxC,MAAMnG,MAAM,GAAG4H,gBAAgB,CAACK,MAAM,CAClC,CAACC,cAAc,EAAEC,mBAAmB,GAAKA,mBAAmB,CAACD,cAAc,CAAC,EAC5EP,iBAAiB,EAAe,CACnC;QAEDvB,WAAW,GAAGJ,iBAAiB,CAAChG,MAAM,CAAC;QACvC6H,QAAQ,GAAGzB,WAAW,CAAChC,KAAK,CAAC/C,GAAG;QAChCyG,QAAQ,GAAG1B,WAAW,CAAChC,KAAK,CAACV,GAAG;QAChCqE,cAAc,GAAGK,aAAa;QAE9B,OAAOA,aAAa,CAACjC,SAAS,CAAC;;IAGnC,SAASiC,aAAaA,CAACjC,SAAiB,EAAA;QACpC,MAAMkC,YAAY,GAAGR,QAAQ,CAAC1B,SAAS,CAAC;QAExC,IAAIkC,YAAY,EAAE;YACd,OAAOA,YAAY;;QAGvB,MAAM7B,MAAM,GAAGN,cAAc,CAACC,SAAS,EAAEC,WAAW,CAAC;QACrD0B,QAAQ,CAAC3B,SAAS,EAAEK,MAAM,CAAC;QAE3B,OAAOA,MAAM;;IAGjB,OAAO,SAAS8B,iBAAiBA,CAAA,EAAA;QAC7B,OAAOP,cAAc,CAACb,MAAM,CAACqB,KAAK,CAAC,IAAI,EAAEjB,SAAgB,CAAC,CAAC;IAC9D,CAAA;AACL;AC/Ca,MAAAkB,SAAS,IAGpBnF,GAAiE,IAAiB;IAChF,MAAMoF,WAAW,IAAIpG,KAAuE,GACxFA,KAAK,CAACgB,GAAG,CAAC,IAAI,EAAE;IAEpBoF,WAAW,CAACtF,aAAa,GAAG,IAAa;IAEzC,OAAOsF,WAAW;AACtB,CAAA;ACZA,MAAMC,mBAAmB,GAAG,4BAA4B;AACxD,MAAMC,aAAa,GAAG,YAAY;AAClC,MAAMC,aAAa,GAAA,WAAA,GAAG,IAAIC,GAAG,CAAC;IAAC,IAAI;IAAE,MAAM;IAAE,QAAQ;CAAC,CAAC;AACvD,MAAMC,eAAe,GAAG,kCAAkC;AAC1D,MAAMC,eAAe,GACjB,2HAA2H;AAC/H,MAAMC,kBAAkB,GAAG,0CAA0C;AACrE,iGAAA;AACA,MAAMC,WAAW,GAAG,iEAAiE;AACrF,MAAMC,UAAU,GACZ,8FAA8F;AAE3F,MAAMC,QAAQ,IAAInF,KAAa,GAClCoF,QAAQ,CAACpF,KAAK,CAAC,IAAI4E,aAAa,CAACnF,GAAG,CAACO,KAAK,CAAC,IAAI2E,aAAa,CAAC5G,IAAI,CAACiC,KAAK,CAAC;AAErE,MAAMqF,iBAAiB,IAAIrF,KAAa,GAC3CsF,mBAAmB,CAACtF,KAAK,EAAE,QAAQ,EAAEuF,YAAY,CAAC;AAE/C,MAAMH,QAAQ,IAAIpF,KAAa,GAAK0C,OAAO,CAAC1C,KAAK,CAAC,IAAI,CAACwF,MAAM,CAACC,KAAK,CAACD,MAAM,CAACxF,KAAK,CAAC,CAAC;AAElF,MAAM0F,iBAAiB,IAAI1F,KAAa,GAAKsF,mBAAmB,CAACtF,KAAK,EAAE,QAAQ,EAAEoF,QAAQ,CAAC;AAE3F,MAAMO,SAAS,IAAI3F,KAAa,GAAK0C,OAAO,CAAC1C,KAAK,CAAC,IAAIwF,MAAM,CAACG,SAAS,CAACH,MAAM,CAACxF,KAAK,CAAC,CAAC;AAEtF,MAAM4F,SAAS,IAAI5F,KAAa,GAAKA,KAAK,CAAC6F,QAAQ,CAAC,GAAG,CAAC,IAAIT,QAAQ,CAACpF,KAAK,CAACzC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAExF,MAAMuI,gBAAgB,IAAI9F,KAAa,GAAK0E,mBAAmB,CAAC3G,IAAI,CAACiC,KAAK,CAAC;AAE3E,MAAM+F,YAAY,IAAI/F,KAAa,GAAK8E,eAAe,CAAC/G,IAAI,CAACiC,KAAK,CAAC;AAE1E,MAAMgG,UAAU,GAAA,WAAA,GAAG,IAAInB,GAAG,CAAC;IAAC,QAAQ;IAAE,MAAM;IAAE,YAAY;CAAC,CAAC;AAErD,MAAMoB,eAAe,IAAIjG,KAAa,GAAKsF,mBAAmB,CAACtF,KAAK,EAAEgG,UAAU,EAAEE,OAAO,CAAC;AAE1F,MAAMC,mBAAmB,IAAInG,KAAa,GAC7CsF,mBAAmB,CAACtF,KAAK,EAAE,UAAU,EAAEkG,OAAO,CAAC;AAEnD,MAAME,WAAW,GAAA,WAAA,GAAG,IAAIvB,GAAG,CAAC;IAAC,OAAO;IAAE,KAAK;CAAC,CAAC;AAEtC,MAAMwB,gBAAgB,IAAIrG,KAAa,GAAKsF,mBAAmB,CAACtF,KAAK,EAAEoG,WAAW,EAAEE,OAAO,CAAC;AAE5F,MAAMC,iBAAiB,IAAIvG,KAAa,GAAKsF,mBAAmB,CAACtF,KAAK,EAAE,EAAE,EAAEwG,QAAQ,CAAC;AAErF,MAAMC,KAAK,GAAGA,CAAA,GAAM,IAAI;AAE/B,MAAMnB,mBAAmB,GAAGA,CACxBtF,KAAa,EACb0G,KAA2B,EAC3BC,SAAqC,KACrC;IACA,MAAMnE,MAAM,GAAGkC,mBAAmB,CAACzG,IAAI,CAAC+B,KAAK,CAAC;IAE9C,IAAIwC,MAAM,EAAE;QACR,IAAIA,MAAM,CAAC,CAAC,CAAC,EAAE;YACX,OAAO,OAAOkE,KAAK,KAAK,QAAQ,GAAGlE,MAAM,CAAC,CAAC,CAAC,KAAKkE,KAAK,GAAGA,KAAK,CAACjH,GAAG,CAAC+C,MAAM,CAAC,CAAC,CAAC,CAAC;;QAGjF,OAAOmE,SAAS,CAACnE,MAAM,CAAC,CAAC,CAAE,CAAC;;IAGhC,OAAO,KAAK;AAChB,CAAC;AAED,MAAM+C,YAAY,IAAIvF,KAAa,GAC/B,uJAAA;IACA,kFAAA;IACA,qGAAA;IACA+E,eAAe,CAAChH,IAAI,CAACiC,KAAK,CAAC,IAAI,CAACgF,kBAAkB,CAACjH,IAAI,CAACiC,KAAK,CAAC;AAElE,MAAMkG,OAAO,GAAGA,CAAA,GAAM,KAAK;AAE3B,MAAMM,QAAQ,IAAIxG,KAAa,GAAKiF,WAAW,CAAClH,IAAI,CAACiC,KAAK,CAAC;AAE3D,MAAMsG,OAAO,IAAItG,KAAa,GAAKkF,UAAU,CAACnH,IAAI,CAACiC,KAAK,CAAC;;;;;;;;;;;;;;;;;;;ACvDlD,MAAM4G,gBAAgB,GAAGA,CAAA,KAAK;IACjC,MAAMC,MAAM,GAAGrC,SAAS,CAAC,QAAQ,CAAC;IAClC,MAAMsC,OAAO,GAAGtC,SAAS,CAAC,SAAS,CAAC;IACpC,MAAMuC,IAAI,GAAGvC,SAAS,CAAC,MAAM,CAAC;IAC9B,MAAMwC,UAAU,GAAGxC,SAAS,CAAC,YAAY,CAAC;IAC1C,MAAMyC,WAAW,GAAGzC,SAAS,CAAC,aAAa,CAAC;IAC5C,MAAM0C,YAAY,GAAG1C,SAAS,CAAC,cAAc,CAAC;IAC9C,MAAM2C,aAAa,GAAG3C,SAAS,CAAC,eAAe,CAAC;IAChD,MAAM4C,WAAW,GAAG5C,SAAS,CAAC,aAAa,CAAC;IAC5C,MAAM6C,QAAQ,GAAG7C,SAAS,CAAC,UAAU,CAAC;IACtC,MAAM8C,SAAS,GAAG9C,SAAS,CAAC,WAAW,CAAC;IACxC,MAAM+C,SAAS,GAAG/C,SAAS,CAAC,WAAW,CAAC;IACxC,MAAMgD,MAAM,GAAGhD,SAAS,CAAC,QAAQ,CAAC;IAClC,MAAMiD,GAAG,GAAGjD,SAAS,CAAC,KAAK,CAAC;IAC5B,MAAMkD,kBAAkB,GAAGlD,SAAS,CAAC,oBAAoB,CAAC;IAC1D,MAAMmD,0BAA0B,GAAGnD,SAAS,CAAC,4BAA4B,CAAC;IAC1E,MAAMoD,KAAK,GAAGpD,SAAS,CAAC,OAAO,CAAC;IAChC,MAAMqD,MAAM,GAAGrD,SAAS,CAAC,QAAQ,CAAC;IAClC,MAAMsD,OAAO,GAAGtD,SAAS,CAAC,SAAS,CAAC;IACpC,MAAMuD,OAAO,GAAGvD,SAAS,CAAC,SAAS,CAAC;IACpC,MAAMwD,QAAQ,GAAGxD,SAAS,CAAC,UAAU,CAAC;IACtC,MAAMyD,KAAK,GAAGzD,SAAS,CAAC,OAAO,CAAC;IAChC,MAAM0D,KAAK,GAAG1D,SAAS,CAAC,OAAO,CAAC;IAChC,MAAM2D,IAAI,GAAG3D,SAAS,CAAC,MAAM,CAAC;IAC9B,MAAM4D,KAAK,GAAG5D,SAAS,CAAC,OAAO,CAAC;IAChC,MAAM6D,SAAS,GAAG7D,SAAS,CAAC,WAAW,CAAC;IAExC,MAAM8D,aAAa,GAAGA,CAAA,GAAM;YAAC,MAAM;YAAE,SAAS;YAAE,MAAM;SAAU;IAChE,MAAMC,WAAW,GAAGA,CAAA,GAAM;YAAC,MAAM;YAAE,QAAQ;YAAE,MAAM;YAAE,SAAS;YAAE,QAAQ;SAAU;IAClF,MAAMC,8BAA8B,GAAGA,CAAA,GAAM;YAAC,MAAM;YAAE1C,gBAAgB;YAAEgB,OAAO;SAAU;IACzF,MAAM2B,uBAAuB,GAAGA,CAAA,GAAM;YAAC3C,gBAAgB;YAAEgB,OAAO;SAAU;IAC1E,MAAM4B,8BAA8B,GAAGA,CAAA,GAAM;YAAC,EAAE;YAAEvD,QAAQ;YAAEE,iBAAiB;SAAU;IACvF,MAAMsD,6BAA6B,GAAGA,CAAA,GAAM;YAAC,MAAM;YAAEvD,QAAQ;YAAEU,gBAAgB;SAAU;IACzF,MAAM8C,YAAY,GAAGA,CAAA,GACjB;YACI,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,aAAa;YACb,UAAU;YACV,OAAO;YACP,cAAc;YACd,WAAW;YACX,KAAK;SACC;IACd,MAAMC,aAAa,GAAGA,CAAA,GAAM;YAAC,OAAO;YAAE,QAAQ;YAAE,QAAQ;YAAE,QAAQ;YAAE,MAAM;SAAU;IACpF,MAAMC,aAAa,GAAGA,CAAA,GAClB;YACI,QAAQ;YACR,UAAU;YACV,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,SAAS;YACT,aAAa;YACb,YAAY;YACZ,YAAY;YACZ,YAAY;YACZ,YAAY;YACZ,WAAW;YACX,KAAK;YACL,YAAY;YACZ,OAAO;YACP,YAAY;SACN;IACd,MAAMC,QAAQ,GAAGA,CAAA,GACb;YAAC,OAAO;YAAE,KAAK;YAAE,QAAQ;YAAE,SAAS;YAAE,QAAQ;YAAE,QAAQ;YAAE,SAAS;SAAU;IACjF,MAAMC,eAAe,GAAGA,CAAA,GAAM;YAAC,EAAE;YAAE,GAAG;YAAElD,gBAAgB;SAAU;IAClE,MAAMmD,SAAS,GAAGA,CAAA,GACd;YAAC,MAAM;YAAE,OAAO;YAAE,KAAK;YAAE,YAAY;YAAE,MAAM;YAAE,MAAM;YAAE,OAAO;YAAE,QAAQ;SAAU;IACtF,MAAMC,qBAAqB,GAAGA,CAAA,GAAM;YAAC9D,QAAQ;YAAEU,gBAAgB;SAAC;IAEhE,OAAO;QACH3F,SAAS,EAAE,GAAG;QACdM,SAAS,EAAE,GAAG;QACdpC,KAAK,EAAE;YACHwI,MAAM,EAAE;gBAACJ,KAAK;aAAC;YACfK,OAAO,EAAE;gBAAC3B,QAAQ;gBAAEE,iBAAiB;aAAC;YACtC0B,IAAI,EAAE;gBAAC,MAAM;gBAAE,EAAE;gBAAEhB,YAAY;gBAAED,gBAAgB;aAAC;YAClDkB,UAAU,EAAEkC,qBAAqB,CAAE,CAAA;YACnCjC,WAAW,EAAE;gBAACJ,MAAM;aAAC;YACrBK,YAAY,EAAE;gBAAC,MAAM;gBAAE,EAAE;gBAAE,MAAM;gBAAEnB,YAAY;gBAAED,gBAAgB;aAAC;YAClEqB,aAAa,EAAEsB,uBAAuB,CAAE,CAAA;YACxCrB,WAAW,EAAEsB,8BAA8B,CAAE,CAAA;YAC7CrB,QAAQ,EAAE6B,qBAAqB,CAAE,CAAA;YACjC5B,SAAS,EAAE0B,eAAe,CAAE,CAAA;YAC5BzB,SAAS,EAAE2B,qBAAqB,CAAE,CAAA;YAClC1B,MAAM,EAAEwB,eAAe,CAAE,CAAA;YACzBvB,GAAG,EAAEgB,uBAAuB,CAAE,CAAA;YAC9Bf,kBAAkB,EAAE;gBAACb,MAAM;aAAC;YAC5Bc,0BAA0B,EAAE;gBAAC/B,SAAS;gBAAEP,iBAAiB;aAAC;YAC1DuC,KAAK,EAAEY,8BAA8B,CAAE,CAAA;YACvCX,MAAM,EAAEW,8BAA8B,CAAE,CAAA;YACxCV,OAAO,EAAEoB,qBAAqB,CAAE,CAAA;YAChCnB,OAAO,EAAEU,uBAAuB,CAAE,CAAA;YAClCT,QAAQ,EAAEkB,qBAAqB,CAAE,CAAA;YACjCjB,KAAK,EAAEiB,qBAAqB,CAAE,CAAA;YAC9BhB,KAAK,EAAEc,eAAe,CAAE,CAAA;YACxBb,IAAI,EAAEe,qBAAqB,CAAE,CAAA;YAC7Bd,KAAK,EAAEK,uBAAuB,CAAE,CAAA;YAChCJ,SAAS,EAAEI,uBAAuB,CAAE;QACvC,CAAA;QACD7J,WAAW,EAAE;;YAET;;;OAGG,GACHuK,MAAM,EAAE;gBAAC;oBAAEA,MAAM,EAAE;wBAAC,MAAM;wBAAE,QAAQ;wBAAE,OAAO;wBAAErD,gBAAgB;qBAAA;iBAAG;aAAC;YACnE;;;OAGG,GACHsD,SAAS,EAAE;gBAAC,WAAW;aAAC;YACxB;;;OAGG,GACHC,OAAO,EAAE;gBAAC;oBAAEA,OAAO,EAAE;wBAACtD,YAAY;qBAAA;gBAAC,CAAE;aAAC;YACtC;;;OAGG,GACH,aAAa,EAAE;gBAAC;oBAAE,aAAa,EAAEkD,SAAS,CAAE;gBAAA,CAAE;aAAC;YAC/C;;;OAGG,GACH,cAAc,EAAE;gBAAC;oBAAE,cAAc,EAAEA,SAAS,CAAE;gBAAA,CAAE;aAAC;YACjD;;;OAGG,GACH,cAAc,EAAE;gBAAC;oBAAE,cAAc,EAAE;wBAAC,MAAM;wBAAE,OAAO;wBAAE,YAAY;wBAAE,cAAc;qBAAA;iBAAG;aAAC;YACrF;;;OAGG,GACH,gBAAgB,EAAE;gBAAC;oBAAE,gBAAgB,EAAE;wBAAC,OAAO;wBAAE,OAAO;qBAAA;gBAAC,CAAE;aAAC;YAC5D;;;OAGG,GACHK,GAAG,EAAE;gBAAC;oBAAEA,GAAG,EAAE;wBAAC,QAAQ;wBAAE,SAAS;qBAAA;gBAAC,CAAE;aAAC;YACrC;;;OAGG,GACHC,OAAO,EAAE;gBACL,OAAO;gBACP,cAAc;gBACd,QAAQ;gBACR,MAAM;gBACN,aAAa;gBACb,OAAO;gBACP,cAAc;gBACd,eAAe;gBACf,YAAY;gBACZ,cAAc;gBACd,oBAAoB;gBACpB,oBAAoB;gBACpB,oBAAoB;gBACpB,iBAAiB;gBACjB,WAAW;gBACX,WAAW;gBACX,MAAM;gBACN,aAAa;gBACb,UAAU;gBACV,WAAW;gBACX,QAAQ;aACX;YACD;;;OAGG,GACHC,KAAK,EAAE;gBAAC;oBAAEA,KAAK,EAAE;wBAAC,OAAO;wBAAE,MAAM;wBAAE,MAAM;wBAAE,OAAO;wBAAE,KAAK;qBAAA;iBAAG;aAAC;YAC7D;;;OAGG,GACHC,KAAK,EAAE;gBAAC;oBAAEA,KAAK,EAAE;wBAAC,MAAM;wBAAE,OAAO;wBAAE,MAAM;wBAAE,MAAM;wBAAE,OAAO;wBAAE,KAAK;qBAAA;iBAAG;aAAC;YACrE;;;OAGG,GACHC,SAAS,EAAE;gBAAC,SAAS;gBAAE,gBAAgB;aAAC;YACxC;;;OAGG,GACH,YAAY,EAAE;gBAAC;oBAAEC,MAAM,EAAE;wBAAC,SAAS;wBAAE,OAAO;wBAAE,MAAM;wBAAE,MAAM;wBAAE,YAAY;qBAAA;iBAAG;aAAC;YAC9E;;;OAGG,GACH,iBAAiB,EAAE;gBAAC;oBAAEA,MAAM,EAAE,CAAC;2BAAGf,YAAY,CAAE,CAAA;wBAAE9C,gBAAgB;qBAAA;iBAAG;aAAC;YACtE;;;OAGG,GACH8D,QAAQ,EAAE;gBAAC;oBAAEA,QAAQ,EAAErB,WAAW,CAAE;gBAAA,CAAE;aAAC;YACvC;;;OAGG,GACH,YAAY,EAAE;gBAAC;oBAAE,YAAY,EAAEA,WAAW,CAAE;gBAAA,CAAE;aAAC;YAC/C;;;OAGG,GACH,YAAY,EAAE;gBAAC;oBAAE,YAAY,EAAEA,WAAW,CAAE;gBAAA,CAAE;aAAC;YAC/C;;;OAGG,GACHsB,UAAU,EAAE;gBAAC;oBAAEA,UAAU,EAAEvB,aAAa,CAAE;gBAAA,CAAE;aAAC;YAC7C;;;OAGG,GACH,cAAc,EAAE;gBAAC;oBAAE,cAAc,EAAEA,aAAa,CAAE;gBAAA,CAAE;aAAC;YACrD;;;OAGG,GACH,cAAc,EAAE;gBAAC;oBAAE,cAAc,EAAEA,aAAa,CAAE;gBAAA,CAAE;aAAC;YACrD;;;OAGG,GACHwB,QAAQ,EAAE;gBAAC,QAAQ;gBAAE,OAAO;gBAAE,UAAU;gBAAE,UAAU;gBAAE,QAAQ;aAAC;YAC/D;;;OAGG,GACHlC,KAAK,EAAE;gBAAC;oBAAEA,KAAK,EAAE;wBAACA,KAAK;qBAAA;gBAAC,CAAE;aAAC;YAC3B;;;OAGG,GACH,SAAS,EAAE;gBAAC;oBAAE,SAAS,EAAE;wBAACA,KAAK;qBAAA;gBAAC,CAAE;aAAC;YACnC;;;OAGG,GACH,SAAS,EAAE;gBAAC;oBAAE,SAAS,EAAE;wBAACA,KAAK;qBAAA;gBAAC,CAAE;aAAC;YACnC;;;OAGG,GACHmC,KAAK,EAAE;gBAAC;oBAAEA,KAAK,EAAE;wBAACnC,KAAK;qBAAA;gBAAC,CAAE;aAAC;YAC3B;;;OAGG,GACHoC,GAAG,EAAE;gBAAC;oBAAEA,GAAG,EAAE;wBAACpC,KAAK;qBAAA;gBAAC,CAAE;aAAC;YACvB;;;OAGG,GACHqC,GAAG,EAAE;gBAAC;oBAAEA,GAAG,EAAE;wBAACrC,KAAK;qBAAA;gBAAC,CAAE;aAAC;YACvB;;;OAGG,GACHsC,KAAK,EAAE;gBAAC;oBAAEA,KAAK,EAAE;wBAACtC,KAAK;qBAAA;gBAAC,CAAE;aAAC;YAC3B;;;OAGG,GACHuC,MAAM,EAAE;gBAAC;oBAAEA,MAAM,EAAE;wBAACvC,KAAK;qBAAA;gBAAC,CAAE;aAAC;YAC7B;;;OAGG,GACHwC,IAAI,EAAE;gBAAC;oBAAEA,IAAI,EAAE;wBAACxC,KAAK;qBAAA;gBAAC,CAAE;aAAC;YACzB;;;OAGG,GACHyC,UAAU,EAAE;gBAAC,SAAS;gBAAE,WAAW;gBAAE,UAAU;aAAC;YAChD;;;OAGG,GACHC,CAAC,EAAE;gBAAC;oBAAEA,CAAC,EAAE;wBAAC,MAAM;wBAAE3E,SAAS;wBAAEG,gBAAgB;qBAAA;iBAAG;aAAC;;YAEjD;;;OAGG,GACHyE,KAAK,EAAE;gBAAC;oBAAEA,KAAK,EAAE/B,8BAA8B,CAAE;gBAAA,CAAE;aAAC;YACpD;;;OAGG,GACH,gBAAgB,EAAE;gBAAC;oBAAEgC,IAAI,EAAE;wBAAC,KAAK;wBAAE,aAAa;wBAAE,KAAK;wBAAE,aAAa;qBAAA;iBAAG;aAAC;YAC1E;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAEA,IAAI,EAAE;wBAAC,MAAM;wBAAE,cAAc;wBAAE,QAAQ;qBAAA;iBAAG;aAAC;YAC3D;;;OAGG,GACHA,IAAI,EAAE;gBAAC;oBAAEA,IAAI,EAAE;wBAAC,GAAG;wBAAE,MAAM;wBAAE,SAAS;wBAAE,MAAM;wBAAE1E,gBAAgB;qBAAA;iBAAG;aAAC;YACpE;;;OAGG,GACH2E,IAAI,EAAE;gBAAC;oBAAEA,IAAI,EAAEzB,eAAe,CAAE;gBAAA,CAAE;aAAC;YACnC;;;OAGG,GACH0B,MAAM,EAAE;gBAAC;oBAAEA,MAAM,EAAE1B,eAAe,CAAE;gBAAA,CAAE;aAAC;YACvC;;;OAGG,GACH2B,KAAK,EAAE;gBAAC;oBAAEA,KAAK,EAAE;wBAAC,OAAO;wBAAE,MAAM;wBAAE,MAAM;wBAAEhF,SAAS;wBAAEG,gBAAgB;qBAAA;iBAAG;aAAC;YAC1E;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAE;wBAACW,KAAK;qBAAA;gBAAC,CAAE;aAAC;YACvC;;;OAGG,GACH,eAAe,EAAE;gBACb;oBACImE,GAAG,EAAE;wBACD,MAAM;wBACN;4BAAEC,IAAI,EAAE;gCAAC,MAAM;gCAAElF,SAAS;gCAAEG,gBAAgB;6BAAA;wBAAG,CAAA;wBAC/CA,gBAAgB;qBAAA;gBAEvB,CAAA;aACJ;YACD;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAE6C,6BAA6B,CAAE;gBAAA,CAAE;aAAC;YAC/D;;;OAGG,GACH,SAAS,EAAE;gBAAC;oBAAE,SAAS,EAAEA,6BAA6B,CAAE;gBAAA,CAAE;aAAC;YAC3D;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAE;wBAAClC,KAAK;qBAAA;gBAAC,CAAE;aAAC;YACvC;;;OAGG,GACH,eAAe,EAAE;gBACb;oBAAEqE,GAAG,EAAE;wBAAC,MAAM;wBAAE;4BAAED,IAAI,EAAE;gCAAClF,SAAS;gCAAEG,gBAAgB;6BAAA;yBAAG;wBAAEA,gBAAgB;qBAAA;gBAAG,CAAA;aAC/E;YACD;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAE6C,6BAA6B,CAAE;gBAAA,CAAE;aAAC;YAC/D;;;OAGG,GACH,SAAS,EAAE;gBAAC;oBAAE,SAAS,EAAEA,6BAA6B,CAAE;gBAAA,CAAE;aAAC;YAC3D;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAE;wBAAC,KAAK;wBAAE,KAAK;wBAAE,OAAO;wBAAE,WAAW;wBAAE,WAAW;qBAAA;iBAAG;aAAC;YACjF;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAE;wBAAC,MAAM;wBAAE,KAAK;wBAAE,KAAK;wBAAE,IAAI;wBAAE7C,gBAAgB;qBAAA;iBAAG;aAAC;YAC9E;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAE;wBAAC,MAAM;wBAAE,KAAK;wBAAE,KAAK;wBAAE,IAAI;wBAAEA,gBAAgB;qBAAA;iBAAG;aAAC;YAC9E;;;OAGG,GACH2B,GAAG,EAAE;gBAAC;oBAAEA,GAAG,EAAE;wBAACA,GAAG;qBAAA;gBAAC,CAAE;aAAC;YACrB;;;OAGG,GACH,OAAO,EAAE;gBAAC;oBAAE,OAAO,EAAE;wBAACA,GAAG;qBAAA;gBAAC,CAAE;aAAC;YAC7B;;;OAGG,GACH,OAAO,EAAE;gBAAC;oBAAE,OAAO,EAAE;wBAACA,GAAG;qBAAA;gBAAC,CAAE;aAAC;YAC7B;;;OAGG,GACH,iBAAiB,EAAE;gBAAC;oBAAEsD,OAAO,EAAE;wBAAC,QAAQ,EAAE;2BAAGhC,QAAQ,CAAE,CAAA;qBAAA;iBAAG;aAAC;YAC3D;;;OAGG,GACH,eAAe,EAAE;gBAAC;oBAAE,eAAe,EAAE;wBAAC,OAAO;wBAAE,KAAK;wBAAE,QAAQ;wBAAE,SAAS;qBAAA;iBAAG;aAAC;YAC7E;;;OAGG,GACH,cAAc,EAAE;gBAAC;oBAAE,cAAc,EAAE;wBAAC,MAAM;wBAAE,OAAO;wBAAE,KAAK;wBAAE,QAAQ;wBAAE,SAAS;qBAAA;iBAAG;aAAC;YACnF;;;OAGG,GACH,eAAe,EAAE;gBAAC;oBAAEiC,OAAO,EAAE;wBAAC,QAAQ,EAAE;2BAAGjC,QAAQ,CAAE,CAAA;wBAAE,UAAU;qBAAA;iBAAG;aAAC;YACrE;;;OAGG,GACH,aAAa,EAAE;gBAAC;oBAAEkC,KAAK,EAAE;wBAAC,OAAO;wBAAE,KAAK;wBAAE,QAAQ;wBAAE,UAAU;wBAAE,SAAS;qBAAA;iBAAG;aAAC;YAC7E;;;OAGG,GACH,YAAY,EAAE;gBAAC;oBAAEC,IAAI,EAAE;wBAAC,MAAM;wBAAE,OAAO;wBAAE,KAAK;wBAAE,QAAQ;wBAAE,SAAS;wBAAE,UAAU;qBAAA;iBAAG;aAAC;YACnF;;;OAGG,GACH,eAAe,EAAE;gBAAC;oBAAE,eAAe,EAAE,CAAC;2BAAGnC,QAAQ,CAAE,CAAA;wBAAE,UAAU;qBAAA;iBAAG;aAAC;YACnE;;;OAGG,GACH,aAAa,EAAE;gBAAC;oBAAE,aAAa,EAAE;wBAAC,OAAO;wBAAE,KAAK;wBAAE,QAAQ;wBAAE,UAAU;wBAAE,SAAS;qBAAA;iBAAG;aAAC;YACrF;;;OAGG,GACH,YAAY,EAAE;gBAAC;oBAAE,YAAY,EAAE;wBAAC,MAAM;wBAAE,OAAO;wBAAE,KAAK;wBAAE,QAAQ;wBAAE,SAAS;qBAAA;iBAAG;aAAC;;YAE/E;;;OAGG,GACHoC,CAAC,EAAE;gBAAC;oBAAEA,CAAC,EAAE;wBAACpD,OAAO;qBAAA;gBAAC,CAAE;aAAC;YACrB;;;OAGG,GACHqD,EAAE,EAAE;gBAAC;oBAAEA,EAAE,EAAE;wBAACrD,OAAO;qBAAA;gBAAC,CAAE;aAAC;YACvB;;;OAGG,GACHsD,EAAE,EAAE;gBAAC;oBAAEA,EAAE,EAAE;wBAACtD,OAAO;qBAAA;gBAAC,CAAE;aAAC;YACvB;;;OAGG,GACHuD,EAAE,EAAE;gBAAC;oBAAEA,EAAE,EAAE;wBAACvD,OAAO;qBAAA;gBAAC,CAAE;aAAC;YACvB;;;OAGG,GACHwD,EAAE,EAAE;gBAAC;oBAAEA,EAAE,EAAE;wBAACxD,OAAO;qBAAA;gBAAC,CAAE;aAAC;YACvB;;;OAGG,GACHyD,EAAE,EAAE;gBAAC;oBAAEA,EAAE,EAAE;wBAACzD,OAAO;qBAAA;gBAAC,CAAE;aAAC;YACvB;;;OAGG,GACH0D,EAAE,EAAE;gBAAC;oBAAEA,EAAE,EAAE;wBAAC1D,OAAO;qBAAA;gBAAC,CAAE;aAAC;YACvB;;;OAGG,GACH2D,EAAE,EAAE;gBAAC;oBAAEA,EAAE,EAAE;wBAAC3D,OAAO;qBAAA;gBAAC,CAAE;aAAC;YACvB;;;OAGG,GACH4D,EAAE,EAAE;gBAAC;oBAAEA,EAAE,EAAE;wBAAC5D,OAAO;qBAAA;gBAAC,CAAE;aAAC;YACvB;;;OAGG,GACH6D,CAAC,EAAE;gBAAC;oBAAEA,CAAC,EAAE;wBAAC/D,MAAM;qBAAA;gBAAC,CAAE;aAAC;YACpB;;;OAGG,GACHgE,EAAE,EAAE;gBAAC;oBAAEA,EAAE,EAAE;wBAAChE,MAAM;qBAAA;gBAAC,CAAE;aAAC;YACtB;;;OAGG,GACHiE,EAAE,EAAE;gBAAC;oBAAEA,EAAE,EAAE;wBAACjE,MAAM;qBAAA;gBAAC,CAAE;aAAC;YACtB;;;OAGG,GACHkE,EAAE,EAAE;gBAAC;oBAAEA,EAAE,EAAE;wBAAClE,MAAM;qBAAA;gBAAC,CAAE;aAAC;YACtB;;;OAGG,GACHmE,EAAE,EAAE;gBAAC;oBAAEA,EAAE,EAAE;wBAACnE,MAAM;qBAAA;gBAAC,CAAE;aAAC;YACtB;;;OAGG,GACHoE,EAAE,EAAE;gBAAC;oBAAEA,EAAE,EAAE;wBAACpE,MAAM;qBAAA;gBAAC,CAAE;aAAC;YACtB;;;OAGG,GACHqE,EAAE,EAAE;gBAAC;oBAAEA,EAAE,EAAE;wBAACrE,MAAM;qBAAA;gBAAC,CAAE;aAAC;YACtB;;;OAGG,GACHsE,EAAE,EAAE;gBAAC;oBAAEA,EAAE,EAAE;wBAACtE,MAAM;qBAAA;gBAAC,CAAE;aAAC;YACtB;;;OAGG,GACHuE,EAAE,EAAE;gBAAC;oBAAEA,EAAE,EAAE;wBAACvE,MAAM;qBAAA;gBAAC,CAAE;aAAC;YACtB;;;OAGG,GACH,SAAS,EAAE;gBAAC;oBAAE,SAAS,EAAE;wBAACO,KAAK;qBAAA;gBAAC,CAAE;aAAC;YACnC;;;OAGG,GACH,iBAAiB,EAAE;gBAAC,iBAAiB;aAAC;YACtC;;;OAGG,GACH,SAAS,EAAE;gBAAC;oBAAE,SAAS,EAAE;wBAACA,KAAK;qBAAA;gBAAC,CAAE;aAAC;YACnC;;;OAGG,GACH,iBAAiB,EAAE;gBAAC,iBAAiB;aAAC;;YAEtC;;;OAGG,GACHiE,CAAC,EAAE;gBACC;oBACIA,CAAC,EAAE;wBACC,MAAM;wBACN,KAAK;wBACL,KAAK;wBACL,KAAK;wBACL,KAAK;wBACL,KAAK;wBACL,KAAK;wBACLvG,gBAAgB;wBAChBgB,OAAO;qBAAA;gBAEd,CAAA;aACJ;YACD;;;OAGG,GACH,OAAO,EAAE;gBAAC;oBAAE,OAAO,EAAE;wBAAChB,gBAAgB;wBAAEgB,OAAO;wBAAE,KAAK;wBAAE,KAAK;wBAAE,KAAK;qBAAA;iBAAG;aAAC;YACxE;;;OAGG,GACH,OAAO,EAAE;gBACL;oBACI,OAAO,EAAE;wBACLhB,gBAAgB;wBAChBgB,OAAO;wBACP,MAAM;wBACN,MAAM;wBACN,KAAK;wBACL,KAAK;wBACL,KAAK;wBACL,OAAO;wBACP;4BAAEwF,MAAM,EAAE;gCAACvG,YAAY;6BAAA;wBAAG,CAAA;wBAC1BA,YAAY;qBAAA;gBAEnB,CAAA;aACJ;YACD;;;OAGG,GACHwG,CAAC,EAAE;gBACC;oBACIA,CAAC,EAAE;wBACCzG,gBAAgB;wBAChBgB,OAAO;wBACP,MAAM;wBACN,KAAK;wBACL,KAAK;wBACL,KAAK;wBACL,KAAK;wBACL,KAAK;wBACL,KAAK;qBAAA;gBAEZ,CAAA;aACJ;YACD;;;OAGG,GACH,OAAO,EAAE;gBACL;oBAAE,OAAO,EAAE;wBAAChB,gBAAgB;wBAAEgB,OAAO;wBAAE,KAAK;wBAAE,KAAK;wBAAE,KAAK;wBAAE,KAAK;wBAAE,KAAK;wBAAE,KAAK;qBAAA;gBAAG,CAAA;aACrF;YACD;;;OAGG,GACH,OAAO,EAAE;gBACL;oBAAE,OAAO,EAAE;wBAAChB,gBAAgB;wBAAEgB,OAAO;wBAAE,KAAK;wBAAE,KAAK;wBAAE,KAAK;wBAAE,KAAK;wBAAE,KAAK;wBAAE,KAAK;qBAAA;gBAAG,CAAA;aACrF;YACD;;;OAGG,GACH0F,IAAI,EAAE;gBAAC;oBAAEA,IAAI,EAAE;wBAAC1G,gBAAgB;wBAAEgB,OAAO;wBAAE,MAAM;wBAAE,KAAK;wBAAE,KAAK;wBAAE,KAAK;qBAAA;iBAAG;aAAC;;YAE1E;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE2F,IAAI,EAAE;wBAAC,MAAM;wBAAE1G,YAAY;wBAAEV,iBAAiB;qBAAA;iBAAG;aAAC;YAClE;;;OAGG,GACH,gBAAgB,EAAE;gBAAC,aAAa;gBAAE,sBAAsB;aAAC;YACzD;;;OAGG,GACH,YAAY,EAAE;gBAAC,QAAQ;gBAAE,YAAY;aAAC;YACtC;;;OAGG,GACH,aAAa,EAAE;gBACX;oBACIqH,IAAI,EAAE;wBACF,MAAM;wBACN,YAAY;wBACZ,OAAO;wBACP,QAAQ;wBACR,QAAQ;wBACR,UAAU;wBACV,MAAM;wBACN,WAAW;wBACX,OAAO;wBACPhH,iBAAiB;qBAAA;gBAExB,CAAA;aACJ;YACD;;;OAGG,GACH,aAAa,EAAE;gBAAC;oBAAEgH,IAAI,EAAE;wBAACjG,KAAK;qBAAA;gBAAC,CAAE;aAAC;YAClC;;;OAGG,GACH,YAAY,EAAE;gBAAC,aAAa;aAAC;YAC7B;;;OAGG,GACH,aAAa,EAAE;gBAAC,SAAS;aAAC;YAC1B;;;OAGG,GACH,kBAAkB,EAAE;gBAAC,cAAc;aAAC;YACpC;;;OAGG,GACH,YAAY,EAAE;gBAAC,aAAa;gBAAE,eAAe;aAAC;YAC9C;;;OAGG,GACH,aAAa,EAAE;gBAAC,mBAAmB;gBAAE,cAAc;aAAC;YACpD;;;OAGG,GACH,cAAc,EAAE;gBAAC,oBAAoB;gBAAE,mBAAmB;aAAC;YAC3D;;;OAGG,GACHkG,QAAQ,EAAE;gBACN;oBACIA,QAAQ,EAAE;wBACN,SAAS;wBACT,OAAO;wBACP,QAAQ;wBACR,MAAM;wBACN,OAAO;wBACP,QAAQ;wBACR7G,gBAAgB;qBAAA;gBAEvB,CAAA;aACJ;YACD;;;OAGG,GACH,YAAY,EAAE;gBAAC;oBAAE,YAAY,EAAE;wBAAC,MAAM;wBAAEV,QAAQ;wBAAEM,iBAAiB;qBAAA;iBAAG;aAAC;YACvE;;;OAGG,GACHkH,OAAO,EAAE;gBACL;oBACIA,OAAO,EAAE;wBACL,MAAM;wBACN,OAAO;wBACP,MAAM;wBACN,QAAQ;wBACR,SAAS;wBACT,OAAO;wBACPzH,QAAQ;wBACRW,gBAAgB;qBAAA;gBAEvB,CAAA;aACJ;YACD;;;OAGG,GACH,YAAY,EAAE;gBAAC;oBAAE,YAAY,EAAE;wBAAC,MAAM;wBAAEA,gBAAgB;qBAAA;gBAAC,CAAE;aAAC;YAC5D;;;OAGG,GACH,iBAAiB,EAAE;gBAAC;oBAAE+G,IAAI,EAAE;wBAAC,MAAM;wBAAE,MAAM;wBAAE,SAAS;wBAAE/G,gBAAgB;qBAAA;iBAAG;aAAC;YAC5E;;;OAGG,GACH,qBAAqB,EAAE;gBAAC;oBAAE+G,IAAI,EAAE;wBAAC,QAAQ;wBAAE,SAAS;qBAAA;gBAAC,CAAE;aAAC;YACxD;;;;OAIG,GACH,mBAAmB,EAAE;gBAAC;oBAAEC,WAAW,EAAE;wBAACjG,MAAM;qBAAA;gBAAC,CAAE;aAAC;YAChD;;;OAGG,GACH,qBAAqB,EAAE;gBAAC;oBAAE,qBAAqB,EAAE;wBAACiB,OAAO;qBAAA;gBAAC,CAAE;aAAC;YAC7D;;;OAGG,GACH,gBAAgB,EAAE;gBAAC;oBAAE2E,IAAI,EAAE;wBAAC,MAAM;wBAAE,QAAQ;wBAAE,OAAO;wBAAE,SAAS;wBAAE,OAAO;wBAAE,KAAK;qBAAA;iBAAG;aAAC;YACpF;;;OAGG,GACH,YAAY,EAAE;gBAAC;oBAAEA,IAAI,EAAE;wBAAC5F,MAAM;qBAAA;gBAAC,CAAE;aAAC;YAClC;;;OAGG,GACH,cAAc,EAAE;gBAAC;oBAAE,cAAc,EAAE;wBAACiB,OAAO;qBAAA;gBAAC,CAAE;aAAC;YAC/C;;;OAGG,GACH,iBAAiB,EAAE;gBAAC,WAAW;gBAAE,UAAU;gBAAE,cAAc;gBAAE,cAAc;aAAC;YAC5E;;;OAGG,GACH,uBAAuB,EAAE;gBAAC;oBAAEiF,UAAU,EAAE,CAAC;2BAAGlE,aAAa,CAAE,CAAA;wBAAE,MAAM;qBAAA;iBAAG;aAAC;YACvE;;;OAGG,GACH,2BAA2B,EAAE;gBACzB;oBAAEkE,UAAU,EAAE;wBAAC,MAAM;wBAAE,WAAW;wBAAE5H,QAAQ;wBAAEE,iBAAiB;qBAAA;gBAAG,CAAA;aACrE;YACD;;;OAGG,GACH,kBAAkB,EAAE;gBAAC;oBAAE,kBAAkB,EAAE;wBAAC,MAAM;wBAAEF,QAAQ;wBAAEW,gBAAgB;qBAAA;iBAAG;aAAC;YAClF;;;OAGG,GACH,uBAAuB,EAAE;gBAAC;oBAAEiH,UAAU,EAAE;wBAAClG,MAAM;qBAAA;gBAAC,CAAE;aAAC;YACnD;;;OAGG,GACH,gBAAgB,EAAE;gBAAC,WAAW;gBAAE,WAAW;gBAAE,YAAY;gBAAE,aAAa;aAAC;YACzE;;;OAGG,GACH,eAAe,EAAE;gBAAC,UAAU;gBAAE,eAAe;gBAAE,WAAW;aAAC;YAC3D;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE4F,IAAI,EAAE;wBAAC,MAAM;wBAAE,QAAQ;wBAAE,SAAS;wBAAE,QAAQ;qBAAA;iBAAG;aAAC;YAChE;;;OAGG,GACHO,MAAM,EAAE;gBAAC;oBAAEA,MAAM,EAAEvE,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YAC/C;;;OAGG,GACH,gBAAgB,EAAE;gBACd;oBACIwE,KAAK,EAAE;wBACH,UAAU;wBACV,KAAK;wBACL,QAAQ;wBACR,QAAQ;wBACR,UAAU;wBACV,aAAa;wBACb,KAAK;wBACL,OAAO;wBACPnH,gBAAgB;qBAAA;gBAEvB,CAAA;aACJ;YACD;;;OAGG,GACHoH,UAAU,EAAE;gBACR;oBAAEA,UAAU,EAAE;wBAAC,QAAQ;wBAAE,QAAQ;wBAAE,KAAK;wBAAE,UAAU;wBAAE,UAAU;wBAAE,cAAc;qBAAA;gBAAG,CAAA;aACtF;YACD;;;OAGG,GACHC,KAAK,EAAE;gBAAC;oBAAEA,KAAK,EAAE;wBAAC,QAAQ;wBAAE,OAAO;wBAAE,KAAK;wBAAE,MAAM;qBAAA;iBAAG;aAAC;YACtD;;;OAGG,GACHC,OAAO,EAAE;gBAAC;oBAAEA,OAAO,EAAE;wBAAC,MAAM;wBAAE,QAAQ;wBAAE,MAAM;qBAAA;iBAAG;aAAC;YAClD;;;OAGG,GACHpC,OAAO,EAAE;gBAAC;oBAAEA,OAAO,EAAE;wBAAC,MAAM;wBAAElF,gBAAgB;qBAAA;gBAAC,CAAE;aAAC;;YAElD;;;OAGG,GACH,eAAe,EAAE;gBAAC;oBAAEuH,EAAE,EAAE;wBAAC,OAAO;wBAAE,OAAO;wBAAE,QAAQ;qBAAA;iBAAG;aAAC;YACvD;;;OAGG,GACH,SAAS,EAAE;gBAAC;oBAAE,SAAS,EAAE;wBAAC,QAAQ;wBAAE,SAAS;wBAAE,SAAS;wBAAE,MAAM;qBAAA;iBAAG;aAAC;YACpE;;;;OAIG,GACH,YAAY,EAAE;gBAAC;oBAAE,YAAY,EAAE;wBAACvF,OAAO;qBAAA;gBAAC,CAAE;aAAC;YAC3C;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAE;wBAAC,QAAQ;wBAAE,SAAS;wBAAE,SAAS;qBAAA;iBAAG;aAAC;YAChE;;;OAGG,GACH,aAAa,EAAE;gBAAC;oBAAEuF,EAAE,EAAE,CAAC;2BAAGzE,YAAY,CAAE,CAAA;wBAAEzC,mBAAmB;qBAAA;iBAAG;aAAC;YACjE;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAEkH,EAAE,EAAE;wBAAC,WAAW;wBAAE;4BAAEC,MAAM,EAAE;gCAAC,EAAE;gCAAE,GAAG;gCAAE,GAAG;gCAAE,OAAO;gCAAE,OAAO;6BAAA;wBAAC,CAAE;qBAAA;gBAAC,CAAE;aAAC;YAClF;;;OAGG,GACH,SAAS,EAAE;gBAAC;oBAAED,EAAE,EAAE;wBAAC,MAAM;wBAAE,OAAO;wBAAE,SAAS;wBAAEpH,eAAe;qBAAA;iBAAG;aAAC;YAClE;;;OAGG,GACH,UAAU,EAAE;gBACR;oBACIoH,EAAE,EAAE;wBACA,MAAM;wBACN;4BAAE,aAAa,EAAE;gCAAC,GAAG;gCAAE,IAAI;gCAAE,GAAG;gCAAE,IAAI;gCAAE,GAAG;gCAAE,IAAI;gCAAE,GAAG;gCAAE,IAAI;6BAAA;wBAAG,CAAA;wBAC/DhH,gBAAgB;qBAAA;gBAEvB,CAAA;aACJ;YACD;;;OAGG,GACH,UAAU,EAAE;gBAAC;oBAAEgH,EAAE,EAAE;wBAACxG,MAAM;qBAAA;gBAAC,CAAE;aAAC;YAC9B;;;OAGG,GACH,mBAAmB,EAAE;gBAAC;oBAAE0G,IAAI,EAAE;wBAAC5F,0BAA0B;qBAAA;gBAAC,CAAE;aAAC;YAC7D;;;OAGG,GACH,kBAAkB,EAAE;gBAAC;oBAAE6F,GAAG,EAAE;wBAAC7F,0BAA0B;qBAAA;gBAAC,CAAE;aAAC;YAC3D;;;OAGG,GACH,iBAAiB,EAAE;gBAAC;oBAAE8F,EAAE,EAAE;wBAAC9F,0BAA0B;qBAAA;gBAAC,CAAE;aAAC;YACzD;;;OAGG,GACH,eAAe,EAAE;gBAAC;oBAAE4F,IAAI,EAAE;wBAAC7F,kBAAkB;qBAAA;gBAAC,CAAE;aAAC;YACjD;;;OAGG,GACH,cAAc,EAAE;gBAAC;oBAAE8F,GAAG,EAAE;wBAAC9F,kBAAkB;qBAAA;gBAAC,CAAE;aAAC;YAC/C;;;OAGG,GACH,aAAa,EAAE;gBAAC;oBAAE+F,EAAE,EAAE;wBAAC/F,kBAAkB;qBAAA;gBAAC,CAAE;aAAC;;YAE7C;;;OAGG,GACHgG,OAAO,EAAE;gBAAC;oBAAEA,OAAO,EAAE;wBAACxG,YAAY;qBAAA;gBAAC,CAAE;aAAC;YACtC;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAE;wBAACA,YAAY;qBAAA;gBAAC,CAAE;aAAC;YAC9C;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAE;wBAACA,YAAY;qBAAA;gBAAC,CAAE;aAAC;YAC9C;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAE;wBAACA,YAAY;qBAAA;gBAAC,CAAE;aAAC;YAC9C;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAE;wBAACA,YAAY;qBAAA;gBAAC,CAAE;aAAC;YAC9C;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAE;wBAACA,YAAY;qBAAA;gBAAC,CAAE;aAAC;YAC9C;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAE;wBAACA,YAAY;qBAAA;gBAAC,CAAE;aAAC;YAC9C;;;OAGG,GACH,YAAY,EAAE;gBAAC;oBAAE,YAAY,EAAE;wBAACA,YAAY;qBAAA;gBAAC,CAAE;aAAC;YAChD;;;OAGG,GACH,YAAY,EAAE;gBAAC;oBAAE,YAAY,EAAE;wBAACA,YAAY;qBAAA;gBAAC,CAAE;aAAC;YAChD;;;OAGG,GACH,YAAY,EAAE;gBAAC;oBAAE,YAAY,EAAE;wBAACA,YAAY;qBAAA;gBAAC,CAAE;aAAC;YAChD;;;OAGG,GACH,YAAY,EAAE;gBAAC;oBAAE,YAAY,EAAE;wBAACA,YAAY;qBAAA;gBAAC,CAAE;aAAC;YAChD;;;OAGG,GACH,YAAY,EAAE;gBAAC;oBAAE,YAAY,EAAE;wBAACA,YAAY;qBAAA;gBAAC,CAAE;aAAC;YAChD;;;OAGG,GACH,YAAY,EAAE;gBAAC;oBAAE,YAAY,EAAE;wBAACA,YAAY;qBAAA;gBAAC,CAAE;aAAC;YAChD;;;OAGG,GACH,YAAY,EAAE;gBAAC;oBAAE,YAAY,EAAE;wBAACA,YAAY;qBAAA;gBAAC,CAAE;aAAC;YAChD;;;OAGG,GACH,YAAY,EAAE;gBAAC;oBAAE,YAAY,EAAE;wBAACA,YAAY;qBAAA;gBAAC,CAAE;aAAC;YAChD;;;OAGG,GACH,UAAU,EAAE;gBAAC;oBAAEyG,MAAM,EAAE;wBAACvG,WAAW;qBAAA;gBAAC,CAAE;aAAC;YACvC;;;OAGG,GACH,YAAY,EAAE;gBAAC;oBAAE,UAAU,EAAE;wBAACA,WAAW;qBAAA;gBAAC,CAAE;aAAC;YAC7C;;;OAGG,GACH,YAAY,EAAE;gBAAC;oBAAE,UAAU,EAAE;wBAACA,WAAW;qBAAA;gBAAC,CAAE;aAAC;YAC7C;;;OAGG,GACH,YAAY,EAAE;gBAAC;oBAAE,UAAU,EAAE;wBAACA,WAAW;qBAAA;gBAAC,CAAE;aAAC;YAC7C;;;OAGG,GACH,YAAY,EAAE;gBAAC;oBAAE,UAAU,EAAE;wBAACA,WAAW;qBAAA;gBAAC,CAAE;aAAC;YAC7C;;;OAGG,GACH,YAAY,EAAE;gBAAC;oBAAE,UAAU,EAAE;wBAACA,WAAW;qBAAA;gBAAC,CAAE;aAAC;YAC7C;;;OAGG,GACH,YAAY,EAAE;gBAAC;oBAAE,UAAU,EAAE;wBAACA,WAAW;qBAAA;gBAAC,CAAE;aAAC;YAC7C;;;OAGG,GACH,YAAY,EAAE;gBAAC;oBAAE,UAAU,EAAE;wBAACA,WAAW;qBAAA;gBAAC,CAAE;aAAC;YAC7C;;;OAGG,GACH,YAAY,EAAE;gBAAC;oBAAE,UAAU,EAAE;wBAACA,WAAW;qBAAA;gBAAC,CAAE;aAAC;YAC7C;;;OAGG,GACH,gBAAgB,EAAE;gBAAC;oBAAE,gBAAgB,EAAE;wBAACU,OAAO;qBAAA;gBAAC,CAAE;aAAC;YACnD;;;OAGG,GACH,cAAc,EAAE;gBAAC;oBAAE6F,MAAM,EAAE,CAAC;2BAAG9E,aAAa,CAAE,CAAA;wBAAE,QAAQ;qBAAA;iBAAG;aAAC;YAC5D;;;OAGG,GACH,UAAU,EAAE;gBAAC;oBAAE,UAAU,EAAE;wBAACzB,WAAW;qBAAA;gBAAC,CAAE;aAAC;YAC3C;;;OAGG,GACH,kBAAkB,EAAE;gBAAC,kBAAkB;aAAC;YACxC;;;OAGG,GACH,UAAU,EAAE;gBAAC;oBAAE,UAAU,EAAE;wBAACA,WAAW;qBAAA;gBAAC,CAAE;aAAC;YAC3C;;;OAGG,GACH,kBAAkB,EAAE;gBAAC,kBAAkB;aAAC;YACxC;;;OAGG,GACH,gBAAgB,EAAE;gBAAC;oBAAE,gBAAgB,EAAE;wBAACU,OAAO;qBAAA;gBAAC,CAAE;aAAC;YACnD;;;OAGG,GACH,cAAc,EAAE;gBAAC;oBAAE8F,MAAM,EAAE/E,aAAa,CAAE;gBAAA,CAAE;aAAC;YAC7C;;;OAGG,GACH,cAAc,EAAE;gBAAC;oBAAE8E,MAAM,EAAE;wBAAC1G,WAAW;qBAAA;gBAAC,CAAE;aAAC;YAC3C;;;OAGG,GACH,gBAAgB,EAAE;gBAAC;oBAAE,UAAU,EAAE;wBAACA,WAAW;qBAAA;gBAAC,CAAE;aAAC;YACjD;;;OAGG,GACH,gBAAgB,EAAE;gBAAC;oBAAE,UAAU,EAAE;wBAACA,WAAW;qBAAA;gBAAC,CAAE;aAAC;YACjD;;;OAGG,GACH,gBAAgB,EAAE;gBAAC;oBAAE,UAAU,EAAE;wBAACA,WAAW;qBAAA;gBAAC,CAAE;aAAC;YACjD;;;OAGG,GACH,gBAAgB,EAAE;gBAAC;oBAAE,UAAU,EAAE;wBAACA,WAAW;qBAAA;gBAAC,CAAE;aAAC;YACjD;;;OAGG,GACH,gBAAgB,EAAE;gBAAC;oBAAE,UAAU,EAAE;wBAACA,WAAW;qBAAA;gBAAC,CAAE;aAAC;YACjD;;;OAGG,GACH,gBAAgB,EAAE;gBAAC;oBAAE,UAAU,EAAE;wBAACA,WAAW;qBAAA;gBAAC,CAAE;aAAC;YACjD;;;OAGG,GACH,gBAAgB,EAAE;gBAAC;oBAAE,UAAU,EAAE;wBAACA,WAAW;qBAAA;gBAAC,CAAE;aAAC;YACjD;;;OAGG,GACH,gBAAgB,EAAE;gBAAC;oBAAE,UAAU,EAAE;wBAACA,WAAW;qBAAA;gBAAC,CAAE;aAAC;YACjD;;;OAGG,GACH,cAAc,EAAE;gBAAC;oBAAE2G,MAAM,EAAE;wBAAC3G,WAAW;qBAAA;gBAAC,CAAE;aAAC;YAC3C;;;OAGG,GACH,eAAe,EAAE;gBAAC;oBAAE4G,OAAO,EAAE;wBAAC,EAAE,EAAE;2BAAGhF,aAAa,CAAE,CAAA;qBAAA;iBAAG;aAAC;YACxD;;;OAGG,GACH,gBAAgB,EAAE;gBAAC;oBAAE,gBAAgB,EAAE;wBAAC1D,QAAQ;wBAAEW,gBAAgB;qBAAA;gBAAC,CAAE;aAAC;YACtE;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE+H,OAAO,EAAE;wBAAC1I,QAAQ;wBAAEE,iBAAiB;qBAAA;gBAAC,CAAE;aAAC;YACzD;;;OAGG,GACH,eAAe,EAAE;gBAAC;oBAAEwI,OAAO,EAAE;wBAAChH,MAAM;qBAAA;gBAAC,CAAE;aAAC;YACxC;;;OAGG,GACH,QAAQ,EAAE;gBAAC;oBAAEiH,IAAI,EAAEpF,8BAA8B,CAAE;gBAAA,CAAE;aAAC;YACtD;;;OAGG,GACH,cAAc,EAAE;gBAAC,YAAY;aAAC;YAC9B;;;OAGG,GACH,YAAY,EAAE;gBAAC;oBAAEoF,IAAI,EAAE;wBAACjH,MAAM;qBAAA;gBAAC,CAAE;aAAC;YAClC;;;OAGG,GACH,cAAc,EAAE;gBAAC;oBAAE,cAAc,EAAE;wBAACiB,OAAO;qBAAA;gBAAC,CAAE;aAAC;YAC/C;;;OAGG,GACH,eAAe,EAAE;gBAAC;oBAAE,aAAa,EAAE;wBAAC3C,QAAQ;wBAAEE,iBAAiB;qBAAA;gBAAC,CAAE;aAAC;YACnE;;;OAGG,GACH,mBAAmB,EAAE;gBAAC;oBAAE,aAAa,EAAE;wBAACwB,MAAM;qBAAA;gBAAC,CAAE;aAAC;;YAElD;;;OAGG,GACHkH,MAAM,EAAE;gBAAC;oBAAEA,MAAM,EAAE;wBAAC,EAAE;wBAAE,OAAO;wBAAE,MAAM;wBAAEhI,YAAY;wBAAEQ,iBAAiB;qBAAA;iBAAG;aAAC;YAC5E;;;OAGG,GACH,cAAc,EAAE;gBAAC;oBAAEwH,MAAM,EAAE;wBAACtH,KAAK;qBAAA;gBAAC,CAAE;aAAC;YACrC;;;OAGG,GACHqB,OAAO,EAAE;gBAAC;oBAAEA,OAAO,EAAE;wBAACA,OAAO;qBAAA;gBAAC,CAAE;aAAC;YACjC;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAE,CAAC;2BAAGgB,aAAa,CAAA,CAAE;wBAAE,cAAc;wBAAE,aAAa;qBAAA;iBAAG;aAAC;YACnF;;;OAGG,GACH,UAAU,EAAE;gBAAC;oBAAE,UAAU,EAAEA,aAAa,CAAE;gBAAA,CAAE;aAAC;;YAE7C;;;;OAIG,GACHkF,MAAM,EAAE;gBAAC;oBAAEA,MAAM,EAAE;wBAAC,EAAE;wBAAE,MAAM;qBAAA;gBAAC,CAAE;aAAC;YAClC;;;OAGG,GACHjH,IAAI,EAAE;gBAAC;oBAAEA,IAAI,EAAE;wBAACA,IAAI;qBAAA;gBAAC,CAAE;aAAC;YACxB;;;OAGG,GACHC,UAAU,EAAE;gBAAC;oBAAEA,UAAU,EAAE;wBAACA,UAAU;qBAAA;gBAAC,CAAE;aAAC;YAC1C;;;OAGG,GACHK,QAAQ,EAAE;gBAAC;oBAAEA,QAAQ,EAAE;wBAACA,QAAQ;qBAAA;gBAAC,CAAE;aAAC;YACpC;;;OAGG,GACH,aAAa,EAAE;gBAAC;oBAAE,aAAa,EAAE;wBAAC,EAAE;wBAAE,MAAM;wBAAEtB,YAAY;wBAAED,gBAAgB;qBAAA;iBAAG;aAAC;YAChF;;;OAGG,GACHwB,SAAS,EAAE;gBAAC;oBAAEA,SAAS,EAAE;wBAACA,SAAS;qBAAA;gBAAC,CAAE;aAAC;YACvC;;;OAGG,GACH,YAAY,EAAE;gBAAC;oBAAE,YAAY,EAAE;wBAACC,SAAS;qBAAA;gBAAC,CAAE;aAAC;YAC7C;;;OAGG,GACHC,MAAM,EAAE;gBAAC;oBAAEA,MAAM,EAAE;wBAACA,MAAM;qBAAA;gBAAC,CAAE;aAAC;YAC9B;;;OAGG,GACHQ,QAAQ,EAAE;gBAAC;oBAAEA,QAAQ,EAAE;wBAACA,QAAQ;qBAAA;gBAAC,CAAE;aAAC;YACpC;;;OAGG,GACHE,KAAK,EAAE;gBAAC;oBAAEA,KAAK,EAAE;wBAACA,KAAK;qBAAA;gBAAC,CAAE;aAAC;YAC3B;;;;OAIG,GACH,iBAAiB,EAAE;gBAAC;oBAAE,iBAAiB,EAAE;wBAAC,EAAE;wBAAE,MAAM;qBAAA;gBAAC,CAAE;aAAC;YACxD;;;OAGG,GACH,eAAe,EAAE;gBAAC;oBAAE,eAAe,EAAE;wBAACnB,IAAI;qBAAA;gBAAC,CAAE;aAAC;YAC9C;;;OAGG,GACH,qBAAqB,EAAE;gBAAC;oBAAE,qBAAqB,EAAE;wBAACC,UAAU;qBAAA;gBAAC,CAAE;aAAC;YAChE;;;OAGG,GACH,mBAAmB,EAAE;gBAAC;oBAAE,mBAAmB,EAAE;wBAACK,QAAQ;qBAAA;gBAAC,CAAE;aAAC;YAC1D;;;OAGG,GACH,oBAAoB,EAAE;gBAAC;oBAAE,oBAAoB,EAAE;wBAACC,SAAS;qBAAA;gBAAC,CAAE;aAAC;YAC7D;;;OAGG,GACH,qBAAqB,EAAE;gBAAC;oBAAE,qBAAqB,EAAE;wBAACC,SAAS;qBAAA;gBAAC,CAAE;aAAC;YAC/D;;;OAGG,GACH,iBAAiB,EAAE;gBAAC;oBAAE,iBAAiB,EAAE;wBAACC,MAAM;qBAAA;gBAAC,CAAE;aAAC;YACpD;;;OAGG,GACH,kBAAkB,EAAE;gBAAC;oBAAE,kBAAkB,EAAE;wBAACM,OAAO;qBAAA;gBAAC,CAAE;aAAC;YACvD;;;OAGG,GACH,mBAAmB,EAAE;gBAAC;oBAAE,mBAAmB,EAAE;wBAACE,QAAQ;qBAAA;gBAAC,CAAE;aAAC;YAC1D;;;OAGG,GACH,gBAAgB,EAAE;gBAAC;oBAAE,gBAAgB,EAAE;wBAACE,KAAK;qBAAA;gBAAC,CAAE;aAAC;;YAEjD;;;OAGG,GACH,iBAAiB,EAAE;gBAAC;oBAAEyF,MAAM,EAAE;wBAAC,UAAU;wBAAE,UAAU;qBAAA;gBAAC,CAAE;aAAC;YACzD;;;OAGG,GACH,gBAAgB,EAAE;gBAAC;oBAAE,gBAAgB,EAAE;wBAACxG,aAAa;qBAAA;gBAAC,CAAE;aAAC;YACzD;;;OAGG,GACH,kBAAkB,EAAE;gBAAC;oBAAE,kBAAkB,EAAE;wBAACA,aAAa;qBAAA;gBAAC,CAAE;aAAC;YAC7D;;;OAGG,GACH,kBAAkB,EAAE;gBAAC;oBAAE,kBAAkB,EAAE;wBAACA,aAAa;qBAAA;gBAAC,CAAE;aAAC;YAC7D;;;OAGG,GACH,cAAc,EAAE;gBAAC;oBAAE8G,KAAK,EAAE;wBAAC,MAAM;wBAAE,OAAO;qBAAA;gBAAC,CAAE;aAAC;YAC9C;;;OAGG,GACHC,OAAO,EAAE;gBAAC;oBAAEA,OAAO,EAAE;wBAAC,KAAK;wBAAE,QAAQ;qBAAA;gBAAC,CAAE;aAAC;;YAEzC;;;OAGG,GACHC,UAAU,EAAE;gBACR;oBACIA,UAAU,EAAE;wBACR,MAAM;wBACN,KAAK;wBACL,EAAE;wBACF,QAAQ;wBACR,SAAS;wBACT,QAAQ;wBACR,WAAW;wBACXrI,gBAAgB;qBAAA;gBAEvB,CAAA;aACJ;YACD;;;OAGG,GACHsI,QAAQ,EAAE;gBAAC;oBAAEA,QAAQ,EAAElF,qBAAqB,CAAE;gBAAA,CAAE;aAAC;YACjD;;;OAGG,GACHmF,IAAI,EAAE;gBAAC;oBAAEA,IAAI,EAAE;wBAAC,QAAQ;wBAAE,IAAI;wBAAE,KAAK;wBAAE,QAAQ;wBAAEvI,gBAAgB;qBAAA;iBAAG;aAAC;YACrE;;;OAGG,GACHwI,KAAK,EAAE;gBAAC;oBAAEA,KAAK,EAAEpF,qBAAqB,CAAE;gBAAA,CAAE;aAAC;YAC3C;;;OAGG,GACHqF,OAAO,EAAE;gBAAC;oBAAEA,OAAO,EAAE;wBAAC,MAAM;wBAAE,MAAM;wBAAE,MAAM;wBAAE,OAAO;wBAAE,QAAQ;wBAAEzI,gBAAgB;qBAAA;iBAAG;aAAC;;YAErF;;;OAGG,GACH0I,SAAS,EAAE;gBAAC;oBAAEA,SAAS,EAAE;wBAAC,EAAE;wBAAE,KAAK;wBAAE,MAAM;qBAAA;iBAAG;aAAC;YAC/C;;;OAGG,GACHvG,KAAK,EAAE;gBAAC;oBAAEA,KAAK,EAAE;wBAACA,KAAK;qBAAA;gBAAC,CAAE;aAAC;YAC3B;;;OAGG,GACH,SAAS,EAAE;gBAAC;oBAAE,SAAS,EAAE;wBAACA,KAAK;qBAAA;gBAAC,CAAE;aAAC;YACnC;;;OAGG,GACH,SAAS,EAAE;gBAAC;oBAAE,SAAS,EAAE;wBAACA,KAAK;qBAAA;gBAAC,CAAE;aAAC;YACnC;;;OAGG,GACHwG,MAAM,EAAE;gBAAC;oBAAEA,MAAM,EAAE;wBAAC9I,SAAS;wBAAEG,gBAAgB;qBAAA;gBAAC,CAAE;aAAC;YACnD;;;OAGG,GACH,aAAa,EAAE;gBAAC;oBAAE,aAAa,EAAE;wBAACuC,SAAS;qBAAA;gBAAC,CAAE;aAAC;YAC/C;;;OAGG,GACH,aAAa,EAAE;gBAAC;oBAAE,aAAa,EAAE;wBAACA,SAAS;qBAAA;gBAAC,CAAE;aAAC;YAC/C;;;OAGG,GACH,QAAQ,EAAE;gBAAC;oBAAE,QAAQ,EAAE;wBAACF,IAAI;qBAAA;gBAAC,CAAE;aAAC;YAChC;;;OAGG,GACH,QAAQ,EAAE;gBAAC;oBAAE,QAAQ,EAAE;wBAACA,IAAI;qBAAA;gBAAC,CAAE;aAAC;YAChC;;;OAGG,GACH,kBAAkB,EAAE;gBAChB;oBACIuG,MAAM,EAAE;wBACJ,QAAQ;wBACR,KAAK;wBACL,WAAW;wBACX,OAAO;wBACP,cAAc;wBACd,QAAQ;wBACR,aAAa;wBACb,MAAM;wBACN,UAAU;wBACV5I,gBAAgB;qBAAA;gBAEvB,CAAA;aACJ;;YAED;;;OAGG,GACH6I,MAAM,EAAE;gBAAC;oBAAEA,MAAM,EAAE;wBAAC,MAAM;wBAAE9H,MAAM;qBAAA;gBAAC,CAAE;aAAC;YACtC;;;OAGG,GACH+H,UAAU,EAAE;gBAAC;oBAAEA,UAAU,EAAE;wBAAC,MAAM;wBAAE,MAAM;qBAAA;gBAAC,CAAE;aAAC;YAC9C;;;OAGG,GACHC,MAAM,EAAE;gBACJ;oBACIA,MAAM,EAAE;wBACJ,MAAM;wBACN,SAAS;wBACT,SAAS;wBACT,MAAM;wBACN,MAAM;wBACN,MAAM;wBACN,MAAM;wBACN,aAAa;wBACb,MAAM;wBACN,cAAc;wBACd,UAAU;wBACV,MAAM;wBACN,WAAW;wBACX,eAAe;wBACf,OAAO;wBACP,MAAM;wBACN,SAAS;wBACT,MAAM;wBACN,UAAU;wBACV,YAAY;wBACZ,YAAY;wBACZ,YAAY;wBACZ,UAAU;wBACV,UAAU;wBACV,UAAU;wBACV,UAAU;wBACV,WAAW;wBACX,WAAW;wBACX,WAAW;wBACX,WAAW;wBACX,WAAW;wBACX,WAAW;wBACX,aAAa;wBACb,aAAa;wBACb,SAAS;wBACT,UAAU;wBACV/I,gBAAgB;qBAAA;gBAEvB,CAAA;aACJ;YACD;;;OAGG,GACH,aAAa,EAAE;gBAAC;oBAAEgJ,KAAK,EAAE;wBAACjI,MAAM;qBAAA;gBAAC,CAAE;aAAC;YACpC;;;OAGG,GACH,gBAAgB,EAAE;gBAAC;oBAAE,gBAAgB,EAAE;wBAAC,MAAM;wBAAE,MAAM;qBAAA;gBAAC,CAAE;aAAC;YAC1D;;;OAGG,GACHkI,MAAM,EAAE;gBAAC;oBAAEA,MAAM,EAAE;wBAAC,MAAM;wBAAE,GAAG;wBAAE,GAAG;wBAAE,EAAE;qBAAA;iBAAG;aAAC;YAC5C;;;OAGG,GACH,iBAAiB,EAAE;gBAAC;oBAAEC,MAAM,EAAE;wBAAC,MAAM;wBAAE,QAAQ;qBAAA;gBAAC,CAAE;aAAC;YACnD;;;OAGG,GACH,UAAU,EAAE;gBAAC;oBAAE,UAAU,EAAEvG,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACvD;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAEA,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACzD;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAEA,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACzD;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAEA,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACzD;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAEA,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACzD;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAEA,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACzD;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAEA,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACzD;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAEA,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACzD;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAEA,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACzD;;;OAGG,GACH,UAAU,EAAE;gBAAC;oBAAE,UAAU,EAAEA,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACvD;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAEA,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACzD;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAEA,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACzD;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAEA,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACzD;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAEA,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACzD;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAEA,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACzD;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAEA,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACzD;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAEA,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACzD;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAE,WAAW,EAAEA,uBAAuB,CAAE;gBAAA,CAAE;aAAC;YACzD;;;OAGG,GACH,YAAY,EAAE;gBAAC;oBAAEwG,IAAI,EAAE;wBAAC,OAAO;wBAAE,KAAK;wBAAE,QAAQ;wBAAE,YAAY;qBAAA;iBAAG;aAAC;YAClE;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAEA,IAAI,EAAE;wBAAC,QAAQ;wBAAE,QAAQ;qBAAA;gBAAC,CAAE;aAAC;YAC7C;;;OAGG,GACH,WAAW,EAAE;gBAAC;oBAAEA,IAAI,EAAE;wBAAC,MAAM;wBAAE,GAAG;wBAAE,GAAG;wBAAE,MAAM;qBAAA;iBAAG;aAAC;YACnD;;;OAGG,GACH,iBAAiB,EAAE;gBAAC;oBAAEA,IAAI,EAAE;wBAAC,WAAW;wBAAE,WAAW;qBAAA;gBAAC,CAAE;aAAC;YACzD;;;OAGG,GACHC,KAAK,EAAE;gBACH;oBACIA,KAAK,EAAE;wBAAC,MAAM;wBAAE,MAAM;wBAAE,cAAc;qBAAA;gBACzC,CAAA;aACJ;YACD;;;OAGG,GACH,SAAS,EAAE;gBACP;oBACI,WAAW,EAAE;wBAAC,GAAG;wBAAE,MAAM;wBAAE,OAAO;qBAAA;gBACrC,CAAA;aACJ;YACD;;;OAGG,GACH,SAAS,EAAE;gBACP;oBACI,WAAW,EAAE;wBAAC,GAAG;wBAAE,IAAI;wBAAE,MAAM;qBAAA;gBAClC,CAAA;aACJ;YACD;;;OAGG,GACH,UAAU,EAAE;gBAAC,kBAAkB;aAAC;YAChC;;;OAGG,GACHC,MAAM,EAAE;gBAAC;oBAAEA,MAAM,EAAE;wBAAC,MAAM;wBAAE,MAAM;wBAAE,KAAK;wBAAE,MAAM;qBAAA;iBAAG;aAAC;YACrD;;;OAGG,GACH,aAAa,EAAE;gBACX;oBAAE,aAAa,EAAE;wBAAC,MAAM;wBAAE,QAAQ;wBAAE,UAAU;wBAAE,WAAW;wBAAErJ,gBAAgB;qBAAA;gBAAG,CAAA;aACnF;;YAED;;;OAGG,GACHsJ,IAAI,EAAE;gBAAC;oBAAEA,IAAI,EAAE;wBAACvI,MAAM;wBAAE,MAAM;qBAAA;gBAAC,CAAE;aAAC;YAClC;;;OAGG,GACH,UAAU,EAAE;gBAAC;oBAAEwI,MAAM,EAAE;wBAAClK,QAAQ;wBAAEE,iBAAiB;wBAAEK,iBAAiB;qBAAA;iBAAG;aAAC;YAC1E;;;OAGG,GACH2J,MAAM,EAAE;gBAAC;oBAAEA,MAAM,EAAE;wBAACxI,MAAM;wBAAE,MAAM;qBAAA;gBAAC,CAAE;aAAC;;YAEtC;;;OAGG,GACHyI,EAAE,EAAE;gBAAC,SAAS;gBAAE,aAAa;aAAC;YAC9B;;;OAGG,GACH,qBAAqB,EAAE;gBAAC;oBAAE,qBAAqB,EAAE;wBAAC,MAAM;wBAAE,MAAM;qBAAA;gBAAC,CAAE;aAAA;QACtE,CAAA;QACDnT,sBAAsB,EAAE;YACpByN,QAAQ,EAAE;gBAAC,YAAY;gBAAE,YAAY;aAAC;YACtCC,UAAU,EAAE;gBAAC,cAAc;gBAAE,cAAc;aAAC;YAC5CjC,KAAK,EAAE;gBAAC,SAAS;gBAAE,SAAS;gBAAE,OAAO;gBAAE,KAAK;gBAAE,KAAK;gBAAE,OAAO;gBAAE,QAAQ;gBAAE,MAAM;aAAC;YAC/E,SAAS,EAAE;gBAAC,OAAO;gBAAE,MAAM;aAAC;YAC5B,SAAS,EAAE;gBAAC,KAAK;gBAAE,QAAQ;aAAC;YAC5B4C,IAAI,EAAE;gBAAC,OAAO;gBAAE,MAAM;gBAAE,QAAQ;aAAC;YACjC/C,GAAG,EAAE;gBAAC,OAAO;gBAAE,OAAO;aAAC;YACvB0D,CAAC,EAAE;gBAAC,IAAI;gBAAE,IAAI;gBAAE,IAAI;gBAAE,IAAI;gBAAE,IAAI;gBAAE,IAAI;gBAAE,IAAI;gBAAE,IAAI;aAAC;YACnDC,EAAE,EAAE;gBAAC,IAAI;gBAAE,IAAI;aAAC;YAChBC,EAAE,EAAE;gBAAC,IAAI;gBAAE,IAAI;aAAC;YAChBO,CAAC,EAAE;gBAAC,IAAI;gBAAE,IAAI;gBAAE,IAAI;gBAAE,IAAI;gBAAE,IAAI;gBAAE,IAAI;gBAAE,IAAI;gBAAE,IAAI;aAAC;YACnDC,EAAE,EAAE;gBAAC,IAAI;gBAAE,IAAI;aAAC;YAChBC,EAAE,EAAE;gBAAC,IAAI;gBAAE,IAAI;aAAC;YAChBU,IAAI,EAAE;gBAAC,GAAG;gBAAE,GAAG;aAAC;YAChB,WAAW,EAAE;gBAAC,SAAS;aAAC;YACxB,YAAY,EAAE;gBACV,aAAa;gBACb,kBAAkB;gBAClB,YAAY;gBACZ,aAAa;gBACb,cAAc;aACjB;YACD,aAAa,EAAE;gBAAC,YAAY;aAAC;YAC7B,kBAAkB,EAAE;gBAAC,YAAY;aAAC;YAClC,YAAY,EAAE;gBAAC,YAAY;aAAC;YAC5B,aAAa,EAAE;gBAAC,YAAY;aAAC;YAC7B,cAAc,EAAE;gBAAC,YAAY;aAAC;YAC9B,YAAY,EAAE;gBAAC,SAAS;gBAAE,UAAU;aAAC;YACrCkB,OAAO,EAAE;gBACL,WAAW;gBACX,WAAW;gBACX,WAAW;gBACX,WAAW;gBACX,WAAW;gBACX,WAAW;gBACX,YAAY;gBACZ,YAAY;gBACZ,YAAY;gBACZ,YAAY;gBACZ,YAAY;gBACZ,YAAY;gBACZ,YAAY;gBACZ,YAAY;aACf;YACD,WAAW,EAAE;gBAAC,YAAY;gBAAE,YAAY;aAAC;YACzC,WAAW,EAAE;gBAAC,YAAY;gBAAE,YAAY;aAAC;YACzC,WAAW,EAAE;gBAAC,YAAY;gBAAE,YAAY;aAAC;YACzC,WAAW,EAAE;gBAAC,YAAY;gBAAE,YAAY;aAAC;YACzC,WAAW,EAAE;gBAAC,YAAY;gBAAE,YAAY;aAAC;YACzC,WAAW,EAAE;gBAAC,YAAY;gBAAE,YAAY;aAAC;YACzC,gBAAgB,EAAE;gBAAC,kBAAkB;gBAAE,kBAAkB;aAAC;YAC1D,UAAU,EAAE;gBACR,YAAY;gBACZ,YAAY;gBACZ,YAAY;gBACZ,YAAY;gBACZ,YAAY;gBACZ,YAAY;aACf;YACD,YAAY,EAAE;gBAAC,YAAY;gBAAE,YAAY;aAAC;YAC1C,YAAY,EAAE;gBAAC,YAAY;gBAAE,YAAY;aAAC;YAC1C,cAAc,EAAE;gBACZ,gBAAgB;gBAChB,gBAAgB;gBAChB,gBAAgB;gBAChB,gBAAgB;gBAChB,gBAAgB;gBAChB,gBAAgB;aACnB;YACD,gBAAgB,EAAE;gBAAC,gBAAgB;gBAAE,gBAAgB;aAAC;YACtD,gBAAgB,EAAE;gBAAC,gBAAgB;gBAAE,gBAAgB;aAAC;YACtD,UAAU,EAAE;gBACR,WAAW;gBACX,WAAW;gBACX,WAAW;gBACX,WAAW;gBACX,WAAW;gBACX,WAAW;gBACX,WAAW;gBACX,WAAW;aACd;YACD,WAAW,EAAE;gBAAC,WAAW;gBAAE,WAAW;aAAC;YACvC,WAAW,EAAE;gBAAC,WAAW;gBAAE,WAAW;aAAC;YACvC,UAAU,EAAE;gBACR,WAAW;gBACX,WAAW;gBACX,WAAW;gBACX,WAAW;gBACX,WAAW;gBACX,WAAW;gBACX,WAAW;gBACX,WAAW;aACd;YACD,WAAW,EAAE;gBAAC,WAAW;gBAAE,WAAW;aAAC;YACvC,WAAW,EAAE;gBAAC,WAAW;gBAAE,WAAW;aAAC;YACvCwB,KAAK,EAAE;gBAAC,SAAS;gBAAE,SAAS;gBAAE,UAAU;aAAC;YACzC,SAAS,EAAE;gBAAC,OAAO;aAAC;YACpB,SAAS,EAAE;gBAAC,OAAO;aAAC;YACpB,UAAU,EAAE;gBAAC,OAAO;aAAA;QACvB,CAAA;QACD9S,8BAA8B,EAAE;YAC5B,WAAW,EAAE;gBAAC,SAAS;aAAA;QAC1B;IACkE,CAAA;AAC3E,CAAA;ACj1DA;;;CAGG,GACU,MAAAmT,YAAY,GAAGA,CACxBC,UAAqB,EACrB,EACIrP,SAAS,EACT7B,MAAM,EACNmC,SAAS,EACTC,0BAA0B,EAC1B+O,MAAM,GAAG,CAAA,CAAE,EACXC,QAAQ,GAAG,CAAE,CAAA,EAC+B,KAChD;IACAC,gBAAgB,CAACH,UAAU,EAAE,WAAW,EAAErP,SAAS,CAAC;IACpDwP,gBAAgB,CAACH,UAAU,EAAE,QAAQ,EAAElR,MAAM,CAAC;IAC9CqR,gBAAgB,CAACH,UAAU,EAAE,WAAW,EAAE/O,SAAS,CAAC;IACpDkP,gBAAgB,CAACH,UAAU,EAAE,4BAA4B,EAAE9O,0BAA0B,CAAC;IAEtF,IAAK,MAAMkP,SAAS,IAAIF,QAAQ,CAAE;QAC9BG,wBAAwB,CACpBL,UAAU,CAACI,SAAkC,CAAC,EAC9CF,QAAQ,CAACE,SAAkC,CAAC,CAC/C;;IAGL,IAAK,MAAMvQ,GAAG,IAAIoQ,MAAM,CAAE;QACtBK,qBAAqB,CACjBN,UAAU,CAACnQ,GAA0B,CAAC,EACtCoQ,MAAM,CAACpQ,GAA0B,CAAC,CACrC;;IAGL,OAAOmQ,UAAU;AACrB,CAAA;AAEA,MAAMG,gBAAgB,GAAGA,CACrBI,UAAa,EACbC,WAAc,EACdC,aAA+B,KAC/B;IACA,IAAIA,aAAa,KAAKzS,SAAS,EAAE;QAC7BuS,UAAU,CAACC,WAAW,CAAC,GAAGC,aAAa;;AAE/C,CAAC;AAED,MAAMJ,wBAAwB,GAAGA,CAC7BE,UAAuD,EACvDG,cAAuE,KACvE;IACA,IAAIA,cAAc,EAAE;QAChB,IAAK,MAAM7Q,GAAG,IAAI6Q,cAAc,CAAE;YAC9BP,gBAAgB,CAACI,UAAU,EAAE1Q,GAAG,EAAE6Q,cAAc,CAAC7Q,GAAG,CAAC,CAAC;;;AAGlE,CAAC;AAED,MAAMyQ,qBAAqB,GAAGA,CAC1BC,UAAuD,EACvDI,WAAoE,KACpE;IACA,IAAIA,WAAW,EAAE;QACb,IAAK,MAAM9Q,GAAG,IAAI8Q,WAAW,CAAE;YAC3B,MAAMC,UAAU,GAAGD,WAAW,CAAC9Q,GAAG,CAAC;YAEnC,IAAI+Q,UAAU,KAAK5S,SAAS,EAAE;gBAC1BuS,UAAU,CAAC1Q,GAAG,CAAC,GAAG,CAAC0Q,UAAU,CAAC1Q,GAAG,CAAC,IAAI,EAAE,EAAEgR,MAAM,CAACD,UAAU,CAAC;;;;AAI5E,CAAC;AClEM,MAAME,mBAAmB,GAAGA,CAI/BC,eAK4B,EAC5B,GAAGC,YAAsC,GAEzC,OAAOD,eAAe,KAAK,UAAA,GACrB7M,mBAAmB,CAACkD,gBAAgB,EAAE2J,eAAe,EAAE,GAAGC,YAAY,CAAA,GACtE9M,mBAAmB,CACf,IAAM6L,YAAY,CAAC3I,gBAAgB,CAAE,CAAA,EAAE2J,eAAe,CAAC,EACvD,GAAGC,YAAY,CAAA;MCpBhBC,OAAO,GAAA,WAAA,GAAG/M,mBAAmB,CAACkD,gBAAgB,CAAA", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]}}, {"offset": {"line": 4123, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4129, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/class-variance-authority/dist/index.mjs"], "sourcesContent": ["/**\n * Copyright 2022 Joe Bell. All rights reserved.\n *\n * This file is licensed to you under the Apache License, Version 2.0\n * (the \"License\"); you may not use this file except in compliance with the\n * License. You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS, WITHOUT\n * WARRANTIES OR REPRESENTATIONS OF ANY KIND, either express or implied. See the\n * License for the specific language governing permissions and limitations under\n * the License.\n */ import { clsx } from \"clsx\";\nconst falsyToString = (value)=>typeof value === \"boolean\" ? `${value}` : value === 0 ? \"0\" : value;\nexport const cx = clsx;\nexport const cva = (base, config)=>(props)=>{\n        var _config_compoundVariants;\n        if ((config === null || config === void 0 ? void 0 : config.variants) == null) return cx(base, props === null || props === void 0 ? void 0 : props.class, props === null || props === void 0 ? void 0 : props.className);\n        const { variants, defaultVariants } = config;\n        const getVariantClassNames = Object.keys(variants).map((variant)=>{\n            const variantProp = props === null || props === void 0 ? void 0 : props[variant];\n            const defaultVariantProp = defaultVariants === null || defaultVariants === void 0 ? void 0 : defaultVariants[variant];\n            if (variantProp === null) return null;\n            const variantKey = falsyToString(variantProp) || falsyToString(defaultVariantProp);\n            return variants[variant][variantKey];\n        });\n        const propsWithoutUndefined = props && Object.entries(props).reduce((acc, param)=>{\n            let [key, value] = param;\n            if (value === undefined) {\n                return acc;\n            }\n            acc[key] = value;\n            return acc;\n        }, {});\n        const getCompoundVariantClassNames = config === null || config === void 0 ? void 0 : (_config_compoundVariants = config.compoundVariants) === null || _config_compoundVariants === void 0 ? void 0 : _config_compoundVariants.reduce((acc, param)=>{\n            let { class: cvClass, className: cvClassName, ...compoundVariantOptions } = param;\n            return Object.entries(compoundVariantOptions).every((param)=>{\n                let [key, value] = param;\n                return Array.isArray(value) ? value.includes({\n                    ...defaultVariants,\n                    ...propsWithoutUndefined\n                }[key]) : ({\n                    ...defaultVariants,\n                    ...propsWithoutUndefined\n                })[key] === value;\n            }) ? [\n                ...acc,\n                cvClass,\n                cvClassName\n            ] : acc;\n        }, []);\n        return cx(base, getVariantClassNames, getCompoundVariantClassNames, props === null || props === void 0 ? void 0 : props.class, props === null || props === void 0 ? void 0 : props.className);\n    };\n\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC;;;;AAAG;;AACJ,MAAM,gBAAgB,CAAC,QAAQ,OAAO,UAAU,YAAY,GAAG,OAAO,GAAG,UAAU,IAAI,MAAM;AACtF,MAAM,KAAK,qIAAA,CAAA,OAAI;AACf,MAAM,MAAM,CAAC,MAAM,SAAS,CAAC;QAC5B,IAAI;QACJ,IAAI,CAAC,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,QAAQ,KAAK,MAAM,OAAO,GAAG,MAAM,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,MAAM,KAAK,EAAE,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,MAAM,SAAS;QACvN,MAAM,EAAE,QAAQ,EAAE,eAAe,EAAE,GAAG;QACtC,MAAM,uBAAuB,OAAO,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;YACpD,MAAM,cAAc,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,KAAK,CAAC,QAAQ;YAChF,MAAM,qBAAqB,oBAAoB,QAAQ,oBAAoB,KAAK,IAAI,KAAK,IAAI,eAAe,CAAC,QAAQ;YACrH,IAAI,gBAAgB,MAAM,OAAO;YACjC,MAAM,aAAa,cAAc,gBAAgB,cAAc;YAC/D,OAAO,QAAQ,CAAC,QAAQ,CAAC,WAAW;QACxC;QACA,MAAM,wBAAwB,SAAS,OAAO,OAAO,CAAC,OAAO,MAAM,CAAC,CAAC,KAAK;YACtE,IAAI,CAAC,KAAK,MAAM,GAAG;YACnB,IAAI,UAAU,WAAW;gBACrB,OAAO;YACX;YACA,GAAG,CAAC,IAAI,GAAG;YACX,OAAO;QACX,GAAG,CAAC;QACJ,MAAM,+BAA+B,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,CAAC,2BAA2B,OAAO,gBAAgB,MAAM,QAAQ,6BAA6B,KAAK,IAAI,KAAK,IAAI,yBAAyB,MAAM,CAAC,CAAC,KAAK;YACvO,IAAI,EAAE,OAAO,OAAO,EAAE,WAAW,WAAW,EAAE,GAAG,wBAAwB,GAAG;YAC5E,OAAO,OAAO,OAAO,CAAC,wBAAwB,KAAK,CAAC,CAAC;gBACjD,IAAI,CAAC,KAAK,MAAM,GAAG;gBACnB,OAAO,MAAM,OAAO,CAAC,SAAS,MAAM,QAAQ,CAAC;oBACzC,GAAG,eAAe;oBAClB,GAAG,qBAAqB;gBAC5B,CAAC,CAAC,IAAI,IAAI,CAAC;oBACP,GAAG,eAAe;oBAClB,GAAG,qBAAqB;gBAC5B,CAAC,CAAC,CAAC,IAAI,KAAK;YAChB,KAAK;mBACE;gBACH;gBACA;aACH,GAAG;QACR,GAAG,EAAE;QACL,OAAO,GAAG,MAAM,sBAAsB,8BAA8B,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,MAAM,KAAK,EAAE,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,MAAM,SAAS;IAChM", "ignoreList": [0]}}, {"offset": {"line": 4189, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4195, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/%40radix-ui/react-compose-refs/src/compose-refs.tsx"], "sourcesContent": ["import * as React from 'react';\n\ntype PossibleRef<T> = React.Ref<T> | undefined;\n\n/**\n * Set a given ref to a given value\n * This utility takes care of different types of refs: callback refs and RefObject(s)\n */\nfunction setRef<T>(ref: PossibleRef<T>, value: T) {\n  if (typeof ref === 'function') {\n    return ref(value);\n  } else if (ref !== null && ref !== undefined) {\n    ref.current = value;\n  }\n}\n\n/**\n * A utility to compose multiple refs together\n * Accepts callback refs and RefObject(s)\n */\nfunction composeRefs<T>(...refs: PossibleRef<T>[]): React.RefCallback<T> {\n  return (node) => {\n    let hasCleanup = false;\n    const cleanups = refs.map((ref) => {\n      const cleanup = setRef(ref, node);\n      if (!hasCleanup && typeof cleanup == 'function') {\n        hasCleanup = true;\n      }\n      return cleanup;\n    });\n\n    // React <19 will log an error to the console if a callback ref returns a\n    // value. We don't use ref cleanups internally so this will only happen if a\n    // user's ref callback returns a value, which we only expect if they are\n    // using the cleanup functionality added in React 19.\n    if (hasCleanup) {\n      return () => {\n        for (let i = 0; i < cleanups.length; i++) {\n          const cleanup = cleanups[i];\n          if (typeof cleanup == 'function') {\n            cleanup();\n          } else {\n            setRef(refs[i], null);\n          }\n        }\n      };\n    }\n  };\n}\n\n/**\n * A custom hook that composes multiple refs\n * Accepts callback refs and RefObject(s)\n */\nfunction useComposedRefs<T>(...refs: PossibleRef<T>[]): React.RefCallback<T> {\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  return React.useCallback(composeRefs(...refs), refs);\n}\n\nexport { composeRefs, useComposedRefs };\n"], "names": [], "mappings": ";;;;;AAAA,YAAY,WAAW;;AAQvB,SAAS,OAAU,GAAA,EAAqB,KAAA,EAAU;IAChD,IAAI,OAAO,QAAQ,YAAY;QAC7B,OAAO,IAAI,KAAK;IAClB,OAAA,IAAW,QAAQ,QAAQ,QAAQ,KAAA,GAAW;QAC5C,IAAI,OAAA,GAAU;IAChB;AACF;AAMA,SAAS,YAAA,GAAkB,IAAA,EAA8C;IACvE,OAAO,CAAC,SAAS;QACf,IAAI,aAAa;QACjB,MAAM,WAAW,KAAK,GAAA,CAAI,CAAC,QAAQ;YACjC,MAAM,UAAU,OAAO,KAAK,IAAI;YAChC,IAAI,CAAC,cAAc,OAAO,WAAW,YAAY;gBAC/C,aAAa;YACf;YACA,OAAO;QACT,CAAC;QAMD,IAAI,YAAY;YACd,OAAO,MAAM;gBACX,IAAA,IAAS,IAAI,GAAG,IAAI,SAAS,MAAA,EAAQ,IAAK;oBACxC,MAAM,UAAU,QAAA,CAAS,CAAC,CAAA;oBAC1B,IAAI,OAAO,WAAW,YAAY;wBAChC,QAAQ;oBACV,OAAO;wBACL,OAAO,IAAA,CAAK,CAAC,CAAA,EAAG,IAAI;oBACtB;gBACF;YACF;QACF;IACF;AACF;AAMA,SAAS,gBAAA,GAAsB,IAAA,EAA8C;IAE3E,OAAa,sMAAA,WAAA,CAAY,YAAY,GAAG,IAAI,GAAG,IAAI;AACrD", "ignoreList": [0]}}, {"offset": {"line": 4238, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4244, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/%40radix-ui/react-slot/src/slot.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { composeRefs } from '@radix-ui/react-compose-refs';\n\n/* -------------------------------------------------------------------------------------------------\n * Slot\n * -----------------------------------------------------------------------------------------------*/\n\ninterface SlotProps extends React.HTMLAttributes<HTMLElement> {\n  children?: React.ReactNode;\n}\n\n/* @__NO_SIDE_EFFECTS__ */ export function createSlot(ownerName: string) {\n  const SlotClone = createSlotClone(ownerName);\n  const Slot = React.forwardRef<HTMLElement, SlotProps>((props, forwardedRef) => {\n    const { children, ...slotProps } = props;\n    const childrenArray = React.Children.toArray(children);\n    const slottable = childrenArray.find(isSlottable);\n\n    if (slottable) {\n      // the new element to render is the one passed as a child of `Slottable`\n      const newElement = slottable.props.children;\n\n      const newChildren = childrenArray.map((child) => {\n        if (child === slottable) {\n          // because the new element will be the one rendered, we are only interested\n          // in grabbing its children (`newElement.props.children`)\n          if (React.Children.count(newElement) > 1) return React.Children.only(null);\n          return React.isValidElement(newElement)\n            ? (newElement.props as { children: React.ReactNode }).children\n            : null;\n        } else {\n          return child;\n        }\n      });\n\n      return (\n        <SlotClone {...slotProps} ref={forwardedRef}>\n          {React.isValidElement(newElement)\n            ? React.cloneElement(newElement, undefined, newChildren)\n            : null}\n        </SlotClone>\n      );\n    }\n\n    return (\n      <SlotClone {...slotProps} ref={forwardedRef}>\n        {children}\n      </SlotClone>\n    );\n  });\n\n  Slot.displayName = `${ownerName}.Slot`;\n  return Slot;\n}\n\nconst Slot = createSlot('Slot');\n\n/* -------------------------------------------------------------------------------------------------\n * SlotClone\n * -----------------------------------------------------------------------------------------------*/\n\ninterface SlotCloneProps {\n  children: React.ReactNode;\n}\n\n/* @__NO_SIDE_EFFECTS__ */ function createSlotClone(ownerName: string) {\n  const SlotClone = React.forwardRef<any, SlotCloneProps>((props, forwardedRef) => {\n    const { children, ...slotProps } = props;\n\n    if (React.isValidElement(children)) {\n      const childrenRef = getElementRef(children);\n      const props = mergeProps(slotProps, children.props as AnyProps);\n      // do not pass ref to React.Fragment for React 19 compatibility\n      if (children.type !== React.Fragment) {\n        props.ref = forwardedRef ? composeRefs(forwardedRef, childrenRef) : childrenRef;\n      }\n      return React.cloneElement(children, props);\n    }\n\n    return React.Children.count(children) > 1 ? React.Children.only(null) : null;\n  });\n\n  SlotClone.displayName = `${ownerName}.SlotClone`;\n  return SlotClone;\n}\n\n/* -------------------------------------------------------------------------------------------------\n * Slottable\n * -----------------------------------------------------------------------------------------------*/\n\nconst SLOTTABLE_IDENTIFIER = Symbol('radix.slottable');\n\ninterface SlottableProps {\n  children: React.ReactNode;\n}\n\ninterface SlottableComponent extends React.FC<SlottableProps> {\n  __radixId: symbol;\n}\n\n/* @__NO_SIDE_EFFECTS__ */ export function createSlottable(ownerName: string) {\n  const Slottable: SlottableComponent = ({ children }) => {\n    return <>{children}</>;\n  };\n  Slottable.displayName = `${ownerName}.Slottable`;\n  Slottable.__radixId = SLOTTABLE_IDENTIFIER;\n  return Slottable;\n}\n\nconst Slottable = createSlottable('Slottable');\n\n/* ---------------------------------------------------------------------------------------------- */\n\ntype AnyProps = Record<string, any>;\n\nfunction isSlottable(\n  child: React.ReactNode\n): child is React.ReactElement<SlottableProps, typeof Slottable> {\n  return (\n    React.isValidElement(child) &&\n    typeof child.type === 'function' &&\n    '__radixId' in child.type &&\n    child.type.__radixId === SLOTTABLE_IDENTIFIER\n  );\n}\n\nfunction mergeProps(slotProps: AnyProps, childProps: AnyProps) {\n  // all child props should override\n  const overrideProps = { ...childProps };\n\n  for (const propName in childProps) {\n    const slotPropValue = slotProps[propName];\n    const childPropValue = childProps[propName];\n\n    const isHandler = /^on[A-Z]/.test(propName);\n    if (isHandler) {\n      // if the handler exists on both, we compose them\n      if (slotPropValue && childPropValue) {\n        overrideProps[propName] = (...args: unknown[]) => {\n          const result = childPropValue(...args);\n          slotPropValue(...args);\n          return result;\n        };\n      }\n      // but if it exists only on the slot, we use only this one\n      else if (slotPropValue) {\n        overrideProps[propName] = slotPropValue;\n      }\n    }\n    // if it's `style`, we merge them\n    else if (propName === 'style') {\n      overrideProps[propName] = { ...slotPropValue, ...childPropValue };\n    } else if (propName === 'className') {\n      overrideProps[propName] = [slotPropValue, childPropValue].filter(Boolean).join(' ');\n    }\n  }\n\n  return { ...slotProps, ...overrideProps };\n}\n\n// Before React 19 accessing `element.props.ref` will throw a warning and suggest using `element.ref`\n// After React 19 accessing `element.ref` does the opposite.\n// https://github.com/facebook/react/pull/28348\n//\n// Access the ref using the method that doesn't yield a warning.\nfunction getElementRef(element: React.ReactElement) {\n  // React <=18 in DEV\n  let getter = Object.getOwnPropertyDescriptor(element.props, 'ref')?.get;\n  let mayWarn = getter && 'isReactWarning' in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return (element as any).ref;\n  }\n\n  // React 19 in DEV\n  getter = Object.getOwnPropertyDescriptor(element, 'ref')?.get;\n  mayWarn = getter && 'isReactWarning' in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return (element.props as { ref?: React.Ref<unknown> }).ref;\n  }\n\n  // Not DEV\n  return (element.props as { ref?: React.Ref<unknown> }).ref || (element as any).ref;\n}\n\nexport {\n  Slot,\n  Slottable,\n  //\n  Slot as Root,\n};\nexport type { SlotProps };\n"], "names": ["Fragment", "Slot", "props", "Slottable"], "mappings": ";;;;;;;;AAAA,YAAY,WAAW;AAoCf,SAkEG,YAAAA,WAlEH;AAnCR,SAAS,mBAAmB;;;;AAmCpB,uBAAA;AAzB0B,SAAS,WAAW,SAAA,EAAmB;IACvE,MAAM,YAAY,aAAA,GAAA,gBAAgB,SAAS;IAC3C,MAAMC,QAAa,sMAAA,UAAA,CAAmC,CAAC,OAAO,iBAAiB;QAC7E,MAAM,EAAE,QAAA,EAAU,GAAG,UAAU,CAAA,GAAI;QACnC,MAAM,gBAAsB,sMAAA,QAAA,CAAS,OAAA,CAAQ,QAAQ;QACrD,MAAM,YAAY,cAAc,IAAA,CAAK,WAAW;QAEhD,IAAI,WAAW;YAEb,MAAM,aAAa,UAAU,KAAA,CAAM,QAAA;YAEnC,MAAM,cAAc,cAAc,GAAA,CAAI,CAAC,UAAU;gBAC/C,IAAI,UAAU,WAAW;oBAGvB,IAAU,sMAAA,QAAA,CAAS,KAAA,CAAM,UAAU,IAAI,EAAG,CAAA,OAAa,sMAAA,QAAA,CAAS,IAAA,CAAK,IAAI;oBACzE,OAAa,sMAAA,cAAA,CAAe,UAAU,IACjC,WAAW,KAAA,CAAwC,QAAA,GACpD;gBACN,OAAO;oBACL,OAAO;gBACT;YACF,CAAC;YAED,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,WAAA;gBAAW,GAAG,SAAA;gBAAW,KAAK;gBAC5B,UAAM,sMAAA,cAAA,CAAe,UAAU,IACtB,sMAAA,YAAA,CAAa,YAAY,KAAA,GAAW,WAAW,IACrD;YAAA,CACN;QAEJ;QAEA,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,WAAA;YAAW,GAAG,SAAA;YAAW,KAAK;YAC5B;QAAA,CACH;IAEJ,CAAC;IAEDA,MAAK,WAAA,GAAc,GAAG,SAAS,CAAA,KAAA,CAAA;IAC/B,OAAOA;AACT;AAEA,IAAM,OAAO,aAAA,GAAA,WAAW,MAAM;AAAA,uBAAA;AAUH,SAAS,gBAAgB,SAAA,EAAmB;IACrE,MAAM,YAAkB,sMAAA,UAAA,CAAgC,CAAC,OAAO,iBAAiB;QAC/E,MAAM,EAAE,QAAA,EAAU,GAAG,UAAU,CAAA,GAAI;QAEnC,IAAU,sMAAA,cAAA,CAAe,QAAQ,GAAG;YAClC,MAAM,cAAc,cAAc,QAAQ;YAC1C,MAAMC,SAAQ,WAAW,WAAW,SAAS,KAAiB;YAE9D,IAAI,SAAS,IAAA,KAAe,sMAAA,QAAA,EAAU;gBACpCA,OAAM,GAAA,GAAM,+LAAe,cAAA,EAAY,cAAc,WAAW,IAAI;YACtE;YACA,OAAa,sMAAA,YAAA,CAAa,UAAUA,MAAK;QAC3C;QAEA,OAAa,sMAAA,QAAA,CAAS,KAAA,CAAM,QAAQ,IAAI,IAAU,sMAAA,QAAA,CAAS,IAAA,CAAK,IAAI,IAAI;IAC1E,CAAC;IAED,UAAU,WAAA,GAAc,GAAG,SAAS,CAAA,UAAA,CAAA;IACpC,OAAO;AACT;AAMA,IAAM,uBAAuB,OAAO,iBAAiB;AAAA,uBAAA;AAUnB,SAAS,gBAAgB,SAAA,EAAmB;IAC5E,MAAMC,aAAgC,CAAC,EAAE,QAAA,CAAS,CAAA,KAAM;QACtD,OAAO,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,0NAAAH,WAAAA,EAAA;YAAG;QAAA,CAAS;IACrB;IACAG,WAAU,WAAA,GAAc,GAAG,SAAS,CAAA,UAAA,CAAA;IACpCA,WAAU,SAAA,GAAY;IACtB,OAAOA;AACT;AAEA,IAAM,YAAY,aAAA,GAAA,gBAAgB,WAAW;AAM7C,SAAS,YACP,KAAA,EAC+D;IAC/D,OACQ,sMAAA,cAAA,CAAe,KAAK,KAC1B,OAAO,MAAM,IAAA,KAAS,cACtB,eAAe,MAAM,IAAA,IACrB,MAAM,IAAA,CAAK,SAAA,KAAc;AAE7B;AAEA,SAAS,WAAW,SAAA,EAAqB,UAAA,EAAsB;IAE7D,MAAM,gBAAgB;QAAE,GAAG,UAAA;IAAW;IAEtC,IAAA,MAAW,YAAY,WAAY;QACjC,MAAM,gBAAgB,SAAA,CAAU,QAAQ,CAAA;QACxC,MAAM,iBAAiB,UAAA,CAAW,QAAQ,CAAA;QAE1C,MAAM,YAAY,WAAW,IAAA,CAAK,QAAQ;QAC1C,IAAI,WAAW;YAEb,IAAI,iBAAiB,gBAAgB;gBACnC,aAAA,CAAc,QAAQ,CAAA,GAAI,CAAA,GAAI,SAAoB;oBAChD,MAAM,SAAS,eAAe,GAAG,IAAI;oBACrC,cAAc,GAAG,IAAI;oBACrB,OAAO;gBACT;YACF,OAAA,IAES,eAAe;gBACtB,aAAA,CAAc,QAAQ,CAAA,GAAI;YAC5B;QACF,OAAA,IAES,aAAa,SAAS;YAC7B,aAAA,CAAc,QAAQ,CAAA,GAAI;gBAAE,GAAG,aAAA;gBAAe,GAAG,cAAA;YAAe;QAClE,OAAA,IAAW,aAAa,aAAa;YACnC,aAAA,CAAc,QAAQ,CAAA,GAAI;gBAAC;gBAAe,cAAc;aAAA,CAAE,MAAA,CAAO,OAAO,EAAE,IAAA,CAAK,GAAG;QACpF;IACF;IAEA,OAAO;QAAE,GAAG,SAAA;QAAW,GAAG,aAAA;IAAc;AAC1C;AAOA,SAAS,cAAc,OAAA,EAA6B;IAElD,IAAI,SAAS,OAAO,wBAAA,CAAyB,QAAQ,KAAA,EAAO,KAAK,GAAG;IACpE,IAAI,UAAU,UAAU,oBAAoB,UAAU,OAAO,cAAA;IAC7D,IAAI,SAAS;QACX,OAAQ,QAAgB,GAAA;IAC1B;IAGA,SAAS,OAAO,wBAAA,CAAyB,SAAS,KAAK,GAAG;IAC1D,UAAU,UAAU,oBAAoB,UAAU,OAAO,cAAA;IACzD,IAAI,SAAS;QACX,OAAQ,QAAQ,KAAA,CAAuC,GAAA;IACzD;IAGA,OAAQ,QAAQ,KAAA,CAAuC,GAAA,IAAQ,QAAgB,GAAA;AACjF", "ignoreList": [0]}}, {"offset": {"line": 4374, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4390, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/%40radix-ui/react-navigation-menu/dist/index.mjs/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Content = registerClientReference(\n    function() { throw new Error(\"Attempted to call Content() from the server but Content is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@radix-ui/react-navigation-menu/dist/index.mjs <module evaluation>\",\n    \"Content\",\n);\nexport const Indicator = registerClientReference(\n    function() { throw new Error(\"Attempted to call Indicator() from the server but Indicator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@radix-ui/react-navigation-menu/dist/index.mjs <module evaluation>\",\n    \"Indicator\",\n);\nexport const Item = registerClientReference(\n    function() { throw new Error(\"Attempted to call Item() from the server but Item is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@radix-ui/react-navigation-menu/dist/index.mjs <module evaluation>\",\n    \"Item\",\n);\nexport const Link = registerClientReference(\n    function() { throw new Error(\"Attempted to call Link() from the server but Link is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@radix-ui/react-navigation-menu/dist/index.mjs <module evaluation>\",\n    \"Link\",\n);\nexport const List = registerClientReference(\n    function() { throw new Error(\"Attempted to call List() from the server but List is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@radix-ui/react-navigation-menu/dist/index.mjs <module evaluation>\",\n    \"List\",\n);\nexport const NavigationMenu = registerClientReference(\n    function() { throw new Error(\"Attempted to call NavigationMenu() from the server but NavigationMenu is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@radix-ui/react-navigation-menu/dist/index.mjs <module evaluation>\",\n    \"NavigationMenu\",\n);\nexport const NavigationMenuContent = registerClientReference(\n    function() { throw new Error(\"Attempted to call NavigationMenuContent() from the server but NavigationMenuContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@radix-ui/react-navigation-menu/dist/index.mjs <module evaluation>\",\n    \"NavigationMenuContent\",\n);\nexport const NavigationMenuIndicator = registerClientReference(\n    function() { throw new Error(\"Attempted to call NavigationMenuIndicator() from the server but NavigationMenuIndicator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@radix-ui/react-navigation-menu/dist/index.mjs <module evaluation>\",\n    \"NavigationMenuIndicator\",\n);\nexport const NavigationMenuItem = registerClientReference(\n    function() { throw new Error(\"Attempted to call NavigationMenuItem() from the server but NavigationMenuItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@radix-ui/react-navigation-menu/dist/index.mjs <module evaluation>\",\n    \"NavigationMenuItem\",\n);\nexport const NavigationMenuLink = registerClientReference(\n    function() { throw new Error(\"Attempted to call NavigationMenuLink() from the server but NavigationMenuLink is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@radix-ui/react-navigation-menu/dist/index.mjs <module evaluation>\",\n    \"NavigationMenuLink\",\n);\nexport const NavigationMenuList = registerClientReference(\n    function() { throw new Error(\"Attempted to call NavigationMenuList() from the server but NavigationMenuList is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@radix-ui/react-navigation-menu/dist/index.mjs <module evaluation>\",\n    \"NavigationMenuList\",\n);\nexport const NavigationMenuSub = registerClientReference(\n    function() { throw new Error(\"Attempted to call NavigationMenuSub() from the server but NavigationMenuSub is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@radix-ui/react-navigation-menu/dist/index.mjs <module evaluation>\",\n    \"NavigationMenuSub\",\n);\nexport const NavigationMenuTrigger = registerClientReference(\n    function() { throw new Error(\"Attempted to call NavigationMenuTrigger() from the server but NavigationMenuTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@radix-ui/react-navigation-menu/dist/index.mjs <module evaluation>\",\n    \"NavigationMenuTrigger\",\n);\nexport const NavigationMenuViewport = registerClientReference(\n    function() { throw new Error(\"Attempted to call NavigationMenuViewport() from the server but NavigationMenuViewport is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@radix-ui/react-navigation-menu/dist/index.mjs <module evaluation>\",\n    \"NavigationMenuViewport\",\n);\nexport const Root = registerClientReference(\n    function() { throw new Error(\"Attempted to call Root() from the server but Root is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@radix-ui/react-navigation-menu/dist/index.mjs <module evaluation>\",\n    \"Root\",\n);\nexport const Sub = registerClientReference(\n    function() { throw new Error(\"Attempted to call Sub() from the server but Sub is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@radix-ui/react-navigation-menu/dist/index.mjs <module evaluation>\",\n    \"Sub\",\n);\nexport const Trigger = registerClientReference(\n    function() { throw new Error(\"Attempted to call Trigger() from the server but Trigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@radix-ui/react-navigation-menu/dist/index.mjs <module evaluation>\",\n    \"Trigger\",\n);\nexport const Viewport = registerClientReference(\n    function() { throw new Error(\"Attempted to call Viewport() from the server but Viewport is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@radix-ui/react-navigation-menu/dist/index.mjs <module evaluation>\",\n    \"Viewport\",\n);\nexport const createNavigationMenuScope = registerClientReference(\n    function() { throw new Error(\"Attempted to call createNavigationMenuScope() from the server but createNavigationMenuScope is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@radix-ui/react-navigation-menu/dist/index.mjs <module evaluation>\",\n    \"createNavigationMenuScope\",\n);\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA;;AACO,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,6FACA;AAEG,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,6FACA;AAEG,MAAM,OAAO,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,6FACA;AAEG,MAAM,OAAO,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,6FACA;AAEG,MAAM,OAAO,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,6FACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,6FACA;AAEG,MAAM,wBAAwB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACvD;IAAa,MAAM,IAAI,MAAM;AAA0P,GACvR,6FACA;AAEG,MAAM,0BAA0B,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzD;IAAa,MAAM,IAAI,MAAM;AAA8P,GAC3R,6FACA;AAEG,MAAM,qBAAqB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,6FACA;AAEG,MAAM,qBAAqB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,6FACA;AAEG,MAAM,qBAAqB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,6FACA;AAEG,MAAM,oBAAoB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,6FACA;AAEG,MAAM,wBAAwB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACvD;IAAa,MAAM,IAAI,MAAM;AAA0P,GACvR,6FACA;AAEG,MAAM,yBAAyB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxD;IAAa,MAAM,IAAI,MAAM;AAA4P,GACzR,6FACA;AAEG,MAAM,OAAO,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,6FACA;AAEG,MAAM,MAAM,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACrC;IAAa,MAAM,IAAI,MAAM;AAAsN,GACnP,6FACA;AAEG,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,6FACA;AAEG,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,6FACA;AAEG,MAAM,4BAA4B,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3D;IAAa,MAAM,IAAI,MAAM;AAAkQ,GAC/R,6FACA", "ignoreList": [0]}}, {"offset": {"line": 4470, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4476, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/%40radix-ui/react-navigation-menu/dist/index.mjs/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Content = registerClientReference(\n    function() { throw new Error(\"Attempted to call Content() from the server but Content is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@radix-ui/react-navigation-menu/dist/index.mjs\",\n    \"Content\",\n);\nexport const Indicator = registerClientReference(\n    function() { throw new Error(\"Attempted to call Indicator() from the server but Indicator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@radix-ui/react-navigation-menu/dist/index.mjs\",\n    \"Indicator\",\n);\nexport const Item = registerClientReference(\n    function() { throw new Error(\"Attempted to call Item() from the server but Item is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@radix-ui/react-navigation-menu/dist/index.mjs\",\n    \"Item\",\n);\nexport const Link = registerClientReference(\n    function() { throw new Error(\"Attempted to call Link() from the server but Link is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@radix-ui/react-navigation-menu/dist/index.mjs\",\n    \"Link\",\n);\nexport const List = registerClientReference(\n    function() { throw new Error(\"Attempted to call List() from the server but List is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@radix-ui/react-navigation-menu/dist/index.mjs\",\n    \"List\",\n);\nexport const NavigationMenu = registerClientReference(\n    function() { throw new Error(\"Attempted to call NavigationMenu() from the server but NavigationMenu is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@radix-ui/react-navigation-menu/dist/index.mjs\",\n    \"NavigationMenu\",\n);\nexport const NavigationMenuContent = registerClientReference(\n    function() { throw new Error(\"Attempted to call NavigationMenuContent() from the server but NavigationMenuContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@radix-ui/react-navigation-menu/dist/index.mjs\",\n    \"NavigationMenuContent\",\n);\nexport const NavigationMenuIndicator = registerClientReference(\n    function() { throw new Error(\"Attempted to call NavigationMenuIndicator() from the server but NavigationMenuIndicator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@radix-ui/react-navigation-menu/dist/index.mjs\",\n    \"NavigationMenuIndicator\",\n);\nexport const NavigationMenuItem = registerClientReference(\n    function() { throw new Error(\"Attempted to call NavigationMenuItem() from the server but NavigationMenuItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@radix-ui/react-navigation-menu/dist/index.mjs\",\n    \"NavigationMenuItem\",\n);\nexport const NavigationMenuLink = registerClientReference(\n    function() { throw new Error(\"Attempted to call NavigationMenuLink() from the server but NavigationMenuLink is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@radix-ui/react-navigation-menu/dist/index.mjs\",\n    \"NavigationMenuLink\",\n);\nexport const NavigationMenuList = registerClientReference(\n    function() { throw new Error(\"Attempted to call NavigationMenuList() from the server but NavigationMenuList is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@radix-ui/react-navigation-menu/dist/index.mjs\",\n    \"NavigationMenuList\",\n);\nexport const NavigationMenuSub = registerClientReference(\n    function() { throw new Error(\"Attempted to call NavigationMenuSub() from the server but NavigationMenuSub is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@radix-ui/react-navigation-menu/dist/index.mjs\",\n    \"NavigationMenuSub\",\n);\nexport const NavigationMenuTrigger = registerClientReference(\n    function() { throw new Error(\"Attempted to call NavigationMenuTrigger() from the server but NavigationMenuTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@radix-ui/react-navigation-menu/dist/index.mjs\",\n    \"NavigationMenuTrigger\",\n);\nexport const NavigationMenuViewport = registerClientReference(\n    function() { throw new Error(\"Attempted to call NavigationMenuViewport() from the server but NavigationMenuViewport is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@radix-ui/react-navigation-menu/dist/index.mjs\",\n    \"NavigationMenuViewport\",\n);\nexport const Root = registerClientReference(\n    function() { throw new Error(\"Attempted to call Root() from the server but Root is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@radix-ui/react-navigation-menu/dist/index.mjs\",\n    \"Root\",\n);\nexport const Sub = registerClientReference(\n    function() { throw new Error(\"Attempted to call Sub() from the server but Sub is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@radix-ui/react-navigation-menu/dist/index.mjs\",\n    \"Sub\",\n);\nexport const Trigger = registerClientReference(\n    function() { throw new Error(\"Attempted to call Trigger() from the server but Trigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@radix-ui/react-navigation-menu/dist/index.mjs\",\n    \"Trigger\",\n);\nexport const Viewport = registerClientReference(\n    function() { throw new Error(\"Attempted to call Viewport() from the server but Viewport is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@radix-ui/react-navigation-menu/dist/index.mjs\",\n    \"Viewport\",\n);\nexport const createNavigationMenuScope = registerClientReference(\n    function() { throw new Error(\"Attempted to call createNavigationMenuScope() from the server but createNavigationMenuScope is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/@radix-ui/react-navigation-menu/dist/index.mjs\",\n    \"createNavigationMenuScope\",\n);\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA;;AACO,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,yEACA;AAEG,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,yEACA;AAEG,MAAM,OAAO,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,yEACA;AAEG,MAAM,OAAO,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,yEACA;AAEG,MAAM,OAAO,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,yEACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,yEACA;AAEG,MAAM,wBAAwB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACvD;IAAa,MAAM,IAAI,MAAM;AAA0P,GACvR,yEACA;AAEG,MAAM,0BAA0B,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzD;IAAa,MAAM,IAAI,MAAM;AAA8P,GAC3R,yEACA;AAEG,MAAM,qBAAqB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,yEACA;AAEG,MAAM,qBAAqB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,yEACA;AAEG,MAAM,qBAAqB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,yEACA;AAEG,MAAM,oBAAoB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,yEACA;AAEG,MAAM,wBAAwB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACvD;IAAa,MAAM,IAAI,MAAM;AAA0P,GACvR,yEACA;AAEG,MAAM,yBAAyB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxD;IAAa,MAAM,IAAI,MAAM;AAA4P,GACzR,yEACA;AAEG,MAAM,OAAO,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,yEACA;AAEG,MAAM,MAAM,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACrC;IAAa,MAAM,IAAI,MAAM;AAAsN,GACnP,yEACA;AAEG,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,yEACA;AAEG,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,yEACA;AAEG,MAAM,4BAA4B,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3D;IAAa,MAAM,IAAI,MAAM;AAAkQ,GAC/R,yEACA", "ignoreList": [0]}}, {"offset": {"line": 4556, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4562, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/%40radix-ui/react-navigation-menu/src/navigation-menu.tsx"], "sourcesContent": ["import * as React from 'react';\nimport ReactDOM from 'react-dom';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { Primitive, dispatchDiscreteCustomEvent } from '@radix-ui/react-primitive';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { composeRefs, useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { useDirection } from '@radix-ui/react-direction';\nimport { Presence } from '@radix-ui/react-presence';\nimport { useId } from '@radix-ui/react-id';\nimport { createCollection } from '@radix-ui/react-collection';\nimport { DismissableLayer } from '@radix-ui/react-dismissable-layer';\nimport { usePrevious } from '@radix-ui/react-use-previous';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\nimport * as VisuallyHiddenPrimitive from '@radix-ui/react-visually-hidden';\n\nimport type { Scope } from '@radix-ui/react-context';\n\ntype Orientation = 'vertical' | 'horizontal';\ntype Direction = 'ltr' | 'rtl';\n\n/* -------------------------------------------------------------------------------------------------\n * NavigationMenu\n * -----------------------------------------------------------------------------------------------*/\n\nconst NAVIGATION_MENU_NAME = 'NavigationMenu';\n\nconst [Collection, useCollection, createCollectionScope] = createCollection<\n  NavigationMenuTriggerElement,\n  { value: string }\n>(NAVIGATION_MENU_NAME);\n\nconst [FocusGroupCollection, useFocusGroupCollection, createFocusGroupCollectionScope] =\n  createCollection<FocusGroupItemElement, {}>(NAVIGATION_MENU_NAME);\n\ntype ScopedProps<P> = P & { __scopeNavigationMenu?: Scope };\nconst [createNavigationMenuContext, createNavigationMenuScope] = createContextScope(\n  NAVIGATION_MENU_NAME,\n  [createCollectionScope, createFocusGroupCollectionScope]\n);\n\ntype ContentData = {\n  ref?: React.Ref<ViewportContentMounterElement>;\n} & ViewportContentMounterProps;\n\ntype NavigationMenuContextValue = {\n  isRootMenu: boolean;\n  value: string;\n  previousValue: string;\n  baseId: string;\n  dir: Direction;\n  orientation: Orientation;\n  rootNavigationMenu: NavigationMenuElement | null;\n  indicatorTrack: HTMLDivElement | null;\n  onIndicatorTrackChange(indicatorTrack: HTMLDivElement | null): void;\n  viewport: NavigationMenuViewportElement | null;\n  onViewportChange(viewport: NavigationMenuViewportElement | null): void;\n  onViewportContentChange(contentValue: string, contentData: ContentData): void;\n  onViewportContentRemove(contentValue: string): void;\n  onTriggerEnter(itemValue: string): void;\n  onTriggerLeave(): void;\n  onContentEnter(): void;\n  onContentLeave(): void;\n  onItemSelect(itemValue: string): void;\n  onItemDismiss(): void;\n};\n\nconst [NavigationMenuProviderImpl, useNavigationMenuContext] =\n  createNavigationMenuContext<NavigationMenuContextValue>(NAVIGATION_MENU_NAME);\n\nconst [ViewportContentProvider, useViewportContentContext] = createNavigationMenuContext<{\n  items: Map<string, ContentData>;\n}>(NAVIGATION_MENU_NAME);\n\ntype NavigationMenuElement = React.ComponentRef<typeof Primitive.nav>;\ntype PrimitiveNavProps = React.ComponentPropsWithoutRef<typeof Primitive.nav>;\ninterface NavigationMenuProps\n  extends Omit<NavigationMenuProviderProps, keyof NavigationMenuProviderPrivateProps>,\n    PrimitiveNavProps {\n  value?: string;\n  defaultValue?: string;\n  onValueChange?: (value: string) => void;\n  dir?: Direction;\n  orientation?: Orientation;\n  /**\n   * The duration from when the pointer enters the trigger until the tooltip gets opened.\n   * @defaultValue 200\n   */\n  delayDuration?: number;\n  /**\n   * How much time a user has to enter another trigger without incurring a delay again.\n   * @defaultValue 300\n   */\n  skipDelayDuration?: number;\n}\n\nconst NavigationMenu = React.forwardRef<NavigationMenuElement, NavigationMenuProps>(\n  (props: ScopedProps<NavigationMenuProps>, forwardedRef) => {\n    const {\n      __scopeNavigationMenu,\n      value: valueProp,\n      onValueChange,\n      defaultValue,\n      delayDuration = 200,\n      skipDelayDuration = 300,\n      orientation = 'horizontal',\n      dir,\n      ...NavigationMenuProps\n    } = props;\n    const [navigationMenu, setNavigationMenu] = React.useState<NavigationMenuElement | null>(null);\n    const composedRef = useComposedRefs(forwardedRef, (node) => setNavigationMenu(node));\n    const direction = useDirection(dir);\n    const openTimerRef = React.useRef(0);\n    const closeTimerRef = React.useRef(0);\n    const skipDelayTimerRef = React.useRef(0);\n    const [isOpenDelayed, setIsOpenDelayed] = React.useState(true);\n    const [value, setValue] = useControllableState({\n      prop: valueProp,\n      onChange: (value) => {\n        const isOpen = value !== '';\n        const hasSkipDelayDuration = skipDelayDuration > 0;\n\n        if (isOpen) {\n          window.clearTimeout(skipDelayTimerRef.current);\n          if (hasSkipDelayDuration) setIsOpenDelayed(false);\n        } else {\n          window.clearTimeout(skipDelayTimerRef.current);\n          skipDelayTimerRef.current = window.setTimeout(\n            () => setIsOpenDelayed(true),\n            skipDelayDuration\n          );\n        }\n\n        onValueChange?.(value);\n      },\n      defaultProp: defaultValue ?? '',\n      caller: NAVIGATION_MENU_NAME,\n    });\n\n    const startCloseTimer = React.useCallback(() => {\n      window.clearTimeout(closeTimerRef.current);\n      closeTimerRef.current = window.setTimeout(() => setValue(''), 150);\n    }, [setValue]);\n\n    const handleOpen = React.useCallback(\n      (itemValue: string) => {\n        window.clearTimeout(closeTimerRef.current);\n        setValue(itemValue);\n      },\n      [setValue]\n    );\n\n    const handleDelayedOpen = React.useCallback(\n      (itemValue: string) => {\n        const isOpenItem = value === itemValue;\n        if (isOpenItem) {\n          // If the item is already open (e.g. we're transitioning from the content to the trigger)\n          // then we want to clear the close timer immediately.\n          window.clearTimeout(closeTimerRef.current);\n        } else {\n          openTimerRef.current = window.setTimeout(() => {\n            window.clearTimeout(closeTimerRef.current);\n            setValue(itemValue);\n          }, delayDuration);\n        }\n      },\n      [value, setValue, delayDuration]\n    );\n\n    React.useEffect(() => {\n      return () => {\n        window.clearTimeout(openTimerRef.current);\n        window.clearTimeout(closeTimerRef.current);\n        window.clearTimeout(skipDelayTimerRef.current);\n      };\n    }, []);\n\n    return (\n      <NavigationMenuProvider\n        scope={__scopeNavigationMenu}\n        isRootMenu={true}\n        value={value}\n        dir={direction}\n        orientation={orientation}\n        rootNavigationMenu={navigationMenu}\n        onTriggerEnter={(itemValue) => {\n          window.clearTimeout(openTimerRef.current);\n          if (isOpenDelayed) handleDelayedOpen(itemValue);\n          else handleOpen(itemValue);\n        }}\n        onTriggerLeave={() => {\n          window.clearTimeout(openTimerRef.current);\n          startCloseTimer();\n        }}\n        onContentEnter={() => window.clearTimeout(closeTimerRef.current)}\n        onContentLeave={startCloseTimer}\n        onItemSelect={(itemValue) => {\n          setValue((prevValue) => (prevValue === itemValue ? '' : itemValue));\n        }}\n        onItemDismiss={() => setValue('')}\n      >\n        <Primitive.nav\n          aria-label=\"Main\"\n          data-orientation={orientation}\n          dir={direction}\n          {...NavigationMenuProps}\n          ref={composedRef}\n        />\n      </NavigationMenuProvider>\n    );\n  }\n);\n\nNavigationMenu.displayName = NAVIGATION_MENU_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * NavigationMenuSub\n * -----------------------------------------------------------------------------------------------*/\n\nconst SUB_NAME = 'NavigationMenuSub';\n\ntype NavigationMenuSubElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface NavigationMenuSubProps\n  extends Omit<NavigationMenuProviderProps, keyof NavigationMenuProviderPrivateProps>,\n    PrimitiveDivProps {\n  value?: string;\n  defaultValue?: string;\n  onValueChange?: (value: string) => void;\n  orientation?: Orientation;\n}\n\nconst NavigationMenuSub = React.forwardRef<NavigationMenuSubElement, NavigationMenuSubProps>(\n  (props: ScopedProps<NavigationMenuSubProps>, forwardedRef) => {\n    const {\n      __scopeNavigationMenu,\n      value: valueProp,\n      onValueChange,\n      defaultValue,\n      orientation = 'horizontal',\n      ...subProps\n    } = props;\n    const context = useNavigationMenuContext(SUB_NAME, __scopeNavigationMenu);\n    const [value, setValue] = useControllableState({\n      prop: valueProp,\n      onChange: onValueChange,\n      defaultProp: defaultValue ?? '',\n      caller: SUB_NAME,\n    });\n\n    return (\n      <NavigationMenuProvider\n        scope={__scopeNavigationMenu}\n        isRootMenu={false}\n        value={value}\n        dir={context.dir}\n        orientation={orientation}\n        rootNavigationMenu={context.rootNavigationMenu}\n        onTriggerEnter={(itemValue) => setValue(itemValue)}\n        onItemSelect={(itemValue) => setValue(itemValue)}\n        onItemDismiss={() => setValue('')}\n      >\n        <Primitive.div data-orientation={orientation} {...subProps} ref={forwardedRef} />\n      </NavigationMenuProvider>\n    );\n  }\n);\n\nNavigationMenuSub.displayName = SUB_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ninterface NavigationMenuProviderPrivateProps {\n  isRootMenu: boolean;\n  scope: Scope;\n  children: React.ReactNode;\n  orientation: Orientation;\n  dir: Direction;\n  rootNavigationMenu: NavigationMenuElement | null;\n  value: string;\n  onTriggerEnter(itemValue: string): void;\n  onTriggerLeave?(): void;\n  onContentEnter?(): void;\n  onContentLeave?(): void;\n  onItemSelect(itemValue: string): void;\n  onItemDismiss(): void;\n}\n\ninterface NavigationMenuProviderProps extends NavigationMenuProviderPrivateProps {}\n\nconst NavigationMenuProvider: React.FC<NavigationMenuProviderProps> = (\n  props: ScopedProps<NavigationMenuProviderProps>\n) => {\n  const {\n    scope,\n    isRootMenu,\n    rootNavigationMenu,\n    dir,\n    orientation,\n    children,\n    value,\n    onItemSelect,\n    onItemDismiss,\n    onTriggerEnter,\n    onTriggerLeave,\n    onContentEnter,\n    onContentLeave,\n  } = props;\n  const [viewport, setViewport] = React.useState<NavigationMenuViewportElement | null>(null);\n  const [viewportContent, setViewportContent] = React.useState<Map<string, ContentData>>(new Map());\n  const [indicatorTrack, setIndicatorTrack] = React.useState<HTMLDivElement | null>(null);\n\n  return (\n    <NavigationMenuProviderImpl\n      scope={scope}\n      isRootMenu={isRootMenu}\n      rootNavigationMenu={rootNavigationMenu}\n      value={value}\n      previousValue={usePrevious(value)}\n      baseId={useId()}\n      dir={dir}\n      orientation={orientation}\n      viewport={viewport}\n      onViewportChange={setViewport}\n      indicatorTrack={indicatorTrack}\n      onIndicatorTrackChange={setIndicatorTrack}\n      onTriggerEnter={useCallbackRef(onTriggerEnter)}\n      onTriggerLeave={useCallbackRef(onTriggerLeave)}\n      onContentEnter={useCallbackRef(onContentEnter)}\n      onContentLeave={useCallbackRef(onContentLeave)}\n      onItemSelect={useCallbackRef(onItemSelect)}\n      onItemDismiss={useCallbackRef(onItemDismiss)}\n      onViewportContentChange={React.useCallback((contentValue, contentData) => {\n        setViewportContent((prevContent) => {\n          prevContent.set(contentValue, contentData);\n          return new Map(prevContent);\n        });\n      }, [])}\n      onViewportContentRemove={React.useCallback((contentValue) => {\n        setViewportContent((prevContent) => {\n          if (!prevContent.has(contentValue)) return prevContent;\n          prevContent.delete(contentValue);\n          return new Map(prevContent);\n        });\n      }, [])}\n    >\n      <Collection.Provider scope={scope}>\n        <ViewportContentProvider scope={scope} items={viewportContent}>\n          {children}\n        </ViewportContentProvider>\n      </Collection.Provider>\n    </NavigationMenuProviderImpl>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * NavigationMenuList\n * -----------------------------------------------------------------------------------------------*/\n\nconst LIST_NAME = 'NavigationMenuList';\n\ntype NavigationMenuListElement = React.ComponentRef<typeof Primitive.ul>;\ntype PrimitiveUnorderedListProps = React.ComponentPropsWithoutRef<typeof Primitive.ul>;\ninterface NavigationMenuListProps extends PrimitiveUnorderedListProps {}\n\nconst NavigationMenuList = React.forwardRef<NavigationMenuListElement, NavigationMenuListProps>(\n  (props: ScopedProps<NavigationMenuListProps>, forwardedRef) => {\n    const { __scopeNavigationMenu, ...listProps } = props;\n    const context = useNavigationMenuContext(LIST_NAME, __scopeNavigationMenu);\n\n    const list = (\n      <Primitive.ul data-orientation={context.orientation} {...listProps} ref={forwardedRef} />\n    );\n\n    return (\n      <Primitive.div style={{ position: 'relative' }} ref={context.onIndicatorTrackChange}>\n        <Collection.Slot scope={__scopeNavigationMenu}>\n          {context.isRootMenu ? <FocusGroup asChild>{list}</FocusGroup> : list}\n        </Collection.Slot>\n      </Primitive.div>\n    );\n  }\n);\n\nNavigationMenuList.displayName = LIST_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * NavigationMenuItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_NAME = 'NavigationMenuItem';\n\ntype FocusProxyElement = React.ComponentRef<typeof VisuallyHiddenPrimitive.Root>;\n\ntype NavigationMenuItemContextValue = {\n  value: string;\n  triggerRef: React.RefObject<NavigationMenuTriggerElement | null>;\n  contentRef: React.RefObject<NavigationMenuContentElement | null>;\n  focusProxyRef: React.RefObject<FocusProxyElement | null>;\n  wasEscapeCloseRef: React.MutableRefObject<boolean>;\n  onEntryKeyDown(): void;\n  onFocusProxyEnter(side: 'start' | 'end'): void;\n  onRootContentClose(): void;\n  onContentFocusOutside(): void;\n};\n\nconst [NavigationMenuItemContextProvider, useNavigationMenuItemContext] =\n  createNavigationMenuContext<NavigationMenuItemContextValue>(ITEM_NAME);\n\ntype NavigationMenuItemElement = React.ComponentRef<typeof Primitive.li>;\ntype PrimitiveListItemProps = React.ComponentPropsWithoutRef<typeof Primitive.li>;\ninterface NavigationMenuItemProps extends PrimitiveListItemProps {\n  value?: string;\n}\n\nconst NavigationMenuItem = React.forwardRef<NavigationMenuItemElement, NavigationMenuItemProps>(\n  (props: ScopedProps<NavigationMenuItemProps>, forwardedRef) => {\n    const { __scopeNavigationMenu, value: valueProp, ...itemProps } = props;\n    const autoValue = useId();\n    // We need to provide an initial deterministic value as `useId` will return\n    // empty string on the first render and we don't want to match our internal \"closed\" value.\n    const value = valueProp || autoValue || 'LEGACY_REACT_AUTO_VALUE';\n    const contentRef = React.useRef<NavigationMenuContentElement>(null);\n    const triggerRef = React.useRef<NavigationMenuTriggerElement>(null);\n    const focusProxyRef = React.useRef<FocusProxyElement>(null);\n    const restoreContentTabOrderRef = React.useRef(() => {});\n    const wasEscapeCloseRef = React.useRef(false);\n\n    const handleContentEntry = React.useCallback((side = 'start') => {\n      if (contentRef.current) {\n        restoreContentTabOrderRef.current();\n        const candidates = getTabbableCandidates(contentRef.current);\n        if (candidates.length) focusFirst(side === 'start' ? candidates : candidates.reverse());\n      }\n    }, []);\n\n    const handleContentExit = React.useCallback(() => {\n      if (contentRef.current) {\n        const candidates = getTabbableCandidates(contentRef.current);\n        if (candidates.length) restoreContentTabOrderRef.current = removeFromTabOrder(candidates);\n      }\n    }, []);\n\n    return (\n      <NavigationMenuItemContextProvider\n        scope={__scopeNavigationMenu}\n        value={value}\n        triggerRef={triggerRef}\n        contentRef={contentRef}\n        focusProxyRef={focusProxyRef}\n        wasEscapeCloseRef={wasEscapeCloseRef}\n        onEntryKeyDown={handleContentEntry}\n        onFocusProxyEnter={handleContentEntry}\n        onRootContentClose={handleContentExit}\n        onContentFocusOutside={handleContentExit}\n      >\n        <Primitive.li {...itemProps} ref={forwardedRef} />\n      </NavigationMenuItemContextProvider>\n    );\n  }\n);\n\nNavigationMenuItem.displayName = ITEM_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * NavigationMenuTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst TRIGGER_NAME = 'NavigationMenuTrigger';\n\ntype NavigationMenuTriggerElement = React.ComponentRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = React.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface NavigationMenuTriggerProps extends PrimitiveButtonProps {}\n\nconst NavigationMenuTrigger = React.forwardRef<\n  NavigationMenuTriggerElement,\n  NavigationMenuTriggerProps\n>((props: ScopedProps<NavigationMenuTriggerProps>, forwardedRef) => {\n  const { __scopeNavigationMenu, disabled, ...triggerProps } = props;\n  const context = useNavigationMenuContext(TRIGGER_NAME, props.__scopeNavigationMenu);\n  const itemContext = useNavigationMenuItemContext(TRIGGER_NAME, props.__scopeNavigationMenu);\n  const ref = React.useRef<NavigationMenuTriggerElement>(null);\n  const composedRefs = useComposedRefs(ref, itemContext.triggerRef, forwardedRef);\n  const triggerId = makeTriggerId(context.baseId, itemContext.value);\n  const contentId = makeContentId(context.baseId, itemContext.value);\n  const hasPointerMoveOpenedRef = React.useRef(false);\n  const wasClickCloseRef = React.useRef(false);\n  const open = itemContext.value === context.value;\n\n  return (\n    <>\n      <Collection.ItemSlot scope={__scopeNavigationMenu} value={itemContext.value}>\n        <FocusGroupItem asChild>\n          <Primitive.button\n            id={triggerId}\n            disabled={disabled}\n            data-disabled={disabled ? '' : undefined}\n            data-state={getOpenState(open)}\n            aria-expanded={open}\n            aria-controls={contentId}\n            {...triggerProps}\n            ref={composedRefs}\n            onPointerEnter={composeEventHandlers(props.onPointerEnter, () => {\n              wasClickCloseRef.current = false;\n              itemContext.wasEscapeCloseRef.current = false;\n            })}\n            onPointerMove={composeEventHandlers(\n              props.onPointerMove,\n              whenMouse(() => {\n                if (\n                  disabled ||\n                  wasClickCloseRef.current ||\n                  itemContext.wasEscapeCloseRef.current ||\n                  hasPointerMoveOpenedRef.current\n                )\n                  return;\n                context.onTriggerEnter(itemContext.value);\n                hasPointerMoveOpenedRef.current = true;\n              })\n            )}\n            onPointerLeave={composeEventHandlers(\n              props.onPointerLeave,\n              whenMouse(() => {\n                if (disabled) return;\n                context.onTriggerLeave();\n                hasPointerMoveOpenedRef.current = false;\n              })\n            )}\n            onClick={composeEventHandlers(props.onClick, () => {\n              context.onItemSelect(itemContext.value);\n              wasClickCloseRef.current = open;\n            })}\n            onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n              const verticalEntryKey = context.dir === 'rtl' ? 'ArrowLeft' : 'ArrowRight';\n              const entryKey = { horizontal: 'ArrowDown', vertical: verticalEntryKey }[\n                context.orientation\n              ];\n              if (open && event.key === entryKey) {\n                itemContext.onEntryKeyDown();\n                // Prevent FocusGroupItem from handling the event\n                event.preventDefault();\n              }\n            })}\n          />\n        </FocusGroupItem>\n      </Collection.ItemSlot>\n\n      {/* Proxy tab order between trigger and content */}\n      {open && (\n        <>\n          <VisuallyHiddenPrimitive.Root\n            aria-hidden\n            tabIndex={0}\n            ref={itemContext.focusProxyRef}\n            onFocus={(event) => {\n              const content = itemContext.contentRef.current;\n              const prevFocusedElement = event.relatedTarget as HTMLElement | null;\n              const wasTriggerFocused = prevFocusedElement === ref.current;\n              const wasFocusFromContent = content?.contains(prevFocusedElement);\n\n              if (wasTriggerFocused || !wasFocusFromContent) {\n                itemContext.onFocusProxyEnter(wasTriggerFocused ? 'start' : 'end');\n              }\n            }}\n          />\n\n          {/* Restructure a11y tree to make content accessible to screen reader when using the viewport */}\n          {context.viewport && <span aria-owns={contentId} />}\n        </>\n      )}\n    </>\n  );\n});\n\nNavigationMenuTrigger.displayName = TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * NavigationMenuLink\n * -----------------------------------------------------------------------------------------------*/\n\nconst LINK_NAME = 'NavigationMenuLink';\nconst LINK_SELECT = 'navigationMenu.linkSelect';\n\ntype NavigationMenuLinkElement = React.ComponentRef<typeof Primitive.a>;\ntype PrimitiveLinkProps = React.ComponentPropsWithoutRef<typeof Primitive.a>;\ninterface NavigationMenuLinkProps extends Omit<PrimitiveLinkProps, 'onSelect'> {\n  active?: boolean;\n  onSelect?: (event: Event) => void;\n}\n\nconst NavigationMenuLink = React.forwardRef<NavigationMenuLinkElement, NavigationMenuLinkProps>(\n  (props: ScopedProps<NavigationMenuLinkProps>, forwardedRef) => {\n    const { __scopeNavigationMenu, active, onSelect, ...linkProps } = props;\n\n    return (\n      <FocusGroupItem asChild>\n        <Primitive.a\n          data-active={active ? '' : undefined}\n          aria-current={active ? 'page' : undefined}\n          {...linkProps}\n          ref={forwardedRef}\n          onClick={composeEventHandlers(\n            props.onClick,\n            (event) => {\n              const target = event.target as HTMLElement;\n              const linkSelectEvent = new CustomEvent(LINK_SELECT, {\n                bubbles: true,\n                cancelable: true,\n              });\n              target.addEventListener(LINK_SELECT, (event) => onSelect?.(event), { once: true });\n              dispatchDiscreteCustomEvent(target, linkSelectEvent);\n\n              if (!linkSelectEvent.defaultPrevented && !event.metaKey) {\n                const rootContentDismissEvent = new CustomEvent(ROOT_CONTENT_DISMISS, {\n                  bubbles: true,\n                  cancelable: true,\n                });\n                dispatchDiscreteCustomEvent(target, rootContentDismissEvent);\n              }\n            },\n            { checkForDefaultPrevented: false }\n          )}\n        />\n      </FocusGroupItem>\n    );\n  }\n);\n\nNavigationMenuLink.displayName = LINK_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * NavigationMenuIndicator\n * -----------------------------------------------------------------------------------------------*/\n\nconst INDICATOR_NAME = 'NavigationMenuIndicator';\n\ntype NavigationMenuIndicatorElement = NavigationMenuIndicatorImplElement;\ninterface NavigationMenuIndicatorProps extends NavigationMenuIndicatorImplProps {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst NavigationMenuIndicator = React.forwardRef<\n  NavigationMenuIndicatorElement,\n  NavigationMenuIndicatorProps\n>((props: ScopedProps<NavigationMenuIndicatorProps>, forwardedRef) => {\n  const { forceMount, ...indicatorProps } = props;\n  const context = useNavigationMenuContext(INDICATOR_NAME, props.__scopeNavigationMenu);\n  const isVisible = Boolean(context.value);\n\n  return context.indicatorTrack\n    ? ReactDOM.createPortal(\n        <Presence present={forceMount || isVisible}>\n          <NavigationMenuIndicatorImpl {...indicatorProps} ref={forwardedRef} />\n        </Presence>,\n        context.indicatorTrack\n      )\n    : null;\n});\n\nNavigationMenuIndicator.displayName = INDICATOR_NAME;\n\ntype NavigationMenuIndicatorImplElement = React.ComponentRef<typeof Primitive.div>;\ninterface NavigationMenuIndicatorImplProps extends PrimitiveDivProps {}\n\nconst NavigationMenuIndicatorImpl = React.forwardRef<\n  NavigationMenuIndicatorImplElement,\n  NavigationMenuIndicatorImplProps\n>((props: ScopedProps<NavigationMenuIndicatorImplProps>, forwardedRef) => {\n  const { __scopeNavigationMenu, ...indicatorProps } = props;\n  const context = useNavigationMenuContext(INDICATOR_NAME, __scopeNavigationMenu);\n  const getItems = useCollection(__scopeNavigationMenu);\n  const [activeTrigger, setActiveTrigger] = React.useState<NavigationMenuTriggerElement | null>(\n    null\n  );\n  const [position, setPosition] = React.useState<{ size: number; offset: number } | null>(null);\n  const isHorizontal = context.orientation === 'horizontal';\n  const isVisible = Boolean(context.value);\n\n  React.useEffect(() => {\n    const items = getItems();\n    const triggerNode = items.find((item) => item.value === context.value)?.ref.current;\n    if (triggerNode) setActiveTrigger(triggerNode);\n  }, [getItems, context.value]);\n\n  /**\n   * Update position when the indicator or parent track size changes\n   */\n  const handlePositionChange = () => {\n    if (activeTrigger) {\n      setPosition({\n        size: isHorizontal ? activeTrigger.offsetWidth : activeTrigger.offsetHeight,\n        offset: isHorizontal ? activeTrigger.offsetLeft : activeTrigger.offsetTop,\n      });\n    }\n  };\n  useResizeObserver(activeTrigger, handlePositionChange);\n  useResizeObserver(context.indicatorTrack, handlePositionChange);\n\n  // We need to wait for the indicator position to be available before rendering to\n  // snap immediately into position rather than transitioning from initial\n  return position ? (\n    <Primitive.div\n      aria-hidden\n      data-state={isVisible ? 'visible' : 'hidden'}\n      data-orientation={context.orientation}\n      {...indicatorProps}\n      ref={forwardedRef}\n      style={{\n        position: 'absolute',\n        ...(isHorizontal\n          ? {\n              left: 0,\n              width: position.size + 'px',\n              transform: `translateX(${position.offset}px)`,\n            }\n          : {\n              top: 0,\n              height: position.size + 'px',\n              transform: `translateY(${position.offset}px)`,\n            }),\n        ...indicatorProps.style,\n      }}\n    />\n  ) : null;\n});\n\n/* -------------------------------------------------------------------------------------------------\n * NavigationMenuContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'NavigationMenuContent';\n\ntype NavigationMenuContentElement = NavigationMenuContentImplElement;\ninterface NavigationMenuContentProps\n  extends Omit<NavigationMenuContentImplProps, keyof NavigationMenuContentImplPrivateProps> {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst NavigationMenuContent = React.forwardRef<\n  NavigationMenuContentElement,\n  NavigationMenuContentProps\n>((props: ScopedProps<NavigationMenuContentProps>, forwardedRef) => {\n  const { forceMount, ...contentProps } = props;\n  const context = useNavigationMenuContext(CONTENT_NAME, props.__scopeNavigationMenu);\n  const itemContext = useNavigationMenuItemContext(CONTENT_NAME, props.__scopeNavigationMenu);\n  const composedRefs = useComposedRefs(itemContext.contentRef, forwardedRef);\n  const open = itemContext.value === context.value;\n\n  const commonProps = {\n    value: itemContext.value,\n    triggerRef: itemContext.triggerRef,\n    focusProxyRef: itemContext.focusProxyRef,\n    wasEscapeCloseRef: itemContext.wasEscapeCloseRef,\n    onContentFocusOutside: itemContext.onContentFocusOutside,\n    onRootContentClose: itemContext.onRootContentClose,\n    ...contentProps,\n  };\n\n  return !context.viewport ? (\n    <Presence present={forceMount || open}>\n      <NavigationMenuContentImpl\n        data-state={getOpenState(open)}\n        {...commonProps}\n        ref={composedRefs}\n        onPointerEnter={composeEventHandlers(props.onPointerEnter, context.onContentEnter)}\n        onPointerLeave={composeEventHandlers(\n          props.onPointerLeave,\n          whenMouse(context.onContentLeave)\n        )}\n        style={{\n          // Prevent interaction when animating out\n          pointerEvents: !open && context.isRootMenu ? 'none' : undefined,\n          ...commonProps.style,\n        }}\n      />\n    </Presence>\n  ) : (\n    <ViewportContentMounter forceMount={forceMount} {...commonProps} ref={composedRefs} />\n  );\n});\n\nNavigationMenuContent.displayName = CONTENT_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype ViewportContentMounterElement = NavigationMenuContentImplElement;\ninterface ViewportContentMounterProps extends NavigationMenuContentImplProps {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst ViewportContentMounter = React.forwardRef<\n  ViewportContentMounterElement,\n  ViewportContentMounterProps\n>((props: ScopedProps<ViewportContentMounterProps>, forwardedRef) => {\n  const context = useNavigationMenuContext(CONTENT_NAME, props.__scopeNavigationMenu);\n  const { onViewportContentChange, onViewportContentRemove } = context;\n\n  useLayoutEffect(() => {\n    onViewportContentChange(props.value, {\n      ref: forwardedRef,\n      ...props,\n    });\n  }, [props, forwardedRef, onViewportContentChange]);\n\n  useLayoutEffect(() => {\n    return () => onViewportContentRemove(props.value);\n  }, [props.value, onViewportContentRemove]);\n\n  // Content is proxied into the viewport\n  return null;\n});\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst ROOT_CONTENT_DISMISS = 'navigationMenu.rootContentDismiss';\n\ntype MotionAttribute = 'to-start' | 'to-end' | 'from-start' | 'from-end';\ntype NavigationMenuContentImplElement = React.ComponentRef<typeof DismissableLayer>;\ntype DismissableLayerProps = React.ComponentPropsWithoutRef<typeof DismissableLayer>;\n\ninterface NavigationMenuContentImplPrivateProps {\n  value: string;\n  triggerRef: React.RefObject<NavigationMenuTriggerElement | null>;\n  focusProxyRef: React.RefObject<FocusProxyElement | null>;\n  wasEscapeCloseRef: React.MutableRefObject<boolean>;\n  onContentFocusOutside(): void;\n  onRootContentClose(): void;\n}\ninterface NavigationMenuContentImplProps\n  extends Omit<DismissableLayerProps, 'onDismiss' | 'disableOutsidePointerEvents'>,\n    NavigationMenuContentImplPrivateProps {}\n\nconst NavigationMenuContentImpl = React.forwardRef<\n  NavigationMenuContentImplElement,\n  NavigationMenuContentImplProps\n>((props: ScopedProps<NavigationMenuContentImplProps>, forwardedRef) => {\n  const {\n    __scopeNavigationMenu,\n    value,\n    triggerRef,\n    focusProxyRef,\n    wasEscapeCloseRef,\n    onRootContentClose,\n    onContentFocusOutside,\n    ...contentProps\n  } = props;\n  const context = useNavigationMenuContext(CONTENT_NAME, __scopeNavigationMenu);\n  const ref = React.useRef<NavigationMenuContentImplElement>(null);\n  const composedRefs = useComposedRefs(ref, forwardedRef);\n  const triggerId = makeTriggerId(context.baseId, value);\n  const contentId = makeContentId(context.baseId, value);\n  const getItems = useCollection(__scopeNavigationMenu);\n  const prevMotionAttributeRef = React.useRef<MotionAttribute | null>(null);\n\n  const { onItemDismiss } = context;\n\n  React.useEffect(() => {\n    const content = ref.current;\n\n    // Bubble dismiss to the root content node and focus its trigger\n    if (context.isRootMenu && content) {\n      const handleClose = () => {\n        onItemDismiss();\n        onRootContentClose();\n        if (content.contains(document.activeElement)) triggerRef.current?.focus();\n      };\n      content.addEventListener(ROOT_CONTENT_DISMISS, handleClose);\n      return () => content.removeEventListener(ROOT_CONTENT_DISMISS, handleClose);\n    }\n  }, [context.isRootMenu, props.value, triggerRef, onItemDismiss, onRootContentClose]);\n\n  const motionAttribute = React.useMemo(() => {\n    const items = getItems();\n    const values = items.map((item) => item.value);\n    if (context.dir === 'rtl') values.reverse();\n    const index = values.indexOf(context.value);\n    const prevIndex = values.indexOf(context.previousValue);\n    const isSelected = value === context.value;\n    const wasSelected = prevIndex === values.indexOf(value);\n\n    // We only want to update selected and the last selected content\n    // this avoids animations being interrupted outside of that range\n    if (!isSelected && !wasSelected) return prevMotionAttributeRef.current;\n\n    const attribute = (() => {\n      // Don't provide a direction on the initial open\n      if (index !== prevIndex) {\n        // If we're moving to this item from another\n        if (isSelected && prevIndex !== -1) return index > prevIndex ? 'from-end' : 'from-start';\n        // If we're leaving this item for another\n        if (wasSelected && index !== -1) return index > prevIndex ? 'to-start' : 'to-end';\n      }\n      // Otherwise we're entering from closed or leaving the list\n      // entirely and should not animate in any direction\n      return null;\n    })();\n\n    prevMotionAttributeRef.current = attribute;\n    return attribute;\n  }, [context.previousValue, context.value, context.dir, getItems, value]);\n\n  return (\n    <FocusGroup asChild>\n      <DismissableLayer\n        id={contentId}\n        aria-labelledby={triggerId}\n        data-motion={motionAttribute}\n        data-orientation={context.orientation}\n        {...contentProps}\n        ref={composedRefs}\n        disableOutsidePointerEvents={false}\n        onDismiss={() => {\n          const rootContentDismissEvent = new Event(ROOT_CONTENT_DISMISS, {\n            bubbles: true,\n            cancelable: true,\n          });\n          ref.current?.dispatchEvent(rootContentDismissEvent);\n        }}\n        onFocusOutside={composeEventHandlers(props.onFocusOutside, (event) => {\n          onContentFocusOutside();\n          const target = event.target as HTMLElement;\n          // Only dismiss content when focus moves outside of the menu\n          if (context.rootNavigationMenu?.contains(target)) event.preventDefault();\n        })}\n        onPointerDownOutside={composeEventHandlers(props.onPointerDownOutside, (event) => {\n          const target = event.target as HTMLElement;\n          const isTrigger = getItems().some((item) => item.ref.current?.contains(target));\n          const isRootViewport = context.isRootMenu && context.viewport?.contains(target);\n          if (isTrigger || isRootViewport || !context.isRootMenu) event.preventDefault();\n        })}\n        onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n          const isMetaKey = event.altKey || event.ctrlKey || event.metaKey;\n          const isTabKey = event.key === 'Tab' && !isMetaKey;\n          if (isTabKey) {\n            const candidates = getTabbableCandidates(event.currentTarget);\n            const focusedElement = document.activeElement;\n            const index = candidates.findIndex((candidate) => candidate === focusedElement);\n            const isMovingBackwards = event.shiftKey;\n            const nextCandidates = isMovingBackwards\n              ? candidates.slice(0, index).reverse()\n              : candidates.slice(index + 1, candidates.length);\n\n            if (focusFirst(nextCandidates)) {\n              // prevent browser tab keydown because we've handled focus\n              event.preventDefault();\n            } else {\n              // If we can't focus that means we're at the edges\n              // so focus the proxy and let browser handle\n              // tab/shift+tab keypress on the proxy instead\n              focusProxyRef.current?.focus();\n            }\n          }\n        })}\n        onEscapeKeyDown={composeEventHandlers(props.onEscapeKeyDown, (_event) => {\n          // prevent the dropdown from reopening\n          // after the escape key has been pressed\n          wasEscapeCloseRef.current = true;\n        })}\n      />\n    </FocusGroup>\n  );\n});\n\n/* -------------------------------------------------------------------------------------------------\n * NavigationMenuViewport\n * -----------------------------------------------------------------------------------------------*/\n\nconst VIEWPORT_NAME = 'NavigationMenuViewport';\n\ntype NavigationMenuViewportElement = NavigationMenuViewportImplElement;\ninterface NavigationMenuViewportProps\n  extends Omit<NavigationMenuViewportImplProps, 'activeContentValue'> {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst NavigationMenuViewport = React.forwardRef<\n  NavigationMenuViewportElement,\n  NavigationMenuViewportProps\n>((props: ScopedProps<NavigationMenuViewportProps>, forwardedRef) => {\n  const { forceMount, ...viewportProps } = props;\n  const context = useNavigationMenuContext(VIEWPORT_NAME, props.__scopeNavigationMenu);\n  const open = Boolean(context.value);\n\n  return (\n    <Presence present={forceMount || open}>\n      <NavigationMenuViewportImpl {...viewportProps} ref={forwardedRef} />\n    </Presence>\n  );\n});\n\nNavigationMenuViewport.displayName = VIEWPORT_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype NavigationMenuViewportImplElement = React.ComponentRef<typeof Primitive.div>;\ninterface NavigationMenuViewportImplProps extends PrimitiveDivProps {}\n\nconst NavigationMenuViewportImpl = React.forwardRef<\n  NavigationMenuViewportImplElement,\n  NavigationMenuViewportImplProps\n>((props: ScopedProps<NavigationMenuViewportImplProps>, forwardedRef) => {\n  const { __scopeNavigationMenu, children, ...viewportImplProps } = props;\n  const context = useNavigationMenuContext(VIEWPORT_NAME, __scopeNavigationMenu);\n  const composedRefs = useComposedRefs(forwardedRef, context.onViewportChange);\n  const viewportContentContext = useViewportContentContext(\n    CONTENT_NAME,\n    props.__scopeNavigationMenu\n  );\n  const [size, setSize] = React.useState<{ width: number; height: number } | null>(null);\n  const [content, setContent] = React.useState<NavigationMenuContentElement | null>(null);\n  const viewportWidth = size ? size?.width + 'px' : undefined;\n  const viewportHeight = size ? size?.height + 'px' : undefined;\n  const open = Boolean(context.value);\n  // We persist the last active content value as the viewport may be animating out\n  // and we want the content to remain mounted for the lifecycle of the viewport.\n  const activeContentValue = open ? context.value : context.previousValue;\n\n  /**\n   * Update viewport size to match the active content node.\n   * We prefer offset dimensions over `getBoundingClientRect` as the latter respects CSS transform.\n   * For example, if content animates in from `scale(0.5)` the dimensions would be anything\n   * from `0.5` to `1` of the intended size.\n   */\n  const handleSizeChange = () => {\n    if (content) setSize({ width: content.offsetWidth, height: content.offsetHeight });\n  };\n  useResizeObserver(content, handleSizeChange);\n\n  return (\n    <Primitive.div\n      data-state={getOpenState(open)}\n      data-orientation={context.orientation}\n      {...viewportImplProps}\n      ref={composedRefs}\n      style={{\n        // Prevent interaction when animating out\n        pointerEvents: !open && context.isRootMenu ? 'none' : undefined,\n        ['--radix-navigation-menu-viewport-width' as any]: viewportWidth,\n        ['--radix-navigation-menu-viewport-height' as any]: viewportHeight,\n        ...viewportImplProps.style,\n      }}\n      onPointerEnter={composeEventHandlers(props.onPointerEnter, context.onContentEnter)}\n      onPointerLeave={composeEventHandlers(props.onPointerLeave, whenMouse(context.onContentLeave))}\n    >\n      {Array.from(viewportContentContext.items).map(([value, { ref, forceMount, ...props }]) => {\n        const isActive = activeContentValue === value;\n        return (\n          <Presence key={value} present={forceMount || isActive}>\n            <NavigationMenuContentImpl\n              {...props}\n              ref={composeRefs(ref, (node) => {\n                // We only want to update the stored node when another is available\n                // as we need to smoothly transition between them.\n                if (isActive && node) setContent(node);\n              })}\n            />\n          </Presence>\n        );\n      })}\n    </Primitive.div>\n  );\n});\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst FOCUS_GROUP_NAME = 'FocusGroup';\n\ntype FocusGroupElement = React.ComponentRef<typeof Primitive.div>;\ninterface FocusGroupProps extends PrimitiveDivProps {}\n\nconst FocusGroup = React.forwardRef<FocusGroupElement, FocusGroupProps>(\n  (props: ScopedProps<FocusGroupProps>, forwardedRef) => {\n    const { __scopeNavigationMenu, ...groupProps } = props;\n    const context = useNavigationMenuContext(FOCUS_GROUP_NAME, __scopeNavigationMenu);\n\n    return (\n      <FocusGroupCollection.Provider scope={__scopeNavigationMenu}>\n        <FocusGroupCollection.Slot scope={__scopeNavigationMenu}>\n          <Primitive.div dir={context.dir} {...groupProps} ref={forwardedRef} />\n        </FocusGroupCollection.Slot>\n      </FocusGroupCollection.Provider>\n    );\n  }\n);\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst ARROW_KEYS = ['ArrowRight', 'ArrowLeft', 'ArrowUp', 'ArrowDown'];\nconst FOCUS_GROUP_ITEM_NAME = 'FocusGroupItem';\n\ntype FocusGroupItemElement = React.ComponentRef<typeof Primitive.button>;\ninterface FocusGroupItemProps extends PrimitiveButtonProps {}\n\nconst FocusGroupItem = React.forwardRef<FocusGroupItemElement, FocusGroupItemProps>(\n  (props: ScopedProps<FocusGroupItemProps>, forwardedRef) => {\n    const { __scopeNavigationMenu, ...groupProps } = props;\n    const getItems = useFocusGroupCollection(__scopeNavigationMenu);\n    const context = useNavigationMenuContext(FOCUS_GROUP_ITEM_NAME, __scopeNavigationMenu);\n\n    return (\n      <FocusGroupCollection.ItemSlot scope={__scopeNavigationMenu}>\n        <Primitive.button\n          {...groupProps}\n          ref={forwardedRef}\n          onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n            const isFocusNavigationKey = ['Home', 'End', ...ARROW_KEYS].includes(event.key);\n            if (isFocusNavigationKey) {\n              let candidateNodes = getItems().map((item) => item.ref.current!);\n              const prevItemKey = context.dir === 'rtl' ? 'ArrowRight' : 'ArrowLeft';\n              const prevKeys = [prevItemKey, 'ArrowUp', 'End'];\n              if (prevKeys.includes(event.key)) candidateNodes.reverse();\n              if (ARROW_KEYS.includes(event.key)) {\n                const currentIndex = candidateNodes.indexOf(event.currentTarget);\n                candidateNodes = candidateNodes.slice(currentIndex + 1);\n              }\n              /**\n               * Imperative focus during keydown is risky so we prevent React's batching updates\n               * to avoid potential bugs. See: https://github.com/facebook/react/issues/20332\n               */\n              setTimeout(() => focusFirst(candidateNodes));\n\n              // Prevent page scroll while navigating\n              event.preventDefault();\n            }\n          })}\n        />\n      </FocusGroupCollection.ItemSlot>\n    );\n  }\n);\n\n/**\n * Returns a list of potential tabbable candidates.\n *\n * NOTE: This is only a close approximation. For example it doesn't take into account cases like when\n * elements are not visible. This cannot be worked out easily by just reading a property, but rather\n * necessitate runtime knowledge (computed styles, etc). We deal with these cases separately.\n *\n * See: https://developer.mozilla.org/en-US/docs/Web/API/TreeWalker\n * Credit: https://github.com/discord/focus-layers/blob/master/src/util/wrapFocus.tsx#L1\n */\nfunction getTabbableCandidates(container: HTMLElement) {\n  const nodes: HTMLElement[] = [];\n  const walker = document.createTreeWalker(container, NodeFilter.SHOW_ELEMENT, {\n    acceptNode: (node: any) => {\n      const isHiddenInput = node.tagName === 'INPUT' && node.type === 'hidden';\n      if (node.disabled || node.hidden || isHiddenInput) return NodeFilter.FILTER_SKIP;\n      // `.tabIndex` is not the same as the `tabindex` attribute. It works on the\n      // runtime's understanding of tabbability, so this automatically accounts\n      // for any kind of element that could be tabbed to.\n      return node.tabIndex >= 0 ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_SKIP;\n    },\n  });\n  while (walker.nextNode()) nodes.push(walker.currentNode as HTMLElement);\n  // we do not take into account the order of nodes with positive `tabIndex` as it\n  // hinders accessibility to have tab order different from visual order.\n  return nodes;\n}\n\nfunction focusFirst(candidates: HTMLElement[]) {\n  const previouslyFocusedElement = document.activeElement;\n  return candidates.some((candidate) => {\n    // if focus is already where we want to go, we don't want to keep going through the candidates\n    if (candidate === previouslyFocusedElement) return true;\n    candidate.focus();\n    return document.activeElement !== previouslyFocusedElement;\n  });\n}\n\nfunction removeFromTabOrder(candidates: HTMLElement[]) {\n  candidates.forEach((candidate) => {\n    candidate.dataset.tabindex = candidate.getAttribute('tabindex') || '';\n    candidate.setAttribute('tabindex', '-1');\n  });\n  return () => {\n    candidates.forEach((candidate) => {\n      const prevTabIndex = candidate.dataset.tabindex as string;\n      candidate.setAttribute('tabindex', prevTabIndex);\n    });\n  };\n}\n\nfunction useResizeObserver(element: HTMLElement | null, onResize: () => void) {\n  const handleResize = useCallbackRef(onResize);\n  useLayoutEffect(() => {\n    let rAF = 0;\n    if (element) {\n      /**\n       * Resize Observer will throw an often benign error that says `ResizeObserver loop\n       * completed with undelivered notifications`. This means that ResizeObserver was not\n       * able to deliver all observations within a single animation frame, so we use\n       * `requestAnimationFrame` to ensure we don't deliver unnecessary observations.\n       * Further reading: https://github.com/WICG/resize-observer/issues/38\n       */\n      const resizeObserver = new ResizeObserver(() => {\n        cancelAnimationFrame(rAF);\n        rAF = window.requestAnimationFrame(handleResize);\n      });\n      resizeObserver.observe(element);\n      return () => {\n        window.cancelAnimationFrame(rAF);\n        resizeObserver.unobserve(element);\n      };\n    }\n  }, [element, handleResize]);\n}\n\nfunction getOpenState(open: boolean) {\n  return open ? 'open' : 'closed';\n}\n\nfunction makeTriggerId(baseId: string, value: string) {\n  return `${baseId}-trigger-${value}`;\n}\n\nfunction makeContentId(baseId: string, value: string) {\n  return `${baseId}-content-${value}`;\n}\n\nfunction whenMouse<E>(handler: React.PointerEventHandler<E>): React.PointerEventHandler<E> {\n  return (event) => (event.pointerType === 'mouse' ? handler(event) : undefined);\n}\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = NavigationMenu;\nconst Sub = NavigationMenuSub;\nconst List = NavigationMenuList;\nconst Item = NavigationMenuItem;\nconst Trigger = NavigationMenuTrigger;\nconst Link = NavigationMenuLink;\nconst Indicator = NavigationMenuIndicator;\nconst Content = NavigationMenuContent;\nconst Viewport = NavigationMenuViewport;\n\nexport {\n  createNavigationMenuScope,\n  //\n  NavigationMenu,\n  NavigationMenuSub,\n  NavigationMenuList,\n  NavigationMenuItem,\n  NavigationMenuTrigger,\n  NavigationMenuLink,\n  NavigationMenuIndicator,\n  NavigationMenuContent,\n  NavigationMenuViewport,\n  //\n  Root,\n  Sub,\n  List,\n  Item,\n  Trigger,\n  Link,\n  Indicator,\n  Content,\n  Viewport,\n};\nexport type {\n  NavigationMenuProps,\n  NavigationMenuSubProps,\n  NavigationMenuListProps,\n  NavigationMenuItemProps,\n  NavigationMenuTriggerProps,\n  NavigationMenuLinkProps,\n  NavigationMenuIndicatorProps,\n  NavigationMenuContentProps,\n  NavigationMenuViewportProps,\n};\n"], "names": ["value", "event", "props", "Root"], "mappings": "", "ignoreList": [0]}}, {"offset": {"line": 4566, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4572, "column": 0}, "map": {"version": 3, "file": "defaultAttributes.js", "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/lucide-react/src/defaultAttributes.ts"], "sourcesContent": ["export default {\n  xmlns: 'http://www.w3.org/2000/svg',\n  width: 24,\n  height: 24,\n  viewBox: '0 0 24 24',\n  fill: 'none',\n  stroke: 'currentColor',\n  strokeWidth: 2,\n  strokeLinecap: 'round',\n  strokeLinejoin: 'round',\n};\n"], "names": [], "mappings": ";;;;;;;;AAAA,CAAe,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACb,CAAA,CAAA,CAAA,CAAA,CAAO,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACP,CAAA,CAAA,CAAA,CAAA,CAAO,EAAA,CAAA,CAAA,CAAA;IACP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAA,CAAA,CAAA,CAAA;IACR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACT,CAAA,CAAA,CAAA,CAAM,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAa,EAAA,CAAA,CAAA;IACb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACf,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAClB,CAAA,CAAA", "ignoreList": [0]}}, {"offset": {"line": 4593, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4599, "column": 0}, "map": {"version": 3, "file": "utils.js", "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/shared/src/utils.ts"], "sourcesContent": ["import { CamelToPascal } from './utility-types';\n\n/**\n * Converts string to kebab case\n *\n * @param {string} string\n * @returns {string} A kebabized string\n */\nexport const toKebabCase = (string: string) =>\n  string.replace(/([a-z0-9])([A-Z])/g, '$1-$2').toLowerCase();\n\n/**\n * Converts string to camel case\n *\n * @param {string} string\n * @returns {string} A camelized string\n */\nexport const toCamelCase = <T extends string>(string: T) =>\n  string.replace(/^([A-Z])|[\\s-_]+(\\w)/g, (match, p1, p2) =>\n    p2 ? p2.toUpperCase() : p1.toLowerCase(),\n  );\n\n/**\n * Converts string to pascal case\n *\n * @param {string} string\n * @returns {string} A pascalized string\n */\nexport const toPascalCase = <T extends string>(string: T): CamelToPascal<T> => {\n  const camelCase = toCamelCase(string);\n\n  return (camelCase.charAt(0).toUpperCase() + camelCase.slice(1)) as CamelToPascal<T>;\n};\n\n/**\n * Merges classes into a single string\n *\n * @param {array} classes\n * @returns {string} A string of classes\n */\nexport const mergeClasses = <ClassType = string | undefined | null>(...classes: ClassType[]) =>\n  classes\n    .filter((className, index, array) => {\n      return (\n        Boolean(className) &&\n        (className as string).trim() !== '' &&\n        array.indexOf(className) === index\n      );\n    })\n    .join(' ')\n    .trim();\n"], "names": [], "mappings": ";;;;;;;;;AAQa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAC1B,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,OAAA,CAAQ,oBAAsB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,EAAE,WAAY,CAAA,CAAA,CAAA;AA+B/C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,YAAA,CAAe,CAAA,CAAA,CAAA,CAA2C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACrE,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACG,MAAA,CAAO,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,OAAO,KAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAEjC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,CAAA,CAChB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAW,CACjC,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IAEjC,CAAC,CACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAK,CAAG,CAAA,CAAA,CAAA,CACR,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0]}}, {"offset": {"line": 4614, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4620, "column": 0}, "map": {"version": 3, "file": "Icon.js", "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/lucide-react/src/Icon.ts"], "sourcesContent": ["import { createElement, forwardRef } from 'react';\nimport defaultAttributes from './defaultAttributes';\nimport { IconNode, LucideProps } from './types';\nimport { mergeClasses } from '@lucide/shared';\n\ninterface IconComponentProps extends LucideProps {\n  iconNode: IconNode;\n}\n\n/**\n * Lucide icon component\n *\n * @component Icon\n * @param {object} props\n * @param {string} props.color - The color of the icon\n * @param {number} props.size - The size of the icon\n * @param {number} props.strokeWidth - The stroke width of the icon\n * @param {boolean} props.absoluteStrokeWidth - Whether to use absolute stroke width\n * @param {string} props.className - The class name of the icon\n * @param {IconNode} props.children - The children of the icon\n * @param {IconNode} props.iconNode - The icon node of the icon\n *\n * @returns {ForwardRefExoticComponent} LucideIcon\n */\nconst Icon = forwardRef<SVGSVGElement, IconComponentProps>(\n  (\n    {\n      color = 'currentColor',\n      size = 24,\n      strokeWidth = 2,\n      absoluteStrokeWidth,\n      className = '',\n      children,\n      iconNode,\n      ...rest\n    },\n    ref,\n  ) => {\n    return createElement(\n      'svg',\n      {\n        ref,\n        ...defaultAttributes,\n        width: size,\n        height: size,\n        stroke: color,\n        strokeWidth: absoluteStrokeWidth ? (Number(strokeWidth) * 24) / Number(size) : strokeWidth,\n        className: mergeClasses('lucide', className),\n        ...rest,\n      },\n      [\n        ...iconNode.map(([tag, attrs]) => createElement(tag, attrs)),\n        ...(Array.isArray(children) ? children : [children]),\n      ],\n    );\n  },\n);\n\nexport default Icon;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAwBA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,6MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CACX,CACE,CAAA,CACE,CAAA,CAAA,CAAA,CAAA,CAAQ,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACR,CAAA,CAAA,CAAA,CAAO,GAAA,CAAA,CAAA,CAAA,CACP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,GAAA,CAAA,CAAA,CACd,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,GAAA,CAAA,CAAA,CAAA,CACZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACA,GAAG,CAAA,CAAA,CAAA,CAAA,EAAA,EAEL,GACG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACI,iNAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CACL,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACA,CAAA;QACE,CAAA,CAAA,CAAA,CAAA;QACA,uKAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACH,CAAA,CAAA,CAAA,CAAA,CAAO,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACR,WAAA,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAuB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAA,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAI,CAAA,CAAA,CAAA,CAAM,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAI,CAAA,CAAA,CAAA,CAAI,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAC/E,SAAA,CAAW,8KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,AAAa,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,SAAS,CAAA,CAAA;QAC3C,GAAG,CAAA,CAAA,CAAA,CAAA;IACL,CAAA,CAAA,CACA,CAAA;WACK,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAI,CAAC,CAAC,CAAK,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAM,6MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EAAc,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAK,CAAC,CAAA,CAAA;WACvD,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW;YAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ;SAAA;KACpD;AAEJ,CAAA", "ignoreList": [0]}}, {"offset": {"line": 4653, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4659, "column": 0}, "map": {"version": 3, "file": "createLucideIcon.js", "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/lucide-react/src/createLucideIcon.ts"], "sourcesContent": ["import { createElement, forwardRef } from 'react';\nimport { mergeClasses, toKebabCase } from '@lucide/shared';\nimport { IconNode, LucideProps } from './types';\nimport Icon from './Icon';\n\n/**\n * Create a Lucide icon component\n * @param {string} iconName\n * @param {array} iconNode\n * @returns {ForwardRefExoticComponent} LucideIcon\n */\nconst createLucideIcon = (iconName: string, iconNode: IconNode) => {\n  const Component = forwardRef<SVGSVGElement, LucideProps>(({ className, ...props }, ref) =>\n    createElement(Icon, {\n      ref,\n      iconNode,\n      className: mergeClasses(`lucide-${toKebabCase(iconName)}`, className),\n      ...props,\n    }),\n  );\n\n  Component.displayName = `${iconName}`;\n\n  return Component;\n};\n\nexport default createLucideIcon;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAWM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,gBAAA,CAAmB,CAAA,CAAA,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB,QAAuB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACjE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,2MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAuC,CAAC,CAAA,CAAE,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,yMACjF,gBAAA,yJAAc,UAAM,CAAA,CAAA,CAAA;YAClB,CAAA,CAAA,CAAA,CAAA;YACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACA,wLAAW,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,8KAAU,cAAA,EAAY,QAAQ,CAAC,EAAA,EAAI,SAAS,CAAA,CAAA;YACpE,GAAG,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CACJ,CAAA;IAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAA,CAAA;IAE5B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,SAAA,CAAA;AACT,CAAA,CAAA", "ignoreList": [0]}}, {"offset": {"line": 4685, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4691, "column": 0}, "map": {"version": 3, "file": "chevron-down.js", "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/lucide-react/src/icons/chevron-down.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [['path', { d: 'm6 9 6 6 6-6', key: 'qrunsl' }]];\n\n/**\n * @component @name ChevronDown\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtNiA5IDYgNiA2LTYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/chevron-down\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ChevronDown = createLucideIcon('ChevronDown', __iconNode);\n\nexport default ChevronDown;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAuB,CAAA,CAAA;IAAC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAC;KAAC;CAAA,CAAA;AAa7E,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,WAAA,CAAc,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAe,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0]}}, {"offset": {"line": 4714, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4730, "column": 0}, "map": {"version": 3, "file": "book-open.js", "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/lucide-react/src/icons/book-open.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M12 7v14', key: '1akyts' }],\n  [\n    'path',\n    {\n      d: 'M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z',\n      key: 'ruj8y',\n    },\n  ],\n];\n\n/**\n * @component @name BookOpen\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgN3YxNCIgLz4KICA8cGF0aCBkPSJNMyAxOGExIDEgMCAwIDEtMS0xVjRhMSAxIDAgMCAxIDEtMWg1YTQgNCAwIDAgMSA0IDQgNCA0IDAgMCAxIDQtNGg1YTEgMSAwIDAgMSAxIDF2MTNhMSAxIDAgMCAxLTEgMWgtNmEzIDMgMCAwIDAtMyAzIDMgMyAwIDAgMC0zLTN6IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/book-open\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst BookOpen = createLucideIcon('BookOpen', __iconNode);\n\nexport default BookOpen;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA,CAAA;IACzC,CAAA;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA,CAAA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,CAAA,CAAA,CAAK,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACP,CAAA;KACF;CACF,CAAA;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAW,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAY,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0]}}, {"offset": {"line": 4760, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4776, "column": 0}, "map": {"version": 3, "file": "life-buoy.js", "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/lucide-react/src/icons/life-buoy.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n  ['path', { d: 'm4.93 4.93 4.24 4.24', key: '1ymg45' }],\n  ['path', { d: 'm14.83 9.17 4.24-4.24', key: '1cb5xl' }],\n  ['path', { d: 'm14.83 14.83 4.24 4.24', key: 'q42g0n' }],\n  ['path', { d: 'm9.17 14.83-4.24 4.24', key: 'bqpfvv' }],\n  ['circle', { cx: '12', cy: '12', r: '4', key: '4exip2' }],\n];\n\n/**\n * @component @name LifeBuoy\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KICA8cGF0aCBkPSJtNC45MyA0LjkzIDQuMjQgNC4yNCIgLz4KICA8cGF0aCBkPSJtMTQuODMgOS4xNyA0LjI0LTQuMjQiIC8+CiAgPHBhdGggZD0ibTE0LjgzIDE0LjgzIDQuMjQgNC4yNCIgLz4KICA8cGF0aCBkPSJtOS4xNyAxNC44My00LjI0IDQuMjQiIC8+CiAgPGNpcmNsZSBjeD0iMTIiIGN5PSIxMiIgcj0iNCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/life-buoy\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst LifeBuoy = createLucideIcon('LifeBuoy', __iconNode);\n\nexport default LifeBuoy;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA,CAAA;IAClC;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA,CAAA;IACzD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAwB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA,CAAA;IACrD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAyB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA,CAAA;IACtD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA0B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA,CAAA;IACvD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAyB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA,CAAA;IACtD;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;CAC1D,CAAA;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAW,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAY,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0]}}, {"offset": {"line": 4838, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4854, "column": 0}, "map": {"version": 3, "file": "info.js", "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/lucide-react/src/icons/info.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n  ['path', { d: 'M12 16v-4', key: '1dtifu' }],\n  ['path', { d: 'M12 8h.01', key: 'e9boi3' }],\n];\n\n/**\n * @component @name Info\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KICA8cGF0aCBkPSJNMTIgMTZ2LTQiIC8+CiAgPHBhdGggZD0iTTEyIDhoLjAxIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/info\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Info = createLucideIcon('Info', __iconNode);\n\nexport default Info;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA,CAAA;IAClC;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA,CAAA;IACzD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA,CAAA;IAC1C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAC5C,CAAA;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,AAAjB,CAAA,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0]}}, {"offset": {"line": 4893, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}
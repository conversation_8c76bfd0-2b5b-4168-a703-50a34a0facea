{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/src/components/ui/backgroundEffect.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ui/backgroundEffect.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/backgroundEffect.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA0S,GACvU,wEACA"}}, {"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/src/components/ui/backgroundEffect.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ui/backgroundEffect.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/backgroundEffect.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAsR,GACnT,oDACA"}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 35, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 45, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/src/components/ui/FloatingActionButton.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ui/FloatingActionButton.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/FloatingActionButton.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA8S,GAC3U,4EACA"}}, {"offset": {"line": 53, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 59, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/src/components/ui/FloatingActionButton.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ui/FloatingActionButton.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/FloatingActionButton.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA0R,GACvT,wDACA"}}, {"offset": {"line": 67, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 73, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 77, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 83, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\";\nimport { twMerge } from \"tailwind-merge\";\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB"}}, {"offset": {"line": 93, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 99, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport { cva, type VariantProps } from \"class-variance-authority\";\nimport { Slot } from \"@radix-ui/react-slot\";\n\nimport { cn } from \"@/lib/utils\";\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-[8px] text-sm font-medium transition-[color,box-shadow] disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 [&_svg]:shrink-0 outline-none focus-visible:border-[var(--highlight)] focus-visible:ring-[var(--highlight)]/50 focus-visible:ring-[3px]\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-[var(--button)] text-[var(--button-text)] shadow-sm hover:bg-[color-mix(in_srgb,var(--button),#00000020)]\",\n        destructive:\n          \"bg-[var(--tertiary)] text-[var(--button-text)] shadow-xs hover:bg-[color-mix(in_srgb,var(--tertiary),#00000020)] focus-visible:ring-[var(--tertiary)]/20\",\n        outline:\n          \"border border-[var(--input-border-color)] bg-[var(--background)] shadow-xs hover:bg-[var(--card-hover)] hover:text-[var(--highlight)]\",\n        secondary:\n          \"bg-[var(--secondary)] text-[var(--button-text)] shadow-xs hover:bg-[color-mix(in_srgb,var(--secondary),#00000020)]\",\n        ghost: \"hover:bg-[var(--card-hover)] hover:text-[var(--highlight)]\",\n        link: \"text-[var(--link-color)] underline-offset-4 hover:text-[var(--link-hover)] hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2\",\n        sm: \"h-8 rounded-[8px] px-3 text-xs\",\n        lg: \"h-10 rounded-[8px] px-8\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n);\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean;\n  }) {\n  const Comp = asChild ? Slot : \"button\";\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  );\n}\n\nexport { Button, buttonVariants };\n"], "names": [], "mappings": ";;;;;AACA;AAGA;AAFA;;;;;AAIA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,6XACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf"}}, {"offset": {"line": 150, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 156, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/src/components/ui/navigation-menu.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva } from \"class-variance-authority\"\nimport { ChevronDownIcon } from \"lucide-react\"\nimport { NavigationMenu as NavigationMenuPrimitive } from \"radix-ui\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction NavigationMenu({\n  className,\n  children,\n  viewport = true,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Root> & {\n  viewport?: boolean\n}) {\n  return (\n    <NavigationMenuPrimitive.Root\n      data-slot=\"navigation-menu\"\n      data-viewport={viewport}\n      className={cn(\n        \"group/navigation-menu relative flex max-w-max flex-1 items-center justify-center\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      {viewport && <NavigationMenuViewport />}\n    </NavigationMenuPrimitive.Root>\n  )\n}\n\nfunction NavigationMenuList({\n  className,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.List>) {\n  return (\n    <NavigationMenuPrimitive.List\n      data-slot=\"navigation-menu-list\"\n      className={cn(\n        \"group flex flex-1 list-none items-center justify-center gap-1\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction NavigationMenuItem({\n  className,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Item>) {\n  return (\n    <NavigationMenuPrimitive.Item\n      data-slot=\"navigation-menu-item\"\n      className={cn(\"relative\", className)}\n      {...props}\n    />\n  )\n}\n\nconst navigationMenuTriggerStyle = cva(\n  \"group inline-flex h-9 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground disabled:pointer-events-none disabled:opacity-50 data-[state=open]:hover:bg-accent data-[state=open]:text-accent-foreground data-[state=open]:focus:bg-accent data-[state=open]:bg-accent/50 focus-visible:ring-ring/50 outline-none transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1\"\n)\n\nfunction NavigationMenuTrigger({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Trigger>) {\n  return (\n    <NavigationMenuPrimitive.Trigger\n      data-slot=\"navigation-menu-trigger\"\n      className={cn(navigationMenuTriggerStyle(), \"group\", className)}\n      {...props}\n    >\n      {children}{\" \"}\n      <ChevronDownIcon\n        className=\"relative top-[1px] ml-1 size-3 transition duration-300 group-data-[state=open]:rotate-180\"\n        aria-hidden=\"true\"\n      />\n    </NavigationMenuPrimitive.Trigger>\n  )\n}\n\nfunction NavigationMenuContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Content>) {\n  return (\n    <NavigationMenuPrimitive.Content\n      data-slot=\"navigation-menu-content\"\n      className={cn(\n        \"data-[motion^=from-]:animate-in data-[motion^=to-]:animate-out data-[motion^=from-]:fade-in data-[motion^=to-]:fade-out data-[motion=from-end]:slide-in-from-right-52 data-[motion=from-start]:slide-in-from-left-52 data-[motion=to-end]:slide-out-to-right-52 data-[motion=to-start]:slide-out-to-left-52 top-0 left-0 w-full p-2 pr-2.5 md:absolute md:w-auto\",\n        \"group-data-[viewport=false]/navigation-menu:bg-popover group-data-[viewport=false]/navigation-menu:text-popover-foreground group-data-[viewport=false]/navigation-menu:data-[state=open]:animate-in group-data-[viewport=false]/navigation-menu:data-[state=closed]:animate-out group-data-[viewport=false]/navigation-menu:data-[state=closed]:zoom-out-95 group-data-[viewport=false]/navigation-menu:data-[state=open]:zoom-in-95 group-data-[viewport=false]/navigation-menu:data-[state=open]:fade-in-0 group-data-[viewport=false]/navigation-menu:data-[state=closed]:fade-out-0 group-data-[viewport=false]/navigation-menu:top-full group-data-[viewport=false]/navigation-menu:mt-1.5 group-data-[viewport=false]/navigation-menu:overflow-hidden group-data-[viewport=false]/navigation-menu:rounded-md group-data-[viewport=false]/navigation-menu:border group-data-[viewport=false]/navigation-menu:shadow group-data-[viewport=false]/navigation-menu:duration-200 **:data-[slot=navigation-menu-link]:focus:ring-0 **:data-[slot=navigation-menu-link]:focus:outline-none\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction NavigationMenuViewport({\n  className,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Viewport>) {\n  return (\n    <div\n      className={cn(\n        \"absolute top-full left-0 isolate z-50 flex justify-center\"\n      )}\n    >\n      <NavigationMenuPrimitive.Viewport\n        data-slot=\"navigation-menu-viewport\"\n        className={cn(\n          \"origin-top-center bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-90 relative mt-1.5 h-[var(--radix-navigation-menu-viewport-height)] w-full overflow-hidden rounded-md border shadow md:w-[var(--radix-navigation-menu-viewport-width)]\",\n          className\n        )}\n        {...props}\n      />\n    </div>\n  )\n}\n\nfunction NavigationMenuLink({\n  className,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Link>) {\n  return (\n    <NavigationMenuPrimitive.Link\n      data-slot=\"navigation-menu-link\"\n      className={cn(\n        \"data-[active]:focus:bg-accent data-[active]:hover:bg-accent data-[active]:bg-accent data-[active]:text-accent-foreground hover:bg-accent focus:bg-accent focus:text-accent-foreground focus-visible:ring-ring/50 [&_svg:not([class*='text-'])]:text-muted-foreground flex flex-col gap-1 rounded-sm p-2 text-sm transition-all outline-none focus-visible:ring-[3px] focus-visible:outline-1 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction NavigationMenuIndicator({\n  className,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Indicator>) {\n  return (\n    <NavigationMenuPrimitive.Indicator\n      data-slot=\"navigation-menu-indicator\"\n      className={cn(\n        \"data-[state=visible]:animate-in data-[state=hidden]:animate-out data-[state=hidden]:fade-out data-[state=visible]:fade-in top-full z-[1] flex h-1.5 items-end justify-center overflow-hidden\",\n        className\n      )}\n      {...props}\n    >\n      <div className=\"bg-border relative top-[60%] h-2 w-2 rotate-45 rounded-tl-sm shadow-md\" />\n    </NavigationMenuPrimitive.Indicator>\n  )\n}\n\nexport {\n  NavigationMenu,\n  NavigationMenuList,\n  NavigationMenuItem,\n  NavigationMenuContent,\n  NavigationMenuTrigger,\n  NavigationMenuLink,\n  NavigationMenuIndicator,\n  NavigationMenuViewport,\n  navigationMenuTriggerStyle,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AACA;AAIA;AAFA;AADA;;;;;;AAKA,SAAS,eAAe,EACtB,SAAS,EACT,QAAQ,EACR,WAAW,IAAI,EACf,GAAG,OAGJ;IACC,qBACE,8OAAC,wNAAA,CAAA,iBAAuB,CAAC,IAAI;QAC3B,aAAU;QACV,iBAAe;QACf,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oFACA;QAED,GAAG,KAAK;;YAER;YACA,0BAAY,8OAAC;;;;;;;;;;;AAGpB;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACuD;IAC1D,qBACE,8OAAC,wNAAA,CAAA,iBAAuB,CAAC,IAAI;QAC3B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACuD;IAC1D,qBACE,8OAAC,wNAAA,CAAA,iBAAuB,CAAC,IAAI;QAC3B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QACzB,GAAG,KAAK;;;;;;AAGf;AAEA,MAAM,6BAA6B,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACnC;AAGF,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,8OAAC,wNAAA,CAAA,iBAAuB,CAAC,OAAO;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B,SAAS;QACpD,GAAG,KAAK;;YAER;YAAU;0BACX,8OAAC,wNAAA,CAAA,kBAAe;gBACd,WAAU;gBACV,eAAY;;;;;;;;;;;;AAIpB;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,8OAAC,wNAAA,CAAA,iBAAuB,CAAC,OAAO;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oWACA,6hCACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV;kBAGF,cAAA,8OAAC,wNAAA,CAAA,iBAAuB,CAAC,QAAQ;YAC/B,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sVACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACuD;IAC1D,qBACE,8OAAC,wNAAA,CAAA,iBAAuB,CAAC,IAAI;QAC3B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qaACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,wBAAwB,EAC/B,SAAS,EACT,GAAG,OAC4D;IAC/D,qBACE,8OAAC,wNAAA,CAAA,iBAAuB,CAAC,SAAS;QAChC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gMACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC;YAAI,WAAU;;;;;;;;;;;AAGrB"}}, {"offset": {"line": 302, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 308, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/src/components/ui/popover.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Popover = registerClientReference(\n    function() { throw new Error(\"Attempted to call Popover() from the server but Popover is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/popover.tsx <module evaluation>\",\n    \"Popover\",\n);\nexport const PopoverAnchor = registerClientReference(\n    function() { throw new Error(\"Attempted to call PopoverAnchor() from the server but PopoverAnchor is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/popover.tsx <module evaluation>\",\n    \"PopoverAnchor\",\n);\nexport const PopoverContent = registerClientReference(\n    function() { throw new Error(\"Attempted to call PopoverContent() from the server but PopoverContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/popover.tsx <module evaluation>\",\n    \"PopoverContent\",\n);\nexport const PopoverTrigger = registerClientReference(\n    function() { throw new Error(\"Attempted to call PopoverTrigger() from the server but PopoverTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/popover.tsx <module evaluation>\",\n    \"PopoverTrigger\",\n);\n"], "names": [], "mappings": ";;;;;;AAAA;;AACO,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,+DACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,+DACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,+DACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,+DACA"}}, {"offset": {"line": 328, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 334, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/src/components/ui/popover.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Popover = registerClientReference(\n    function() { throw new Error(\"Attempted to call Popover() from the server but Popover is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/popover.tsx\",\n    \"Popover\",\n);\nexport const PopoverAnchor = registerClientReference(\n    function() { throw new Error(\"Attempted to call PopoverAnchor() from the server but PopoverAnchor is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/popover.tsx\",\n    \"PopoverAnchor\",\n);\nexport const PopoverContent = registerClientReference(\n    function() { throw new Error(\"Attempted to call PopoverContent() from the server but PopoverContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/popover.tsx\",\n    \"PopoverContent\",\n);\nexport const PopoverTrigger = registerClientReference(\n    function() { throw new Error(\"Attempted to call PopoverTrigger() from the server but PopoverTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/popover.tsx\",\n    \"PopoverTrigger\",\n);\n"], "names": [], "mappings": ";;;;;;AAAA;;AACO,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,2CACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,2CACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,2CACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,2CACA"}}, {"offset": {"line": 354, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 360, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 364, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 370, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/src/components/common/Logo.tsx"], "sourcesContent": ["export default function Logo() {\n  return (\n    <div>\n      <h1>Logo</h1>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAe,SAAS;IACtB,qBACE,8OAAC;kBACC,cAAA,8OAAC;sBAAG;;;;;;;;;;;AAGV"}}, {"offset": {"line": 390, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 396, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/src/components/common/Navbar.tsx"], "sourcesContent": ["import { BookOpenIcon, InfoIcon, LifeBuoyIcon } from \"lucide-react\";\n\nimport { cn } from \"@/lib/utils\";\nimport { Button } from \"@/components/ui/button\";\nimport {\n  NavigationMenu,\n  NavigationMenuContent,\n  NavigationMenuItem,\n  NavigationMenuLink,\n  NavigationMenuList,\n  NavigationMenuTrigger,\n} from \"@/components/ui/navigation-menu\";\nimport {\n  Popover,\n  PopoverContent,\n  PopoverTrigger,\n} from \"@/components/ui/popover\";\nimport Logo from \"./Logo\";\n\nconst navigationLinks = [\n  { href: \"#\", label: \"Home\" },\n  {\n    label: \"Features\",\n    submenu: true,\n    type: \"description\",\n    items: [\n      {\n        href: \"#\",\n        label: \"Components\",\n        description: \"Browse all components in the library.\",\n      },\n      {\n        href: \"#\",\n        label: \"Documentation\",\n        description: \"Learn how to use the library.\",\n      },\n      {\n        href: \"#\",\n        label: \"Templates\",\n        description: \"Pre-built layouts for common use cases.\",\n      },\n    ],\n  },\n  {\n    label: \"Pricing\",\n    submenu: true,\n    type: \"simple\",\n    items: [\n      { href: \"#\", label: \"Product A\" },\n      { href: \"#\", label: \"Product B\" },\n      { href: \"#\", label: \"Product C\" },\n      { href: \"#\", label: \"Product D\" },\n    ],\n  },\n  {\n    label: \"About\",\n    submenu: true,\n    type: \"icon\",\n    items: [\n      { href: \"#\", label: \"Getting Started\", icon: \"BookOpenIcon\" },\n      { href: \"#\", label: \"Tutorials\", icon: \"LifeBuoyIcon\" },\n      { href: \"#\", label: \"About Us\", icon: \"InfoIcon\" },\n    ],\n  },\n];\n\nexport default function Navbar() {\n  return (\n    <header className=\"border-b   border-[var(--border)] bg-[var(--background)] \">\n      <div className=\"flex h-16 items-center justify-between gap-4 container mx-auto\">\n        <div className=\"flex items-center gap-2\">\n          <Popover>\n            <PopoverTrigger asChild>\n              <Button\n                className=\"group size-8 md:hidden text-[var(--menu-color)] hover:bg-[var(--card-hover)]\"\n                variant=\"ghost\"\n                size=\"icon\"\n              >\n                <svg\n                  className=\"pointer-events-none\"\n                  width={16}\n                  height={16}\n                  viewBox=\"0 0 24 24\"\n                  fill=\"none\"\n                  stroke=\"currentColor\"\n                  strokeWidth=\"2\"\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                >\n                  <path\n                    d=\"M4 12L20 12\"\n                    className=\"origin-center -translate-y-[7px] transition-all duration-300 ease-[cubic-bezier(.5,.85,.25,1.1)] group-aria-expanded:translate-x-0 group-aria-expanded:translate-y-0 group-aria-expanded:rotate-[315deg]\"\n                  />\n                  <path\n                    d=\"M4 12H20\"\n                    className=\"origin-center transition-all duration-300 ease-[cubic-bezier(.5,.85,.25,1.8)] group-aria-expanded:rotate-45\"\n                  />\n                  <path\n                    d=\"M4 12H20\"\n                    className=\"origin-center translate-y-[7px] transition-all duration-300 ease-[cubic-bezier(.5,.85,.25,1.1)] group-aria-expanded:translate-y-0 group-aria-expanded:rotate-[135deg]\"\n                  />\n                </svg>\n              </Button>\n            </PopoverTrigger>\n            <PopoverContent\n              align=\"start\"\n              className=\"w-64 p-1 md:hidden bg-[var(--background)] border border-[var(--border)]\"\n            >\n              <NavigationMenu className=\"max-w-none *:w-full\">\n                <NavigationMenuList className=\"flex-col items-start gap-0 md:gap-2\">\n                  {navigationLinks.map((link, index) => (\n                    <NavigationMenuItem key={index} className=\"w-full\">\n                      {link.submenu ? (\n                        <>\n                          <div className=\"text-[var(--nav-item)] px-2 py-1.5 text-xs font-medium\">\n                            {link.label}\n                          </div>\n                          <ul>\n                            {link.items.map((item, itemIndex) => (\n                              <li key={itemIndex}>\n                                <NavigationMenuLink\n                                  href={item.href}\n                                  className=\"py-1.5 text-[var(--paragraph)] hover:text-[var(--highlight)]\"\n                                >\n                                  {item.label}\n                                </NavigationMenuLink>\n                              </li>\n                            ))}\n                          </ul>\n                        </>\n                      ) : (\n                        <NavigationMenuLink\n                          href={link.href}\n                          className=\"py-1.5 text-[var(--paragraph)] hover:text-[var(--highlight)]\"\n                        >\n                          {link.label}\n                        </NavigationMenuLink>\n                      )}\n                      {index < navigationLinks.length - 1 &&\n                        ((!link.submenu &&\n                          navigationLinks[index + 1].submenu) ||\n                          (link.submenu &&\n                            !navigationLinks[index + 1].submenu) ||\n                          (link.submenu &&\n                            navigationLinks[index + 1].submenu &&\n                            link.type !== navigationLinks[index + 1].type)) && (\n                          <div\n                            role=\"separator\"\n                            aria-orientation=\"horizontal\"\n                            className=\"bg-[var(--border)] -mx-1 my-1 h-px w-full\"\n                          />\n                        )}\n                    </NavigationMenuItem>\n                  ))}\n                </NavigationMenuList>\n              </NavigationMenu>\n            </PopoverContent>\n          </Popover>\n          <div className=\"flex items-center gap-6\">\n            <a\n              href=\"#\"\n              className=\"text-[var(--link-color)] hover:text-[var(--link-hover)]\"\n            >\n              <Logo />\n            </a>\n            <NavigationMenu viewport={false} className=\"max-md:hidden\">\n              <NavigationMenuList className=\"gap-2\">\n                {navigationLinks.map((link, index) => (\n                  <NavigationMenuItem key={index}>\n                    {link.submenu ? (\n                      <>\n                        <NavigationMenuTrigger className=\"text-[var(--nav-item)] hover:text-[var(--highlight)] bg-transparent px-2 py-1.5 font-medium *:[svg]:-me-0.5 *:[svg]:size-3.5\">\n                          {link.label}\n                        </NavigationMenuTrigger>\n                        <NavigationMenuContent className=\"z-50 p-1 bg-[var(--background)] border border-[var(--border)]\">\n                          <ul\n                            className={cn(\n                              link.type === \"description\"\n                                ? \"min-w-64\"\n                                : \"min-w-48\"\n                            )}\n                          >\n                            {link.items.map((item, itemIndex) => (\n                              <li key={itemIndex}>\n                                <NavigationMenuLink\n                                  href={item.href}\n                                  className=\"py-1.5 text-[var(--paragraph)] hover:text-[var(--highlight)]\"\n                                >\n                                  {link.type === \"icon\" && \"icon\" in item && (\n                                    <div className=\"flex items-center gap-2\">\n                                      {item.icon === \"BookOpenIcon\" && (\n                                        <BookOpenIcon\n                                          size={16}\n                                          className=\"text-[var(--menu-color)] opacity-60\"\n                                          aria-hidden=\"true\"\n                                        />\n                                      )}\n                                      {item.icon === \"LifeBuoyIcon\" && (\n                                        <LifeBuoyIcon\n                                          size={16}\n                                          className=\"text-[var(--menu-color)] opacity-60\"\n                                          aria-hidden=\"true\"\n                                        />\n                                      )}\n                                      {item.icon === \"InfoIcon\" && (\n                                        <InfoIcon\n                                          size={16}\n                                          className=\"text-[var(--menu-color)] opacity-60\"\n                                          aria-hidden=\"true\"\n                                        />\n                                      )}\n                                      <span>{item.label}</span>\n                                    </div>\n                                  )}\n                                  {link.type === \"description\" &&\n                                  \"description\" in item ? (\n                                    <div className=\"space-y-1\">\n                                      <div className=\"font-medium\">\n                                        {item.label}\n                                      </div>\n                                      <p className=\"text-[var(--paragraph)] line-clamp-2 text-xs\">\n                                        {item.description}\n                                      </p>\n                                    </div>\n                                  ) : (\n                                    !link.type ||\n                                    (link.type !== \"icon\" &&\n                                      link.type !== \"description\" && (\n                                        <span>{item.label}</span>\n                                      ))\n                                  )}\n                                </NavigationMenuLink>\n                              </li>\n                            ))}\n                          </ul>\n                        </NavigationMenuContent>\n                      </>\n                    ) : (\n                      <NavigationMenuLink\n                        href={link.href}\n                        className=\"text-[var(--nav-item)] hover:text-[var(--highlight)] py-1.5 font-medium\"\n                      >\n                        {link.label}\n                      </NavigationMenuLink>\n                    )}\n                  </NavigationMenuItem>\n                ))}\n              </NavigationMenuList>\n            </NavigationMenu>\n          </div>\n        </div>\n        <div className=\"flex items-center gap-2\">\n          <Button\n            asChild\n            variant=\"ghost\"\n            size=\"sm\"\n            className=\"text-sm text-[var(--button-text)] hover:bg-[var(--card-hover)]\"\n          >\n            <a href=\"#\">Sign In</a>\n          </Button>\n          <Button\n            asChild\n            size=\"sm\"\n            className=\"text-sm bg-[var(--button)] text-[var(--button-text)] \"\n          >\n            <a href=\"#\">Get Started</a>\n          </Button>\n        </div>\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAQA;AAKA;AAjBA;AAAA;AAAA;;;;;;;;AAmBA,MAAM,kBAAkB;IACtB;QAAE,MAAM;QAAK,OAAO;IAAO;IAC3B;QACE,OAAO;QACP,SAAS;QACT,MAAM;QACN,OAAO;YACL;gBACE,MAAM;gBACN,OAAO;gBACP,aAAa;YACf;YACA;gBACE,MAAM;gBACN,OAAO;gBACP,aAAa;YACf;YACA;gBACE,MAAM;gBACN,OAAO;gBACP,aAAa;YACf;SACD;IACH;IACA;QACE,OAAO;QACP,SAAS;QACT,MAAM;QACN,OAAO;YACL;gBAAE,MAAM;gBAAK,OAAO;YAAY;YAChC;gBAAE,MAAM;gBAAK,OAAO;YAAY;YAChC;gBAAE,MAAM;gBAAK,OAAO;YAAY;YAChC;gBAAE,MAAM;gBAAK,OAAO;YAAY;SACjC;IACH;IACA;QACE,OAAO;QACP,SAAS;QACT,MAAM;QACN,OAAO;YACL;gBAAE,MAAM;gBAAK,OAAO;gBAAmB,MAAM;YAAe;YAC5D;gBAAE,MAAM;gBAAK,OAAO;gBAAa,MAAM;YAAe;YACtD;gBAAE,MAAM;gBAAK,OAAO;gBAAY,MAAM;YAAW;SAClD;IACH;CACD;AAEc,SAAS;IACtB,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,mIAAA,CAAA,UAAO;;8CACN,8OAAC,mIAAA,CAAA,iBAAc;oCAAC,OAAO;8CACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCACL,WAAU;wCACV,SAAQ;wCACR,MAAK;kDAEL,cAAA,8OAAC;4CACC,WAAU;4CACV,OAAO;4CACP,QAAQ;4CACR,SAAQ;4CACR,MAAK;4CACL,QAAO;4CACP,aAAY;4CACZ,eAAc;4CACd,gBAAe;4CACf,OAAM;;8DAEN,8OAAC;oDACC,GAAE;oDACF,WAAU;;;;;;8DAEZ,8OAAC;oDACC,GAAE;oDACF,WAAU;;;;;;8DAEZ,8OAAC;oDACC,GAAE;oDACF,WAAU;;;;;;;;;;;;;;;;;;;;;;8CAKlB,8OAAC,mIAAA,CAAA,iBAAc;oCACb,OAAM;oCACN,WAAU;8CAEV,cAAA,8OAAC,8IAAA,CAAA,iBAAc;wCAAC,WAAU;kDACxB,cAAA,8OAAC,8IAAA,CAAA,qBAAkB;4CAAC,WAAU;sDAC3B,gBAAgB,GAAG,CAAC,CAAC,MAAM,sBAC1B,8OAAC,8IAAA,CAAA,qBAAkB;oDAAa,WAAU;;wDACvC,KAAK,OAAO,iBACX;;8EACE,8OAAC;oEAAI,WAAU;8EACZ,KAAK,KAAK;;;;;;8EAEb,8OAAC;8EACE,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,0BACrB,8OAAC;sFACC,cAAA,8OAAC,8IAAA,CAAA,qBAAkB;gFACjB,MAAM,KAAK,IAAI;gFACf,WAAU;0FAET,KAAK,KAAK;;;;;;2EALN;;;;;;;;;;;yFAYf,8OAAC,8IAAA,CAAA,qBAAkB;4DACjB,MAAM,KAAK,IAAI;4DACf,WAAU;sEAET,KAAK,KAAK;;;;;;wDAGd,QAAQ,gBAAgB,MAAM,GAAG,KAChC,CAAC,AAAC,CAAC,KAAK,OAAO,IACb,eAAe,CAAC,QAAQ,EAAE,CAAC,OAAO,IACjC,KAAK,OAAO,IACX,CAAC,eAAe,CAAC,QAAQ,EAAE,CAAC,OAAO,IACpC,KAAK,OAAO,IACX,eAAe,CAAC,QAAQ,EAAE,CAAC,OAAO,IAClC,KAAK,IAAI,KAAK,eAAe,CAAC,QAAQ,EAAE,CAAC,IAAI,AAAC,mBAChD,8OAAC;4DACC,MAAK;4DACL,oBAAiB;4DACjB,WAAU;;;;;;;mDAtCO;;;;;;;;;;;;;;;;;;;;;;;;;;sCA+CnC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,WAAU;8CAEV,cAAA,8OAAC,oIAAA,CAAA,UAAI;;;;;;;;;;8CAEP,8OAAC,8IAAA,CAAA,iBAAc;oCAAC,UAAU;oCAAO,WAAU;8CACzC,cAAA,8OAAC,8IAAA,CAAA,qBAAkB;wCAAC,WAAU;kDAC3B,gBAAgB,GAAG,CAAC,CAAC,MAAM,sBAC1B,8OAAC,8IAAA,CAAA,qBAAkB;0DAChB,KAAK,OAAO,iBACX;;sEACE,8OAAC,8IAAA,CAAA,wBAAqB;4DAAC,WAAU;sEAC9B,KAAK,KAAK;;;;;;sEAEb,8OAAC,8IAAA,CAAA,wBAAqB;4DAAC,WAAU;sEAC/B,cAAA,8OAAC;gEACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,KAAK,IAAI,KAAK,gBACV,aACA;0EAGL,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,0BACrB,8OAAC;kFACC,cAAA,8OAAC,8IAAA,CAAA,qBAAkB;4EACjB,MAAM,KAAK,IAAI;4EACf,WAAU;;gFAET,KAAK,IAAI,KAAK,UAAU,UAAU,sBACjC,8OAAC;oFAAI,WAAU;;wFACZ,KAAK,IAAI,KAAK,gCACb,8OAAC,kNAAA,CAAA,eAAY;4FACX,MAAM;4FACN,WAAU;4FACV,eAAY;;;;;;wFAGf,KAAK,IAAI,KAAK,gCACb,8OAAC,kNAAA,CAAA,eAAY;4FACX,MAAM;4FACN,WAAU;4FACV,eAAY;;;;;;wFAGf,KAAK,IAAI,KAAK,4BACb,8OAAC,sMAAA,CAAA,WAAQ;4FACP,MAAM;4FACN,WAAU;4FACV,eAAY;;;;;;sGAGhB,8OAAC;sGAAM,KAAK,KAAK;;;;;;;;;;;;gFAGpB,KAAK,IAAI,KAAK,iBACf,iBAAiB,qBACf,8OAAC;oFAAI,WAAU;;sGACb,8OAAC;4FAAI,WAAU;sGACZ,KAAK,KAAK;;;;;;sGAEb,8OAAC;4FAAE,WAAU;sGACV,KAAK,WAAW;;;;;;;;;;;2FAIrB,CAAC,KAAK,IAAI,IACT,KAAK,IAAI,KAAK,UACb,KAAK,IAAI,KAAK,+BACZ,8OAAC;8FAAM,KAAK,KAAK;;;;;;;;;;;;uEA7ClB;;;;;;;;;;;;;;;;iFAuDjB,8OAAC,8IAAA,CAAA,qBAAkB;oDACjB,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,KAAK;;;;;;+CA1EQ;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAmFnC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,kIAAA,CAAA,SAAM;4BACL,OAAO;4BACP,SAAQ;4BACR,MAAK;4BACL,WAAU;sCAEV,cAAA,8OAAC;gCAAE,MAAK;0CAAI;;;;;;;;;;;sCAEd,8OAAC,kIAAA,CAAA,SAAM;4BACL,OAAO;4BACP,MAAK;4BACL,WAAU;sCAEV,cAAA,8OAAC;gCAAE,MAAK;0CAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMxB"}}, {"offset": {"line": 873, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 879, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/src/components/common/Footer.tsx"], "sourcesContent": ["import React from \"react\";\n\nexport default function Footer() {\n  return (\n    <div>\n      <h1>footer</h1>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;kBACC,cAAA,8OAAC;sBAAG;;;;;;;;;;;AAGV"}}, {"offset": {"line": 899, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 905, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/src/app/layout.tsx"], "sourcesContent": ["import type { <PERSON>ada<PERSON>, Viewport } from \"next\";\nimport \"./globals.css\";\n\nimport BackgroundEffect from \"@/components/ui/backgroundEffect\";\nimport { CustomDialogProvider } from \"@/components/ui/custom-dialog\";\nimport FloatingActionButton from \"@/components/ui/FloatingActionButton\";\nimport { Analytics } from \"@vercel/analytics/react\";\nimport { SpeedInsights } from \"@vercel/speed-insights/next\";\nimport { Toaster } from \"sonner\";\nimport Navbar from \"@/components/common/Navbar\";\nimport Footer from \"@/components/common/Footer\";\n\nconst domain = \"templgen.com\";\nconst websitePath = {\n  main: `https://${domain}`,\n};\nconst webImage = `${websitePath.main}/images/profile.jpg`; // Adjust this image path as needed\nconst email = \"<EMAIL>\"; // Replace with your actual email\n\nconst keywords = [\n  \"templgen\",\n  \"website templates\",\n  \"mobile templates\",\n  \"React templates\",\n  \"Next.js templates\",\n  \"MERN templates\",\n  \"UI templates\",\n  \"frontend templates\",\n  \"backend templates\",\n  \"software templates\",\n  \"web development\",\n  \"template marketplace\",\n  \"responsive templates\",\n];\n\nexport const metadata: Metadata = {\n  metadataBase: new URL(websitePath.main),\n  title: {\n    template: \"TemplGen - %s\",\n    default: \"TemplGen - Website & Mobile Templates Marketplace\",\n  },\n  description:\n    \"TemplGen offers premium website and mobile templates for developers and businesses. Explore React, Next.js, and MERN templates to accelerate your projects.\",\n  keywords: keywords.join(\", \"),\n  authors: [{ name: \"TemplGen\", url: websitePath.main }],\n  creator: \"TemplGen\",\n  publisher: \"TemplGen\",\n  formatDetection: {\n    email: false,\n    address: false,\n    telephone: false,\n  },\n  alternates: {\n    canonical: websitePath.main,\n    languages: {\n      en: `${websitePath.main}/en`,\n      ar: `${websitePath.main}/ar`,\n    },\n  },\n  openGraph: {\n    type: \"website\",\n    locale: \"en_US\",\n    alternateLocale: \"ar_SA\",\n    title: \"TemplGen Marketplace\",\n    description:\n      \"Browse and purchase premium website and mobile templates from TemplGen. High-quality React, Next.js, and MERN templates ready to use.\",\n    url: websitePath.main,\n    siteName: \"TemplGen\",\n    images: [\n      {\n        url: webImage,\n        width: 400,\n        height: 400,\n        alt: \"TemplGen Logo\",\n      },\n    ],\n    countryName: \"Global\",\n    emails: [email],\n  },\n  twitter: {\n    card: \"summary_large_image\",\n    title: \"TemplGen Marketplace\",\n    description:\n      \"Discover and buy premium templates for web and mobile development at TemplGen.\",\n    images: webImage,\n    creator: \"@templgen\",\n  },\n  robots: {\n    index: true,\n    follow: true,\n    googleBot: {\n      index: true,\n      follow: true,\n      \"max-image-preview\": \"large\",\n      \"max-snippet\": -1,\n    },\n  },\n  verification: {\n    // Add verification tokens here if any\n  },\n};\n\nexport const viewport: Viewport = {\n  themeColor: \"#16161a\",\n  width: \"device-width\",\n  initialScale: 1,\n  maximumScale: 5,\n};\n\nexport default function RootLayout({\n  children,\n}: {\n  children: React.ReactNode;\n}) {\n  return (\n    <html lang=\"en\" dir=\"ltr\">\n      <head>\n        <link rel=\"preconnect\" href=\"https://fonts.googleapis.com\" />\n        <link\n          rel=\"preconnect\"\n          href=\"https://fonts.gstatic.com\"\n          crossOrigin=\"anonymous\"\n        />\n        <meta name=\"apple-mobile-web-app-title\" content=\"TemplGen\" />\n\n        <script\n          dangerouslySetInnerHTML={{\n            __html: `\n              (function() {\n                const observer = new MutationObserver((mutations) => {\n                  mutations.forEach(({ target }) => {\n                    if (target.nodeType === 1) {\n                      const elem = target;\n                      if (elem.hasAttribute('data-gr-ext-installed') ||\n                          elem.hasAttribute('data-new-gr-c-s-check-loaded')) {\n                        elem.removeAttribute('data-gr-ext-installed');\n                        elem.removeAttribute('data-new-gr-c-s-check-loaded');\n                      }\n                    }\n                  });\n                });\n                observer.observe(document.documentElement, {\n                  attributes: true,\n                  subtree: true,\n                  attributeFilter: ['data-gr-ext-installed', 'data-new-gr-c-s-check-loaded']\n                });\n              })();\n            `,\n          }}\n        />\n\n        <script\n          type=\"application/ld+json\"\n          dangerouslySetInnerHTML={{\n            __html: JSON.stringify({\n              \"@context\": \"https://schema.org\",\n              \"@type\": \"Organization\",\n              name: \"TemplGen\",\n              url: websitePath.main,\n              logo: webImage,\n              sameAs: [\n                \"https://github.com/templgen\",\n                \"https://www.linkedin.com/company/templgen\",\n                \"https://www.youtube.com/@templgen\",\n              ],\n              description:\n                \"Marketplace for premium website and mobile templates including React, Next.js, and MERN stacks.\",\n            }),\n          }}\n        />\n      </head>\n      <body className=\"flex relative dark flex-col min-h-screen\">\n        <SpeedInsights />\n        <Analytics />\n        {children}\n        <Toaster />\n        <Navbar />\n        <BackgroundEffect />\n        <main className=\"z-40 max-md:z-30 mx-auto w-full flex-grow\">\n          {children}\n        </main>\n        <Footer />\n        <FloatingActionButton threshold={400} />\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;AAEA,MAAM,SAAS;AACf,MAAM,cAAc;IAClB,MAAM,CAAC,QAAQ,EAAE,QAAQ;AAC3B;AACA,MAAM,WAAW,GAAG,YAAY,IAAI,CAAC,mBAAmB,CAAC,EAAE,mCAAmC;AAC9F,MAAM,QAAQ,wBAAwB,iCAAiC;AAEvE,MAAM,WAAW;IACf;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEM,MAAM,WAAqB;IAChC,cAAc,IAAI,IAAI,YAAY,IAAI;IACtC,OAAO;QACL,UAAU;QACV,SAAS;IACX;IACA,aACE;IACF,UAAU,SAAS,IAAI,CAAC;IACxB,SAAS;QAAC;YAAE,MAAM;YAAY,KAAK,YAAY,IAAI;QAAC;KAAE;IACtD,SAAS;IACT,WAAW;IACX,iBAAiB;QACf,OAAO;QACP,SAAS;QACT,WAAW;IACb;IACA,YAAY;QACV,WAAW,YAAY,IAAI;QAC3B,WAAW;YACT,IAAI,GAAG,YAAY,IAAI,CAAC,GAAG,CAAC;YAC5B,IAAI,GAAG,YAAY,IAAI,CAAC,GAAG,CAAC;QAC9B;IACF;IACA,WAAW;QACT,MAAM;QACN,QAAQ;QACR,iBAAiB;QACjB,OAAO;QACP,aACE;QACF,KAAK,YAAY,IAAI;QACrB,UAAU;QACV,QAAQ;YACN;gBACE,KAAK;gBACL,OAAO;gBACP,QAAQ;gBACR,KAAK;YACP;SACD;QACD,aAAa;QACb,QAAQ;YAAC;SAAM;IACjB;IACA,SAAS;QACP,MAAM;QACN,OAAO;QACP,aACE;QACF,QAAQ;QACR,SAAS;IACX;IACA,QAAQ;QACN,OAAO;QACP,QAAQ;QACR,WAAW;YACT,OAAO;YACP,QAAQ;YACR,qBAAqB;YACrB,eAAe,CAAC;QAClB;IACF;IACA,cAAc;IAEd;AACF;AAEO,MAAM,WAAqB;IAChC,YAAY;IACZ,OAAO;IACP,cAAc;IACd,cAAc;AAChB;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGT;IACC,qBACE,8OAAC;QAAK,MAAK;QAAK,KAAI;;0BAClB,8OAAC;;kCACC,8OAAC;wBAAK,KAAI;wBAAa,MAAK;;;;;;kCAC5B,8OAAC;wBACC,KAAI;wBACJ,MAAK;wBACL,aAAY;;;;;;kCAEd,8OAAC;wBAAK,MAAK;wBAA6B,SAAQ;;;;;;kCAEhD,8OAAC;wBACC,yBAAyB;4BACvB,QAAQ,CAAC;;;;;;;;;;;;;;;;;;;;YAoBT,CAAC;wBACH;;;;;;kCAGF,8OAAC;wBACC,MAAK;wBACL,yBAAyB;4BACvB,QAAQ,KAAK,SAAS,CAAC;gCACrB,YAAY;gCACZ,SAAS;gCACT,MAAM;gCACN,KAAK,YAAY,IAAI;gCACrB,MAAM;gCACN,QAAQ;oCACN;oCACA;oCACA;iCACD;gCACD,aACE;4BACJ;wBACF;;;;;;;;;;;;0BAGJ,8OAAC;gBAAK,WAAU;;kCACd,8OAAC,uKAAA,CAAA,gBAAa;;;;;kCACd,8OAAC,gKAAA,CAAA,YAAS;;;;;oBACT;kCACD,8OAAC,wIAAA,CAAA,UAAO;;;;;kCACR,8OAAC,sIAAA,CAAA,UAAM;;;;;kCACP,8OAAC,4IAAA,CAAA,UAAgB;;;;;kCACjB,8OAAC;wBAAK,WAAU;kCACb;;;;;;kCAEH,8OAAC,sIAAA,CAAA,UAAM;;;;;kCACP,8OAAC,gJAAA,CAAA,UAAoB;wBAAC,WAAW;;;;;;;;;;;;;;;;;;AAIzC"}}, {"offset": {"line": 1174, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}
{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/%40swc/helpers/cjs/_interop_require_default.cjs"], "sourcesContent": ["\"use strict\";\n\nfunction _interop_require_default(obj) {\n    return obj && obj.__esModule ? obj : { default: obj };\n}\nexports._ = _interop_require_default;\n"], "names": [], "mappings": "AAAA;AAEA,SAAS,yBAAyB,GAAG;IACjC,OAAO,OAAO,IAAI,UAAU,GAAG,MAAM;QAAE,SAAS;IAAI;AACxD;AACA,QAAQ,CAAC,GAAG", "ignoreList": [0]}}, {"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/%40swc/helpers/cjs/_interop_require_wildcard.cjs"], "sourcesContent": ["\"use strict\";\n\nfunction _getRequireWildcardCache(nodeInterop) {\n    if (typeof WeakMap !== \"function\") return null;\n\n    var cacheBabelInterop = new WeakMap();\n    var cacheNodeInterop = new WeakMap();\n\n    return (_getRequireWildcardCache = function(nodeInterop) {\n        return nodeInterop ? cacheNodeInterop : cacheBabelInterop;\n    })(nodeInterop);\n}\nfunction _interop_require_wildcard(obj, nodeInterop) {\n    if (!nodeInterop && obj && obj.__esModule) return obj;\n    if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") return { default: obj };\n\n    var cache = _getRequireWildcardCache(nodeInterop);\n\n    if (cache && cache.has(obj)) return cache.get(obj);\n\n    var newObj = { __proto__: null };\n    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;\n\n    for (var key in obj) {\n        if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) {\n            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;\n            if (desc && (desc.get || desc.set)) Object.defineProperty(newObj, key, desc);\n            else newObj[key] = obj[key];\n        }\n    }\n\n    newObj.default = obj;\n\n    if (cache) cache.set(obj, newObj);\n\n    return newObj;\n}\nexports._ = _interop_require_wildcard;\n"], "names": [], "mappings": "AAAA;AAEA,SAAS,yBAAyB,WAAW;IACzC,IAAI,OAAO,YAAY,YAAY,OAAO;IAE1C,IAAI,oBAAoB,IAAI;IAC5B,IAAI,mBAAmB,IAAI;IAE3B,OAAO,CAAC,2BAA2B,SAAS,WAAW;QACnD,OAAO,cAAc,mBAAmB;IAC5C,CAAC,EAAE;AACP;AACA,SAAS,0BAA0B,GAAG,EAAE,WAAW;IAC/C,IAAI,CAAC,eAAe,OAAO,IAAI,UAAU,EAAE,OAAO;IAClD,IAAI,QAAQ,QAAQ,OAAO,QAAQ,YAAY,OAAO,QAAQ,YAAY,OAAO;QAAE,SAAS;IAAI;IAEhG,IAAI,QAAQ,yBAAyB;IAErC,IAAI,SAAS,MAAM,GAAG,CAAC,MAAM,OAAO,MAAM,GAAG,CAAC;IAE9C,IAAI,SAAS;QAAE,WAAW;IAAK;IAC/B,IAAI,wBAAwB,OAAO,cAAc,IAAI,OAAO,wBAAwB;IAEpF,IAAK,IAAI,OAAO,IAAK;QACjB,IAAI,QAAQ,aAAa,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,MAAM;YACrE,IAAI,OAAO,wBAAwB,OAAO,wBAAwB,CAAC,KAAK,OAAO;YAC/E,IAAI,QAAQ,CAAC,KAAK,GAAG,IAAI,KAAK,GAAG,GAAG,OAAO,cAAc,CAAC,QAAQ,KAAK;iBAClE,MAAM,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI;QAC/B;IACJ;IAEA,OAAO,OAAO,GAAG;IAEjB,IAAI,OAAO,MAAM,GAAG,CAAC,KAAK;IAE1B,OAAO;AACX;AACA,QAAQ,CAAC,GAAG", "ignoreList": [0]}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 56, "column": 0}, "map": {"version": 3, "file": "defaultAttributes.js", "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/lucide-react/src/defaultAttributes.ts"], "sourcesContent": ["export default {\n  xmlns: 'http://www.w3.org/2000/svg',\n  width: 24,\n  height: 24,\n  viewBox: '0 0 24 24',\n  fill: 'none',\n  stroke: 'currentColor',\n  strokeWidth: 2,\n  strokeLinecap: 'round',\n  strokeLinejoin: 'round',\n};\n"], "names": [], "mappings": ";;;;;;;;AAAA,CAAe,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACb,CAAA,CAAA,CAAA,CAAA,CAAO,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACP,CAAA,CAAA,CAAA,CAAA,CAAO,EAAA,CAAA,CAAA,CAAA;IACP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAA,CAAA,CAAA,CAAA;IACR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACT,CAAA,CAAA,CAAA,CAAM,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAa,EAAA,CAAA,CAAA;IACb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACf,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAClB,CAAA,CAAA", "ignoreList": [0]}}, {"offset": {"line": 77, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 83, "column": 0}, "map": {"version": 3, "file": "utils.js", "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/shared/src/utils.ts"], "sourcesContent": ["import { CamelToPascal } from './utility-types';\n\n/**\n * Converts string to kebab case\n *\n * @param {string} string\n * @returns {string} A kebabized string\n */\nexport const toKebabCase = (string: string) =>\n  string.replace(/([a-z0-9])([A-Z])/g, '$1-$2').toLowerCase();\n\n/**\n * Converts string to camel case\n *\n * @param {string} string\n * @returns {string} A camelized string\n */\nexport const toCamelCase = <T extends string>(string: T) =>\n  string.replace(/^([A-Z])|[\\s-_]+(\\w)/g, (match, p1, p2) =>\n    p2 ? p2.toUpperCase() : p1.toLowerCase(),\n  );\n\n/**\n * Converts string to pascal case\n *\n * @param {string} string\n * @returns {string} A pascalized string\n */\nexport const toPascalCase = <T extends string>(string: T): CamelToPascal<T> => {\n  const camelCase = toCamelCase(string);\n\n  return (camelCase.charAt(0).toUpperCase() + camelCase.slice(1)) as CamelToPascal<T>;\n};\n\n/**\n * Merges classes into a single string\n *\n * @param {array} classes\n * @returns {string} A string of classes\n */\nexport const mergeClasses = <ClassType = string | undefined | null>(...classes: ClassType[]) =>\n  classes\n    .filter((className, index, array) => {\n      return (\n        Boolean(className) &&\n        (className as string).trim() !== '' &&\n        array.indexOf(className) === index\n      );\n    })\n    .join(' ')\n    .trim();\n"], "names": [], "mappings": ";;;;;;;;;AAQa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAC1B,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,OAAA,CAAQ,oBAAsB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,EAAE,WAAY,CAAA,CAAA,CAAA;AA+B/C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,YAAA,CAAe,CAAA,CAAA,CAAA,CAA2C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACrE,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACG,MAAA,CAAO,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,OAAO,KAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAEjC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,CAAA,CAChB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAW,CACjC,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IAEjC,CAAC,CACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAK,CAAG,CAAA,CAAA,CAAA,CACR,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0]}}, {"offset": {"line": 98, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "file": "Icon.js", "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/lucide-react/src/Icon.ts"], "sourcesContent": ["import { createElement, forwardRef } from 'react';\nimport defaultAttributes from './defaultAttributes';\nimport { IconNode, LucideProps } from './types';\nimport { mergeClasses } from '@lucide/shared';\n\ninterface IconComponentProps extends LucideProps {\n  iconNode: IconNode;\n}\n\n/**\n * Lucide icon component\n *\n * @component Icon\n * @param {object} props\n * @param {string} props.color - The color of the icon\n * @param {number} props.size - The size of the icon\n * @param {number} props.strokeWidth - The stroke width of the icon\n * @param {boolean} props.absoluteStrokeWidth - Whether to use absolute stroke width\n * @param {string} props.className - The class name of the icon\n * @param {IconNode} props.children - The children of the icon\n * @param {IconNode} props.iconNode - The icon node of the icon\n *\n * @returns {ForwardRefExoticComponent} LucideIcon\n */\nconst Icon = forwardRef<SVGSVGElement, IconComponentProps>(\n  (\n    {\n      color = 'currentColor',\n      size = 24,\n      strokeWidth = 2,\n      absoluteStrokeWidth,\n      className = '',\n      children,\n      iconNode,\n      ...rest\n    },\n    ref,\n  ) => {\n    return createElement(\n      'svg',\n      {\n        ref,\n        ...defaultAttributes,\n        width: size,\n        height: size,\n        stroke: color,\n        strokeWidth: absoluteStrokeWidth ? (Number(strokeWidth) * 24) / Number(size) : strokeWidth,\n        className: mergeClasses('lucide', className),\n        ...rest,\n      },\n      [\n        ...iconNode.map(([tag, attrs]) => createElement(tag, attrs)),\n        ...(Array.isArray(children) ? children : [children]),\n      ],\n    );\n  },\n);\n\nexport default Icon;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAwBA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,6MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CACX,CACE,CAAA,CACE,CAAA,CAAA,CAAA,CAAA,CAAQ,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACR,CAAA,CAAA,CAAA,CAAO,GAAA,CAAA,CAAA,CAAA,CACP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,GAAA,CAAA,CAAA,CACd,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,GAAA,CAAA,CAAA,CAAA,CACZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACA,GAAG,CAAA,CAAA,CAAA,CAAA,EAAA,EAEL,GACG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACI,iNAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CACL,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACA,CAAA;QACE,CAAA,CAAA,CAAA,CAAA;QACA,uKAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACH,CAAA,CAAA,CAAA,CAAA,CAAO,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACR,WAAA,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAuB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAA,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAI,CAAA,CAAA,CAAA,CAAM,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAI,CAAA,CAAA,CAAA,CAAI,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAC/E,SAAA,CAAW,8KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,AAAa,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,SAAS,CAAA,CAAA;QAC3C,GAAG,CAAA,CAAA,CAAA,CAAA;IACL,CAAA,CAAA,CACA,CAAA;WACK,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAI,CAAC,CAAC,CAAK,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAM,6MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EAAc,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAK,CAAC,CAAA,CAAA;WACvD,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW;YAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ;SAAA;KACpD;AAEJ,CAAA", "ignoreList": [0]}}, {"offset": {"line": 137, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 143, "column": 0}, "map": {"version": 3, "file": "createLucideIcon.js", "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/lucide-react/src/createLucideIcon.ts"], "sourcesContent": ["import { createElement, forwardRef } from 'react';\nimport { mergeClasses, toKebabCase } from '@lucide/shared';\nimport { IconNode, LucideProps } from './types';\nimport Icon from './Icon';\n\n/**\n * Create a Lucide icon component\n * @param {string} iconName\n * @param {array} iconNode\n * @returns {ForwardRefExoticComponent} LucideIcon\n */\nconst createLucideIcon = (iconName: string, iconNode: IconNode) => {\n  const Component = forwardRef<SVGSVGElement, LucideProps>(({ className, ...props }, ref) =>\n    createElement(Icon, {\n      ref,\n      iconNode,\n      className: mergeClasses(`lucide-${toKebabCase(iconName)}`, className),\n      ...props,\n    }),\n  );\n\n  Component.displayName = `${iconName}`;\n\n  return Component;\n};\n\nexport default createLucideIcon;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAWM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,gBAAA,CAAmB,CAAA,CAAA,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB,QAAuB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACjE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,2MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAuC,CAAC,CAAA,CAAE,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,yMACjF,gBAAA,yJAAc,UAAM,CAAA,CAAA,CAAA;YAClB,CAAA,CAAA,CAAA,CAAA;YACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACA,wLAAW,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,8KAAU,cAAA,EAAY,QAAQ,CAAC,EAAA,EAAI,SAAS,CAAA,CAAA;YACpE,GAAG,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CACJ,CAAA;IAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAA,CAAA;IAE5B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,SAAA,CAAA;AACT,CAAA,CAAA", "ignoreList": [0]}}, {"offset": {"line": 169, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 175, "column": 0}, "map": {"version": 3, "file": "house.js", "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/lucide-react/src/icons/house.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8', key: '5wwlr5' }],\n  [\n    'path',\n    {\n      d: 'M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z',\n      key: '1d0kgt',\n    },\n  ],\n];\n\n/**\n * @component @name House\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTUgMjF2LThhMSAxIDAgMCAwLTEtMWgtNGExIDEgMCAwIDAtMSAxdjgiIC8+CiAgPHBhdGggZD0iTTMgMTBhMiAyIDAgMCAxIC43MDktMS41MjhsNy01Ljk5OWEyIDIgMCAwIDEgMi41ODIgMGw3IDUuOTk5QTIgMiAwIDAgMSAyMSAxMHY5YTIgMiAwIDAgMS0yIDJINWEyIDIgMCAwIDEtMi0yeiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/house\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst House = createLucideIcon('House', __iconNode);\n\nexport default House;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA8C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA,CAAA;IAC3E,CAAA;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA,CAAA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,CAAA,CAAA,CAAK,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACP,CAAA;KACF;CACF,CAAA;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAQ,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0]}}, {"offset": {"line": 205, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 221, "column": 0}, "map": {"version": 3, "file": "briefcase.js", "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/lucide-react/src/icons/briefcase.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16', key: 'jecpp' }],\n  ['rect', { width: '20', height: '14', x: '2', y: '6', rx: '2', key: 'i6l2r4' }],\n];\n\n/**\n * @component @name Briefcase\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTYgMjBWNGEyIDIgMCAwIDAtMi0yaC00YTIgMiAwIDAgMC0yIDJ2MTYiIC8+CiAgPHJlY3Qgd2lkdGg9IjIwIiBoZWlnaHQ9IjE0IiB4PSIyIiB5PSI2IiByeD0iMiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/briefcase\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Briefcase = createLucideIcon('Briefcase', __iconNode);\n\nexport default Briefcase;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA8C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAS;KAAA,CAAA;IAC1E;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ;QAAA,CAAA;YAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO;YAAM,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAG,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAChF,CAAA;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,SAAA,CAAY,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAa,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0]}}, {"offset": {"line": 255, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 271, "column": 0}, "map": {"version": 3, "file": "file-text.js", "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/lucide-react/src/icons/file-text.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z', key: '1rqfz7' }],\n  ['path', { d: 'M14 2v4a2 2 0 0 0 2 2h4', key: 'tnqrlb' }],\n  ['path', { d: 'M10 9H8', key: 'b1mrlr' }],\n  ['path', { d: 'M16 13H8', key: 't4e002' }],\n  ['path', { d: 'M16 17H8', key: 'z1uh3a' }],\n];\n\n/**\n * @component @name FileText\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTUgMkg2YTIgMiAwIDAgMC0yIDJ2MTZhMiAyIDAgMCAwIDIgMmgxMmEyIDIgMCAwIDAgMi0yVjdaIiAvPgogIDxwYXRoIGQ9Ik0xNCAydjRhMiAyIDAgMCAwIDIgMmg0IiAvPgogIDxwYXRoIGQ9Ik0xMCA5SDgiIC8+CiAgPHBhdGggZD0iTTE2IDEzSDgiIC8+CiAgPHBhdGggZD0iTTE2IDE3SDgiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/file-text\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst FileText = createLucideIcon('FileText', __iconNode);\n\nexport default FileText;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA8D,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA,CAAA;IAC3F;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA2B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA,CAAA;IACxD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA,CAAA;IACxC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA,CAAA;IACzC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAC3C,CAAA;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAW,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAY,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0]}}, {"offset": {"line": 322, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 338, "column": 0}, "map": {"version": 3, "file": "mail.js", "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/lucide-react/src/icons/mail.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['rect', { width: '20', height: '16', x: '2', y: '4', rx: '2', key: '18n3k1' }],\n  ['path', { d: 'm22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7', key: '1ocrg3' }],\n];\n\n/**\n * @component @name Mail\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMjAiIGhlaWdodD0iMTYiIHg9IjIiIHk9IjQiIHJ4PSIyIiAvPgogIDxwYXRoIGQ9Im0yMiA3LTguOTcgNS43YTEuOTQgMS45NCAwIDAgMS0yLjA2IDBMMiA3IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/mail\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Mail = createLucideIcon('Mail', __iconNode);\n\nexport default Mail;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ;QAAA,CAAA;YAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO;YAAM,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAG,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA,CAAA;IAC9E;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA6C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAC5E,CAAA;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0]}}, {"offset": {"line": 372, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 388, "column": 0}, "map": {"version": 3, "file": "menu.js", "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/lucide-react/src/icons/menu.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['line', { x1: '4', x2: '20', y1: '12', y2: '12', key: '1e0a9i' }],\n  ['line', { x1: '4', x2: '20', y1: '6', y2: '6', key: '1owob3' }],\n  ['line', { x1: '4', x2: '20', y1: '18', y2: '18', key: 'yk5zj1' }],\n];\n\n/**\n * @component @name Menu\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8bGluZSB4MT0iNCIgeDI9IjIwIiB5MT0iMTIiIHkyPSIxMiIgLz4KICA8bGluZSB4MT0iNCIgeDI9IjIwIiB5MT0iNiIgeTI9IjYiIC8+CiAgPGxpbmUgeDE9IjQiIHgyPSIyMCIgeTE9IjE4IiB5Mj0iMTgiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/menu\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Menu = createLucideIcon('Menu', __iconNode);\n\nexport default Menu;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA,CAAA;IACjE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAK,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAK;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA,CAAA;IAC/D;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CACnE,CAAA;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0]}}, {"offset": {"line": 434, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 450, "column": 0}, "map": {"version": 3, "file": "x.js", "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/lucide-react/src/icons/x.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M18 6 6 18', key: '1bl5f8' }],\n  ['path', { d: 'm6 6 12 12', key: 'd8bk6v' }],\n];\n\n/**\n * @component @name X\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTggNiA2IDE4IiAvPgogIDxwYXRoIGQ9Im02IDYgMTIgMTIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/x\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst X = createLucideIcon('X', __iconNode);\n\nexport default X;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAc,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA,CAAA;IAC3C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAc,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAC7C,CAAA;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAI,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,EAAK,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0]}}, {"offset": {"line": 480, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 496, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/motion-utils/dist/es/noop.mjs"], "sourcesContent": ["/*#__NO_SIDE_EFFECTS__*/\nconst noop = (any) => any;\n\nexport { noop };\n"], "names": [], "mappings": "AAAA,sBAAsB;;;AACtB,MAAM,OAAO,CAAC,MAAQ", "ignoreList": [0]}}, {"offset": {"line": 501, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 507, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/motion-utils/dist/es/errors.mjs"], "sourcesContent": ["import { noop } from './noop.mjs';\n\nlet warning = noop;\nlet invariant = noop;\nif (process.env.NODE_ENV !== \"production\") {\n    warning = (check, message) => {\n        if (!check && typeof console !== \"undefined\") {\n            console.warn(message);\n        }\n    };\n    invariant = (check, message) => {\n        if (!check) {\n            throw new Error(message);\n        }\n    };\n}\n\nexport { invariant, warning };\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,IAAI,UAAU,sJAAA,CAAA,OAAI;AAClB,IAAI,YAAY,sJAAA,CAAA,OAAI;AACpB,wCAA2C;IACvC,UAAU,CAAC,OAAO;QACd,IAAI,CAAC,SAAS,OAAO,YAAY,aAAa;YAC1C,QAAQ,IAAI,CAAC;QACjB;IACJ;IACA,YAAY,CAAC,OAAO;QAChB,IAAI,CAAC,OAAO;YACR,MAAM,IAAI,MAAM;QACpB;IACJ;AACJ", "ignoreList": [0]}}, {"offset": {"line": 528, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 534, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/motion-utils/dist/es/memo.mjs"], "sourcesContent": ["/*#__NO_SIDE_EFFECTS__*/\nfunction memo(callback) {\n    let result;\n    return () => {\n        if (result === undefined)\n            result = callback();\n        return result;\n    };\n}\n\nexport { memo };\n"], "names": [], "mappings": "AAAA,sBAAsB;;;AACtB,SAAS,KAAK,QAAQ;IAClB,IAAI;IACJ,OAAO;QACH,IAAI,WAAW,WACX,SAAS;QACb,OAAO;IACX;AACJ", "ignoreList": [0]}}, {"offset": {"line": 545, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 551, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/motion-utils/dist/es/progress.mjs"], "sourcesContent": ["/*\n  Progress within given range\n\n  Given a lower limit and an upper limit, we return the progress\n  (expressed as a number 0-1) represented by the given value, and\n  limit that progress to within 0-1.\n\n  @param [number]: Lower limit\n  @param [number]: Upper limit\n  @param [number]: Value to find progress within given range\n  @return [number]: Progress of value within range as expressed 0-1\n*/\n/*#__NO_SIDE_EFFECTS__*/\nconst progress = (from, to, value) => {\n    const toFromDifference = to - from;\n    return toFromDifference === 0 ? 1 : (value - from) / toFromDifference;\n};\n\nexport { progress };\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;AAWA,GACA,sBAAsB;;;AACtB,MAAM,WAAW,CAAC,MAAM,IAAI;IACxB,MAAM,mBAAmB,KAAK;IAC9B,OAAO,qBAAqB,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI;AACzD", "ignoreList": [0]}}, {"offset": {"line": 570, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 576, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/motion-utils/dist/es/time-conversion.mjs"], "sourcesContent": ["/**\n * Converts seconds to milliseconds\n *\n * @param seconds - Time in seconds.\n * @return milliseconds - Converted time in milliseconds.\n */\n/*#__NO_SIDE_EFFECTS__*/\nconst secondsToMilliseconds = (seconds) => seconds * 1000;\n/*#__NO_SIDE_EFFECTS__*/\nconst millisecondsToSeconds = (milliseconds) => milliseconds / 1000;\n\nexport { millisecondsToSeconds, secondsToMilliseconds };\n"], "names": [], "mappings": "AAAA;;;;;CAKC,GACD,sBAAsB;;;;AACtB,MAAM,wBAAwB,CAAC,UAAY,UAAU;AACrD,sBAAsB,GACtB,MAAM,wBAAwB,CAAC,eAAiB,eAAe", "ignoreList": [0]}}, {"offset": {"line": 588, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 594, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 600, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 619, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/motion-dom/dist/es/utils/supports/scroll-timeline.mjs"], "sourcesContent": ["import { memo } from 'motion-utils';\n\nconst supportsScrollTimeline = memo(() => window.ScrollTimeline !== undefined);\n\nexport { supportsScrollTimeline };\n"], "names": [], "mappings": ";;;AAAA;AAAA;;AAEA,MAAM,yBAAyB,CAAA,GAAA,sJAAA,CAAA,OAAI,AAAD,EAAE,IAAM,OAAO,cAAc,KAAK", "ignoreList": [0]}}, {"offset": {"line": 627, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 633, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/motion-dom/dist/es/animation/controls/BaseGroup.mjs"], "sourcesContent": ["import { supportsScrollTimeline } from '../../utils/supports/scroll-timeline.mjs';\n\nclass BaseGroupPlaybackControls {\n    constructor(animations) {\n        // Bound to accomodate common `return animation.stop` pattern\n        this.stop = () => this.runAll(\"stop\");\n        this.animations = animations.filter(Boolean);\n    }\n    get finished() {\n        // Support for new finished Promise and legacy thennable API\n        return Promise.all(this.animations.map((animation) => \"finished\" in animation ? animation.finished : animation));\n    }\n    /**\n     * TODO: Filter out cancelled or stopped animations before returning\n     */\n    getAll(propName) {\n        return this.animations[0][propName];\n    }\n    setAll(propName, newValue) {\n        for (let i = 0; i < this.animations.length; i++) {\n            this.animations[i][propName] = newValue;\n        }\n    }\n    attachTimeline(timeline, fallback) {\n        const subscriptions = this.animations.map((animation) => {\n            if (supportsScrollTimeline() && animation.attachTimeline) {\n                return animation.attachTimeline(timeline);\n            }\n            else if (typeof fallback === \"function\") {\n                return fallback(animation);\n            }\n        });\n        return () => {\n            subscriptions.forEach((cancel, i) => {\n                cancel && cancel();\n                this.animations[i].stop();\n            });\n        };\n    }\n    get time() {\n        return this.getAll(\"time\");\n    }\n    set time(time) {\n        this.setAll(\"time\", time);\n    }\n    get speed() {\n        return this.getAll(\"speed\");\n    }\n    set speed(speed) {\n        this.setAll(\"speed\", speed);\n    }\n    get startTime() {\n        return this.getAll(\"startTime\");\n    }\n    get duration() {\n        let max = 0;\n        for (let i = 0; i < this.animations.length; i++) {\n            max = Math.max(max, this.animations[i].duration);\n        }\n        return max;\n    }\n    runAll(methodName) {\n        this.animations.forEach((controls) => controls[methodName]());\n    }\n    flatten() {\n        this.runAll(\"flatten\");\n    }\n    play() {\n        this.runAll(\"play\");\n    }\n    pause() {\n        this.runAll(\"pause\");\n    }\n    cancel() {\n        this.runAll(\"cancel\");\n    }\n    complete() {\n        this.runAll(\"complete\");\n    }\n}\n\nexport { BaseGroupPlaybackControls };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM;IACF,YAAY,UAAU,CAAE;QACpB,6DAA6D;QAC7D,IAAI,CAAC,IAAI,GAAG,IAAM,IAAI,CAAC,MAAM,CAAC;QAC9B,IAAI,CAAC,UAAU,GAAG,WAAW,MAAM,CAAC;IACxC;IACA,IAAI,WAAW;QACX,4DAA4D;QAC5D,OAAO,QAAQ,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,YAAc,cAAc,YAAY,UAAU,QAAQ,GAAG;IACzG;IACA;;KAEC,GACD,OAAO,QAAQ,EAAE;QACb,OAAO,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,SAAS;IACvC;IACA,OAAO,QAAQ,EAAE,QAAQ,EAAE;QACvB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,IAAK;YAC7C,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,SAAS,GAAG;QACnC;IACJ;IACA,eAAe,QAAQ,EAAE,QAAQ,EAAE;QAC/B,MAAM,gBAAgB,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;YACvC,IAAI,CAAA,GAAA,uLAAA,CAAA,yBAAsB,AAAD,OAAO,UAAU,cAAc,EAAE;gBACtD,OAAO,UAAU,cAAc,CAAC;YACpC,OACK,IAAI,OAAO,aAAa,YAAY;gBACrC,OAAO,SAAS;YACpB;QACJ;QACA,OAAO;YACH,cAAc,OAAO,CAAC,CAAC,QAAQ;gBAC3B,UAAU;gBACV,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,IAAI;YAC3B;QACJ;IACJ;IACA,IAAI,OAAO;QACP,OAAO,IAAI,CAAC,MAAM,CAAC;IACvB;IACA,IAAI,KAAK,IAAI,EAAE;QACX,IAAI,CAAC,MAAM,CAAC,QAAQ;IACxB;IACA,IAAI,QAAQ;QACR,OAAO,IAAI,CAAC,MAAM,CAAC;IACvB;IACA,IAAI,MAAM,KAAK,EAAE;QACb,IAAI,CAAC,MAAM,CAAC,SAAS;IACzB;IACA,IAAI,YAAY;QACZ,OAAO,IAAI,CAAC,MAAM,CAAC;IACvB;IACA,IAAI,WAAW;QACX,IAAI,MAAM;QACV,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,IAAK;YAC7C,MAAM,KAAK,GAAG,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,QAAQ;QACnD;QACA,OAAO;IACX;IACA,OAAO,UAAU,EAAE;QACf,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,WAAa,QAAQ,CAAC,WAAW;IAC9D;IACA,UAAU;QACN,IAAI,CAAC,MAAM,CAAC;IAChB;IACA,OAAO;QACH,IAAI,CAAC,MAAM,CAAC;IAChB;IACA,QAAQ;QACJ,IAAI,CAAC,MAAM,CAAC;IAChB;IACA,SAAS;QACL,IAAI,CAAC,MAAM,CAAC;IAChB;IACA,WAAW;QACP,IAAI,CAAC,MAAM,CAAC;IAChB;AACJ", "ignoreList": [0]}}, {"offset": {"line": 715, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 721, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/motion-dom/dist/es/animation/controls/Group.mjs"], "sourcesContent": ["import { BaseGroupPlaybackControls } from './BaseGroup.mjs';\n\n/**\n * TODO: This is a temporary class to support the legacy\n * thennable API\n */\nclass GroupPlaybackControls extends BaseGroupPlaybackControls {\n    then(onResolve, onReject) {\n        return Promise.all(this.animations).then(onResolve).catch(onReject);\n    }\n}\n\nexport { GroupPlaybackControls };\n"], "names": [], "mappings": ";;;AAAA;;AAEA;;;CAGC,GACD,MAAM,8BAA8B,kLAAA,CAAA,4BAAyB;IACzD,KAAK,SAAS,EAAE,QAAQ,EAAE;QACtB,OAAO,QAAQ,GAAG,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,WAAW,KAAK,CAAC;IAC9D;AACJ", "ignoreList": [0]}}, {"offset": {"line": 735, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 741, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/motion-dom/dist/es/animation/utils/get-value-transition.mjs"], "sourcesContent": ["function getValueTransition(transition, key) {\n    return transition\n        ? transition[key] ||\n            transition[\"default\"] ||\n            transition\n        : undefined;\n}\n\nexport { getValueTransition };\n"], "names": [], "mappings": ";;;AAAA,SAAS,mBAAmB,UAAU,EAAE,GAAG;IACvC,OAAO,aACD,UAAU,CAAC,IAAI,IACb,UAAU,CAAC,UAAU,IACrB,aACF;AACV", "ignoreList": [0]}}, {"offset": {"line": 748, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 754, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/motion-dom/dist/es/animation/generators/utils/calc-duration.mjs"], "sourcesContent": ["/**\n * Implement a practical max duration for keyframe generation\n * to prevent infinite loops\n */\nconst maxGeneratorDuration = 20000;\nfunction calcGeneratorDuration(generator) {\n    let duration = 0;\n    const timeStep = 50;\n    let state = generator.next(duration);\n    while (!state.done && duration < maxGeneratorDuration) {\n        duration += timeStep;\n        state = generator.next(duration);\n    }\n    return duration >= maxGeneratorDuration ? Infinity : duration;\n}\n\nexport { calcGeneratorDuration, maxGeneratorDuration };\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AACD,MAAM,uBAAuB;AAC7B,SAAS,sBAAsB,SAAS;IACpC,IAAI,WAAW;IACf,MAAM,WAAW;IACjB,IAAI,QAAQ,UAAU,IAAI,CAAC;IAC3B,MAAO,CAAC,MAAM,IAAI,IAAI,WAAW,qBAAsB;QACnD,YAAY;QACZ,QAAQ,UAAU,IAAI,CAAC;IAC3B;IACA,OAAO,YAAY,uBAAuB,WAAW;AACzD", "ignoreList": [0]}}, {"offset": {"line": 773, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 779, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/motion-dom/dist/es/animation/generators/utils/create-generator-easing.mjs"], "sourcesContent": ["import { millisecondsToSeconds } from 'motion-utils';\nimport { calcGeneratorDuration, maxGeneratorDuration } from './calc-duration.mjs';\n\n/**\n * Create a progress => progress easing function from a generator.\n */\nfunction createGeneratorEasing(options, scale = 100, createGenerator) {\n    const generator = createGenerator({ ...options, keyframes: [0, scale] });\n    const duration = Math.min(calcGeneratorDuration(generator), maxGeneratorDuration);\n    return {\n        type: \"keyframes\",\n        ease: (progress) => {\n            return generator.next(duration * progress).value / scale;\n        },\n        duration: millisecondsToSeconds(duration),\n    };\n}\n\nexport { createGeneratorEasing };\n"], "names": [], "mappings": ";;;AAAA;AACA;AADA;;;AAGA;;CAEC,GACD,SAAS,sBAAsB,OAAO,EAAE,QAAQ,GAAG,EAAE,eAAe;IAChE,MAAM,YAAY,gBAAgB;QAAE,GAAG,OAAO;QAAE,WAAW;YAAC;YAAG;SAAM;IAAC;IACtE,MAAM,WAAW,KAAK,GAAG,CAAC,CAAA,GAAA,oMAAA,CAAA,wBAAqB,AAAD,EAAE,YAAY,oMAAA,CAAA,uBAAoB;IAChF,OAAO;QACH,MAAM;QACN,MAAM,CAAC;YACH,OAAO,UAAU,IAAI,CAAC,WAAW,UAAU,KAAK,GAAG;QACvD;QACA,UAAU,CAAA,GAAA,oKAAA,CAAA,wBAAqB,AAAD,EAAE;IACpC;AACJ", "ignoreList": [0]}}, {"offset": {"line": 807, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 813, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/motion-dom/dist/es/animation/generators/utils/is-generator.mjs"], "sourcesContent": ["function isGenerator(type) {\n    return typeof type === \"function\";\n}\n\nexport { isGenerator };\n"], "names": [], "mappings": ";;;AAAA,SAAS,YAAY,IAAI;IACrB,OAAO,OAAO,SAAS;AAC3B", "ignoreList": [0]}}, {"offset": {"line": 820, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 826, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/motion-dom/dist/es/animation/waapi/utils/attach-timeline.mjs"], "sourcesContent": ["function attachTimeline(animation, timeline) {\n    animation.timeline = timeline;\n    animation.onfinish = null;\n}\n\nexport { attachTimeline };\n"], "names": [], "mappings": ";;;AAAA,SAAS,eAAe,SAAS,EAAE,QAAQ;IACvC,UAAU,QAAQ,GAAG;IACrB,UAAU,QAAQ,GAAG;AACzB", "ignoreList": [0]}}, {"offset": {"line": 834, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 840, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/motion-dom/dist/es/animation/waapi/NativeAnimationControls.mjs"], "sourcesContent": ["import { millisecondsToSeconds, secondsToMilliseconds, noop } from 'motion-utils';\nimport { attachTimeline } from './utils/attach-timeline.mjs';\n\nclass NativeAnimationControls {\n    constructor(animation) {\n        this.animation = animation;\n    }\n    get duration() {\n        var _a, _b, _c;\n        const durationInMs = ((_b = (_a = this.animation) === null || _a === void 0 ? void 0 : _a.effect) === null || _b === void 0 ? void 0 : _b.getComputedTiming().duration) ||\n            ((_c = this.options) === null || _c === void 0 ? void 0 : _c.duration) ||\n            300;\n        return millisecondsToSeconds(Number(durationInMs));\n    }\n    get time() {\n        var _a;\n        if (this.animation) {\n            return millisecondsToSeconds(((_a = this.animation) === null || _a === void 0 ? void 0 : _a.currentTime) || 0);\n        }\n        return 0;\n    }\n    set time(newTime) {\n        if (this.animation) {\n            this.animation.currentTime = secondsToMilliseconds(newTime);\n        }\n    }\n    get speed() {\n        return this.animation ? this.animation.playbackRate : 1;\n    }\n    set speed(newSpeed) {\n        if (this.animation) {\n            this.animation.playbackRate = newSpeed;\n        }\n    }\n    get state() {\n        return this.animation ? this.animation.playState : \"finished\";\n    }\n    get startTime() {\n        return this.animation ? this.animation.startTime : null;\n    }\n    get finished() {\n        return this.animation ? this.animation.finished : Promise.resolve();\n    }\n    play() {\n        this.animation && this.animation.play();\n    }\n    pause() {\n        this.animation && this.animation.pause();\n    }\n    stop() {\n        if (!this.animation ||\n            this.state === \"idle\" ||\n            this.state === \"finished\") {\n            return;\n        }\n        if (this.animation.commitStyles) {\n            this.animation.commitStyles();\n        }\n        this.cancel();\n    }\n    flatten() {\n        var _a;\n        if (!this.animation)\n            return;\n        (_a = this.animation.effect) === null || _a === void 0 ? void 0 : _a.updateTiming({ easing: \"linear\" });\n    }\n    attachTimeline(timeline) {\n        if (this.animation)\n            attachTimeline(this.animation, timeline);\n        return noop;\n    }\n    complete() {\n        this.animation && this.animation.finish();\n    }\n    cancel() {\n        try {\n            this.animation && this.animation.cancel();\n        }\n        catch (e) { }\n    }\n}\n\nexport { NativeAnimationControls };\n"], "names": [], "mappings": ";;;AAAA;AACA;AADA;AAAA;;;AAGA,MAAM;IACF,YAAY,SAAS,CAAE;QACnB,IAAI,CAAC,SAAS,GAAG;IACrB;IACA,IAAI,WAAW;QACX,IAAI,IAAI,IAAI;QACZ,MAAM,eAAe,CAAC,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,SAAS,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,MAAM,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,iBAAiB,GAAG,QAAQ,KAClK,CAAC,CAAC,KAAK,IAAI,CAAC,OAAO,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,QAAQ,KACrE;QACJ,OAAO,CAAA,GAAA,oKAAA,CAAA,wBAAqB,AAAD,EAAE,OAAO;IACxC;IACA,IAAI,OAAO;QACP,IAAI;QACJ,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,OAAO,CAAA,GAAA,oKAAA,CAAA,wBAAqB,AAAD,EAAE,CAAC,CAAC,KAAK,IAAI,CAAC,SAAS,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,WAAW,KAAK;QAChH;QACA,OAAO;IACX;IACA,IAAI,KAAK,OAAO,EAAE;QACd,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,IAAI,CAAC,SAAS,CAAC,WAAW,GAAG,CAAA,GAAA,oKAAA,CAAA,wBAAqB,AAAD,EAAE;QACvD;IACJ;IACA,IAAI,QAAQ;QACR,OAAO,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,GAAG;IAC1D;IACA,IAAI,MAAM,QAAQ,EAAE;QAChB,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,IAAI,CAAC,SAAS,CAAC,YAAY,GAAG;QAClC;IACJ;IACA,IAAI,QAAQ;QACR,OAAO,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,GAAG;IACvD;IACA,IAAI,YAAY;QACZ,OAAO,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,GAAG;IACvD;IACA,IAAI,WAAW;QACX,OAAO,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,GAAG,QAAQ,OAAO;IACrE;IACA,OAAO;QACH,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI;IACzC;IACA,QAAQ;QACJ,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK;IAC1C;IACA,OAAO;QACH,IAAI,CAAC,IAAI,CAAC,SAAS,IACf,IAAI,CAAC,KAAK,KAAK,UACf,IAAI,CAAC,KAAK,KAAK,YAAY;YAC3B;QACJ;QACA,IAAI,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE;YAC7B,IAAI,CAAC,SAAS,CAAC,YAAY;QAC/B;QACA,IAAI,CAAC,MAAM;IACf;IACA,UAAU;QACN,IAAI;QACJ,IAAI,CAAC,IAAI,CAAC,SAAS,EACf;QACJ,CAAC,KAAK,IAAI,CAAC,SAAS,CAAC,MAAM,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,YAAY,CAAC;YAAE,QAAQ;QAAS;IACzG;IACA,eAAe,QAAQ,EAAE;QACrB,IAAI,IAAI,CAAC,SAAS,EACd,CAAA,GAAA,iMAAA,CAAA,iBAAc,AAAD,EAAE,IAAI,CAAC,SAAS,EAAE;QACnC,OAAO,sJAAA,CAAA,OAAI;IACf;IACA,WAAW;QACP,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM;IAC3C;IACA,SAAS;QACL,IAAI;YACA,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM;QAC3C,EACA,OAAO,GAAG,CAAE;IAChB;AACJ", "ignoreList": [0]}}, {"offset": {"line": 923, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 929, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/motion-dom/dist/es/utils/is-bezier-definition.mjs"], "sourcesContent": ["const isBezierDefinition = (easing) => Array.isArray(easing) && typeof easing[0] === \"number\";\n\nexport { isBezierDefinition };\n"], "names": [], "mappings": ";;;AAAA,MAAM,qBAAqB,CAAC,SAAW,MAAM,OAAO,CAAC,WAAW,OAAO,MAAM,CAAC,EAAE,KAAK", "ignoreList": [0]}}, {"offset": {"line": 934, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 940, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/motion-dom/dist/es/utils/supports/flags.mjs"], "sourcesContent": ["/**\n * Add the ability for test suites to manually set support flags\n * to better test more environments.\n */\nconst supportsFlags = {\n    linearEasing: undefined,\n};\n\nexport { supportsFlags };\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AACD,MAAM,gBAAgB;IAClB,cAAc;AAClB", "ignoreList": [0]}}, {"offset": {"line": 950, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 956, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/motion-dom/dist/es/utils/supports/memo.mjs"], "sourcesContent": ["import { memo } from 'motion-utils';\nimport { supportsFlags } from './flags.mjs';\n\nfunction memoSupports(callback, supportsFlag) {\n    const memoized = memo(callback);\n    return () => { var _a; return (_a = supportsFlags[supportsFlag]) !== null && _a !== void 0 ? _a : memoized(); };\n}\n\nexport { memoSupports };\n"], "names": [], "mappings": ";;;AAAA;AACA;AADA;;;AAGA,SAAS,aAAa,QAAQ,EAAE,YAAY;IACxC,MAAM,WAAW,CAAA,GAAA,sJAAA,CAAA,OAAI,AAAD,EAAE;IACtB,OAAO;QAAQ,IAAI;QAAI,OAAO,CAAC,KAAK,0KAAA,CAAA,gBAAa,CAAC,aAAa,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;IAAY;AAClH", "ignoreList": [0]}}, {"offset": {"line": 972, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 978, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/motion-dom/dist/es/utils/supports/linear-easing.mjs"], "sourcesContent": ["import { memoSupports } from './memo.mjs';\n\nconst supportsLinearEasing = /*@__PURE__*/ memoSupports(() => {\n    try {\n        document\n            .createElement(\"div\")\n            .animate({ opacity: 0 }, { easing: \"linear(0, 1)\" });\n    }\n    catch (e) {\n        return false;\n    }\n    return true;\n}, \"linearEasing\");\n\nexport { supportsLinearEasing };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,uBAAuB,WAAW,GAAG,CAAA,GAAA,yKAAA,CAAA,eAAY,AAAD,EAAE;IACpD,IAAI;QACA,SACK,aAAa,CAAC,OACd,OAAO,CAAC;YAAE,SAAS;QAAE,GAAG;YAAE,QAAQ;QAAe;IAC1D,EACA,OAAO,GAAG;QACN,OAAO;IACX;IACA,OAAO;AACX,GAAG", "ignoreList": [0]}}, {"offset": {"line": 996, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1002, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/motion-dom/dist/es/animation/waapi/utils/linear.mjs"], "sourcesContent": ["import { progress } from 'motion-utils';\n\nconst generateLinearEasing = (easing, duration, // as milliseconds\nresolution = 10 // as milliseconds\n) => {\n    let points = \"\";\n    const numPoints = Math.max(Math.round(duration / resolution), 2);\n    for (let i = 0; i < numPoints; i++) {\n        points += easing(progress(0, numPoints - 1, i)) + \", \";\n    }\n    return `linear(${points.substring(0, points.length - 2)})`;\n};\n\nexport { generateLinearEasing };\n"], "names": [], "mappings": ";;;AAAA;AAAA;;AAEA,MAAM,uBAAuB,CAAC,QAAQ,UACtC,aAAa,GAAG,kBAAkB;AAAnB;IAEX,IAAI,SAAS;IACb,MAAM,YAAY,KAAK,GAAG,CAAC,KAAK,KAAK,CAAC,WAAW,aAAa;IAC9D,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,IAAK;QAChC,UAAU,OAAO,CAAA,GAAA,0JAAA,CAAA,WAAQ,AAAD,EAAE,GAAG,YAAY,GAAG,MAAM;IACtD;IACA,OAAO,CAAC,OAAO,EAAE,OAAO,SAAS,CAAC,GAAG,OAAO,MAAM,GAAG,GAAG,CAAC,CAAC;AAC9D", "ignoreList": [0]}}, {"offset": {"line": 1018, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1024, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/motion-dom/dist/es/animation/waapi/utils/easing.mjs"], "sourcesContent": ["import { isBezierDefinition } from '../../../utils/is-bezier-definition.mjs';\nimport { supportsLinearEasing } from '../../../utils/supports/linear-easing.mjs';\nimport { generateLinearEasing } from './linear.mjs';\n\nfunction isWaapiSupportedEasing(easing) {\n    return Boolean((typeof easing === \"function\" && supportsLinearEasing()) ||\n        !easing ||\n        (typeof easing === \"string\" &&\n            (easing in supportedWaapiEasing || supportsLinearEasing())) ||\n        isBezierDefinition(easing) ||\n        (Array.isArray(easing) && easing.every(isWaapiSupportedEasing)));\n}\nconst cubicBezierAsString = ([a, b, c, d]) => `cubic-bezier(${a}, ${b}, ${c}, ${d})`;\nconst supportedWaapiEasing = {\n    linear: \"linear\",\n    ease: \"ease\",\n    easeIn: \"ease-in\",\n    easeOut: \"ease-out\",\n    easeInOut: \"ease-in-out\",\n    circIn: /*@__PURE__*/ cubicBezierAsString([0, 0.65, 0.55, 1]),\n    circOut: /*@__PURE__*/ cubicBezierAsString([0.55, 0, 1, 0.45]),\n    backIn: /*@__PURE__*/ cubicBezierAsString([0.31, 0.01, 0.66, -0.59]),\n    backOut: /*@__PURE__*/ cubicBezierAsString([0.33, 1.53, 0.69, 0.99]),\n};\nfunction mapEasingToNativeEasing(easing, duration) {\n    if (!easing) {\n        return undefined;\n    }\n    else if (typeof easing === \"function\" && supportsLinearEasing()) {\n        return generateLinearEasing(easing, duration);\n    }\n    else if (isBezierDefinition(easing)) {\n        return cubicBezierAsString(easing);\n    }\n    else if (Array.isArray(easing)) {\n        return easing.map((segmentEasing) => mapEasingToNativeEasing(segmentEasing, duration) ||\n            supportedWaapiEasing.easeOut);\n    }\n    else {\n        return supportedWaapiEasing[easing];\n    }\n}\n\nexport { cubicBezierAsString, isWaapiSupportedEasing, mapEasingToNativeEasing, supportedWaapiEasing };\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;;;;AAEA,SAAS,uBAAuB,MAAM;IAClC,OAAO,QAAQ,AAAC,OAAO,WAAW,cAAc,CAAA,GAAA,qLAAA,CAAA,uBAAoB,AAAD,OAC/D,CAAC,UACA,OAAO,WAAW,YACf,CAAC,UAAU,wBAAwB,CAAA,GAAA,qLAAA,CAAA,uBAAoB,AAAD,GAAG,KAC7D,CAAA,GAAA,mLAAA,CAAA,qBAAkB,AAAD,EAAE,WAClB,MAAM,OAAO,CAAC,WAAW,OAAO,KAAK,CAAC;AAC/C;AACA,MAAM,sBAAsB,CAAC,CAAC,GAAG,GAAG,GAAG,EAAE,GAAK,CAAC,aAAa,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;AACpF,MAAM,uBAAuB;IACzB,QAAQ;IACR,MAAM;IACN,QAAQ;IACR,SAAS;IACT,WAAW;IACX,QAAQ,WAAW,GAAG,oBAAoB;QAAC;QAAG;QAAM;QAAM;KAAE;IAC5D,SAAS,WAAW,GAAG,oBAAoB;QAAC;QAAM;QAAG;QAAG;KAAK;IAC7D,QAAQ,WAAW,GAAG,oBAAoB;QAAC;QAAM;QAAM;QAAM,CAAC;KAAK;IACnE,SAAS,WAAW,GAAG,oBAAoB;QAAC;QAAM;QAAM;QAAM;KAAK;AACvE;AACA,SAAS,wBAAwB,MAAM,EAAE,QAAQ;IAC7C,IAAI,CAAC,QAAQ;QACT,OAAO;IACX,OACK,IAAI,OAAO,WAAW,cAAc,CAAA,GAAA,qLAAA,CAAA,uBAAoB,AAAD,KAAK;QAC7D,OAAO,CAAA,GAAA,qLAAA,CAAA,uBAAoB,AAAD,EAAE,QAAQ;IACxC,OACK,IAAI,CAAA,GAAA,mLAAA,CAAA,qBAAkB,AAAD,EAAE,SAAS;QACjC,OAAO,oBAAoB;IAC/B,OACK,IAAI,MAAM,OAAO,CAAC,SAAS;QAC5B,OAAO,OAAO,GAAG,CAAC,CAAC,gBAAkB,wBAAwB,eAAe,aACxE,qBAAqB,OAAO;IACpC,OACK;QACD,OAAO,oBAAoB,CAAC,OAAO;IACvC;AACJ", "ignoreList": [0]}}, {"offset": {"line": 1085, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1091, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/motion-dom/dist/es/gestures/drag/state/is-active.mjs"], "sourcesContent": ["const isDragging = {\n    x: false,\n    y: false,\n};\nfunction isDragActive() {\n    return isDragging.x || isDragging.y;\n}\n\nexport { isDragActive, isDragging };\n"], "names": [], "mappings": ";;;;AAAA,MAAM,aAAa;IACf,GAAG;IACH,GAAG;AACP;AACA,SAAS;IACL,OAAO,WAAW,CAAC,IAAI,WAAW,CAAC;AACvC", "ignoreList": [0]}}, {"offset": {"line": 1103, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1109, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/motion-dom/dist/es/utils/resolve-elements.mjs"], "sourcesContent": ["function resolveElements(elementOrSelector, scope, selectorCache) {\n    var _a;\n    if (elementOrSelector instanceof Element) {\n        return [elementOrSelector];\n    }\n    else if (typeof elementOrSelector === \"string\") {\n        let root = document;\n        if (scope) {\n            // TODO: Refactor to utils package\n            // invariant(\n            //     Boolean(scope.current),\n            //     \"Scope provided, but no element detected.\"\n            // )\n            root = scope.current;\n        }\n        const elements = (_a = selectorCache === null || selectorCache === void 0 ? void 0 : selectorCache[elementOrSelector]) !== null && _a !== void 0 ? _a : root.querySelectorAll(elementOrSelector);\n        return elements ? Array.from(elements) : [];\n    }\n    return Array.from(elementOrSelector);\n}\n\nexport { resolveElements };\n"], "names": [], "mappings": ";;;AAAA,SAAS,gBAAgB,iBAAiB,EAAE,KAAK,EAAE,aAAa;IAC5D,IAAI;IACJ,IAAI,6BAA6B,SAAS;QACtC,OAAO;YAAC;SAAkB;IAC9B,OACK,IAAI,OAAO,sBAAsB,UAAU;QAC5C,IAAI,OAAO;QACX,IAAI,OAAO;YACP,kCAAkC;YAClC,aAAa;YACb,8BAA8B;YAC9B,iDAAiD;YACjD,IAAI;YACJ,OAAO,MAAM,OAAO;QACxB;QACA,MAAM,WAAW,CAAC,KAAK,kBAAkB,QAAQ,kBAAkB,KAAK,IAAI,KAAK,IAAI,aAAa,CAAC,kBAAkB,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,KAAK,gBAAgB,CAAC;QAC9K,OAAO,WAAW,MAAM,IAAI,CAAC,YAAY,EAAE;IAC/C;IACA,OAAO,MAAM,IAAI,CAAC;AACtB", "ignoreList": [0]}}, {"offset": {"line": 1134, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1140, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/motion-dom/dist/es/gestures/utils/setup.mjs"], "sourcesContent": ["import { resolveElements } from '../../utils/resolve-elements.mjs';\n\nfunction setupGesture(elementOrSelector, options) {\n    const elements = resolveElements(elementOrSelector);\n    const gestureAbortController = new AbortController();\n    const eventOptions = {\n        passive: true,\n        ...options,\n        signal: gestureAbortController.signal,\n    };\n    const cancel = () => gestureAbortController.abort();\n    return [elements, eventOptions, cancel];\n}\n\nexport { setupGesture };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,SAAS,aAAa,iBAAiB,EAAE,OAAO;IAC5C,MAAM,WAAW,CAAA,GAAA,4KAAA,CAAA,kBAAe,AAAD,EAAE;IACjC,MAAM,yBAAyB,IAAI;IACnC,MAAM,eAAe;QACjB,SAAS;QACT,GAAG,OAAO;QACV,QAAQ,uBAAuB,MAAM;IACzC;IACA,MAAM,SAAS,IAAM,uBAAuB,KAAK;IACjD,OAAO;QAAC;QAAU;QAAc;KAAO;AAC3C", "ignoreList": [0]}}, {"offset": {"line": 1161, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1167, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/motion-dom/dist/es/gestures/hover.mjs"], "sourcesContent": ["import { isDragActive } from './drag/state/is-active.mjs';\nimport { setupGesture } from './utils/setup.mjs';\n\nfunction isValidHover(event) {\n    return !(event.pointerType === \"touch\" || isDragActive());\n}\n/**\n * Create a hover gesture. hover() is different to .addEventListener(\"pointerenter\")\n * in that it has an easier syntax, filters out polyfilled touch events, interoperates\n * with drag gestures, and automatically removes the \"pointerennd\" event listener when the hover ends.\n *\n * @public\n */\nfunction hover(elementOrSelector, onHoverStart, options = {}) {\n    const [elements, eventOptions, cancel] = setupGesture(elementOrSelector, options);\n    const onPointerEnter = (enterEvent) => {\n        if (!isValidHover(enterEvent))\n            return;\n        const { target } = enterEvent;\n        const onHoverEnd = onHoverStart(target, enterEvent);\n        if (typeof onHoverEnd !== \"function\" || !target)\n            return;\n        const onPointerLeave = (leaveEvent) => {\n            if (!isValidHover(leaveEvent))\n                return;\n            onHoverEnd(leaveEvent);\n            target.removeEventListener(\"pointerleave\", onPointerLeave);\n        };\n        target.addEventListener(\"pointerleave\", onPointerLeave, eventOptions);\n    };\n    elements.forEach((element) => {\n        element.addEventListener(\"pointerenter\", onPointerEnter, eventOptions);\n    });\n    return cancel;\n}\n\nexport { hover };\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,SAAS,aAAa,KAAK;IACvB,OAAO,CAAC,CAAC,MAAM,WAAW,KAAK,WAAW,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,GAAG;AAC5D;AACA;;;;;;CAMC,GACD,SAAS,MAAM,iBAAiB,EAAE,YAAY,EAAE,UAAU,CAAC,CAAC;IACxD,MAAM,CAAC,UAAU,cAAc,OAAO,GAAG,CAAA,GAAA,0KAAA,CAAA,eAAY,AAAD,EAAE,mBAAmB;IACzE,MAAM,iBAAiB,CAAC;QACpB,IAAI,CAAC,aAAa,aACd;QACJ,MAAM,EAAE,MAAM,EAAE,GAAG;QACnB,MAAM,aAAa,aAAa,QAAQ;QACxC,IAAI,OAAO,eAAe,cAAc,CAAC,QACrC;QACJ,MAAM,iBAAiB,CAAC;YACpB,IAAI,CAAC,aAAa,aACd;YACJ,WAAW;YACX,OAAO,mBAAmB,CAAC,gBAAgB;QAC/C;QACA,OAAO,gBAAgB,CAAC,gBAAgB,gBAAgB;IAC5D;IACA,SAAS,OAAO,CAAC,CAAC;QACd,QAAQ,gBAAgB,CAAC,gBAAgB,gBAAgB;IAC7D;IACA,OAAO;AACX", "ignoreList": [0]}}, {"offset": {"line": 1203, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1209, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/motion-dom/dist/es/gestures/utils/is-node-or-child.mjs"], "sourcesContent": ["/**\n * Recursively traverse up the tree to check whether the provided child node\n * is the parent or a descendant of it.\n *\n * @param parent - Element to find\n * @param child - Element to test against parent\n */\nconst isNodeOrChild = (parent, child) => {\n    if (!child) {\n        return false;\n    }\n    else if (parent === child) {\n        return true;\n    }\n    else {\n        return isNodeOrChild(parent, child.parentElement);\n    }\n};\n\nexport { isNodeOrChild };\n"], "names": [], "mappings": "AAAA;;;;;;CAMC;;;AACD,MAAM,gBAAgB,CAAC,QAAQ;IAC3B,IAAI,CAAC,OAAO;QACR,OAAO;IACX,OACK,IAAI,WAAW,OAAO;QACvB,OAAO;IACX,OACK;QACD,OAAO,cAAc,QAAQ,MAAM,aAAa;IACpD;AACJ", "ignoreList": [0]}}, {"offset": {"line": 1228, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1234, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/motion-dom/dist/es/gestures/utils/is-primary-pointer.mjs"], "sourcesContent": ["const isPrimaryPointer = (event) => {\n    if (event.pointerType === \"mouse\") {\n        return typeof event.button !== \"number\" || event.button <= 0;\n    }\n    else {\n        /**\n         * isPrimary is true for all mice buttons, whereas every touch point\n         * is regarded as its own input. So subsequent concurrent touch points\n         * will be false.\n         *\n         * Specifically match against false here as incomplete versions of\n         * PointerEvents in very old browser might have it set as undefined.\n         */\n        return event.isPrimary !== false;\n    }\n};\n\nexport { isPrimaryPointer };\n"], "names": [], "mappings": ";;;AAAA,MAAM,mBAAmB,CAAC;IACtB,IAAI,MAAM,WAAW,KAAK,SAAS;QAC/B,OAAO,OAAO,MAAM,MAAM,KAAK,YAAY,MAAM,MAAM,IAAI;IAC/D,OACK;QACD;;;;;;;SAOC,GACD,OAAO,MAAM,SAAS,KAAK;IAC/B;AACJ", "ignoreList": [0]}}, {"offset": {"line": 1252, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1258, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/motion-dom/dist/es/gestures/press/utils/is-keyboard-accessible.mjs"], "sourcesContent": ["const focusableElements = new Set([\n    \"BUTTON\",\n    \"INPUT\",\n    \"SELECT\",\n    \"TEXTAREA\",\n    \"A\",\n]);\nfunction isElementKeyboardAccessible(element) {\n    return (focusableElements.has(element.tagName) ||\n        element.tabIndex !== -1);\n}\n\nexport { isElementKeyboardAccessible };\n"], "names": [], "mappings": ";;;AAAA,MAAM,oBAAoB,IAAI,IAAI;IAC9B;IACA;IACA;IACA;IACA;CACH;AACD,SAAS,4BAA4B,OAAO;IACxC,OAAQ,kBAAkB,GAAG,CAAC,QAAQ,OAAO,KACzC,QAAQ,QAAQ,KAAK,CAAC;AAC9B", "ignoreList": [0]}}, {"offset": {"line": 1272, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1278, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/motion-dom/dist/es/gestures/press/utils/state.mjs"], "sourcesContent": ["const isPressing = new WeakSet();\n\nexport { isPressing };\n"], "names": [], "mappings": ";;;AAAA,MAAM,aAAa,IAAI", "ignoreList": [0]}}, {"offset": {"line": 1283, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1289, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/motion-dom/dist/es/gestures/press/utils/keyboard.mjs"], "sourcesContent": ["import { isPressing } from './state.mjs';\n\n/**\n * Filter out events that are not \"Enter\" keys.\n */\nfunction filterEvents(callback) {\n    return (event) => {\n        if (event.key !== \"Enter\")\n            return;\n        callback(event);\n    };\n}\nfunction firePointerEvent(target, type) {\n    target.dispatchEvent(new PointerEvent(\"pointer\" + type, { isPrimary: true, bubbles: true }));\n}\nconst enableKeyboardPress = (focusEvent, eventOptions) => {\n    const element = focusEvent.currentTarget;\n    if (!element)\n        return;\n    const handleKeydown = filterEvents(() => {\n        if (isPressing.has(element))\n            return;\n        firePointerEvent(element, \"down\");\n        const handleKeyup = filterEvents(() => {\n            firePointerEvent(element, \"up\");\n        });\n        const handleBlur = () => firePointerEvent(element, \"cancel\");\n        element.addEventListener(\"keyup\", handleKeyup, eventOptions);\n        element.addEventListener(\"blur\", handleBlur, eventOptions);\n    });\n    element.addEventListener(\"keydown\", handleKeydown, eventOptions);\n    /**\n     * Add an event listener that fires on blur to remove the keydown events.\n     */\n    element.addEventListener(\"blur\", () => element.removeEventListener(\"keydown\", handleKeydown), eventOptions);\n};\n\nexport { enableKeyboardPress };\n"], "names": [], "mappings": ";;;AAAA;;AAEA;;CAEC,GACD,SAAS,aAAa,QAAQ;IAC1B,OAAO,CAAC;QACJ,IAAI,MAAM,GAAG,KAAK,SACd;QACJ,SAAS;IACb;AACJ;AACA,SAAS,iBAAiB,MAAM,EAAE,IAAI;IAClC,OAAO,aAAa,CAAC,IAAI,aAAa,YAAY,MAAM;QAAE,WAAW;QAAM,SAAS;IAAK;AAC7F;AACA,MAAM,sBAAsB,CAAC,YAAY;IACrC,MAAM,UAAU,WAAW,aAAa;IACxC,IAAI,CAAC,SACD;IACJ,MAAM,gBAAgB,aAAa;QAC/B,IAAI,mLAAA,CAAA,aAAU,CAAC,GAAG,CAAC,UACf;QACJ,iBAAiB,SAAS;QAC1B,MAAM,cAAc,aAAa;YAC7B,iBAAiB,SAAS;QAC9B;QACA,MAAM,aAAa,IAAM,iBAAiB,SAAS;QACnD,QAAQ,gBAAgB,CAAC,SAAS,aAAa;QAC/C,QAAQ,gBAAgB,CAAC,QAAQ,YAAY;IACjD;IACA,QAAQ,gBAAgB,CAAC,WAAW,eAAe;IACnD;;KAEC,GACD,QAAQ,gBAAgB,CAAC,QAAQ,IAAM,QAAQ,mBAAmB,CAAC,WAAW,gBAAgB;AAClG", "ignoreList": [0]}}, {"offset": {"line": 1327, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1333, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/motion-dom/dist/es/gestures/press/index.mjs"], "sourcesContent": ["import { isDragActive } from '../drag/state/is-active.mjs';\nimport { isNodeOrChild } from '../utils/is-node-or-child.mjs';\nimport { isPrimaryPointer } from '../utils/is-primary-pointer.mjs';\nimport { setupGesture } from '../utils/setup.mjs';\nimport { isElementKeyboardAccessible } from './utils/is-keyboard-accessible.mjs';\nimport { enableKeyboardPress } from './utils/keyboard.mjs';\nimport { isPressing } from './utils/state.mjs';\n\n/**\n * Filter out events that are not primary pointer events, or are triggering\n * while a Motion gesture is active.\n */\nfunction isValidPressEvent(event) {\n    return isPrimaryPointer(event) && !isDragActive();\n}\n/**\n * Create a press gesture.\n *\n * Press is different to `\"pointerdown\"`, `\"pointerup\"` in that it\n * automatically filters out secondary pointer events like right\n * click and multitouch.\n *\n * It also adds accessibility support for keyboards, where\n * an element with a press gesture will receive focus and\n *  trigger on Enter `\"keydown\"` and `\"keyup\"` events.\n *\n * This is different to a browser's `\"click\"` event, which does\n * respond to keyboards but only for the `\"click\"` itself, rather\n * than the press start and end/cancel. The element also needs\n * to be focusable for this to work, whereas a press gesture will\n * make an element focusable by default.\n *\n * @public\n */\nfunction press(elementOrSelector, onPressStart, options = {}) {\n    const [elements, eventOptions, cancelEvents] = setupGesture(elementOrSelector, options);\n    const startPress = (startEvent) => {\n        const element = startEvent.currentTarget;\n        if (!isValidPressEvent(startEvent) || isPressing.has(element))\n            return;\n        isPressing.add(element);\n        const onPressEnd = onPressStart(element, startEvent);\n        const onPointerEnd = (endEvent, success) => {\n            window.removeEventListener(\"pointerup\", onPointerUp);\n            window.removeEventListener(\"pointercancel\", onPointerCancel);\n            if (!isValidPressEvent(endEvent) || !isPressing.has(element)) {\n                return;\n            }\n            isPressing.delete(element);\n            if (typeof onPressEnd === \"function\") {\n                onPressEnd(endEvent, { success });\n            }\n        };\n        const onPointerUp = (upEvent) => {\n            onPointerEnd(upEvent, options.useGlobalTarget ||\n                isNodeOrChild(element, upEvent.target));\n        };\n        const onPointerCancel = (cancelEvent) => {\n            onPointerEnd(cancelEvent, false);\n        };\n        window.addEventListener(\"pointerup\", onPointerUp, eventOptions);\n        window.addEventListener(\"pointercancel\", onPointerCancel, eventOptions);\n    };\n    elements.forEach((element) => {\n        if (!isElementKeyboardAccessible(element) &&\n            element.getAttribute(\"tabindex\") === null) {\n            element.tabIndex = 0;\n        }\n        const target = options.useGlobalTarget ? window : element;\n        target.addEventListener(\"pointerdown\", startPress, eventOptions);\n        element.addEventListener(\"focus\", (event) => enableKeyboardPress(event, eventOptions), eventOptions);\n    });\n    return cancelEvents;\n}\n\nexport { press };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAEA;;;CAGC,GACD,SAAS,kBAAkB,KAAK;IAC5B,OAAO,CAAA,GAAA,6LAAA,CAAA,mBAAgB,AAAD,EAAE,UAAU,CAAC,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD;AAClD;AACA;;;;;;;;;;;;;;;;;;CAkBC,GACD,SAAS,MAAM,iBAAiB,EAAE,YAAY,EAAE,UAAU,CAAC,CAAC;IACxD,MAAM,CAAC,UAAU,cAAc,aAAa,GAAG,CAAA,GAAA,0KAAA,CAAA,eAAY,AAAD,EAAE,mBAAmB;IAC/E,MAAM,aAAa,CAAC;QAChB,MAAM,UAAU,WAAW,aAAa;QACxC,IAAI,CAAC,kBAAkB,eAAe,mLAAA,CAAA,aAAU,CAAC,GAAG,CAAC,UACjD;QACJ,mLAAA,CAAA,aAAU,CAAC,GAAG,CAAC;QACf,MAAM,aAAa,aAAa,SAAS;QACzC,MAAM,eAAe,CAAC,UAAU;YAC5B,OAAO,mBAAmB,CAAC,aAAa;YACxC,OAAO,mBAAmB,CAAC,iBAAiB;YAC5C,IAAI,CAAC,kBAAkB,aAAa,CAAC,mLAAA,CAAA,aAAU,CAAC,GAAG,CAAC,UAAU;gBAC1D;YACJ;YACA,mLAAA,CAAA,aAAU,CAAC,MAAM,CAAC;YAClB,IAAI,OAAO,eAAe,YAAY;gBAClC,WAAW,UAAU;oBAAE;gBAAQ;YACnC;QACJ;QACA,MAAM,cAAc,CAAC;YACjB,aAAa,SAAS,QAAQ,eAAe,IACzC,CAAA,GAAA,8LAAA,CAAA,gBAAa,AAAD,EAAE,SAAS,QAAQ,MAAM;QAC7C;QACA,MAAM,kBAAkB,CAAC;YACrB,aAAa,aAAa;QAC9B;QACA,OAAO,gBAAgB,CAAC,aAAa,aAAa;QAClD,OAAO,gBAAgB,CAAC,iBAAiB,iBAAiB;IAC9D;IACA,SAAS,OAAO,CAAC,CAAC;QACd,IAAI,CAAC,CAAA,GAAA,0MAAA,CAAA,8BAA2B,AAAD,EAAE,YAC7B,QAAQ,YAAY,CAAC,gBAAgB,MAAM;YAC3C,QAAQ,QAAQ,GAAG;QACvB;QACA,MAAM,SAAS,QAAQ,eAAe,GAAG,SAAS;QAClD,OAAO,gBAAgB,CAAC,eAAe,YAAY;QACnD,QAAQ,gBAAgB,CAAC,SAAS,CAAC,QAAU,CAAA,GAAA,sLAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,eAAe;IAC3F;IACA,OAAO;AACX", "ignoreList": [0]}}, {"offset": {"line": 1414, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1420, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/motion-dom/dist/es/animation/waapi/utils/convert-options.mjs"], "sourcesContent": ["import { secondsToMilliseconds } from 'motion-utils';\nimport { supportsLinearEasing } from '../../../utils/supports/linear-easing.mjs';\nimport { createGeneratorEasing } from '../../generators/utils/create-generator-easing.mjs';\nimport { isGenerator } from '../../generators/utils/is-generator.mjs';\nimport { mapEasingToNativeEasing } from './easing.mjs';\n\nconst defaultEasing = \"easeOut\";\nfunction applyGeneratorOptions(options) {\n    var _a;\n    if (isGenerator(options.type)) {\n        const generatorOptions = createGeneratorEasing(options, 100, options.type);\n        options.ease = supportsLinearEasing()\n            ? generatorOptions.ease\n            : defaultEasing;\n        options.duration = secondsToMilliseconds(generatorOptions.duration);\n        options.type = \"keyframes\";\n    }\n    else {\n        options.duration = secondsToMilliseconds((_a = options.duration) !== null && _a !== void 0 ? _a : 0.3);\n        options.ease = options.ease || defaultEasing;\n    }\n}\n// TODO: Reuse for NativeAnimation\nfunction convertMotionOptionsToNative(valueName, keyframes, options) {\n    var _a;\n    const nativeKeyframes = {};\n    const nativeOptions = {\n        fill: \"both\",\n        easing: \"linear\",\n        composite: \"replace\",\n    };\n    nativeOptions.delay = secondsToMilliseconds((_a = options.delay) !== null && _a !== void 0 ? _a : 0);\n    applyGeneratorOptions(options);\n    nativeOptions.duration = options.duration;\n    const { ease, times } = options;\n    if (times)\n        nativeKeyframes.offset = times;\n    nativeKeyframes[valueName] = keyframes;\n    const easing = mapEasingToNativeEasing(ease, options.duration);\n    /**\n     * If this is an easing array, apply to keyframes, not animation as a whole\n     */\n    if (Array.isArray(easing)) {\n        nativeKeyframes.easing = easing;\n    }\n    else {\n        nativeOptions.easing = easing;\n    }\n    return {\n        keyframes: nativeKeyframes,\n        options: nativeOptions,\n    };\n}\n\nexport { applyGeneratorOptions, convertMotionOptionsToNative };\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AAJA;;;;;;AAMA,MAAM,gBAAgB;AACtB,SAAS,sBAAsB,OAAO;IAClC,IAAI;IACJ,IAAI,CAAA,GAAA,mMAAA,CAAA,cAAW,AAAD,EAAE,QAAQ,IAAI,GAAG;QAC3B,MAAM,mBAAmB,CAAA,GAAA,iNAAA,CAAA,wBAAqB,AAAD,EAAE,SAAS,KAAK,QAAQ,IAAI;QACzE,QAAQ,IAAI,GAAG,CAAA,GAAA,qLAAA,CAAA,uBAAoB,AAAD,MAC5B,iBAAiB,IAAI,GACrB;QACN,QAAQ,QAAQ,GAAG,CAAA,GAAA,oKAAA,CAAA,wBAAqB,AAAD,EAAE,iBAAiB,QAAQ;QAClE,QAAQ,IAAI,GAAG;IACnB,OACK;QACD,QAAQ,QAAQ,GAAG,CAAA,GAAA,oKAAA,CAAA,wBAAqB,AAAD,EAAE,CAAC,KAAK,QAAQ,QAAQ,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;QAClG,QAAQ,IAAI,GAAG,QAAQ,IAAI,IAAI;IACnC;AACJ;AACA,kCAAkC;AAClC,SAAS,6BAA6B,SAAS,EAAE,SAAS,EAAE,OAAO;IAC/D,IAAI;IACJ,MAAM,kBAAkB,CAAC;IACzB,MAAM,gBAAgB;QAClB,MAAM;QACN,QAAQ;QACR,WAAW;IACf;IACA,cAAc,KAAK,GAAG,CAAA,GAAA,oKAAA,CAAA,wBAAqB,AAAD,EAAE,CAAC,KAAK,QAAQ,KAAK,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;IAClG,sBAAsB;IACtB,cAAc,QAAQ,GAAG,QAAQ,QAAQ;IACzC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG;IACxB,IAAI,OACA,gBAAgB,MAAM,GAAG;IAC7B,eAAe,CAAC,UAAU,GAAG;IAC7B,MAAM,SAAS,CAAA,GAAA,qLAAA,CAAA,0BAAuB,AAAD,EAAE,MAAM,QAAQ,QAAQ;IAC7D;;KAEC,GACD,IAAI,MAAM,OAAO,CAAC,SAAS;QACvB,gBAAgB,MAAM,GAAG;IAC7B,OACK;QACD,cAAc,MAAM,GAAG;IAC3B;IACA,OAAO;QACH,WAAW;QACX,SAAS;IACb;AACJ", "ignoreList": [0]}}, {"offset": {"line": 1477, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1483, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/motion-dom/dist/es/animation/waapi/PseudoAnimation.mjs"], "sourcesContent": ["import { NativeAnimationControls } from './NativeAnimationControls.mjs';\nimport { convertMotionOptionsToNative } from './utils/convert-options.mjs';\n\nclass PseudoAnimation extends NativeAnimationControls {\n    constructor(target, pseudoElement, valueName, keyframes, options) {\n        const animationOptions = convertMotionOptionsToNative(valueName, keyframes, options);\n        const animation = target.animate(animationOptions.keyframes, {\n            pseudoElement,\n            ...animationOptions.options,\n        });\n        super(animation);\n    }\n}\n\nexport { PseudoAnimation };\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,MAAM,wBAAwB,6LAAA,CAAA,0BAAuB;IACjD,YAAY,MAAM,EAAE,aAAa,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO,CAAE;QAC9D,MAAM,mBAAmB,CAAA,GAAA,iMAAA,CAAA,+BAA4B,AAAD,EAAE,WAAW,WAAW;QAC5E,MAAM,YAAY,OAAO,OAAO,CAAC,iBAAiB,SAAS,EAAE;YACzD;YACA,GAAG,iBAAiB,OAAO;QAC/B;QACA,KAAK,CAAC;IACV;AACJ", "ignoreList": [0]}}, {"offset": {"line": 1501, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1507, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/motion-dom/dist/es/view/utils/choose-layer-type.mjs"], "sourcesContent": ["function chooseLayerType(valueName) {\n    if (valueName === \"layout\")\n        return \"group\";\n    if (valueName === \"enter\" || valueName === \"new\")\n        return \"new\";\n    if (valueName === \"exit\" || valueName === \"old\")\n        return \"old\";\n    return \"group\";\n}\n\nexport { chooseLayerType };\n"], "names": [], "mappings": ";;;AAAA,SAAS,gBAAgB,SAAS;IAC9B,IAAI,cAAc,UACd,OAAO;IACX,IAAI,cAAc,WAAW,cAAc,OACvC,OAAO;IACX,IAAI,cAAc,UAAU,cAAc,OACtC,OAAO;IACX,OAAO;AACX", "ignoreList": [0]}}, {"offset": {"line": 1517, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1523, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/motion-dom/dist/es/view/utils/css.mjs"], "sourcesContent": ["let pendingRules = {};\nlet style = null;\nconst css = {\n    set: (selector, values) => {\n        pendingRules[selector] = values;\n    },\n    commit: () => {\n        if (!style) {\n            style = document.createElement(\"style\");\n            style.id = \"motion-view\";\n        }\n        let cssText = \"\";\n        for (const selector in pendingRules) {\n            const rule = pendingRules[selector];\n            cssText += `${selector} {\\n`;\n            for (const [property, value] of Object.entries(rule)) {\n                cssText += `  ${property}: ${value};\\n`;\n            }\n            cssText += \"}\\n\";\n        }\n        style.textContent = cssText;\n        document.head.appendChild(style);\n        pendingRules = {};\n    },\n    remove: () => {\n        if (style && style.parentElement) {\n            style.parentElement.removeChild(style);\n        }\n    },\n};\n\nexport { css };\n"], "names": [], "mappings": ";;;AAAA,IAAI,eAAe,CAAC;AACpB,IAAI,QAAQ;AACZ,MAAM,MAAM;IACR,KAAK,CAAC,UAAU;QACZ,YAAY,CAAC,SAAS,GAAG;IAC7B;IACA,QAAQ;QACJ,IAAI,CAAC,OAAO;YACR,QAAQ,SAAS,aAAa,CAAC;YAC/B,MAAM,EAAE,GAAG;QACf;QACA,IAAI,UAAU;QACd,IAAK,MAAM,YAAY,aAAc;YACjC,MAAM,OAAO,YAAY,CAAC,SAAS;YACnC,WAAW,GAAG,SAAS,IAAI,CAAC;YAC5B,KAAK,MAAM,CAAC,UAAU,MAAM,IAAI,OAAO,OAAO,CAAC,MAAO;gBAClD,WAAW,CAAC,EAAE,EAAE,SAAS,EAAE,EAAE,MAAM,GAAG,CAAC;YAC3C;YACA,WAAW;QACf;QACA,MAAM,WAAW,GAAG;QACpB,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,eAAe,CAAC;IACpB;IACA,QAAQ;QACJ,IAAI,SAAS,MAAM,aAAa,EAAE;YAC9B,MAAM,aAAa,CAAC,WAAW,CAAC;QACpC;IACJ;AACJ", "ignoreList": [0]}}, {"offset": {"line": 1557, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1563, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/motion-dom/dist/es/view/utils/get-layer-name.mjs"], "sourcesContent": ["function getLayerName(pseudoElement) {\n    const match = pseudoElement.match(/::view-transition-(old|new|group|image-pair)\\((.*?)\\)/);\n    if (!match)\n        return null;\n    return { layer: match[2], type: match[1] };\n}\n\nexport { getLayerName };\n"], "names": [], "mappings": ";;;AAAA,SAAS,aAAa,aAAa;IAC/B,MAAM,QAAQ,cAAc,KAAK,CAAC;IAClC,IAAI,CAAC,OACD,OAAO;IACX,OAAO;QAAE,OAAO,KAAK,CAAC,EAAE;QAAE,MAAM,KAAK,CAAC,EAAE;IAAC;AAC7C", "ignoreList": [0]}}, {"offset": {"line": 1575, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1581, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/motion-dom/dist/es/view/utils/get-view-animations.mjs"], "sourcesContent": ["function filterViewAnimations(animation) {\n    var _a;\n    const { effect } = animation;\n    if (!effect)\n        return false;\n    return (effect.target === document.documentElement &&\n        ((_a = effect.pseudoElement) === null || _a === void 0 ? void 0 : _a.startsWith(\"::view-transition\")));\n}\nfunction getViewAnimations() {\n    return document.getAnimations().filter(filterViewAnimations);\n}\n\nexport { getViewAnimations };\n"], "names": [], "mappings": ";;;AAAA,SAAS,qBAAqB,SAAS;IACnC,IAAI;IACJ,MAAM,EAAE,MAAM,EAAE,GAAG;IACnB,IAAI,CAAC,QACD,OAAO;IACX,OAAQ,OAAO,MAAM,KAAK,SAAS,eAAe,IAC9C,CAAC,CAAC,KAAK,OAAO,aAAa,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,UAAU,CAAC,oBAAoB;AAC5G;AACA,SAAS;IACL,OAAO,SAAS,aAAa,GAAG,MAAM,CAAC;AAC3C", "ignoreList": [0]}}, {"offset": {"line": 1594, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1600, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/motion-dom/dist/es/view/utils/has-target.mjs"], "sourcesContent": ["function hasTarget(target, targets) {\n    return targets.has(target) && Object.keys(targets.get(target)).length > 0;\n}\n\nexport { hasTarget };\n"], "names": [], "mappings": ";;;AAAA,SAAS,UAAU,MAAM,EAAE,OAAO;IAC9B,OAAO,QAAQ,GAAG,CAAC,WAAW,OAAO,IAAI,CAAC,QAAQ,GAAG,CAAC,SAAS,MAAM,GAAG;AAC5E", "ignoreList": [0]}}, {"offset": {"line": 1607, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1613, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/motion-dom/dist/es/view/start.mjs"], "sourcesContent": ["import { secondsToMilliseconds } from 'motion-utils';\nimport { BaseGroupPlaybackControls } from '../animation/controls/BaseGroup.mjs';\nimport { getValueTransition } from '../animation/utils/get-value-transition.mjs';\nimport { NativeAnimationControls } from '../animation/waapi/NativeAnimationControls.mjs';\nimport { PseudoAnimation } from '../animation/waapi/PseudoAnimation.mjs';\nimport { applyGeneratorOptions } from '../animation/waapi/utils/convert-options.mjs';\nimport { mapEasingToNativeEasing } from '../animation/waapi/utils/easing.mjs';\nimport { chooseLayerType } from './utils/choose-layer-type.mjs';\nimport { css } from './utils/css.mjs';\nimport { getLayerName } from './utils/get-layer-name.mjs';\nimport { getViewAnimations } from './utils/get-view-animations.mjs';\nimport { hasTarget } from './utils/has-target.mjs';\n\nconst definitionNames = [\"layout\", \"enter\", \"exit\", \"new\", \"old\"];\nfunction startViewAnimation(update, defaultOptions, targets) {\n    if (!document.startViewTransition) {\n        return new Promise(async (resolve) => {\n            await update();\n            resolve(new BaseGroupPlaybackControls([]));\n        });\n    }\n    // TODO: Go over existing targets and ensure they all have ids\n    /**\n     * If we don't have any animations defined for the root target,\n     * remove it from being captured.\n     */\n    if (!hasTarget(\"root\", targets)) {\n        css.set(\":root\", {\n            \"view-transition-name\": \"none\",\n        });\n    }\n    /**\n     * Set the timing curve to linear for all view transition layers.\n     * This gets baked into the keyframes, which can't be changed\n     * without breaking the generated animation.\n     *\n     * This allows us to set easing via updateTiming - which can be changed.\n     */\n    css.set(\"::view-transition-group(*), ::view-transition-old(*), ::view-transition-new(*)\", { \"animation-timing-function\": \"linear !important\" });\n    css.commit(); // Write\n    const transition = document.startViewTransition(async () => {\n        await update();\n        // TODO: Go over new targets and ensure they all have ids\n    });\n    transition.finished.finally(() => {\n        css.remove(); // Write\n    });\n    return new Promise((resolve) => {\n        transition.ready.then(() => {\n            var _a;\n            const generatedViewAnimations = getViewAnimations();\n            const animations = [];\n            /**\n             * Create animations for our definitions\n             */\n            targets.forEach((definition, target) => {\n                // TODO: If target is not \"root\", resolve elements\n                // and iterate over each\n                for (const key of definitionNames) {\n                    if (!definition[key])\n                        continue;\n                    const { keyframes, options } = definition[key];\n                    for (let [valueName, valueKeyframes] of Object.entries(keyframes)) {\n                        if (!valueKeyframes)\n                            continue;\n                        const valueOptions = {\n                            ...getValueTransition(defaultOptions, valueName),\n                            ...getValueTransition(options, valueName),\n                        };\n                        const type = chooseLayerType(key);\n                        /**\n                         * If this is an opacity animation, and keyframes are not an array,\n                         * we need to convert them into an array and set an initial value.\n                         */\n                        if (valueName === \"opacity\" &&\n                            !Array.isArray(valueKeyframes)) {\n                            const initialValue = type === \"new\" ? 0 : 1;\n                            valueKeyframes = [initialValue, valueKeyframes];\n                        }\n                        /**\n                         * Resolve stagger function if provided.\n                         */\n                        if (typeof valueOptions.delay === \"function\") {\n                            valueOptions.delay = valueOptions.delay(0, 1);\n                        }\n                        const animation = new PseudoAnimation(document.documentElement, `::view-transition-${type}(${target})`, valueName, valueKeyframes, valueOptions);\n                        animations.push(animation);\n                    }\n                }\n            });\n            /**\n             * Handle browser generated animations\n             */\n            for (const animation of generatedViewAnimations) {\n                if (animation.playState === \"finished\")\n                    continue;\n                const { effect } = animation;\n                if (!effect || !(effect instanceof KeyframeEffect))\n                    continue;\n                const { pseudoElement } = effect;\n                if (!pseudoElement)\n                    continue;\n                const name = getLayerName(pseudoElement);\n                if (!name)\n                    continue;\n                const targetDefinition = targets.get(name.layer);\n                if (!targetDefinition) {\n                    /**\n                     * If transition name is group then update the timing of the animation\n                     * whereas if it's old or new then we could possibly replace it using\n                     * the above method.\n                     */\n                    const transitionName = name.type === \"group\" ? \"layout\" : \"\";\n                    const animationTransition = {\n                        ...getValueTransition(defaultOptions, transitionName),\n                    };\n                    applyGeneratorOptions(animationTransition);\n                    const easing = mapEasingToNativeEasing(animationTransition.ease, animationTransition.duration);\n                    effect.updateTiming({\n                        delay: secondsToMilliseconds((_a = animationTransition.delay) !== null && _a !== void 0 ? _a : 0),\n                        duration: animationTransition.duration,\n                        easing,\n                    });\n                    animations.push(new NativeAnimationControls(animation));\n                }\n                else if (hasOpacity(targetDefinition, \"enter\") &&\n                    hasOpacity(targetDefinition, \"exit\") &&\n                    effect\n                        .getKeyframes()\n                        .some((keyframe) => keyframe.mixBlendMode)) {\n                    animations.push(new NativeAnimationControls(animation));\n                }\n                else {\n                    animation.cancel();\n                }\n            }\n            resolve(new BaseGroupPlaybackControls(animations));\n        });\n    });\n}\nfunction hasOpacity(target, key) {\n    var _a;\n    return (_a = target === null || target === void 0 ? void 0 : target[key]) === null || _a === void 0 ? void 0 : _a.keyframes.opacity;\n}\n\nexport { startViewAnimation };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAXA;;;;;;;;;;;;;AAaA,MAAM,kBAAkB;IAAC;IAAU;IAAS;IAAQ;IAAO;CAAM;AACjE,SAAS,mBAAmB,MAAM,EAAE,cAAc,EAAE,OAAO;IACvD,IAAI,CAAC,SAAS,mBAAmB,EAAE;QAC/B,OAAO,IAAI,QAAQ,OAAO;YACtB,MAAM;YACN,QAAQ,IAAI,kLAAA,CAAA,4BAAyB,CAAC,EAAE;QAC5C;IACJ;IACA,8DAA8D;IAC9D;;;KAGC,GACD,IAAI,CAAC,CAAA,GAAA,8KAAA,CAAA,YAAS,AAAD,EAAE,QAAQ,UAAU;QAC7B,oKAAA,CAAA,MAAG,CAAC,GAAG,CAAC,SAAS;YACb,wBAAwB;QAC5B;IACJ;IACA;;;;;;KAMC,GACD,oKAAA,CAAA,MAAG,CAAC,GAAG,CAAC,kFAAkF;QAAE,6BAA6B;IAAoB;IAC7I,oKAAA,CAAA,MAAG,CAAC,MAAM,IAAI,QAAQ;IACtB,MAAM,aAAa,SAAS,mBAAmB,CAAC;QAC5C,MAAM;IACN,yDAAyD;IAC7D;IACA,WAAW,QAAQ,CAAC,OAAO,CAAC;QACxB,oKAAA,CAAA,MAAG,CAAC,MAAM,IAAI,QAAQ;IAC1B;IACA,OAAO,IAAI,QAAQ,CAAC;QAChB,WAAW,KAAK,CAAC,IAAI,CAAC;YAClB,IAAI;YACJ,MAAM,0BAA0B,CAAA,GAAA,0LAAA,CAAA,oBAAiB,AAAD;YAChD,MAAM,aAAa,EAAE;YACrB;;aAEC,GACD,QAAQ,OAAO,CAAC,CAAC,YAAY;gBACzB,kDAAkD;gBAClD,wBAAwB;gBACxB,KAAK,MAAM,OAAO,gBAAiB;oBAC/B,IAAI,CAAC,UAAU,CAAC,IAAI,EAChB;oBACJ,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,UAAU,CAAC,IAAI;oBAC9C,KAAK,IAAI,CAAC,WAAW,eAAe,IAAI,OAAO,OAAO,CAAC,WAAY;wBAC/D,IAAI,CAAC,gBACD;wBACJ,MAAM,eAAe;4BACjB,GAAG,CAAA,GAAA,gMAAA,CAAA,qBAAkB,AAAD,EAAE,gBAAgB,UAAU;4BAChD,GAAG,CAAA,GAAA,gMAAA,CAAA,qBAAkB,AAAD,EAAE,SAAS,UAAU;wBAC7C;wBACA,MAAM,OAAO,CAAA,GAAA,wLAAA,CAAA,kBAAe,AAAD,EAAE;wBAC7B;;;yBAGC,GACD,IAAI,cAAc,aACd,CAAC,MAAM,OAAO,CAAC,iBAAiB;4BAChC,MAAM,eAAe,SAAS,QAAQ,IAAI;4BAC1C,iBAAiB;gCAAC;gCAAc;6BAAe;wBACnD;wBACA;;yBAEC,GACD,IAAI,OAAO,aAAa,KAAK,KAAK,YAAY;4BAC1C,aAAa,KAAK,GAAG,aAAa,KAAK,CAAC,GAAG;wBAC/C;wBACA,MAAM,YAAY,IAAI,qLAAA,CAAA,kBAAe,CAAC,SAAS,eAAe,EAAE,CAAC,kBAAkB,EAAE,KAAK,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE,WAAW,gBAAgB;wBACnI,WAAW,IAAI,CAAC;oBACpB;gBACJ;YACJ;YACA;;aAEC,GACD,KAAK,MAAM,aAAa,wBAAyB;gBAC7C,IAAI,UAAU,SAAS,KAAK,YACxB;gBACJ,MAAM,EAAE,MAAM,EAAE,GAAG;gBACnB,IAAI,CAAC,UAAU,CAAC,CAAC,kBAAkB,cAAc,GAC7C;gBACJ,MAAM,EAAE,aAAa,EAAE,GAAG;gBAC1B,IAAI,CAAC,eACD;gBACJ,MAAM,OAAO,CAAA,GAAA,qLAAA,CAAA,eAAY,AAAD,EAAE;gBAC1B,IAAI,CAAC,MACD;gBACJ,MAAM,mBAAmB,QAAQ,GAAG,CAAC,KAAK,KAAK;gBAC/C,IAAI,CAAC,kBAAkB;oBACnB;;;;qBAIC,GACD,MAAM,iBAAiB,KAAK,IAAI,KAAK,UAAU,WAAW;oBAC1D,MAAM,sBAAsB;wBACxB,GAAG,CAAA,GAAA,gMAAA,CAAA,qBAAkB,AAAD,EAAE,gBAAgB,eAAe;oBACzD;oBACA,CAAA,GAAA,iMAAA,CAAA,wBAAqB,AAAD,EAAE;oBACtB,MAAM,SAAS,CAAA,GAAA,qLAAA,CAAA,0BAAuB,AAAD,EAAE,oBAAoB,IAAI,EAAE,oBAAoB,QAAQ;oBAC7F,OAAO,YAAY,CAAC;wBAChB,OAAO,CAAA,GAAA,oKAAA,CAAA,wBAAqB,AAAD,EAAE,CAAC,KAAK,oBAAoB,KAAK,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;wBAC/F,UAAU,oBAAoB,QAAQ;wBACtC;oBACJ;oBACA,WAAW,IAAI,CAAC,IAAI,6LAAA,CAAA,0BAAuB,CAAC;gBAChD,OACK,IAAI,WAAW,kBAAkB,YAClC,WAAW,kBAAkB,WAC7B,OACK,YAAY,GACZ,IAAI,CAAC,CAAC,WAAa,SAAS,YAAY,GAAG;oBAChD,WAAW,IAAI,CAAC,IAAI,6LAAA,CAAA,0BAAuB,CAAC;gBAChD,OACK;oBACD,UAAU,MAAM;gBACpB;YACJ;YACA,QAAQ,IAAI,kLAAA,CAAA,4BAAyB,CAAC;QAC1C;IACJ;AACJ;AACA,SAAS,WAAW,MAAM,EAAE,GAAG;IAC3B,IAAI;IACJ,OAAO,CAAC,KAAK,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,MAAM,CAAC,IAAI,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,SAAS,CAAC,OAAO;AACvI", "ignoreList": [0]}}, {"offset": {"line": 1764, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1770, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/motion-dom/dist/es/view/index.mjs"], "sourcesContent": ["import { noop } from 'motion-utils';\nimport { startViewAnimation } from './start.mjs';\n\n/**\n * TODO:\n * - Create view transition on next tick\n * - Replace animations with Motion animations\n * - Return GroupAnimation on next tick\n */\nclass ViewTransitionBuilder {\n    constructor(update, options = {}) {\n        this.currentTarget = \"root\";\n        this.targets = new Map();\n        this.notifyReady = noop;\n        this.readyPromise = new Promise((resolve) => {\n            this.notifyReady = resolve;\n        });\n        queueMicrotask(() => {\n            startViewAnimation(update, options, this.targets).then((animation) => this.notifyReady(animation));\n        });\n    }\n    get(selector) {\n        this.currentTarget = selector;\n        return this;\n    }\n    layout(keyframes, options) {\n        this.updateTarget(\"layout\", keyframes, options);\n        return this;\n    }\n    new(keyframes, options) {\n        this.updateTarget(\"new\", keyframes, options);\n        return this;\n    }\n    old(keyframes, options) {\n        this.updateTarget(\"old\", keyframes, options);\n        return this;\n    }\n    enter(keyframes, options) {\n        this.updateTarget(\"enter\", keyframes, options);\n        return this;\n    }\n    exit(keyframes, options) {\n        this.updateTarget(\"exit\", keyframes, options);\n        return this;\n    }\n    crossfade(options) {\n        this.updateTarget(\"enter\", { opacity: 1 }, options);\n        this.updateTarget(\"exit\", { opacity: 0 }, options);\n        return this;\n    }\n    updateTarget(target, keyframes, options = {}) {\n        const { currentTarget, targets } = this;\n        if (!targets.has(currentTarget)) {\n            targets.set(currentTarget, {});\n        }\n        const targetData = targets.get(currentTarget);\n        targetData[target] = { keyframes, options };\n    }\n    then(resolve, reject) {\n        return this.readyPromise.then(resolve, reject);\n    }\n}\nfunction view(update, defaultOptions = {}) {\n    return new ViewTransitionBuilder(update, defaultOptions);\n}\n\nexport { ViewTransitionBuilder, view };\n"], "names": [], "mappings": ";;;;AAAA;AACA;AADA;;;AAGA;;;;;CAKC,GACD,MAAM;IACF,YAAY,MAAM,EAAE,UAAU,CAAC,CAAC,CAAE;QAC9B,IAAI,CAAC,aAAa,GAAG;QACrB,IAAI,CAAC,OAAO,GAAG,IAAI;QACnB,IAAI,CAAC,WAAW,GAAG,sJAAA,CAAA,OAAI;QACvB,IAAI,CAAC,YAAY,GAAG,IAAI,QAAQ,CAAC;YAC7B,IAAI,CAAC,WAAW,GAAG;QACvB;QACA,eAAe;YACX,CAAA,GAAA,6JAAA,CAAA,qBAAkB,AAAD,EAAE,QAAQ,SAAS,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,YAAc,IAAI,CAAC,WAAW,CAAC;QAC3F;IACJ;IACA,IAAI,QAAQ,EAAE;QACV,IAAI,CAAC,aAAa,GAAG;QACrB,OAAO,IAAI;IACf;IACA,OAAO,SAAS,EAAE,OAAO,EAAE;QACvB,IAAI,CAAC,YAAY,CAAC,UAAU,WAAW;QACvC,OAAO,IAAI;IACf;IACA,IAAI,SAAS,EAAE,OAAO,EAAE;QACpB,IAAI,CAAC,YAAY,CAAC,OAAO,WAAW;QACpC,OAAO,IAAI;IACf;IACA,IAAI,SAAS,EAAE,OAAO,EAAE;QACpB,IAAI,CAAC,YAAY,CAAC,OAAO,WAAW;QACpC,OAAO,IAAI;IACf;IACA,MAAM,SAAS,EAAE,OAAO,EAAE;QACtB,IAAI,CAAC,YAAY,CAAC,SAAS,WAAW;QACtC,OAAO,IAAI;IACf;IACA,KAAK,SAAS,EAAE,OAAO,EAAE;QACrB,IAAI,CAAC,YAAY,CAAC,QAAQ,WAAW;QACrC,OAAO,IAAI;IACf;IACA,UAAU,OAAO,EAAE;QACf,IAAI,CAAC,YAAY,CAAC,SAAS;YAAE,SAAS;QAAE,GAAG;QAC3C,IAAI,CAAC,YAAY,CAAC,QAAQ;YAAE,SAAS;QAAE,GAAG;QAC1C,OAAO,IAAI;IACf;IACA,aAAa,MAAM,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC,EAAE;QAC1C,MAAM,EAAE,aAAa,EAAE,OAAO,EAAE,GAAG,IAAI;QACvC,IAAI,CAAC,QAAQ,GAAG,CAAC,gBAAgB;YAC7B,QAAQ,GAAG,CAAC,eAAe,CAAC;QAChC;QACA,MAAM,aAAa,QAAQ,GAAG,CAAC;QAC/B,UAAU,CAAC,OAAO,GAAG;YAAE;YAAW;QAAQ;IAC9C;IACA,KAAK,OAAO,EAAE,MAAM,EAAE;QAClB,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS;IAC3C;AACJ;AACA,SAAS,KAAK,MAAM,EAAE,iBAAiB,CAAC,CAAC;IACrC,OAAO,IAAI,sBAAsB,QAAQ;AAC7C", "ignoreList": [0]}}, {"offset": {"line": 1848, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1854, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/motion-dom/dist/es/gestures/drag/state/set-active.mjs"], "sourcesContent": ["import { isDragging } from './is-active.mjs';\n\nfunction setDragLock(axis) {\n    if (axis === \"x\" || axis === \"y\") {\n        if (isDragging[axis]) {\n            return null;\n        }\n        else {\n            isDragging[axis] = true;\n            return () => {\n                isDragging[axis] = false;\n            };\n        }\n    }\n    else {\n        if (isDragging.x || isDragging.y) {\n            return null;\n        }\n        else {\n            isDragging.x = isDragging.y = true;\n            return () => {\n                isDragging.x = isDragging.y = false;\n            };\n        }\n    }\n}\n\nexport { setDragLock };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,SAAS,YAAY,IAAI;IACrB,IAAI,SAAS,OAAO,SAAS,KAAK;QAC9B,IAAI,yLAAA,CAAA,aAAU,CAAC,KAAK,EAAE;YAClB,OAAO;QACX,OACK;YACD,yLAAA,CAAA,aAAU,CAAC,KAAK,GAAG;YACnB,OAAO;gBACH,yLAAA,CAAA,aAAU,CAAC,KAAK,GAAG;YACvB;QACJ;IACJ,OACK;QACD,IAAI,yLAAA,CAAA,aAAU,CAAC,CAAC,IAAI,yLAAA,CAAA,aAAU,CAAC,CAAC,EAAE;YAC9B,OAAO;QACX,OACK;YACD,yLAAA,CAAA,aAAU,CAAC,CAAC,GAAG,yLAAA,CAAA,aAAU,CAAC,CAAC,GAAG;YAC9B,OAAO;gBACH,yLAAA,CAAA,aAAU,CAAC,CAAC,GAAG,yLAAA,CAAA,aAAU,CAAC,CAAC,GAAG;YAClC;QACJ;IACJ;AACJ", "ignoreList": [0]}}, {"offset": {"line": 1881, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1887, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 1909, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1944, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/%40vercel/analytics/src/react/index.tsx", "file:///home/<USER>/Desktop/templgen/node_modules/%40vercel/analytics/package.json", "file:///home/<USER>/Desktop/templgen/node_modules/%40vercel/analytics/src/queue.ts", "file:///home/<USER>/Desktop/templgen/node_modules/%40vercel/analytics/src/utils.ts", "file:///home/<USER>/Desktop/templgen/node_modules/%40vercel/analytics/src/generic.ts", "file:///home/<USER>/Desktop/templgen/node_modules/%40vercel/analytics/src/react/utils.ts"], "sourcesContent": ["'use client';\nimport { useEffect } from 'react';\nimport { inject, track, pageview } from '../generic';\nimport type { AnalyticsProps, BeforeSend, BeforeSendEvent } from '../types';\nimport { getBasePath } from './utils';\n\n/**\n * Injects the Vercel Web Analytics script into the page head and starts tracking page views. Read more in our [documentation](https://vercel.com/docs/concepts/analytics/package).\n * @param [props] - Analytics options.\n * @param [props.mode] - The mode to use for the analytics script. Defaults to `auto`.\n *  - `auto` - Automatically detect the environment.  Uses `production` if the environment cannot be determined.\n *  - `production` - Always use the production script. (Sends events to the server)\n *  - `development` - Always use the development script. (Logs events to the console)\n * @param [props.debug] - Whether to enable debug logging in development. Defaults to `true`.\n * @param [props.beforeSend] - A middleware function to modify events before they are sent. Should return the event object or `null` to cancel the event.\n * @example\n * ```js\n * import { Analytics } from '@vercel/analytics/react';\n *\n * export default function App() {\n *  return (\n *   <div>\n *    <Analytics />\n *    <h1>My App</h1>\n *  </div>\n * );\n * }\n * ```\n */\nfunction Analytics(\n  props: AnalyticsProps & {\n    framework?: string;\n    route?: string | null;\n    path?: string | null;\n    basePath?: string;\n  }\n): null {\n  useEffect(() => {\n    if (props.beforeSend) {\n      window.va?.('beforeSend', props.beforeSend);\n    }\n  }, [props.beforeSend]);\n\n  // biome-ignore lint/correctness/useExhaustiveDependencies: only run once\n  useEffect(() => {\n    inject({\n      framework: props.framework || 'react',\n      basePath: props.basePath ?? getBasePath(),\n      ...(props.route !== undefined && { disableAutoTrack: true }),\n      ...props,\n    });\n    // eslint-disable-next-line react-hooks/exhaustive-deps -- only run once\n  }, []);\n\n  useEffect(() => {\n    // explicitely track page view, since we disabled auto tracking\n    if (props.route && props.path) {\n      pageview({ route: props.route, path: props.path });\n    }\n  }, [props.route, props.path]);\n\n  return null;\n}\n\nexport { track, Analytics };\nexport type { AnalyticsProps, BeforeSend, BeforeSendEvent };\n", "{\n  \"name\": \"@vercel/analytics\",\n  \"version\": \"1.5.0\",\n  \"description\": \"Gain real-time traffic insights with Vercel Web Analytics\",\n  \"keywords\": [\n    \"analytics\",\n    \"vercel\"\n  ],\n  \"repository\": {\n    \"url\": \"github:vercel/analytics\",\n    \"directory\": \"packages/web\"\n  },\n  \"license\": \"MPL-2.0\",\n  \"exports\": {\n    \"./package.json\": \"./package.json\",\n    \".\": {\n      \"browser\": \"./dist/index.mjs\",\n      \"import\": \"./dist/index.mjs\",\n      \"require\": \"./dist/index.js\"\n    },\n    \"./astro\": {\n      \"import\": \"./dist/astro/component.ts\"\n    },\n    \"./next\": {\n      \"browser\": \"./dist/next/index.mjs\",\n      \"import\": \"./dist/next/index.mjs\",\n      \"require\": \"./dist/next/index.js\"\n    },\n    \"./nuxt\": {\n      \"browser\": \"./dist/nuxt/index.mjs\",\n      \"import\": \"./dist/nuxt/index.mjs\",\n      \"require\": \"./dist/nuxt/index.js\"\n    },\n    \"./react\": {\n      \"browser\": \"./dist/react/index.mjs\",\n      \"import\": \"./dist/react/index.mjs\",\n      \"require\": \"./dist/react/index.js\"\n    },\n    \"./remix\": {\n      \"browser\": \"./dist/remix/index.mjs\",\n      \"import\": \"./dist/remix/index.mjs\",\n      \"require\": \"./dist/remix/index.js\"\n    },\n    \"./server\": {\n      \"node\": \"./dist/server/index.mjs\",\n      \"edge-light\": \"./dist/server/index.mjs\",\n      \"import\": \"./dist/server/index.mjs\",\n      \"require\": \"./dist/server/index.js\",\n      \"default\": \"./dist/server/index.js\"\n    },\n    \"./sveltekit\": {\n      \"svelte\": \"./dist/sveltekit/index.mjs\",\n      \"types\": \"./dist/sveltekit/index.d.ts\"\n    },\n    \"./vue\": {\n      \"browser\": \"./dist/vue/index.mjs\",\n      \"import\": \"./dist/vue/index.mjs\",\n      \"require\": \"./dist/vue/index.js\"\n    }\n  },\n  \"main\": \"./dist/index.mjs\",\n  \"types\": \"./dist/index.d.ts\",\n  \"typesVersions\": {\n    \"*\": {\n      \"*\": [\n        \"dist/index.d.ts\"\n      ],\n      \"next\": [\n        \"dist/next/index.d.ts\"\n      ],\n      \"nuxt\": [\n        \"dist/nuxt/index.d.ts\"\n      ],\n      \"react\": [\n        \"dist/react/index.d.ts\"\n      ],\n      \"remix\": [\n        \"dist/remix/index.d.ts\"\n      ],\n      \"server\": [\n        \"dist/server/index.d.ts\"\n      ],\n      \"sveltekit\": [\n        \"dist/sveltekit/index.d.ts\"\n      ],\n      \"vue\": [\n        \"dist/vue/index.d.ts\"\n      ]\n    }\n  },\n  \"scripts\": {\n    \"build\": \"tsup && pnpm copy-astro\",\n    \"copy-astro\": \"cp -R src/astro dist/\",\n    \"dev\": \"pnpm copy-astro && tsup --watch\",\n    \"lint\": \"eslint .\",\n    \"lint-fix\": \"eslint . --fix\",\n    \"test\": \"vitest\",\n    \"type-check\": \"tsc --noEmit\"\n  },\n  \"eslintConfig\": {\n    \"extends\": [\n      \"@vercel/eslint-config\"\n    ],\n    \"rules\": {\n      \"tsdoc/syntax\": \"off\"\n    },\n    \"ignorePatterns\": [\n      \"jest.setup.ts\"\n    ]\n  },\n  \"devDependencies\": {\n    \"@swc/core\": \"^1.9.2\",\n    \"@testing-library/jest-dom\": \"^6.6.3\",\n    \"@testing-library/react\": \"^16.0.1\",\n    \"@types/node\": \"^22.9.0\",\n    \"@types/react\": \"^18.3.12\",\n    \"@vercel/eslint-config\": \"workspace:0.0.0\",\n    \"server-only\": \"^0.0.1\",\n    \"svelte\": \"^5.1.10\",\n    \"tsup\": \"8.3.5\",\n    \"vitest\": \"^2.1.5\",\n    \"vue\": \"^3.5.12\",\n    \"vue-router\": \"^4.4.5\"\n  },\n  \"peerDependencies\": {\n    \"@remix-run/react\": \"^2\",\n    \"@sveltejs/kit\": \"^1 || ^2\",\n    \"next\": \">= 13\",\n    \"react\": \"^18 || ^19 || ^19.0.0-rc\",\n    \"svelte\": \">= 4\",\n    \"vue\": \"^3\",\n    \"vue-router\": \"^4\"\n  },\n  \"peerDependenciesMeta\": {\n    \"@remix-run/react\": {\n      \"optional\": true\n    },\n    \"@sveltejs/kit\": {\n      \"optional\": true\n    },\n    \"next\": {\n      \"optional\": true\n    },\n    \"react\": {\n      \"optional\": true\n    },\n    \"svelte\": {\n      \"optional\": true\n    },\n    \"vue\": {\n      \"optional\": true\n    },\n    \"vue-router\": {\n      \"optional\": true\n    }\n  }\n}\n", "export const initQueue = (): void => {\n  // initialize va until script is loaded\n  if (window.va) return;\n\n  window.va = function a(...params): void {\n    (window.vaq = window.vaq || []).push(params);\n  };\n};\n", "import type { AllowedPropertyValues, AnalyticsProps, Mode } from './types';\n\nexport function isBrowser(): boolean {\n  return typeof window !== 'undefined';\n}\n\nfunction detectEnvironment(): 'development' | 'production' {\n  try {\n    const env = process.env.NODE_ENV;\n    if (env === 'development' || env === 'test') {\n      return 'development';\n    }\n  } catch (e) {\n    // do nothing, this is okay\n  }\n  return 'production';\n}\n\nexport function setMode(mode: Mode = 'auto'): void {\n  if (mode === 'auto') {\n    window.vam = detectEnvironment();\n    return;\n  }\n\n  window.vam = mode;\n}\n\nexport function getMode(): Mode {\n  const mode = isBrowser() ? window.vam : detectEnvironment();\n  return mode || 'production';\n}\n\nexport function isProduction(): boolean {\n  return getMode() === 'production';\n}\n\nexport function isDevelopment(): boolean {\n  return getMode() === 'development';\n}\n\nfunction removeKey(\n  key: string,\n  { [key]: _, ...rest }\n): Record<string, unknown> {\n  return rest;\n}\n\nexport function parseProperties(\n  properties: Record<string, unknown> | undefined,\n  options: {\n    strip?: boolean;\n  }\n): Error | Record<string, AllowedPropertyValues> | undefined {\n  if (!properties) return undefined;\n  let props = properties;\n  const errorProperties: string[] = [];\n  for (const [key, value] of Object.entries(properties)) {\n    if (typeof value === 'object' && value !== null) {\n      if (options.strip) {\n        props = removeKey(key, props);\n      } else {\n        errorProperties.push(key);\n      }\n    }\n  }\n\n  if (errorProperties.length > 0 && !options.strip) {\n    throw Error(\n      `The following properties are not valid: ${errorProperties.join(\n        ', '\n      )}. Only strings, numbers, booleans, and null are allowed.`\n    );\n  }\n  return props as Record<string, AllowedPropertyValues>;\n}\n\nexport function computeRoute(\n  pathname: string | null,\n  pathParams: Record<string, string | string[]> | null\n): string | null {\n  if (!pathname || !pathParams) {\n    return pathname;\n  }\n\n  let result = pathname;\n  try {\n    const entries = Object.entries(pathParams);\n    // simple keys must be handled first\n    for (const [key, value] of entries) {\n      if (!Array.isArray(value)) {\n        const matcher = turnValueToRegExp(value);\n        if (matcher.test(result)) {\n          result = result.replace(matcher, `/[${key}]`);\n        }\n      }\n    }\n    // array values next\n    for (const [key, value] of entries) {\n      if (Array.isArray(value)) {\n        const matcher = turnValueToRegExp(value.join('/'));\n        if (matcher.test(result)) {\n          result = result.replace(matcher, `/[...${key}]`);\n        }\n      }\n    }\n    return result;\n  } catch (e) {\n    return pathname;\n  }\n}\n\nfunction turnValueToRegExp(value: string): RegExp {\n  return new RegExp(`/${escapeRegExp(value)}(?=[/?#]|$)`);\n}\n\nfunction escapeRegExp(string: string): string {\n  return string.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&');\n}\n\nexport function getScriptSrc(\n  props: AnalyticsProps & { basePath?: string }\n): string {\n  if (props.scriptSrc) {\n    return props.scriptSrc;\n  }\n  if (isDevelopment()) {\n    return 'https://va.vercel-scripts.com/v1/script.debug.js';\n  }\n  if (props.basePath) {\n    return `${props.basePath}/insights/script.js`;\n  }\n  return '/_vercel/insights/script.js';\n}\n", "import { name as packageName, version } from '../package.json';\nimport { initQueue } from './queue';\nimport type {\n  AllowedPropertyValues,\n  AnalyticsProps,\n  FlagsDataInput,\n  BeforeSend,\n  BeforeSendEvent,\n} from './types';\nimport {\n  isBrowser,\n  parseProperties,\n  setMode,\n  isDevelopment,\n  isProduction,\n  computeRoute,\n  getScriptSrc,\n} from './utils';\n\n/**\n * Injects the Vercel Web Analytics script into the page head and starts tracking page views. Read more in our [documentation](https://vercel.com/docs/concepts/analytics/package).\n * @param [props] - Analytics options.\n * @param [props.mode] - The mode to use for the analytics script. Defaults to `auto`.\n *  - `auto` - Automatically detect the environment.  Uses `production` if the environment cannot be determined.\n *  - `production` - Always use the production script. (Sends events to the server)\n *  - `development` - Always use the development script. (Logs events to the console)\n * @param [props.debug] - Whether to enable debug logging in development. Defaults to `true`.\n * @param [props.beforeSend] - A middleware function to modify events before they are sent. Should return the event object or `null` to cancel the event.\n * @param [props.dsn] - The DSN of the project to send events to. Only required when self-hosting.\n * @param [props.disableAutoTrack] - Whether the injected script should track page views from pushState events. Disable if route is updated after pushState, a manually call page pageview().\n */\nfunction inject(\n  props: AnalyticsProps & {\n    framework?: string;\n    disableAutoTrack?: boolean;\n    basePath?: string;\n  } = {\n    debug: true,\n  }\n): void {\n  if (!isBrowser()) return;\n\n  setMode(props.mode);\n\n  initQueue();\n\n  if (props.beforeSend) {\n    window.va?.('beforeSend', props.beforeSend);\n  }\n\n  const src = getScriptSrc(props);\n\n  if (document.head.querySelector(`script[src*=\"${src}\"]`)) return;\n\n  const script = document.createElement('script');\n  script.src = src;\n  script.defer = true;\n  script.dataset.sdkn =\n    packageName + (props.framework ? `/${props.framework}` : '');\n  script.dataset.sdkv = version;\n\n  if (props.disableAutoTrack) {\n    script.dataset.disableAutoTrack = '1';\n  }\n  if (props.endpoint) {\n    script.dataset.endpoint = props.endpoint;\n  } else if (props.basePath) {\n    script.dataset.endpoint = `${props.basePath}/insights`;\n  }\n  if (props.dsn) {\n    script.dataset.dsn = props.dsn;\n  }\n\n  script.onerror = (): void => {\n    const errorMessage = isDevelopment()\n      ? 'Please check if any ad blockers are enabled and try again.'\n      : 'Be sure to enable Web Analytics for your project and deploy again. See https://vercel.com/docs/analytics/quickstart for more information.';\n\n    // eslint-disable-next-line no-console -- Logging to console is intentional\n    console.log(\n      `[Vercel Web Analytics] Failed to load script from ${src}. ${errorMessage}`\n    );\n  };\n\n  if (isDevelopment() && props.debug === false) {\n    script.dataset.debug = 'false';\n  }\n\n  document.head.appendChild(script);\n}\n\n/**\n * Tracks a custom event. Please refer to the [documentation](https://vercel.com/docs/concepts/analytics/custom-events) for more information on custom events.\n * @param name - The name of the event.\n * * Examples: `Purchase`, `Click Button`, or `Play Video`.\n * @param [properties] - Additional properties of the event. Nested objects are not supported. Allowed values are `string`, `number`, `boolean`, and `null`.\n */\nfunction track(\n  name: string,\n  properties?: Record<string, AllowedPropertyValues>,\n  options?: {\n    flags?: FlagsDataInput;\n  }\n): void {\n  if (!isBrowser()) {\n    const msg =\n      '[Vercel Web Analytics] Please import `track` from `@vercel/analytics/server` when using this function in a server environment';\n\n    if (isProduction()) {\n      // eslint-disable-next-line no-console -- Show warning in production\n      console.warn(msg);\n    } else {\n      throw new Error(msg);\n    }\n\n    return;\n  }\n\n  if (!properties) {\n    window.va?.('event', { name, options });\n    return;\n  }\n\n  try {\n    const props = parseProperties(properties, {\n      strip: isProduction(),\n    });\n\n    window.va?.('event', {\n      name,\n      data: props,\n      options,\n    });\n  } catch (err) {\n    if (err instanceof Error && isDevelopment()) {\n      // eslint-disable-next-line no-console -- Logging to console is intentional\n      console.error(err);\n    }\n  }\n}\n\nfunction pageview({\n  route,\n  path,\n}: {\n  route?: string | null;\n  path?: string;\n}): void {\n  window.va?.('pageview', { route, path });\n}\n\nexport { inject, track, pageview, computeRoute };\nexport type { AnalyticsProps, BeforeSend, BeforeSendEvent };\n\n// eslint-disable-next-line import/no-default-export -- Default export is intentional\nexport default {\n  inject,\n  track,\n  computeRoute,\n};\n", "export function getBasePath(): string | undefined {\n  // !! important !!\n  // do not access env variables using process.env[varname]\n  // some bundles won't replace the value at build time.\n  // eslint-disable-next-line @typescript-eslint/prefer-optional-chain -- we can't use optionnal here, it'll break if process does not exist.\n  if (typeof process === 'undefined' || typeof process.env === 'undefined') {\n    return undefined;\n  }\n  return process.env.REACT_APP_VERCEL_OBSERVABILITY_BASEPATH;\n}\n"], "names": ["name"], "mappings": ";;;;;AACA,SAAS,iBAAiB;;;;ACAxB,IAAA,OAAQ;AACR,IAAA,UAAW;;ACFN,IAAM,YAAY,MAAY;IAEnC,IAAI,OAAO,EAAA,CAAI,CAAA;IAEf,OAAO,EAAA,GAAK,SAAS,EAAA,GAAK,MAAA,EAAc;QACtC,CAAC,OAAO,GAAA,GAAM,OAAO,GAAA,IAAO,CAAC,CAAA,EAAG,IAAA,CAAK,MAAM;IAC7C;AACF;;ACLO,SAAS,YAAqB;IACnC,OAAO,OAAO,WAAW;AAC3B;AAEA,SAAS,oBAAkD;IACzD,IAAI;QACF,MAAM,MAAM,QAAQ,IAAI;QACxB,IAAI,QAAQ,iBAAiB,QAAQ,GAAQ;YAC3C,OAAO;QACT;IACF,EAAA,OAAS,GAAG,CAEZ;IACA,OAAO;AACT;AAEO,SAAS,QAAQ,OAAa,MAAA,EAAc;IACjD,IAAI,SAAS,QAAQ;QACnB,OAAO,GAAA,GAAM,kBAAkB;QAC/B;IACF;IAEA,OAAO,GAAA,GAAM;AACf;AAEO,SAAS,UAAgB;IAC9B,MAAM,OAAO,UAAU,IAAI,OAAO,GAAA,GAAM,kBAAkB;IAC1D,OAAO,QAAQ;AACjB;AAEO,SAAS,eAAwB;IACtC,OAAO,QAAQ,MAAM;AACvB;AAEO,SAAS,gBAAyB;IACvC,OAAO,QAAQ,MAAM;AACvB;AAEA,SAAS,UACP,GAAA,EACA,EAAE,CAAC,GAAG,CAAA,EAAG,CAAA,EAAG,GAAG,KAAK,CAAA,EACK;IACzB,OAAO;AACT;AAEO,SAAS,gBACd,UAAA,EACA,OAAA,EAG2D;IAC3D,IAAI,CAAC,WAAY,CAAA,OAAO,KAAA;IACxB,IAAI,QAAQ;IACZ,MAAM,kBAA4B,CAAC,CAAA;IACnC,KAAA,MAAW,CAAC,KAAK,KAAK,CAAA,IAAK,OAAO,OAAA,CAAQ,UAAU,EAAG;QACrD,IAAI,OAAO,UAAU,YAAY,UAAU,MAAM;YAC/C,IAAI,QAAQ,KAAA,EAAO;gBACjB,QAAQ,UAAU,KAAK,KAAK;YAC9B,OAAO;gBACL,gBAAgB,IAAA,CAAK,GAAG;YAC1B;QACF;IACF;IAEA,IAAI,gBAAgB,MAAA,GAAS,KAAK,CAAC,QAAQ,KAAA,EAAO;QAChD,MAAM,MACJ,CAAA,wCAAA,EAA2C,gBAAgB,IAAA,CACzD,MACD,wDAAA,CAAA;IAEL;IACA,OAAO;AACT;AA6CO,SAAS,aACd,KAAA,EACQ;IACR,IAAI,MAAM,SAAA,EAAW;QACnB,OAAO,MAAM,SAAA;IACf;IACA,IAAI,cAAc,GAAG;QACnB,OAAO;IACT;IACA,IAAI,MAAM,QAAA,EAAU;QAClB,OAAO,GAAG,MAAM,QAAQ,CAAA,mBAAA,CAAA;IAC1B;IACA,OAAO;AACT;;ACrGA,SAAS,OACP,QAII;IACF,OAAO;AACT,CAAA,EACM;IAvCR,IAAA;IAwCE,IAAI,CAAC,UAAU,EAAG,CAAA;IAElB,QAAQ,MAAM,IAAI;IAElB,UAAU;IAEV,IAAI,MAAM,UAAA,EAAY;QACpB,CAAA,KAAA,OAAO,EAAA,KAAP,OAAA,KAAA,IAAA,GAAA,IAAA,CAAA,QAAY,cAAc,MAAM,UAAA;IAClC;IAEA,MAAM,MAAM,aAAa,KAAK;IAE9B,IAAI,SAAS,IAAA,CAAK,aAAA,CAAc,CAAA,aAAA,EAAgB,GAAG,CAAA,EAAA,CAAI,EAAG,CAAA;IAE1D,MAAM,SAAS,SAAS,aAAA,CAAc,QAAQ;IAC9C,OAAO,GAAA,GAAM;IACb,OAAO,KAAA,GAAQ;IACf,OAAO,OAAA,CAAQ,IAAA,GACb,OAAA,CAAe,MAAM,SAAA,GAAY,CAAA,CAAA,EAAI,MAAM,SAAS,EAAA,GAAK,EAAA;IAC3D,OAAO,OAAA,CAAQ,IAAA,GAAO;IAEtB,IAAI,MAAM,gBAAA,EAAkB;QAC1B,OAAO,OAAA,CAAQ,gBAAA,GAAmB;IACpC;IACA,IAAI,MAAM,QAAA,EAAU;QAClB,OAAO,OAAA,CAAQ,QAAA,GAAW,MAAM,QAAA;IAClC,OAAA,IAAW,MAAM,QAAA,EAAU;QACzB,OAAO,OAAA,CAAQ,QAAA,GAAW,GAAG,MAAM,QAAQ,CAAA,SAAA,CAAA;IAC7C;IACA,IAAI,MAAM,GAAA,EAAK;QACb,OAAO,OAAA,CAAQ,GAAA,GAAM,MAAM,GAAA;IAC7B;IAEA,OAAO,OAAA,GAAU,MAAY;QAC3B,MAAM,eAAe,cAAc,IAC/B,+DACA;QAGJ,QAAQ,GAAA,CACN,CAAA,kDAAA,EAAqD,GAAG,CAAA,EAAA,EAAK,YAAY,EAAA;IAE7E;IAEA,IAAI,cAAc,KAAK,MAAM,KAAA,KAAU,OAAO;QAC5C,OAAO,OAAA,CAAQ,KAAA,GAAQ;IACzB;IAEA,SAAS,IAAA,CAAK,WAAA,CAAY,MAAM;AAClC;AAQA,SAAS,MACPA,KAAAA,EACA,UAAA,EACA,OAAA,EAGM;IAvGR,IAAA,IAAA;IAwGE,IAAI,CAAC,UAAU,GAAG;QAChB,MAAM,MACJ;QAEF,IAAI,aAAa,GAAG;YAElB,QAAQ,IAAA,CAAK,GAAG;QAClB,OAAO;YACL,MAAM,IAAI,MAAM,GAAG;QACrB;QAEA;IACF;IAEA,IAAI,CAAC,YAAY;QACf,CAAA,KAAA,OAAO,EAAA,KAAP,OAAA,KAAA,IAAA,GAAA,IAAA,CAAA,QAAY,SAAS;YAAE,MAAAA;YAAM;QAAQ;QACrC;IACF;IAEA,IAAI;QACF,MAAM,QAAQ,gBAAgB,YAAY;YACxC,OAAO,aAAa;QACtB,CAAC;QAED,CAAA,KAAA,OAAO,EAAA,KAAP,OAAA,KAAA,IAAA,GAAA,IAAA,CAAA,QAAY,SAAS;YACnB,MAAAA;YACA,MAAM;YACN;QACF;IACF,EAAA,OAAS,KAAK;QACZ,IAAI,eAAe,SAAS,cAAc,GAAG;YAE3C,QAAQ,KAAA,CAAM,GAAG;QACnB;IACF;AACF;AAEA,SAAS,SAAS,EAChB,KAAA,EACA,IAAA,EACF,EAGS;IAnJT,IAAA;IAoJE,CAAA,KAAA,OAAO,EAAA,KAAP,OAAA,KAAA,IAAA,GAAA,IAAA,CAAA,QAAY,YAAY;QAAE;QAAO;IAAK;AACxC;;ACrJO,SAAS,cAAkC;IAKhD,IAAI,OAAO,YAAY,eAAe,OAAO,QAAQ,GAAA,KAAQ,aAAa;QACxE,OAAO,KAAA;IACT;IACA,OAAO,QAAQ,GAAA,CAAI,uCAAA;AACrB;;ALoBA,SAAS,UACP,KAAA,EAMM;IACN,CAAA,GAAA,qMAAA,CAAA,YAAA,EAAU,MAAM;QArClB,IAAA;QAsCI,IAAI,MAAM,UAAA,EAAY;YACpB,CAAA,KAAA,OAAO,EAAA,KAAP,OAAA,KAAA,IAAA,GAAA,IAAA,CAAA,QAAY,cAAc,MAAM,UAAA;QAClC;IACF,GAAG;QAAC,MAAM,UAAU;KAAC;IAGrB,CAAA,GAAA,qMAAA,CAAA,YAAA,EAAU,MAAM;QACd,OAAO;YACL,WAAW,MAAM,SAAA,IAAa;YAC9B,UAAU,MAAM,QAAA,IAAY,YAAY;YACxC,GAAI,MAAM,KAAA,KAAU,KAAA,KAAa;gBAAE,kBAAkB;YAAK,CAAA;YAC1D,GAAG,KAAA;QACL,CAAC;IAEH,GAAG,CAAC,CAAC;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAA,EAAU,MAAM;QAEd,IAAI,MAAM,KAAA,IAAS,MAAM,IAAA,EAAM;YAC7B,SAAS;gBAAE,OAAO,MAAM,KAAA;gBAAO,MAAM,MAAM,IAAA;YAAK,CAAC;QACnD;IACF,GAAG;QAAC,MAAM,KAAA;QAAO,MAAM,IAAI;KAAC;IAE5B,OAAO;AACT", "ignoreList": [0, 1, 2, 3, 4, 5]}}, {"offset": {"line": 2145, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2151, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/%40vercel/speed-insights/src/nextjs/index.tsx", "file:///home/<USER>/Desktop/templgen/node_modules/%40vercel/speed-insights/src/react/index.tsx", "file:///home/<USER>/Desktop/templgen/node_modules/%40vercel/speed-insights/package.json", "file:///home/<USER>/Desktop/templgen/node_modules/%40vercel/speed-insights/src/queue.ts", "file:///home/<USER>/Desktop/templgen/node_modules/%40vercel/speed-insights/src/utils.ts", "file:///home/<USER>/Desktop/templgen/node_modules/%40vercel/speed-insights/src/generic.ts", "file:///home/<USER>/Desktop/templgen/node_modules/%40vercel/speed-insights/src/react/utils.ts", "file:///home/<USER>/Desktop/templgen/node_modules/%40vercel/speed-insights/src/nextjs/utils.ts"], "sourcesContent": ["'use client';\n\nimport React, { Suspense } from 'react';\nimport { SpeedInsights as SpeedInsightsScript } from '../react';\nimport type { SpeedInsightsProps } from '../types';\nimport { getBasePath, useRoute } from './utils';\n\ntype Props = Omit<SpeedInsightsProps, 'route'>;\n\nfunction SpeedInsightsComponent(props: Props): React.ReactElement {\n  const route = useRoute();\n\n  return (\n    <SpeedInsightsScript\n      route={route}\n      {...props}\n      framework=\"next\"\n      basePath={getBasePath()}\n    />\n  );\n}\n\nexport function SpeedInsights(props: Props): null {\n  // Because of incompatible types between ReactNode in React 19 and React 18 we return null (which is also what we render)\n  return (\n    <Suspense fallback={null}>\n      <SpeedInsightsComponent {...props} />\n    </Suspense>\n  ) as never;\n}\n", "'use client';\n\nimport { useEffect, useRef } from 'react';\nimport type { SpeedInsightsProps } from '../types';\nimport { computeRoute, injectSpeedInsights } from '../generic';\nimport { getBasePath } from './utils';\n\nexport function SpeedInsights(\n  props: SpeedInsightsProps & {\n    framework?: string;\n    basePath?: string;\n  },\n): JSX.Element | null {\n  useEffect(() => {\n    if (props.beforeSend) {\n      window.si?.('beforeSend', props.beforeSend);\n    }\n  }, [props.beforeSend]);\n\n  const setScriptRoute = useRef<((path: string) => void) | null>(null);\n  useEffect(() => {\n    if (!setScriptRoute.current) {\n      const script = injectSpeedInsights({\n        framework: props.framework ?? 'react',\n        basePath: props.basePath ?? getBasePath(),\n        ...props,\n      });\n      if (script) {\n        setScriptRoute.current = script.setRoute;\n      }\n    } else if (props.route) {\n      setScriptRoute.current(props.route);\n    }\n  }, [props.route]);\n\n  return null;\n}\n\nexport { computeRoute };\n", "{\n  \"name\": \"@vercel/speed-insights\",\n  \"version\": \"1.2.0\",\n  \"description\": \"Speed Insights is a tool for measuring web performance and providing suggestions for improvement.\",\n  \"keywords\": [\n    \"speed-insights\",\n    \"vercel\"\n  ],\n  \"repository\": {\n    \"url\": \"github:vercel/speed-insights\",\n    \"directory\": \"packages/web\"\n  },\n  \"license\": \"Apache-2.0\",\n  \"exports\": {\n    \"./package.json\": \"./package.json\",\n    \".\": {\n      \"browser\": \"./dist/index.mjs\",\n      \"import\": \"./dist/index.mjs\",\n      \"require\": \"./dist/index.js\"\n    },\n    \"./astro\": {\n      \"import\": \"./dist/astro/component.ts\"\n    },\n    \"./next\": {\n      \"browser\": \"./dist/next/index.mjs\",\n      \"import\": \"./dist/next/index.mjs\",\n      \"require\": \"./dist/next/index.js\"\n    },\n    \"./nuxt\": {\n      \"browser\": \"./dist/nuxt/index.mjs\",\n      \"import\": \"./dist/nuxt/index.mjs\",\n      \"require\": \"./dist/nuxt/index.js\"\n    },\n    \"./react\": {\n      \"browser\": \"./dist/react/index.mjs\",\n      \"import\": \"./dist/react/index.mjs\",\n      \"require\": \"./dist/react/index.js\"\n    },\n    \"./remix\": {\n      \"browser\": \"./dist/remix/index.mjs\",\n      \"import\": \"./dist/remix/index.mjs\",\n      \"require\": \"./dist/remix/index.js\"\n    },\n    \"./sveltekit\": {\n      \"svelte\": \"./dist/sveltekit/index.mjs\",\n      \"types\": \"./dist/sveltekit/index.d.ts\"\n    },\n    \"./vue\": {\n      \"browser\": \"./dist/vue/index.mjs\",\n      \"import\": \"./dist/vue/index.mjs\",\n      \"require\": \"./dist/vue/index.js\"\n    }\n  },\n  \"main\": \"./dist/index.js\",\n  \"types\": \"./dist/index.d.ts\",\n  \"typesVersions\": {\n    \"*\": {\n      \"*\": [\n        \"dist/index.d.ts\"\n      ],\n      \"react\": [\n        \"dist/react/index.d.ts\"\n      ],\n      \"next\": [\n        \"dist/next/index.d.ts\"\n      ],\n      \"nuxt\": [\n        \"dist/nuxt/index.d.ts\"\n      ],\n      \"remix\": [\n        \"dist/remix/index.d.ts\"\n      ],\n      \"sveltekit\": [\n        \"dist/sveltekit/index.d.ts\"\n      ],\n      \"vue\": [\n        \"dist/vue/index.d.ts\"\n      ]\n    }\n  },\n  \"scripts\": {\n    \"build\": \"tsup && pnpm copy-astro\",\n    \"copy-astro\": \"cp -R src/astro dist/\",\n    \"dev\": \"pnpm copy-astro && tsup --watch\",\n    \"postinstall\": \"node scripts/postinstall.mjs\",\n    \"lint\": \"eslint .\",\n    \"lint-fix\": \"eslint . --fix\",\n    \"test\": \"vitest\",\n    \"type-check\": \"tsc --noEmit\"\n  },\n  \"devDependencies\": {\n    \"@remix-run/react\": \"^2.14.0\",\n    \"@sveltejs/kit\": \"^2.8.1\",\n    \"@swc/core\": \"^1.9.2\",\n    \"@testing-library/jest-dom\": \"^6.6.3\",\n    \"@testing-library/react\": \"^16.0.1\",\n    \"@types/node\": \"^22.9.1\",\n    \"@types/react\": \"^18.3.12\",\n    \"copyfiles\": \"^2.4.1\",\n    \"jsdom\": \"^25.0.1\",\n    \"next\": \"^14.0.4\",\n    \"react\": \"^18.3.1\",\n    \"react-dom\": \"^18.3.1\",\n    \"svelte\": \"^5.2.7\",\n    \"tsup\": \"8.3.5\",\n    \"vitest\": \"^2.1.5\",\n    \"vue\": \"^3.5.13\",\n    \"vue-router\": \"^4.4.5\"\n  },\n  \"peerDependencies\": {\n    \"@sveltejs/kit\": \"^1 || ^2\",\n    \"next\": \">= 13\",\n    \"react\": \"^18 || ^19 || ^19.0.0-rc\",\n    \"svelte\": \">= 4\",\n    \"vue\": \"^3\",\n    \"vue-router\": \"^4\"\n  },\n  \"peerDependenciesMeta\": {\n    \"@sveltejs/kit\": {\n      \"optional\": true\n    },\n    \"next\": {\n      \"optional\": true\n    },\n    \"react\": {\n      \"optional\": true\n    },\n    \"svelte\": {\n      \"optional\": true\n    },\n    \"vue\": {\n      \"optional\": true\n    },\n    \"vue-router\": {\n      \"optional\": true\n    }\n  }\n}\n", "export const initQueue = (): void => {\n  // initialize va until script is loaded\n  if (window.si) return;\n\n  window.si = function a(...params): void {\n    (window.siq = window.siq || []).push(params);\n  };\n};\n", "import type { SpeedInsightsProps } from './types';\n\nexport function isBrowser(): boolean {\n  return typeof window !== 'undefined';\n}\n\nfunction detectEnvironment(): 'development' | 'production' {\n  try {\n    const env = process.env.NODE_ENV;\n    if (env === 'development' || env === 'test') {\n      return 'development';\n    }\n  } catch (e) {\n    // do nothing, this is okay\n  }\n  return 'production';\n}\n\nexport function isProduction(): boolean {\n  return detectEnvironment() === 'production';\n}\n\nexport function isDevelopment(): boolean {\n  return detectEnvironment() === 'development';\n}\n\nexport function computeRoute(\n  pathname: string | null,\n  pathParams: Record<string, string | string[]> | null,\n): string | null {\n  if (!pathname || !pathParams) {\n    return pathname;\n  }\n\n  let result = pathname;\n  try {\n    const entries = Object.entries(pathParams);\n    // simple keys must be handled first\n    for (const [key, value] of entries) {\n      if (!Array.isArray(value)) {\n        const matcher = turnValueToRegExp(value);\n        if (matcher.test(result)) {\n          result = result.replace(matcher, `/[${key}]`);\n        }\n      }\n    }\n    // array values next\n    for (const [key, value] of entries) {\n      if (Array.isArray(value)) {\n        const matcher = turnValueToRegExp(value.join('/'));\n        if (matcher.test(result)) {\n          result = result.replace(matcher, `/[...${key}]`);\n        }\n      }\n    }\n    return result;\n  } catch (e) {\n    return pathname;\n  }\n}\n\nfunction turnValueToRegExp(value: string): RegExp {\n  return new RegExp(`/${escapeRegExp(value)}(?=[/?#]|$)`);\n}\n\nfunction escapeRegExp(string: string): string {\n  return string.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&');\n}\n\nexport function getScriptSrc(\n  props: SpeedInsightsProps & { basePath?: string },\n): string {\n  if (props.scriptSrc) {\n    return props.scriptSrc;\n  }\n  if (isDevelopment()) {\n    return 'https://va.vercel-scripts.com/v1/speed-insights/script.debug.js';\n  }\n  if (props.dsn) {\n    return 'https://va.vercel-scripts.com/v1/speed-insights/script.js';\n  }\n  if (props.basePath) {\n    return `${props.basePath}/speed-insights/script.js`;\n  }\n  return '/_vercel/speed-insights/script.js';\n}\n", "import { name as packageName, version } from '../package.json';\nimport { initQueue } from './queue';\nimport type { SpeedInsightsProps } from './types';\nimport { computeRoute, getScriptSrc, isBrowser, isDevelopment } from './utils';\n\n/**\n * Injects the Vercel Speed Insights script into the page head and starts tracking page views. Read more in our [documentation](https://vercel.com/docs/speed-insights).\n * @param [props] - Speed Insights options.\n * @param [props.debug] - Whether to enable debug logging in development. Defaults to `true`.\n * @param [props.beforeSend] - A middleware function to modify events before they are sent. Should return the event object or `null` to cancel the event.\n * @param [props.sampleRate] - When setting to 0.5, 50% of the events will be sent to Vercel Speed Insights. Defaults to `1`.\n * @param [props.route] - The dynamic route of the page.\n * @param [props.dsn] - The DSN of the project to send events to. Only required when self-hosting.\n */\nfunction injectSpeedInsights(\n  props: SpeedInsightsProps & {\n    framework?: string;\n    basePath?: string;\n  } = {},\n): {\n  setRoute: (route: string | null) => void;\n} | null {\n  // When route is null, it means that pages router is not ready yet. Will resolve soon\n  if (!isBrowser() || props.route === null) return null;\n\n  initQueue();\n\n  const src = getScriptSrc(props);\n\n  if (document.head.querySelector(`script[src*=\"${src}\"]`)) return null;\n\n  if (props.beforeSend) {\n    window.si?.('beforeSend', props.beforeSend);\n  }\n\n  const script = document.createElement('script');\n  script.src = src;\n  script.defer = true;\n  script.dataset.sdkn =\n    packageName + (props.framework ? `/${props.framework}` : '');\n  script.dataset.sdkv = version;\n\n  if (props.sampleRate) {\n    script.dataset.sampleRate = props.sampleRate.toString();\n  }\n  if (props.route) {\n    script.dataset.route = props.route;\n  }\n  if (props.endpoint) {\n    script.dataset.endpoint = props.endpoint;\n  } else if (props.basePath) {\n    script.dataset.endpoint = `${props.basePath}/speed-insights/vitals`;\n  }\n  if (props.dsn) {\n    script.dataset.dsn = props.dsn;\n  }\n  if (isDevelopment() && props.debug === false) {\n    script.dataset.debug = 'false';\n  }\n\n  script.onerror = (): void => {\n    // eslint-disable-next-line no-console -- Logging is okay here\n    console.log(\n      `[Vercel Speed Insights] Failed to load script from ${src}. Please check if any content blockers are enabled and try again.`,\n    );\n  };\n\n  document.head.appendChild(script);\n\n  return {\n    setRoute: (route: string | null): void => {\n      script.dataset.route = route ?? undefined;\n    },\n  };\n}\n\nexport { injectSpeedInsights, computeRoute };\nexport type { SpeedInsightsProps };\n\n// eslint-disable-next-line import/no-default-export -- Allow default export\nexport default {\n  injectSpeedInsights,\n  computeRoute,\n};\n", "export function getBasePath(): string | undefined {\n  // !! important !!\n  // do not access env variables using process.env[varname]\n  // some bundles won't replace the value at build time.\n  // eslint-disable-next-line @typescript-eslint/prefer-optional-chain -- we can't use optionnal here, it'll break if process does not exist.\n  if (typeof process === 'undefined' || typeof process.env === 'undefined') {\n    return undefined;\n  }\n  return process.env.REACT_APP_VERCEL_OBSERVABILITY_BASEPATH;\n}\n", "'use client';\n/* eslint-disable @typescript-eslint/no-unnecessary-condition -- can be empty in pages router */\nimport { useParams, usePathname, useSearchParams } from 'next/navigation.js';\nimport { computeRoute } from '../utils';\n\nexport const useRoute = (): string | null => {\n  const params = useParams();\n  const searchParams = useSearchParams() || new URLSearchParams();\n  const path = usePathname();\n  // Until we have route parameters, we don't compute the route\n  if (!params) {\n    return null;\n  }\n  // in Next.js@13, useParams() could return an empty object for pages router, and we default to searchParams.\n  const finalParams = Object.keys(params).length\n    ? params\n    : Object.fromEntries(searchParams.entries());\n  return computeRoute(path, finalParams);\n};\n\nexport function getBasePath(): string | undefined {\n  // !! important !!\n  // do not access env variables using process.env[varname]\n  // some bundles won't replace the value at build time.\n  // eslint-disable-next-line @typescript-eslint/prefer-optional-chain -- we can't use optionnal here, it'll break if process does not exist.\n  if (typeof process === 'undefined' || typeof process.env === 'undefined') {\n    return undefined;\n  }\n  return process.env.NEXT_PUBLIC_VERCEL_OBSERVABILITY_BASEPATH;\n}\n"], "names": ["get<PERSON><PERSON><PERSON><PERSON>", "get<PERSON><PERSON><PERSON><PERSON>", "SpeedInsights"], "mappings": ";;;;AAEA,OAAO,SAAS,gBAAgB;;AOAhC,SAAS,WAAW,aAAa,uBAAuB;;;;;ALDtD,IAAA,OAAQ;AACR,IAAA,UAAW;;ACFN,IAAM,YAAY,MAAY;IAEnC,IAAI,OAAO,EAAA,CAAI,CAAA;IAEf,OAAO,EAAA,GAAK,SAAS,EAAA,GAAK,MAAA,EAAc;QACtC,CAAC,OAAO,GAAA,GAAM,OAAO,GAAA,IAAO,CAAC,CAAA,EAAG,IAAA,CAAK,MAAM;IAC7C;AACF;;ACLO,SAAS,YAAqB;IACnC,OAAO,OAAO,WAAW;AAC3B;AAEA,SAAS,oBAAkD;IACzD,IAAI;QACF,MAAM,MAAM,QAAQ,IAAI;QACxB,IAAI,QAAQ,iBAAiB,QAAQ,GAAQ;YAC3C,OAAO;QACT;IACF,EAAA,OAAS,GAAG,CAEZ;IACA,OAAO;AACT;AAMO,SAAS,gBAAyB;IACvC,OAAO,kBAAkB,MAAM;AACjC;AAEO,SAAS,aACd,QAAA,EACA,UAAA,EACe;IACf,IAAI,CAAC,YAAY,CAAC,YAAY;QAC5B,OAAO;IACT;IAEA,IAAI,SAAS;IACb,IAAI;QACF,MAAM,UAAU,OAAO,OAAA,CAAQ,UAAU;QAEzC,KAAA,MAAW,CAAC,KAAK,KAAK,CAAA,IAAK,QAAS;YAClC,IAAI,CAAC,MAAM,OAAA,CAAQ,KAAK,GAAG;gBACzB,MAAM,UAAU,kBAAkB,KAAK;gBACvC,IAAI,QAAQ,IAAA,CAAK,MAAM,GAAG;oBACxB,SAAS,OAAO,OAAA,CAAQ,SAAS,CAAA,EAAA,EAAK,GAAG,CAAA,CAAA,CAAG;gBAC9C;YACF;QACF;QAEA,KAAA,MAAW,CAAC,KAAK,KAAK,CAAA,IAAK,QAAS;YAClC,IAAI,MAAM,OAAA,CAAQ,KAAK,GAAG;gBACxB,MAAM,UAAU,kBAAkB,MAAM,IAAA,CAAK,GAAG,CAAC;gBACjD,IAAI,QAAQ,IAAA,CAAK,MAAM,GAAG;oBACxB,SAAS,OAAO,OAAA,CAAQ,SAAS,CAAA,KAAA,EAAQ,GAAG,CAAA,CAAA,CAAG;gBACjD;YACF;QACF;QACA,OAAO;IACT,EAAA,OAAS,GAAG;QACV,OAAO;IACT;AACF;AAEA,SAAS,kBAAkB,KAAA,EAAuB;IAChD,OAAO,IAAI,OAAO,CAAA,CAAA,EAAI,aAAa,KAAK,CAAC,CAAA,WAAA,CAAa;AACxD;AAEA,SAAS,aAAa,MAAA,EAAwB;IAC5C,OAAO,OAAO,OAAA,CAAQ,uBAAuB,MAAM;AACrD;AAEO,SAAS,aACd,KAAA,EACQ;IACR,IAAI,MAAM,SAAA,EAAW;QACnB,OAAO,MAAM,SAAA;IACf;IACA,IAAI,cAAc,GAAG;QACnB,OAAO;IACT;IACA,IAAI,MAAM,GAAA,EAAK;QACb,OAAO;IACT;IACA,IAAI,MAAM,QAAA,EAAU;QAClB,OAAO,GAAG,MAAM,QAAQ,CAAA,yBAAA,CAAA;IAC1B;IACA,OAAO;AACT;;ACvEA,SAAS,oBACP,QAGI,CAAC,CAAA,EAGE;IArBT,IAAA;IAuBE,IAAI,CAAC,UAAU,KAAK,MAAM,KAAA,KAAU,KAAM,CAAA,OAAO;IAEjD,UAAU;IAEV,MAAM,MAAM,aAAa,KAAK;IAE9B,IAAI,SAAS,IAAA,CAAK,aAAA,CAAc,CAAA,aAAA,EAAgB,GAAG,CAAA,EAAA,CAAI,EAAG,CAAA,OAAO;IAEjE,IAAI,MAAM,UAAA,EAAY;QACpB,CAAA,KAAA,OAAO,EAAA,KAAP,OAAA,KAAA,IAAA,GAAA,IAAA,CAAA,QAAY,cAAc,MAAM,UAAA;IAClC;IAEA,MAAM,SAAS,SAAS,aAAA,CAAc,QAAQ;IAC9C,OAAO,GAAA,GAAM;IACb,OAAO,KAAA,GAAQ;IACf,OAAO,OAAA,CAAQ,IAAA,GACb,OAAA,CAAe,MAAM,SAAA,GAAY,CAAA,CAAA,EAAI,MAAM,SAAS,EAAA,GAAK,EAAA;IAC3D,OAAO,OAAA,CAAQ,IAAA,GAAO;IAEtB,IAAI,MAAM,UAAA,EAAY;QACpB,OAAO,OAAA,CAAQ,UAAA,GAAa,MAAM,UAAA,CAAW,QAAA,CAAS;IACxD;IACA,IAAI,MAAM,KAAA,EAAO;QACf,OAAO,OAAA,CAAQ,KAAA,GAAQ,MAAM,KAAA;IAC/B;IACA,IAAI,MAAM,QAAA,EAAU;QAClB,OAAO,OAAA,CAAQ,QAAA,GAAW,MAAM,QAAA;IAClC,OAAA,IAAW,MAAM,QAAA,EAAU;QACzB,OAAO,OAAA,CAAQ,QAAA,GAAW,GAAG,MAAM,QAAQ,CAAA,sBAAA,CAAA;IAC7C;IACA,IAAI,MAAM,GAAA,EAAK;QACb,OAAO,OAAA,CAAQ,GAAA,GAAM,MAAM,GAAA;IAC7B;IACA,IAAI,cAAc,KAAK,MAAM,KAAA,KAAU,OAAO;QAC5C,OAAO,OAAA,CAAQ,KAAA,GAAQ;IACzB;IAEA,OAAO,OAAA,GAAU,MAAY;QAE3B,QAAQ,GAAA,CACN,CAAA,mDAAA,EAAsD,GAAG,CAAA,iEAAA,CAAA;IAE7D;IAEA,SAAS,IAAA,CAAK,WAAA,CAAY,MAAM;IAEhC,OAAO;QACL,UAAU,CAAC,UAA+B;YACxC,OAAO,OAAA,CAAQ,KAAA,GAAQ,SAAS,KAAA;QAClC;IACF;AACF;;AC1EO,SAAS,cAAkC;IAKhD,IAAI,OAAO,YAAY,eAAe,OAAO,QAAQ,GAAA,KAAQ,aAAa;QACxE,OAAO,KAAA;IACT;IACA,OAAO,QAAQ,GAAA,CAAI,uCAAA;AACrB;;ALFO,SAAS,cACd,KAAA,EAIoB;IACpB,CAAA,GAAA,qMAAA,CAAA,YAAA,EAAU,MAAM;QAblB,IAAA;QAcI,IAAI,MAAM,UAAA,EAAY;YACpB,CAAA,KAAA,OAAO,EAAA,KAAP,OAAA,KAAA,IAAA,GAAA,IAAA,CAAA,QAAY,cAAc,MAAM,UAAA;QAClC;IACF,GAAG;QAAC,MAAM,UAAU;KAAC;IAErB,MAAM,2NAAiB,SAAA,EAAwC,IAAI;IACnE,CAAA,GAAA,qMAAA,CAAA,YAAA,EAAU,MAAM;QACd,IAAI,CAAC,eAAe,OAAA,EAAS;YAC3B,MAAM,SAAS,oBAAoB;gBACjC,WAAW,MAAM,SAAA,IAAa;gBAC9B,UAAU,MAAM,QAAA,IAAY,YAAY;gBACxC,GAAG,KAAA;YACL,CAAC;YACD,IAAI,QAAQ;gBACV,eAAe,OAAA,GAAU,OAAO,QAAA;YAClC;QACF,OAAA,IAAW,MAAM,KAAA,EAAO;YACtB,eAAe,OAAA,CAAQ,MAAM,KAAK;QACpC;IACF,GAAG;QAAC,MAAM,KAAK;KAAC;IAEhB,OAAO;AACT;;AM/BO,IAAM,WAAW,MAAqB;IAC3C,MAAM,gJAAS,YAAA,CAAU;IACzB,MAAM,gBAAe,wJAAA,CAAgB,MAAK,IAAI,gBAAgB;IAC9D,MAAM,8IAAO,cAAA,CAAY;IAEzB,IAAI,CAAC,QAAQ;QACX,OAAO;IACT;IAEA,MAAM,cAAc,OAAO,IAAA,CAAK,MAAM,EAAE,MAAA,GACpC,SACA,OAAO,WAAA,CAAY,aAAa,OAAA,CAAQ,CAAC;IAC7C,OAAO,aAAa,MAAM,WAAW;AACvC;AAEO,SAASA,eAAkC;IAKhD,IAAI,OAAO,YAAY,eAAe,OAAO,QAAQ,GAAA,KAAQ,aAAa;QACxE,OAAO,KAAA;IACT;IACA,OAAO,QAAQ,GAAA,CAAI,yCAAA;AACrB;;APpBA,SAAS,uBAAuB,KAAA,EAAkC;IAChE,MAAM,QAAQ,SAAS;IAEvB,OACE,aAAA,GAAA,qMAAA,CAAA,UAAA,CAAA,aAAA,CAAC,eAAA;QACC;QACC,GAAG,KAAA;QACJ,WAAU;QACV,UAAUC,aAAY;IAAA;AAG5B;AAEO,SAASC,eAAc,KAAA,EAAoB;IAEhD,OACE,aAAA,GAAA,qMAAA,CAAA,UAAA,CAAA,aAAA,uMAAC,WAAA,EAAA;QAAS,UAAU;IAAA,GAClB,aAAA,GAAA,qMAAA,CAAA,UAAA,CAAA,aAAA,CAAC,wBAAA;QAAwB,GAAG,KAAA;IAAA,CAAO,CACrC;AAEJ", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7]}}, {"offset": {"line": 2350, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2356, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/sonner/src/index.tsx", "file:///home/<USER>/Desktop/templgen/node_modules/sonner/src/assets.tsx", "file:///home/<USER>/Desktop/templgen/node_modules/sonner/src/hooks.tsx", "file:///home/<USER>/Desktop/templgen/node_modules/sonner/src/state.ts", "file:///home/<USER>/Desktop/templgen/node_modules/sonner/dist/%23style-inject%3A%23style-inject", "file:///home/<USER>/Desktop/templgen/node_modules/sonner/src/styles.css", "file:///home/<USER>/Desktop/templgen/node_modules/sonner/src/types.ts"], "sourcesContent": ["'use client';\n\nimport React, { forwardRef, isValidElement } from 'react';\nimport ReactDOM from 'react-dom';\n\nimport { CloseIcon, getAsset, Loader } from './assets';\nimport { useIsDocumentHidden } from './hooks';\nimport { toast, ToastState } from './state';\nimport './styles.css';\nimport {\n  isAction,\n  SwipeDirection,\n  type ExternalToast,\n  type HeightT,\n  type ToasterProps,\n  type ToastProps,\n  type ToastT,\n  type ToastToDismiss,\n} from './types';\n\n// Visible toasts amount\nconst VISIBLE_TOASTS_AMOUNT = 3;\n\n// Viewport padding\nconst VIEWPORT_OFFSET = '32px';\n\n// Mobile viewport padding\nconst MOBILE_VIEWPORT_OFFSET = '16px';\n\n// Default lifetime of a toasts (in ms)\nconst TOAST_LIFETIME = 4000;\n\n// Default toast width\nconst TOAST_WIDTH = 356;\n\n// Default gap between toasts\nconst GAP = 14;\n\n// Threshold to dismiss a toast\nconst SWIPE_THRESHOLD = 20;\n\n// Equal to exit animation duration\nconst TIME_BEFORE_UNMOUNT = 200;\n\nfunction cn(...classes: (string | undefined)[]) {\n  return classes.filter(Boolean).join(' ');\n}\n\nfunction getDefaultSwipeDirections(position: string): Array<SwipeDirection> {\n  const [y, x] = position.split('-');\n  const directions: Array<SwipeDirection> = [];\n\n  if (y) {\n    directions.push(y as SwipeDirection);\n  }\n\n  if (x) {\n    directions.push(x as SwipeDirection);\n  }\n\n  return directions;\n}\n\nconst Toast = (props: ToastProps) => {\n  const {\n    invert: ToasterInvert,\n    toast,\n    unstyled,\n    interacting,\n    setHeights,\n    visibleToasts,\n    heights,\n    index,\n    toasts,\n    expanded,\n    removeToast,\n    defaultRichColors,\n    closeButton: closeButtonFromToaster,\n    style,\n    cancelButtonStyle,\n    actionButtonStyle,\n    className = '',\n    descriptionClassName = '',\n    duration: durationFromToaster,\n    position,\n    gap,\n    loadingIcon: loadingIconProp,\n    expandByDefault,\n    classNames,\n    icons,\n    closeButtonAriaLabel = 'Close toast',\n    pauseWhenPageIsHidden,\n  } = props;\n  const [swipeDirection, setSwipeDirection] = React.useState<'x' | 'y' | null>(null);\n  const [swipeOutDirection, setSwipeOutDirection] = React.useState<'left' | 'right' | 'up' | 'down' | null>(null);\n  const [mounted, setMounted] = React.useState(false);\n  const [removed, setRemoved] = React.useState(false);\n  const [swiping, setSwiping] = React.useState(false);\n  const [swipeOut, setSwipeOut] = React.useState(false);\n  const [isSwiped, setIsSwiped] = React.useState(false);\n  const [offsetBeforeRemove, setOffsetBeforeRemove] = React.useState(0);\n  const [initialHeight, setInitialHeight] = React.useState(0);\n  const remainingTime = React.useRef(toast.duration || durationFromToaster || TOAST_LIFETIME);\n  const dragStartTime = React.useRef<Date | null>(null);\n  const toastRef = React.useRef<HTMLLIElement>(null);\n  const isFront = index === 0;\n  const isVisible = index + 1 <= visibleToasts;\n  const toastType = toast.type;\n  const dismissible = toast.dismissible !== false;\n  const toastClassname = toast.className || '';\n  const toastDescriptionClassname = toast.descriptionClassName || '';\n  // Height index is used to calculate the offset as it gets updated before the toast array, which means we can calculate the new layout faster.\n  const heightIndex = React.useMemo(\n    () => heights.findIndex((height) => height.toastId === toast.id) || 0,\n    [heights, toast.id],\n  );\n  const closeButton = React.useMemo(\n    () => toast.closeButton ?? closeButtonFromToaster,\n    [toast.closeButton, closeButtonFromToaster],\n  );\n  const duration = React.useMemo(\n    () => toast.duration || durationFromToaster || TOAST_LIFETIME,\n    [toast.duration, durationFromToaster],\n  );\n  const closeTimerStartTimeRef = React.useRef(0);\n  const offset = React.useRef(0);\n  const lastCloseTimerStartTimeRef = React.useRef(0);\n  const pointerStartRef = React.useRef<{ x: number; y: number } | null>(null);\n  const [y, x] = position.split('-');\n  const toastsHeightBefore = React.useMemo(() => {\n    return heights.reduce((prev, curr, reducerIndex) => {\n      // Calculate offset up until current toast\n      if (reducerIndex >= heightIndex) {\n        return prev;\n      }\n\n      return prev + curr.height;\n    }, 0);\n  }, [heights, heightIndex]);\n  const isDocumentHidden = useIsDocumentHidden();\n\n  const invert = toast.invert || ToasterInvert;\n  const disabled = toastType === 'loading';\n\n  offset.current = React.useMemo(() => heightIndex * gap + toastsHeightBefore, [heightIndex, toastsHeightBefore]);\n\n  React.useEffect(() => {\n    remainingTime.current = duration;\n  }, [duration]);\n\n  React.useEffect(() => {\n    // Trigger enter animation without using CSS animation\n    setMounted(true);\n  }, []);\n\n  React.useEffect(() => {\n    const toastNode = toastRef.current;\n    if (toastNode) {\n      const height = toastNode.getBoundingClientRect().height;\n      // Add toast height to heights array after the toast is mounted\n      setInitialHeight(height);\n      setHeights((h) => [{ toastId: toast.id, height, position: toast.position }, ...h]);\n      return () => setHeights((h) => h.filter((height) => height.toastId !== toast.id));\n    }\n  }, [setHeights, toast.id]);\n\n  React.useLayoutEffect(() => {\n    if (!mounted) return;\n    const toastNode = toastRef.current;\n    const originalHeight = toastNode.style.height;\n    toastNode.style.height = 'auto';\n    const newHeight = toastNode.getBoundingClientRect().height;\n    toastNode.style.height = originalHeight;\n\n    setInitialHeight(newHeight);\n\n    setHeights((heights) => {\n      const alreadyExists = heights.find((height) => height.toastId === toast.id);\n      if (!alreadyExists) {\n        return [{ toastId: toast.id, height: newHeight, position: toast.position }, ...heights];\n      } else {\n        return heights.map((height) => (height.toastId === toast.id ? { ...height, height: newHeight } : height));\n      }\n    });\n  }, [mounted, toast.title, toast.description, setHeights, toast.id]);\n\n  const deleteToast = React.useCallback(() => {\n    // Save the offset for the exit swipe animation\n    setRemoved(true);\n    setOffsetBeforeRemove(offset.current);\n    setHeights((h) => h.filter((height) => height.toastId !== toast.id));\n\n    setTimeout(() => {\n      removeToast(toast);\n    }, TIME_BEFORE_UNMOUNT);\n  }, [toast, removeToast, setHeights, offset]);\n\n  React.useEffect(() => {\n    if ((toast.promise && toastType === 'loading') || toast.duration === Infinity || toast.type === 'loading') return;\n    let timeoutId: NodeJS.Timeout;\n\n    // Pause the timer on each hover\n    const pauseTimer = () => {\n      if (lastCloseTimerStartTimeRef.current < closeTimerStartTimeRef.current) {\n        // Get the elapsed time since the timer started\n        const elapsedTime = new Date().getTime() - closeTimerStartTimeRef.current;\n\n        remainingTime.current = remainingTime.current - elapsedTime;\n      }\n\n      lastCloseTimerStartTimeRef.current = new Date().getTime();\n    };\n\n    const startTimer = () => {\n      // setTimeout(, Infinity) behaves as if the delay is 0.\n      // As a result, the toast would be closed immediately, giving the appearance that it was never rendered.\n      // See: https://github.com/denysdovhan/wtfjs?tab=readme-ov-file#an-infinite-timeout\n      if (remainingTime.current === Infinity) return;\n\n      closeTimerStartTimeRef.current = new Date().getTime();\n\n      // Let the toast know it has started\n      timeoutId = setTimeout(() => {\n        toast.onAutoClose?.(toast);\n        deleteToast();\n      }, remainingTime.current);\n    };\n\n    if (expanded || interacting || (pauseWhenPageIsHidden && isDocumentHidden)) {\n      pauseTimer();\n    } else {\n      startTimer();\n    }\n\n    return () => clearTimeout(timeoutId);\n  }, [expanded, interacting, toast, toastType, pauseWhenPageIsHidden, isDocumentHidden, deleteToast]);\n\n  React.useEffect(() => {\n    if (toast.delete) {\n      deleteToast();\n    }\n  }, [deleteToast, toast.delete]);\n\n  function getLoadingIcon() {\n    if (icons?.loading) {\n      return (\n        <div\n          className={cn(classNames?.loader, toast?.classNames?.loader, 'sonner-loader')}\n          data-visible={toastType === 'loading'}\n        >\n          {icons.loading}\n        </div>\n      );\n    }\n\n    if (loadingIconProp) {\n      return (\n        <div\n          className={cn(classNames?.loader, toast?.classNames?.loader, 'sonner-loader')}\n          data-visible={toastType === 'loading'}\n        >\n          {loadingIconProp}\n        </div>\n      );\n    }\n    return <Loader className={cn(classNames?.loader, toast?.classNames?.loader)} visible={toastType === 'loading'} />;\n  }\n\n  return (\n    <li\n      tabIndex={0}\n      ref={toastRef}\n      className={cn(\n        className,\n        toastClassname,\n        classNames?.toast,\n        toast?.classNames?.toast,\n        classNames?.default,\n        classNames?.[toastType],\n        toast?.classNames?.[toastType],\n      )}\n      data-sonner-toast=\"\"\n      data-rich-colors={toast.richColors ?? defaultRichColors}\n      data-styled={!Boolean(toast.jsx || toast.unstyled || unstyled)}\n      data-mounted={mounted}\n      data-promise={Boolean(toast.promise)}\n      data-swiped={isSwiped}\n      data-removed={removed}\n      data-visible={isVisible}\n      data-y-position={y}\n      data-x-position={x}\n      data-index={index}\n      data-front={isFront}\n      data-swiping={swiping}\n      data-dismissible={dismissible}\n      data-type={toastType}\n      data-invert={invert}\n      data-swipe-out={swipeOut}\n      data-swipe-direction={swipeOutDirection}\n      data-expanded={Boolean(expanded || (expandByDefault && mounted))}\n      style={\n        {\n          '--index': index,\n          '--toasts-before': index,\n          '--z-index': toasts.length - index,\n          '--offset': `${removed ? offsetBeforeRemove : offset.current}px`,\n          '--initial-height': expandByDefault ? 'auto' : `${initialHeight}px`,\n          ...style,\n          ...toast.style,\n        } as React.CSSProperties\n      }\n      onDragEnd={() => {\n        setSwiping(false);\n        setSwipeDirection(null);\n        pointerStartRef.current = null;\n      }}\n      onPointerDown={(event) => {\n        if (disabled || !dismissible) return;\n        dragStartTime.current = new Date();\n        setOffsetBeforeRemove(offset.current);\n        // Ensure we maintain correct pointer capture even when going outside of the toast (e.g. when swiping)\n        (event.target as HTMLElement).setPointerCapture(event.pointerId);\n        if ((event.target as HTMLElement).tagName === 'BUTTON') return;\n        setSwiping(true);\n        pointerStartRef.current = { x: event.clientX, y: event.clientY };\n      }}\n      onPointerUp={() => {\n        if (swipeOut || !dismissible) return;\n\n        pointerStartRef.current = null;\n        const swipeAmountX = Number(\n          toastRef.current?.style.getPropertyValue('--swipe-amount-x').replace('px', '') || 0,\n        );\n        const swipeAmountY = Number(\n          toastRef.current?.style.getPropertyValue('--swipe-amount-y').replace('px', '') || 0,\n        );\n        const timeTaken = new Date().getTime() - dragStartTime.current?.getTime();\n\n        const swipeAmount = swipeDirection === 'x' ? swipeAmountX : swipeAmountY;\n        const velocity = Math.abs(swipeAmount) / timeTaken;\n\n        if (Math.abs(swipeAmount) >= SWIPE_THRESHOLD || velocity > 0.11) {\n          setOffsetBeforeRemove(offset.current);\n          toast.onDismiss?.(toast);\n\n          if (swipeDirection === 'x') {\n            setSwipeOutDirection(swipeAmountX > 0 ? 'right' : 'left');\n          } else {\n            setSwipeOutDirection(swipeAmountY > 0 ? 'down' : 'up');\n          }\n\n          deleteToast();\n          setSwipeOut(true);\n          setIsSwiped(false);\n          return;\n        }\n\n        setSwiping(false);\n        setSwipeDirection(null);\n      }}\n      onPointerMove={(event) => {\n        if (!pointerStartRef.current || !dismissible) return;\n\n        const isHighlighted = window.getSelection()?.toString().length > 0;\n        if (isHighlighted) return;\n\n        const yDelta = event.clientY - pointerStartRef.current.y;\n        const xDelta = event.clientX - pointerStartRef.current.x;\n\n        const swipeDirections = props.swipeDirections ?? getDefaultSwipeDirections(position);\n\n        // Determine swipe direction if not already locked\n        if (!swipeDirection && (Math.abs(xDelta) > 1 || Math.abs(yDelta) > 1)) {\n          setSwipeDirection(Math.abs(xDelta) > Math.abs(yDelta) ? 'x' : 'y');\n        }\n\n        let swipeAmount = { x: 0, y: 0 };\n\n        // Only apply swipe in the locked direction\n        if (swipeDirection === 'y') {\n          // Handle vertical swipes\n          if (swipeDirections.includes('top') || swipeDirections.includes('bottom')) {\n            if (swipeDirections.includes('top') && yDelta < 0) {\n              swipeAmount.y = yDelta;\n            } else if (swipeDirections.includes('bottom') && yDelta > 0) {\n              swipeAmount.y = yDelta;\n            }\n          }\n        } else if (swipeDirection === 'x') {\n          // Handle horizontal swipes\n          if (swipeDirections.includes('left') || swipeDirections.includes('right')) {\n            if (swipeDirections.includes('left') && xDelta < 0) {\n              swipeAmount.x = xDelta;\n            } else if (swipeDirections.includes('right') && xDelta > 0) {\n              swipeAmount.x = xDelta;\n            }\n          }\n        }\n\n        if (Math.abs(swipeAmount.x) > 0 || Math.abs(swipeAmount.y) > 0) {\n          setIsSwiped(true);\n        }\n\n        // Apply transform using both x and y values\n        toastRef.current?.style.setProperty('--swipe-amount-x', `${swipeAmount.x}px`);\n        toastRef.current?.style.setProperty('--swipe-amount-y', `${swipeAmount.y}px`);\n      }}\n    >\n      {closeButton && !toast.jsx ? (\n        <button\n          aria-label={closeButtonAriaLabel}\n          data-disabled={disabled}\n          data-close-button\n          onClick={\n            disabled || !dismissible\n              ? () => {}\n              : () => {\n                  deleteToast();\n                  toast.onDismiss?.(toast);\n                }\n          }\n          className={cn(classNames?.closeButton, toast?.classNames?.closeButton)}\n        >\n          {icons?.close ?? CloseIcon}\n        </button>\n      ) : null}\n      {/* TODO: This can be cleaner */}\n      {toast.jsx || isValidElement(toast.title) ? (\n        toast.jsx ? (\n          toast.jsx\n        ) : typeof toast.title === 'function' ? (\n          toast.title()\n        ) : (\n          toast.title\n        )\n      ) : (\n        <>\n          {toastType || toast.icon || toast.promise ? (\n            <div data-icon=\"\" className={cn(classNames?.icon, toast?.classNames?.icon)}>\n              {toast.promise || (toast.type === 'loading' && !toast.icon) ? toast.icon || getLoadingIcon() : null}\n              {toast.type !== 'loading' ? toast.icon || icons?.[toastType] || getAsset(toastType) : null}\n            </div>\n          ) : null}\n\n          <div data-content=\"\" className={cn(classNames?.content, toast?.classNames?.content)}>\n            <div data-title=\"\" className={cn(classNames?.title, toast?.classNames?.title)}>\n              {typeof toast.title === 'function' ? toast.title() : toast.title}\n            </div>\n            {toast.description ? (\n              <div\n                data-description=\"\"\n                className={cn(\n                  descriptionClassName,\n                  toastDescriptionClassname,\n                  classNames?.description,\n                  toast?.classNames?.description,\n                )}\n              >\n                {typeof toast.description === 'function' ? toast.description() : toast.description}\n              </div>\n            ) : null}\n          </div>\n          {isValidElement(toast.cancel) ? (\n            toast.cancel\n          ) : toast.cancel && isAction(toast.cancel) ? (\n            <button\n              data-button\n              data-cancel\n              style={toast.cancelButtonStyle || cancelButtonStyle}\n              onClick={(event) => {\n                // We need to check twice because typescript\n                if (!isAction(toast.cancel)) return;\n                if (!dismissible) return;\n                toast.cancel.onClick?.(event);\n                deleteToast();\n              }}\n              className={cn(classNames?.cancelButton, toast?.classNames?.cancelButton)}\n            >\n              {toast.cancel.label}\n            </button>\n          ) : null}\n          {isValidElement(toast.action) ? (\n            toast.action\n          ) : toast.action && isAction(toast.action) ? (\n            <button\n              data-button\n              data-action\n              style={toast.actionButtonStyle || actionButtonStyle}\n              onClick={(event) => {\n                // We need to check twice because typescript\n                if (!isAction(toast.action)) return;\n                toast.action.onClick?.(event);\n                if (event.defaultPrevented) return;\n                deleteToast();\n              }}\n              className={cn(classNames?.actionButton, toast?.classNames?.actionButton)}\n            >\n              {toast.action.label}\n            </button>\n          ) : null}\n        </>\n      )}\n    </li>\n  );\n};\n\nfunction getDocumentDirection(): ToasterProps['dir'] {\n  if (typeof window === 'undefined') return 'ltr';\n  if (typeof document === 'undefined') return 'ltr'; // For Fresh purpose\n\n  const dirAttribute = document.documentElement.getAttribute('dir');\n\n  if (dirAttribute === 'auto' || !dirAttribute) {\n    return window.getComputedStyle(document.documentElement).direction as ToasterProps['dir'];\n  }\n\n  return dirAttribute as ToasterProps['dir'];\n}\n\nfunction assignOffset(defaultOffset: ToasterProps['offset'], mobileOffset: ToasterProps['mobileOffset']) {\n  const styles = {} as React.CSSProperties;\n\n  [defaultOffset, mobileOffset].forEach((offset, index) => {\n    const isMobile = index === 1;\n    const prefix = isMobile ? '--mobile-offset' : '--offset';\n    const defaultValue = isMobile ? MOBILE_VIEWPORT_OFFSET : VIEWPORT_OFFSET;\n\n    function assignAll(offset: string | number) {\n      ['top', 'right', 'bottom', 'left'].forEach((key) => {\n        styles[`${prefix}-${key}`] = typeof offset === 'number' ? `${offset}px` : offset;\n      });\n    }\n\n    if (typeof offset === 'number' || typeof offset === 'string') {\n      assignAll(offset);\n    } else if (typeof offset === 'object') {\n      ['top', 'right', 'bottom', 'left'].forEach((key) => {\n        if (offset[key] === undefined) {\n          styles[`${prefix}-${key}`] = defaultValue;\n        } else {\n          styles[`${prefix}-${key}`] = typeof offset[key] === 'number' ? `${offset[key]}px` : offset[key];\n        }\n      });\n    } else {\n      assignAll(defaultValue);\n    }\n  });\n\n  return styles;\n}\n\nfunction useSonner() {\n  const [activeToasts, setActiveToasts] = React.useState<ToastT[]>([]);\n\n  React.useEffect(() => {\n    return ToastState.subscribe((toast) => {\n      if ((toast as ToastToDismiss).dismiss) {\n        setTimeout(() => {\n          ReactDOM.flushSync(() => {\n            setActiveToasts((toasts) => toasts.filter((t) => t.id !== toast.id));\n          });\n        });\n        return;\n      }\n\n      // Prevent batching, temp solution.\n      setTimeout(() => {\n        ReactDOM.flushSync(() => {\n          setActiveToasts((toasts) => {\n            const indexOfExistingToast = toasts.findIndex((t) => t.id === toast.id);\n\n            // Update the toast if it already exists\n            if (indexOfExistingToast !== -1) {\n              return [\n                ...toasts.slice(0, indexOfExistingToast),\n                { ...toasts[indexOfExistingToast], ...toast },\n                ...toasts.slice(indexOfExistingToast + 1),\n              ];\n            }\n\n            return [toast, ...toasts];\n          });\n        });\n      });\n    });\n  }, []);\n\n  return {\n    toasts: activeToasts,\n  };\n}\n\nconst Toaster = forwardRef<HTMLElement, ToasterProps>(function Toaster(props, ref) {\n  const {\n    invert,\n    position = 'bottom-right',\n    hotkey = ['altKey', 'KeyT'],\n    expand,\n    closeButton,\n    className,\n    offset,\n    mobileOffset,\n    theme = 'light',\n    richColors,\n    duration,\n    style,\n    visibleToasts = VISIBLE_TOASTS_AMOUNT,\n    toastOptions,\n    dir = getDocumentDirection(),\n    gap = GAP,\n    loadingIcon,\n    icons,\n    containerAriaLabel = 'Notifications',\n    pauseWhenPageIsHidden,\n  } = props;\n  const [toasts, setToasts] = React.useState<ToastT[]>([]);\n  const possiblePositions = React.useMemo(() => {\n    return Array.from(\n      new Set([position].concat(toasts.filter((toast) => toast.position).map((toast) => toast.position))),\n    );\n  }, [toasts, position]);\n  const [heights, setHeights] = React.useState<HeightT[]>([]);\n  const [expanded, setExpanded] = React.useState(false);\n  const [interacting, setInteracting] = React.useState(false);\n  const [actualTheme, setActualTheme] = React.useState(\n    theme !== 'system'\n      ? theme\n      : typeof window !== 'undefined'\n      ? window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches\n        ? 'dark'\n        : 'light'\n      : 'light',\n  );\n\n  const listRef = React.useRef<HTMLOListElement>(null);\n  const hotkeyLabel = hotkey.join('+').replace(/Key/g, '').replace(/Digit/g, '');\n  const lastFocusedElementRef = React.useRef<HTMLElement>(null);\n  const isFocusWithinRef = React.useRef(false);\n\n  const removeToast = React.useCallback((toastToRemove: ToastT) => {\n    setToasts((toasts) => {\n      if (!toasts.find((toast) => toast.id === toastToRemove.id)?.delete) {\n        ToastState.dismiss(toastToRemove.id);\n      }\n\n      return toasts.filter(({ id }) => id !== toastToRemove.id);\n    });\n  }, []);\n\n  React.useEffect(() => {\n    return ToastState.subscribe((toast) => {\n      if ((toast as ToastToDismiss).dismiss) {\n        setToasts((toasts) => toasts.map((t) => (t.id === toast.id ? { ...t, delete: true } : t)));\n        return;\n      }\n\n      // Prevent batching, temp solution.\n      setTimeout(() => {\n        ReactDOM.flushSync(() => {\n          setToasts((toasts) => {\n            const indexOfExistingToast = toasts.findIndex((t) => t.id === toast.id);\n\n            // Update the toast if it already exists\n            if (indexOfExistingToast !== -1) {\n              return [\n                ...toasts.slice(0, indexOfExistingToast),\n                { ...toasts[indexOfExistingToast], ...toast },\n                ...toasts.slice(indexOfExistingToast + 1),\n              ];\n            }\n\n            return [toast, ...toasts];\n          });\n        });\n      });\n    });\n  }, []);\n\n  React.useEffect(() => {\n    if (theme !== 'system') {\n      setActualTheme(theme);\n      return;\n    }\n\n    if (theme === 'system') {\n      // check if current preference is dark\n      if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {\n        // it's currently dark\n        setActualTheme('dark');\n      } else {\n        // it's not dark\n        setActualTheme('light');\n      }\n    }\n\n    if (typeof window === 'undefined') return;\n    const darkMediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n\n    try {\n      // Chrome & Firefox\n      darkMediaQuery.addEventListener('change', ({ matches }) => {\n        if (matches) {\n          setActualTheme('dark');\n        } else {\n          setActualTheme('light');\n        }\n      });\n    } catch (error) {\n      // Safari < 14\n      darkMediaQuery.addListener(({ matches }) => {\n        try {\n          if (matches) {\n            setActualTheme('dark');\n          } else {\n            setActualTheme('light');\n          }\n        } catch (e) {\n          console.error(e);\n        }\n      });\n    }\n  }, [theme]);\n\n  React.useEffect(() => {\n    // Ensure expanded is always false when no toasts are present / only one left\n    if (toasts.length <= 1) {\n      setExpanded(false);\n    }\n  }, [toasts]);\n\n  React.useEffect(() => {\n    const handleKeyDown = (event: KeyboardEvent) => {\n      const isHotkeyPressed = hotkey.every((key) => (event as any)[key] || event.code === key);\n\n      if (isHotkeyPressed) {\n        setExpanded(true);\n        listRef.current?.focus();\n      }\n\n      if (\n        event.code === 'Escape' &&\n        (document.activeElement === listRef.current || listRef.current?.contains(document.activeElement))\n      ) {\n        setExpanded(false);\n      }\n    };\n    document.addEventListener('keydown', handleKeyDown);\n\n    return () => document.removeEventListener('keydown', handleKeyDown);\n  }, [hotkey]);\n\n  React.useEffect(() => {\n    if (listRef.current) {\n      return () => {\n        if (lastFocusedElementRef.current) {\n          lastFocusedElementRef.current.focus({ preventScroll: true });\n          lastFocusedElementRef.current = null;\n          isFocusWithinRef.current = false;\n        }\n      };\n    }\n  }, [listRef.current]);\n\n  return (\n    // Remove item from normal navigation flow, only available via hotkey\n    <section\n      ref={ref}\n      aria-label={`${containerAriaLabel} ${hotkeyLabel}`}\n      tabIndex={-1}\n      aria-live=\"polite\"\n      aria-relevant=\"additions text\"\n      aria-atomic=\"false\"\n      suppressHydrationWarning\n    >\n      {possiblePositions.map((position, index) => {\n        const [y, x] = position.split('-');\n\n        if (!toasts.length) return null;\n\n        return (\n          <ol\n            key={position}\n            dir={dir === 'auto' ? getDocumentDirection() : dir}\n            tabIndex={-1}\n            ref={listRef}\n            className={className}\n            data-sonner-toaster\n            data-theme={actualTheme}\n            data-y-position={y}\n            data-lifted={expanded && toasts.length > 1 && !expand}\n            data-x-position={x}\n            style={\n              {\n                '--front-toast-height': `${heights[0]?.height || 0}px`,\n                '--width': `${TOAST_WIDTH}px`,\n                '--gap': `${gap}px`,\n                ...style,\n                ...assignOffset(offset, mobileOffset),\n              } as React.CSSProperties\n            }\n            onBlur={(event) => {\n              if (isFocusWithinRef.current && !event.currentTarget.contains(event.relatedTarget)) {\n                isFocusWithinRef.current = false;\n                if (lastFocusedElementRef.current) {\n                  lastFocusedElementRef.current.focus({ preventScroll: true });\n                  lastFocusedElementRef.current = null;\n                }\n              }\n            }}\n            onFocus={(event) => {\n              const isNotDismissible =\n                event.target instanceof HTMLElement && event.target.dataset.dismissible === 'false';\n\n              if (isNotDismissible) return;\n\n              if (!isFocusWithinRef.current) {\n                isFocusWithinRef.current = true;\n                lastFocusedElementRef.current = event.relatedTarget as HTMLElement;\n              }\n            }}\n            onMouseEnter={() => setExpanded(true)}\n            onMouseMove={() => setExpanded(true)}\n            onMouseLeave={() => {\n              // Avoid setting expanded to false when interacting with a toast, e.g. swiping\n              if (!interacting) {\n                setExpanded(false);\n              }\n            }}\n            onDragEnd={() => setExpanded(false)}\n            onPointerDown={(event) => {\n              const isNotDismissible =\n                event.target instanceof HTMLElement && event.target.dataset.dismissible === 'false';\n\n              if (isNotDismissible) return;\n              setInteracting(true);\n            }}\n            onPointerUp={() => setInteracting(false)}\n          >\n            {toasts\n              .filter((toast) => (!toast.position && index === 0) || toast.position === position)\n              .map((toast, index) => (\n                <Toast\n                  key={toast.id}\n                  icons={icons}\n                  index={index}\n                  toast={toast}\n                  defaultRichColors={richColors}\n                  duration={toastOptions?.duration ?? duration}\n                  className={toastOptions?.className}\n                  descriptionClassName={toastOptions?.descriptionClassName}\n                  invert={invert}\n                  visibleToasts={visibleToasts}\n                  closeButton={toastOptions?.closeButton ?? closeButton}\n                  interacting={interacting}\n                  position={position}\n                  style={toastOptions?.style}\n                  unstyled={toastOptions?.unstyled}\n                  classNames={toastOptions?.classNames}\n                  cancelButtonStyle={toastOptions?.cancelButtonStyle}\n                  actionButtonStyle={toastOptions?.actionButtonStyle}\n                  removeToast={removeToast}\n                  toasts={toasts.filter((t) => t.position == toast.position)}\n                  heights={heights.filter((h) => h.position == toast.position)}\n                  setHeights={setHeights}\n                  expandByDefault={expand}\n                  gap={gap}\n                  loadingIcon={loadingIcon}\n                  expanded={expanded}\n                  pauseWhenPageIsHidden={pauseWhenPageIsHidden}\n                  swipeDirections={props.swipeDirections}\n                />\n              ))}\n          </ol>\n        );\n      })}\n    </section>\n  );\n});\nexport { toast, Toaster, type ExternalToast, type ToastT, type ToasterProps, useSonner };\nexport { type ToastClassnames, type ToastToDismiss, type Action } from './types';\n", "'use client';\nimport React from 'react';\nimport type { ToastTypes } from './types';\n\nexport const getAsset = (type: ToastTypes): JSX.Element | null => {\n  switch (type) {\n    case 'success':\n      return SuccessIcon;\n\n    case 'info':\n      return InfoIcon;\n\n    case 'warning':\n      return WarningIcon;\n\n    case 'error':\n      return ErrorIcon;\n\n    default:\n      return null;\n  }\n};\n\nconst bars = Array(12).fill(0);\n\nexport const Loader = ({ visible, className }: { visible: boolean, className?: string }) => {\n  return (\n    <div className={['sonner-loading-wrapper', className].filter(Boolean).join(' ')} data-visible={visible}>\n      <div className=\"sonner-spinner\">\n        {bars.map((_, i) => (\n          <div className=\"sonner-loading-bar\" key={`spinner-bar-${i}`} />\n        ))}\n      </div>\n    </div>\n  );\n};\n\nconst SuccessIcon = (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 20 20\" fill=\"currentColor\" height=\"20\" width=\"20\">\n    <path\n      fillRule=\"evenodd\"\n      d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z\"\n      clipRule=\"evenodd\"\n    />\n  </svg>\n);\n\nconst WarningIcon = (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"currentColor\" height=\"20\" width=\"20\">\n    <path\n      fillRule=\"evenodd\"\n      d=\"M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z\"\n      clipRule=\"evenodd\"\n    />\n  </svg>\n);\n\nconst InfoIcon = (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 20 20\" fill=\"currentColor\" height=\"20\" width=\"20\">\n    <path\n      fillRule=\"evenodd\"\n      d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z\"\n      clipRule=\"evenodd\"\n    />\n  </svg>\n);\n\nconst ErrorIcon = (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 20 20\" fill=\"currentColor\" height=\"20\" width=\"20\">\n    <path\n      fillRule=\"evenodd\"\n      d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z\"\n      clipRule=\"evenodd\"\n    />\n  </svg>\n);\n\nexport const CloseIcon = (\n  <svg\n    xmlns=\"http://www.w3.org/2000/svg\"\n    width=\"12\"\n    height=\"12\"\n    viewBox=\"0 0 24 24\"\n    fill=\"none\"\n    stroke=\"currentColor\"\n    strokeWidth=\"1.5\"\n    strokeLinecap=\"round\"\n    strokeLinejoin=\"round\"\n  >\n    <line x1=\"18\" y1=\"6\" x2=\"6\" y2=\"18\"></line>\n    <line x1=\"6\" y1=\"6\" x2=\"18\" y2=\"18\"></line>\n  </svg>\n);\n", "import React from 'react';\n\nexport const useIsDocumentHidden = () => {\n  const [isDocumentHidden, setIsDocumentHidden] = React.useState(document.hidden);\n\n  React.useEffect(() => {\n    const callback = () => {\n      setIsDocumentHidden(document.hidden);\n    };\n    document.addEventListener('visibilitychange', callback);\n    return () => window.removeEventListener('visibilitychange', callback);\n  }, []);\n\n  return isDocumentHidden;\n};\n", "import type { ExternalToast, PromiseD<PERSON>, PromiseT, ToastT, ToastToDismiss, ToastTypes } from './types';\n\nimport React from 'react';\n\nlet toastsCounter = 1;\n\ntype titleT = (() => React.ReactNode) | React.ReactNode;\n\nclass Observer {\n  subscribers: Array<(toast: ExternalToast | ToastToDismiss) => void>;\n  toasts: Array<ToastT | ToastToDismiss>;\n  dismissedToasts: Set<string | number>;\n\n  constructor() {\n    this.subscribers = [];\n    this.toasts = [];\n    this.dismissedToasts = new Set();\n  }\n\n  // We use arrow functions to maintain the correct `this` reference\n  subscribe = (subscriber: (toast: ToastT | ToastToDismiss) => void) => {\n    this.subscribers.push(subscriber);\n\n    return () => {\n      const index = this.subscribers.indexOf(subscriber);\n      this.subscribers.splice(index, 1);\n    };\n  };\n\n  publish = (data: ToastT) => {\n    this.subscribers.forEach((subscriber) => subscriber(data));\n  };\n\n  addToast = (data: ToastT) => {\n    this.publish(data);\n    this.toasts = [...this.toasts, data];\n  };\n\n  create = (\n    data: ExternalToast & {\n      message?: titleT;\n      type?: ToastTypes;\n      promise?: PromiseT;\n      jsx?: React.ReactElement;\n    },\n  ) => {\n    const { message, ...rest } = data;\n    const id = typeof data?.id === 'number' || data.id?.length > 0 ? data.id : toastsCounter++;\n    const alreadyExists = this.toasts.find((toast) => {\n      return toast.id === id;\n    });\n    const dismissible = data.dismissible === undefined ? true : data.dismissible;\n\n    if (this.dismissedToasts.has(id)) {\n      this.dismissedToasts.delete(id);\n    }\n\n    if (alreadyExists) {\n      this.toasts = this.toasts.map((toast) => {\n        if (toast.id === id) {\n          this.publish({ ...toast, ...data, id, title: message });\n          return {\n            ...toast,\n            ...data,\n            id,\n            dismissible,\n            title: message,\n          };\n        }\n\n        return toast;\n      });\n    } else {\n      this.addToast({ title: message, ...rest, dismissible, id });\n    }\n\n    return id;\n  };\n\n  dismiss = (id?: number | string) => {\n    this.dismissedToasts.add(id);\n\n    if (!id) {\n      this.toasts.forEach((toast) => {\n        this.subscribers.forEach((subscriber) => subscriber({ id: toast.id, dismiss: true }));\n      });\n    }\n    this.subscribers.forEach((subscriber) => subscriber({ id, dismiss: true }));\n    return id;\n  };\n\n  message = (message: titleT | React.ReactNode, data?: ExternalToast) => {\n    return this.create({ ...data, message });\n  };\n\n  error = (message: titleT | React.ReactNode, data?: ExternalToast) => {\n    return this.create({ ...data, message, type: 'error' });\n  };\n\n  success = (message: titleT | React.ReactNode, data?: ExternalToast) => {\n    return this.create({ ...data, type: 'success', message });\n  };\n\n  info = (message: titleT | React.ReactNode, data?: ExternalToast) => {\n    return this.create({ ...data, type: 'info', message });\n  };\n\n  warning = (message: titleT | React.ReactNode, data?: ExternalToast) => {\n    return this.create({ ...data, type: 'warning', message });\n  };\n\n  loading = (message: titleT | React.ReactNode, data?: ExternalToast) => {\n    return this.create({ ...data, type: 'loading', message });\n  };\n\n  promise = <ToastData>(promise: PromiseT<ToastData>, data?: PromiseData<ToastData>) => {\n    if (!data) {\n      // Nothing to show\n      return;\n    }\n\n    let id: string | number | undefined = undefined;\n    if (data.loading !== undefined) {\n      id = this.create({\n        ...data,\n        promise,\n        type: 'loading',\n        message: data.loading,\n        description: typeof data.description !== 'function' ? data.description : undefined,\n      });\n    }\n\n    const p = promise instanceof Promise ? promise : promise();\n\n    let shouldDismiss = id !== undefined;\n    let result: ['resolve', ToastData] | ['reject', unknown];\n\n    const originalPromise = p\n      .then(async (response) => {\n        result = ['resolve', response];\n        const isReactElementResponse = React.isValidElement(response);\n        if (isReactElementResponse) {\n          shouldDismiss = false;\n          this.create({ id, type: 'default', message: response });\n        } else if (isHttpResponse(response) && !response.ok) {\n          shouldDismiss = false;\n          const message =\n            typeof data.error === 'function' ? await data.error(`HTTP error! status: ${response.status}`) : data.error;\n          const description =\n            typeof data.description === 'function'\n              ? await data.description(`HTTP error! status: ${response.status}`)\n              : data.description;\n          this.create({ id, type: 'error', message, description });\n        } else if (data.success !== undefined) {\n          shouldDismiss = false;\n          const message = typeof data.success === 'function' ? await data.success(response) : data.success;\n          const description =\n            typeof data.description === 'function' ? await data.description(response) : data.description;\n          this.create({ id, type: 'success', message, description });\n        }\n      })\n      .catch(async (error) => {\n        result = ['reject', error];\n        if (data.error !== undefined) {\n          shouldDismiss = false;\n          const message = typeof data.error === 'function' ? await data.error(error) : data.error;\n          const description = typeof data.description === 'function' ? await data.description(error) : data.description;\n          this.create({ id, type: 'error', message, description });\n        }\n      })\n      .finally(() => {\n        if (shouldDismiss) {\n          // Toast is still in load state (and will be indefinitely — dismiss it)\n          this.dismiss(id);\n          id = undefined;\n        }\n\n        data.finally?.();\n      });\n\n    const unwrap = () =>\n      new Promise<ToastData>((resolve, reject) =>\n        originalPromise.then(() => (result[0] === 'reject' ? reject(result[1]) : resolve(result[1]))).catch(reject),\n      );\n\n    if (typeof id !== 'string' && typeof id !== 'number') {\n      // cannot Object.assign on undefined\n      return { unwrap };\n    } else {\n      return Object.assign(id, { unwrap });\n    }\n  };\n\n  custom = (jsx: (id: number | string) => React.ReactElement, data?: ExternalToast) => {\n    const id = data?.id || toastsCounter++;\n    this.create({ jsx: jsx(id), id, ...data });\n    return id;\n  };\n\n  getActiveToasts = () => {\n    return this.toasts.filter((toast) => !this.dismissedToasts.has(toast.id));\n  };\n}\n\nexport const ToastState = new Observer();\n\n// bind this to the toast function\nconst toastFunction = (message: titleT, data?: ExternalToast) => {\n  const id = data?.id || toastsCounter++;\n\n  ToastState.addToast({\n    title: message,\n    ...data,\n    id,\n  });\n  return id;\n};\n\nconst isHttpResponse = (data: any): data is Response => {\n  return (\n    data &&\n    typeof data === 'object' &&\n    'ok' in data &&\n    typeof data.ok === 'boolean' &&\n    'status' in data &&\n    typeof data.status === 'number'\n  );\n};\n\nconst basicToast = toastFunction;\n\nconst getHistory = () => ToastState.toasts;\nconst getToasts = () => ToastState.getActiveToasts();\n\n// We use `Object.assign` to maintain the correct types as we would lose them otherwise\nexport const toast = Object.assign(\n  basicToast,\n  {\n    success: ToastState.success,\n    info: ToastState.info,\n    warning: ToastState.warning,\n    error: ToastState.error,\n    custom: ToastState.custom,\n    message: ToastState.message,\n    promise: ToastState.promise,\n    dismiss: ToastState.dismiss,\n    loading: ToastState.loading,\n  },\n  { getHistory, getToasts },\n);\n", "\n          export default function styleInject(css, { insertAt } = {}) {\n            if (!css || typeof document === 'undefined') return\n          \n            const head = document.head || document.getElementsByTagName('head')[0]\n            const style = document.createElement('style')\n            style.type = 'text/css'\n          \n            if (insertAt === 'top') {\n              if (head.firstChild) {\n                head.insertBefore(style, head.firstChild)\n              } else {\n                head.appendChild(style)\n              }\n            } else {\n              head.appendChild(style)\n            }\n          \n            if (style.styleSheet) {\n              style.styleSheet.cssText = css\n            } else {\n              style.appendChild(document.createTextNode(css))\n            }\n          }\n          ", "import styleInject from '#style-inject';styleInject(\":where(html[dir=\\\"ltr\\\"]),:where([data-sonner-toaster][dir=\\\"ltr\\\"]){--toast-icon-margin-start: -3px;--toast-icon-margin-end: 4px;--toast-svg-margin-start: -1px;--toast-svg-margin-end: 0px;--toast-button-margin-start: auto;--toast-button-margin-end: 0;--toast-close-button-start: 0;--toast-close-button-end: unset;--toast-close-button-transform: translate(-35%, -35%)}:where(html[dir=\\\"rtl\\\"]),:where([data-sonner-toaster][dir=\\\"rtl\\\"]){--toast-icon-margin-start: 4px;--toast-icon-margin-end: -3px;--toast-svg-margin-start: 0px;--toast-svg-margin-end: -1px;--toast-button-margin-start: 0;--toast-button-margin-end: auto;--toast-close-button-start: unset;--toast-close-button-end: 0;--toast-close-button-transform: translate(35%, -35%)}:where([data-sonner-toaster]){position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1: hsl(0, 0%, 99%);--gray2: hsl(0, 0%, 97.3%);--gray3: hsl(0, 0%, 95.1%);--gray4: hsl(0, 0%, 93%);--gray5: hsl(0, 0%, 90.9%);--gray6: hsl(0, 0%, 88.7%);--gray7: hsl(0, 0%, 85.8%);--gray8: hsl(0, 0%, 78%);--gray9: hsl(0, 0%, 56.1%);--gray10: hsl(0, 0%, 52.3%);--gray11: hsl(0, 0%, 43.5%);--gray12: hsl(0, 0%, 9%);--border-radius: 8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:none;z-index:999999999;transition:transform .4s ease}:where([data-sonner-toaster][data-lifted=\\\"true\\\"]){transform:translateY(-10px)}@media (hover: none) and (pointer: coarse){:where([data-sonner-toaster][data-lifted=\\\"true\\\"]){transform:none}}:where([data-sonner-toaster][data-x-position=\\\"right\\\"]){right:var(--offset-right)}:where([data-sonner-toaster][data-x-position=\\\"left\\\"]){left:var(--offset-left)}:where([data-sonner-toaster][data-x-position=\\\"center\\\"]){left:50%;transform:translate(-50%)}:where([data-sonner-toaster][data-y-position=\\\"top\\\"]){top:var(--offset-top)}:where([data-sonner-toaster][data-y-position=\\\"bottom\\\"]){bottom:var(--offset-bottom)}:where([data-sonner-toast]){--y: translateY(100%);--lift-amount: calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);filter:blur(0);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:none;overflow-wrap:anywhere}:where([data-sonner-toast][data-styled=\\\"true\\\"]){padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px #0000001a;width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}:where([data-sonner-toast]:focus-visible){box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast][data-y-position=\\\"top\\\"]){top:0;--y: translateY(-100%);--lift: 1;--lift-amount: calc(1 * var(--gap))}:where([data-sonner-toast][data-y-position=\\\"bottom\\\"]){bottom:0;--y: translateY(100%);--lift: -1;--lift-amount: calc(var(--lift) * var(--gap))}:where([data-sonner-toast]) :where([data-description]){font-weight:400;line-height:1.4;color:inherit}:where([data-sonner-toast]) :where([data-title]){font-weight:500;line-height:1.5;color:inherit}:where([data-sonner-toast]) :where([data-icon]){display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}:where([data-sonner-toast][data-promise=\\\"true\\\"]) :where([data-icon])>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}:where([data-sonner-toast]) :where([data-icon])>*{flex-shrink:0}:where([data-sonner-toast]) :where([data-icon]) svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}:where([data-sonner-toast]) :where([data-content]){display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;cursor:pointer;outline:none;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}:where([data-sonner-toast]) :where([data-button]):focus-visible{box-shadow:0 0 0 2px #0006}:where([data-sonner-toast]) :where([data-button]):first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}:where([data-sonner-toast]) :where([data-cancel]){color:var(--normal-text);background:rgba(0,0,0,.08)}:where([data-sonner-toast][data-theme=\\\"dark\\\"]) :where([data-cancel]){background:rgba(255,255,255,.3)}:where([data-sonner-toast]) :where([data-close-button]){position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;color:var(--gray12);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}[data-sonner-toast] [data-close-button]{background:var(--gray1)}:where([data-sonner-toast]) :where([data-close-button]):focus-visible{box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast]) :where([data-disabled=\\\"true\\\"]){cursor:not-allowed}:where([data-sonner-toast]):hover :where([data-close-button]):hover{background:var(--gray2);border-color:var(--gray5)}:where([data-sonner-toast][data-swiping=\\\"true\\\"]):before{content:\\\"\\\";position:absolute;left:-50%;right:-50%;height:100%;z-index:-1}:where([data-sonner-toast][data-y-position=\\\"top\\\"][data-swiping=\\\"true\\\"]):before{bottom:50%;transform:scaleY(3) translateY(50%)}:where([data-sonner-toast][data-y-position=\\\"bottom\\\"][data-swiping=\\\"true\\\"]):before{top:50%;transform:scaleY(3) translateY(-50%)}:where([data-sonner-toast][data-swiping=\\\"false\\\"][data-removed=\\\"true\\\"]):before{content:\\\"\\\";position:absolute;inset:0;transform:scaleY(2)}:where([data-sonner-toast]):after{content:\\\"\\\";position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}:where([data-sonner-toast][data-mounted=\\\"true\\\"]){--y: translateY(0);opacity:1}:where([data-sonner-toast][data-expanded=\\\"false\\\"][data-front=\\\"false\\\"]){--scale: var(--toasts-before) * .05 + 1;--y: translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}:where([data-sonner-toast])>*{transition:opacity .4s}:where([data-sonner-toast][data-expanded=\\\"false\\\"][data-front=\\\"false\\\"][data-styled=\\\"true\\\"])>*{opacity:0}:where([data-sonner-toast][data-visible=\\\"false\\\"]){opacity:0;pointer-events:none}:where([data-sonner-toast][data-mounted=\\\"true\\\"][data-expanded=\\\"true\\\"]){--y: translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}:where([data-sonner-toast][data-removed=\\\"true\\\"][data-front=\\\"true\\\"][data-swipe-out=\\\"false\\\"]){--y: translateY(calc(var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed=\\\"true\\\"][data-front=\\\"false\\\"][data-swipe-out=\\\"false\\\"][data-expanded=\\\"true\\\"]){--y: translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed=\\\"true\\\"][data-front=\\\"false\\\"][data-swipe-out=\\\"false\\\"][data-expanded=\\\"false\\\"]){--y: translateY(40%);opacity:0;transition:transform .5s,opacity .2s}:where([data-sonner-toast][data-removed=\\\"true\\\"][data-front=\\\"false\\\"]):before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount-y, 0px)) translate(var(--swipe-amount-x, 0px));transition:none}[data-sonner-toast][data-swiped=true]{user-select:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation-duration:.2s;animation-timing-function:ease-out;animation-fill-mode:forwards}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=left]{animation-name:swipe-out-left}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=right]{animation-name:swipe-out-right}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=up]{animation-name:swipe-out-up}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=down]{animation-name:swipe-out-down}@keyframes swipe-out-left{0%{transform:var(--y) translate(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translate(calc(var(--swipe-amount-x) - 100%));opacity:0}}@keyframes swipe-out-right{0%{transform:var(--y) translate(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translate(calc(var(--swipe-amount-x) + 100%));opacity:0}}@keyframes swipe-out-up{0%{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) - 100%));opacity:0}}@keyframes swipe-out-down{0%{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) + 100%));opacity:0}}@media (max-width: 600px){[data-sonner-toaster]{position:fixed;right:var(--mobile-offset-right);left:var(--mobile-offset-left);width:100%}[data-sonner-toaster][dir=rtl]{left:calc(var(--mobile-offset-left) * -1)}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset-left) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset-left)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--mobile-offset-bottom)}[data-sonner-toaster][data-y-position=top]{top:var(--mobile-offset-top)}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset-left);right:var(--mobile-offset-right);transform:none}}[data-sonner-toaster][data-theme=light]{--normal-bg: #fff;--normal-border: var(--gray4);--normal-text: var(--gray12);--success-bg: hsl(143, 85%, 96%);--success-border: hsl(145, 92%, 91%);--success-text: hsl(140, 100%, 27%);--info-bg: hsl(208, 100%, 97%);--info-border: hsl(221, 91%, 91%);--info-text: hsl(210, 92%, 45%);--warning-bg: hsl(49, 100%, 97%);--warning-border: hsl(49, 91%, 91%);--warning-text: hsl(31, 92%, 45%);--error-bg: hsl(359, 100%, 97%);--error-border: hsl(359, 100%, 94%);--error-text: hsl(360, 100%, 45%)}[data-sonner-toaster][data-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg: #000;--normal-border: hsl(0, 0%, 20%);--normal-text: var(--gray1)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg: #fff;--normal-border: var(--gray3);--normal-text: var(--gray12)}[data-sonner-toaster][data-theme=dark]{--normal-bg: #000;--normal-bg-hover: hsl(0, 0%, 12%);--normal-border: hsl(0, 0%, 20%);--normal-border-hover: hsl(0, 0%, 25%);--normal-text: var(--gray1);--success-bg: hsl(150, 100%, 6%);--success-border: hsl(147, 100%, 12%);--success-text: hsl(150, 86%, 65%);--info-bg: hsl(215, 100%, 6%);--info-border: hsl(223, 100%, 12%);--info-text: hsl(216, 87%, 65%);--warning-bg: hsl(64, 100%, 6%);--warning-border: hsl(60, 100%, 12%);--warning-text: hsl(46, 87%, 65%);--error-bg: hsl(358, 76%, 10%);--error-border: hsl(357, 89%, 16%);--error-text: hsl(358, 100%, 81%)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast] [data-close-button]{background:var(--normal-bg);border-color:var(--normal-border);color:var(--normal-text)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast] [data-close-button]:hover{background:var(--normal-bg-hover);border-color:var(--normal-border-hover)}[data-rich-colors=true][data-sonner-toast][data-type=success],[data-rich-colors=true][data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=info],[data-rich-colors=true][data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning],[data-rich-colors=true][data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=error],[data-rich-colors=true][data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size: 16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:nth-child(1){animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}to{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}to{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}to{opacity:.15}}@media (prefers-reduced-motion){[data-sonner-toast],[data-sonner-toast]>*,.sonner-loading-bar{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}\\n\")", "import React from 'react';\n\nexport type ToastTypes = 'normal' | 'action' | 'success' | 'info' | 'warning' | 'error' | 'loading' | 'default';\n\nexport type PromiseT<Data = any> = Promise<Data> | (() => Promise<Data>);\n\nexport type PromiseTResult<Data = any> =\n  | string\n  | React.ReactNode\n  | ((data: Data) => React.ReactNode | string | Promise<React.ReactNode | string>);\n\nexport type PromiseExternalToast = Omit<ExternalToast, 'description'>;\n\nexport type PromiseData<ToastData = any> = PromiseExternalToast & {\n  loading?: string | React.ReactNode;\n  success?: PromiseTResult<ToastData>;\n  error?: PromiseTResult;\n  description?: PromiseTResult;\n  finally?: () => void | Promise<void>;\n};\n\nexport interface ToastClassnames {\n  toast?: string;\n  title?: string;\n  description?: string;\n  loader?: string;\n  closeButton?: string;\n  cancelButton?: string;\n  actionButton?: string;\n  success?: string;\n  error?: string;\n  info?: string;\n  warning?: string;\n  loading?: string;\n  default?: string;\n  content?: string;\n  icon?: string;\n}\n\nexport interface ToastIcons {\n  success?: React.ReactNode;\n  info?: React.ReactNode;\n  warning?: React.ReactNode;\n  error?: React.ReactNode;\n  loading?: React.ReactNode;\n  close?: React.ReactNode;\n}\n\nexport interface Action {\n  label: React.ReactNode;\n  onClick: (event: React.MouseEvent<HTMLButtonElement, MouseEvent>) => void;\n  actionButtonStyle?: React.CSSProperties;\n}\n\nexport interface ToastT {\n  id: number | string;\n  title?: (() => React.ReactNode) | React.ReactNode;\n  type?: ToastTypes;\n  icon?: React.ReactNode;\n  jsx?: React.ReactNode;\n  richColors?: boolean;\n  invert?: boolean;\n  closeButton?: boolean;\n  dismissible?: boolean;\n  description?: (() => React.ReactNode) | React.ReactNode;\n  duration?: number;\n  delete?: boolean;\n  action?: Action | React.ReactNode;\n  cancel?: Action | React.ReactNode;\n  onDismiss?: (toast: ToastT) => void;\n  onAutoClose?: (toast: ToastT) => void;\n  promise?: PromiseT;\n  cancelButtonStyle?: React.CSSProperties;\n  actionButtonStyle?: React.CSSProperties;\n  style?: React.CSSProperties;\n  unstyled?: boolean;\n  className?: string;\n  classNames?: ToastClassnames;\n  descriptionClassName?: string;\n  position?: Position;\n}\n\nexport function isAction(action: Action | React.ReactNode): action is Action {\n  return (action as Action).label !== undefined;\n}\n\nexport type Position = 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right' | 'top-center' | 'bottom-center';\nexport interface HeightT {\n  height: number;\n  toastId: number | string;\n  position: Position;\n}\n\ninterface ToastOptions {\n  className?: string;\n  closeButton?: boolean;\n  descriptionClassName?: string;\n  style?: React.CSSProperties;\n  cancelButtonStyle?: React.CSSProperties;\n  actionButtonStyle?: React.CSSProperties;\n  duration?: number;\n  unstyled?: boolean;\n  classNames?: ToastClassnames;\n}\n\ntype Offset =\n  | {\n      top?: string | number;\n      right?: string | number;\n      bottom?: string | number;\n      left?: string | number;\n    }\n  | string\n  | number;\n\nexport interface ToasterProps {\n  invert?: boolean;\n  theme?: 'light' | 'dark' | 'system';\n  position?: Position;\n  hotkey?: string[];\n  richColors?: boolean;\n  expand?: boolean;\n  duration?: number;\n  gap?: number;\n  visibleToasts?: number;\n  closeButton?: boolean;\n  toastOptions?: ToastOptions;\n  className?: string;\n  style?: React.CSSProperties;\n  offset?: Offset;\n  mobileOffset?: Offset;\n  dir?: 'rtl' | 'ltr' | 'auto';\n  swipeDirections?: SwipeDirection[];\n  /**\n   * @deprecated Please use the `icons` prop instead:\n   * ```jsx\n   * <Toaster\n   *   icons={{ loading: <LoadingIcon /> }}\n   * />\n   * ```\n   */\n  loadingIcon?: React.ReactNode;\n  icons?: ToastIcons;\n  containerAriaLabel?: string;\n  pauseWhenPageIsHidden?: boolean;\n}\n\nexport type SwipeDirection = 'top' | 'right' | 'bottom' | 'left';\n\nexport interface ToastProps {\n  toast: ToastT;\n  toasts: ToastT[];\n  index: number;\n  swipeDirections?: SwipeDirection[];\n  expanded: boolean;\n  invert: boolean;\n  heights: HeightT[];\n  setHeights: React.Dispatch<React.SetStateAction<HeightT[]>>;\n  removeToast: (toast: ToastT) => void;\n  gap?: number;\n  position: Position;\n  visibleToasts: number;\n  expandByDefault: boolean;\n  closeButton: boolean;\n  interacting: boolean;\n  style?: React.CSSProperties;\n  cancelButtonStyle?: React.CSSProperties;\n  actionButtonStyle?: React.CSSProperties;\n  duration?: number;\n  className?: string;\n  unstyled?: boolean;\n  descriptionClassName?: string;\n  loadingIcon?: React.ReactNode;\n  classNames?: ToastClassnames;\n  icons?: ToastIcons;\n  closeButtonAriaLabel?: string;\n  pauseWhenPageIsHidden: boolean;\n  defaultRichColors?: boolean;\n}\n\nexport enum SwipeStateTypes {\n  SwipedOut = 'SwipedOut',\n  SwipedBack = 'SwipedBack',\n  NotSwiped = 'NotSwiped',\n}\n\nexport type Theme = 'light' | 'dark';\n\nexport interface ToastToDismiss {\n  id: number | string;\n  dismiss: boolean;\n}\n\nexport type ExternalToast = Omit<ToastT, 'id' | 'type' | 'title' | 'jsx' | 'delete' | 'promise'> & {\n  id?: number | string;\n};\n"], "names": ["React", "forwardRef", "isValidElement", "ReactDOM", "React", "getAsset", "type", "SuccessIcon", "InfoIcon", "WarningIcon", "ErrorIcon", "bars", "Loader", "visible", "className", "_", "i", "CloseIcon", "React", "useIsDocumentHidden", "isDocumentHidden", "setIsDocumentHidden", "callback", "React", "toastsCounter", "Observer", "subscriber", "index", "data", "_a", "message", "rest", "id", "alreadyExists", "toast", "dismissible", "promise", "p", "<PERSON><PERSON><PERSON><PERSON>", "result", "originalPromise", "response", "isHttpResponse", "description", "error", "unwrap", "resolve", "reject", "jsx", "ToastState", "toastFunction", "basicToast", "getHistory", "getToasts", "styleInject", "css", "insertAt", "head", "style", "styleInject", "isAction", "action", "VISIBLE_TOASTS_AMOUNT", "VIEWPORT_OFFSET", "MOBILE_VIEWPORT_OFFSET", "TOAST_LIFETIME", "TOAST_WIDTH", "GAP", "SWIPE_THRESHOLD", "TIME_BEFORE_UNMOUNT", "cn", "classes", "getDefaultSwipeDirections", "position", "y", "x", "directions", "Toast", "props", "_a", "_b", "_c", "_d", "_e", "_f", "_g", "_h", "_i", "_j", "_k", "ToasterInvert", "toast", "unstyled", "interacting", "setHeights", "visibleToasts", "heights", "index", "toasts", "expanded", "removeToast", "defaultRichColors", "closeButtonFromToaster", "style", "cancelButtonStyle", "actionButtonStyle", "className", "descriptionClassName", "durationFromToaster", "gap", "loadingIconProp", "expandByDefault", "classNames", "icons", "closeButtonAriaLabel", "pauseWhenPageIsHidden", "swipeDirection", "setSwipeDirection", "React", "swipeOutDirection", "setSwipeOutDirection", "mounted", "setMounted", "removed", "setRemoved", "swiping", "setSwiping", "swipeOut", "setSwipeOut", "isSwiped", "setIsSwiped", "offsetBeforeRemove", "setOffsetBeforeRemove", "initialHeight", "setInitialHeight", "remainingTime", "dragStartTime", "toastRef", "isFront", "isVisible", "toastType", "dismissible", "toastClassname", "toastDescriptionClassname", "heightIndex", "height", "closeButton", "duration", "closeTimerStartTimeRef", "offset", "lastCloseTimerStartTimeRef", "pointerStartRef", "toastsHeightBefore", "prev", "curr", "reducerIndex", "isDocumentHidden", "useIsDocumentHidden", "invert", "disabled", "toastNode", "h", "originalHeight", "newHeight", "deleteToast", "timeoutId", "elapsedTime", "getLoadingIcon", "Loader", "event", "swipeAmountX", "swipeAmountY", "timeTaken", "swipeAmount", "velocity", "y<PERSON><PERSON><PERSON>", "xDelta", "swipeDirections", "CloseIcon", "isValidElement", "getAsset", "isAction", "getDocumentDirection", "dirAttribute", "assignOffset", "defaultOffset", "mobileOffset", "styles", "isMobile", "prefix", "defaultValue", "assignAll", "key", "useSonner", "activeToasts", "setActiveToasts", "ToastState", "ReactDOM", "t", "indexOfExistingToast", "Toaster", "forwardRef", "ref", "hotkey", "expand", "theme", "richColors", "toastOptions", "dir", "loadingIcon", "containerAriaLabel", "setToasts", "possiblePositions", "setExpanded", "setInteracting", "actualTheme", "setActualTheme", "listRef", "hotkeyLabel", "lastFocusedElementRef", "isFocusWithinRef", "toast<PERSON>oRemove", "id", "darkMediaQuery", "matches", "error", "e", "handleKeyDown"], "mappings": ";;;;;AAEA,OAAOA,GAAS,cAAAC,GAAY,kBAAAC,OAAsB;AAClD,OAAOC,OAAc,YCFrB,OAAOC,MAAW;;;;;AAGX,IAAMC,KAAYC,GAAyC;IAChE,OAAQA,EAAM;QACZ,KAAK;YACH,OAAOC;QAET,KAAK;YACH,OAAOC;QAET,KAAK;YACH,OAAOC;QAET,KAAK;YACH,OAAOC;QAET;YACE,OAAO;IACX;AACF,GAEMC,KAAO,MAAM,EAAE,EAAE,IAAA,CAAK,CAAC,GAEhBC,KAAS,CAAC,EAAE,SAAAC,CAAAA,EAAS,WAAAC,CAAU,EAAA,yMAExCV,UAAAA,CAAA,aAAA,CAAC,OAAA;QAAI,WAAW;YAAC;YAA0BU,CAAS;SAAA,CAAE,MAAA,CAAO,OAAO,EAAE,IAAA,CAAK,GAAG;QAAG,gBAAcD;IAAAA,yMAC7FT,UAAAA,CAAA,aAAA,CAAC,OAAA;QAAI,WAAU;IAAA,GACZO,GAAK,GAAA,CAAI,CAACI,GAAGC,yMACZZ,WAAAA,CAAA,aAAA,CAAC,OAAA;YAAI,WAAU;YAAqB,KAAK,CAAA,YAAA,EAAeY,GAAAA,CAAK,CAC9D,CACH,CACF;QAH8DA,OAO5DT,KACJH,gNAAAA,CAAA,aAAA,CAAC,OAAA;IAAI,OAAM;IAA6B,SAAQ;IAAY,MAAK;IAAe,QAAO;IAAK,OAAM;AAAA,yMAChGA,UAAAA,CAAA,aAAA,CAAC,QAAA;IACC,UAAS;IACT,GAAE;IACF,UAAS;AAAA,CACX,CACF,GAGIK,2MACJL,UAAAA,CAAA,aAAA,CAAC,OAAA;IAAI,OAAM;IAA6B,SAAQ;IAAY,MAAK;IAAe,QAAO;IAAK,OAAM;AAAA,yMAChGA,UAAAA,CAAA,aAAA,CAAC,QAAA;IACC,UAAS;IACT,GAAE;IACF,UAAS;AAAA,CACX,CACF,GAGII,2MACJJ,UAAAA,CAAA,aAAA,CAAC,OAAA;IAAI,OAAM;IAA6B,SAAQ;IAAY,MAAK;IAAe,QAAO;IAAK,OAAM;AAAA,yMAChGA,UAAAA,CAAA,aAAA,CAAC,QAAA;IACC,UAAS;IACT,GAAE;IACF,UAAS;AAAA,CACX,CACF,GAGIM,2MACJN,UAAAA,CAAA,aAAA,CAAC,OAAA;IAAI,OAAM;IAA6B,SAAQ;IAAY,MAAK;IAAe,QAAO;IAAK,OAAM;AAAA,yMAChGA,UAAAA,CAAA,aAAA,CAAC,QAAA;IACC,UAAS;IACT,GAAE;IACF,UAAS;AAAA,CACX,CACF,GAGWa,2MACXb,UAAAA,CAAA,aAAA,CAAC,OAAA;IACC,OAAM;IACN,OAAM;IACN,QAAO;IACP,SAAQ;IACR,MAAK;IACL,QAAO;IACP,aAAY;IACZ,eAAc;IACd,gBAAe;AAAA,yMAEfA,UAAAA,CAAA,aAAA,CAAC,QAAA;IAAK,IAAG;IAAK,IAAG;IAAI,IAAG;IAAI,IAAG;AAAA,CAAK,wMACpCA,WAAAA,CAAA,aAAA,CAAC,QAAA;IAAK,IAAG;IAAI,IAAG;IAAI,IAAG;IAAK,IAAG;AAAA,CAAK,CACtC,EC3FF,OAAOc,OAAW;;AAEX,IAAMC,KAAsB,IAAM;IACvC,IAAM,CAACC,GAAkBC,CAAmB,CAAA,yMAAIH,UAAAA,CAAM,QAAA,CAAS,SAAS,MAAM;IAE9E,6MAAAA,UAAAA,CAAM,SAAA,CAAU,IAAM;QACpB,IAAMI,IAAW,IAAM;YACrBD,EAAoB,SAAS,MAAM;QACrC;QACA,OAAA,SAAS,gBAAA,CAAiB,oBAAoBC,CAAQ,GAC/C,IAAM,OAAO,mBAAA,CAAoB,oBAAoBA,CAAQ;IACtE,GAAG,CAAC,CAAC,GAEEF;AACT,ECZA,OAAOG,OAAW;;AAElB,IAAIC,KAAgB,GAIdC,KAAN,KAAe;IAKb,aAAc;QAOd,IAAA,CAAA,SAAA,IAAaC,IAAAA,CACX,IAAA,CAAK,WAAA,CAAY,IAAA,CAAKA,CAAU,GAEzB,IAAM;gBACX,IAAMC,IAAQ,IAAA,CAAK,WAAA,CAAY,OAAA,CAAQD,CAAU;gBACjD,IAAA,CAAK,WAAA,CAAY,MAAA,CAAOC,GAAO,CAAC;YAClC,CAAA;QAGF,IAAA,CAAA,OAAA,IAAWC,GAAiB;YAC1B,IAAA,CAAK,WAAA,CAAY,OAAA,EAASF,IAAeA,EAAWE,CAAI,CAAC;QAC3D;QAEA,IAAA,CAAA,QAAA,GAAYA,GAAiB;YAC3B,IAAA,CAAK,OAAA,CAAQA,CAAI,GACjB,IAAA,CAAK,MAAA,GAAS,CAAC;mBAAG,IAAA,CAAK,MAAA;gBAAQA,CAAI;;QACrC;QAEA,IAAA,CAAA,MAAA,IACEA,GAMG;YA7CP,IAAAC;YA8CI,IAAM,EAAE,SAAAC,CAAAA,EAAS,GAAGC,CAAK,EAAA,GAAIH,GACvBI,IAAK,OAAA,CAAOJ,KAAA,OAAA,KAAA,IAAAA,EAAM,EAAA,KAAO,YAAA,CAAA,CAAYC,IAAAD,EAAK,EAAA,KAAL,OAAA,KAAA,IAAAC,EAAS,MAAA,IAAS,IAAID,EAAK,EAAA,GAAKJ,MACrES,IAAgB,IAAA,CAAK,MAAA,CAAO,IAAA,EAAMC,IAC/BA,EAAM,EAAA,KAAOF,CACrB,GACKG,IAAcP,EAAK,WAAA,KAAgB,KAAA,IAAY,CAAA,IAAOA,EAAK,WAAA;YAEjE,OAAI,IAAA,CAAK,eAAA,CAAgB,GAAA,CAAII,CAAE,KAC7B,IAAA,CAAK,eAAA,CAAgB,MAAA,CAAOA,CAAE,GAG5BC,IACF,IAAA,CAAK,MAAA,GAAS,IAAA,CAAK,MAAA,CAAO,GAAA,CAAKC,KACzBA,EAAM,EAAA,KAAOF,IAAAA,CACf,IAAA,CAAK,OAAA,CAAQ;oBAAE,GAAGE,CAAAA;oBAAO,GAAGN,CAAAA;oBAAM,IAAAI;oBAAI,OAAOF;gBAAQ,CAAC,GAC/C;oBACL,GAAGI,CAAAA;oBACH,GAAGN,CAAAA;oBACH,IAAAI;oBACA,aAAAG;oBACA,OAAOL;gBACT,CAAA,IAGKI,CACR,IAED,IAAA,CAAK,QAAA,CAAS;gBAAE,OAAOJ;gBAAS,GAAGC,CAAAA;gBAAM,aAAAI;gBAAa,IAAAH;YAAG,CAAC,GAGrDA;QACT;QAEA,IAAA,CAAA,OAAA,IAAWA,IAAAA,CACT,IAAA,CAAK,eAAA,CAAgB,GAAA,CAAIA,CAAE,GAEtBA,KACH,IAAA,CAAK,MAAA,CAAO,OAAA,EAASE,GAAU;gBAC7B,IAAA,CAAK,WAAA,CAAY,OAAA,EAASR,IAAeA,EAAW;wBAAE,IAAIQ,EAAM,EAAA;wBAAI,SAAS,CAAA;oBAAK,CAAC,CAAC;YACtF,CAAC,GAEH,IAAA,CAAK,WAAA,CAAY,OAAA,EAASR,IAAeA,EAAW;oBAAE,IAAAM;oBAAI,SAAS,CAAA;gBAAK,CAAC,CAAC,GACnEA,CAAAA;QAGT,IAAA,CAAA,OAAA,GAAU,CAACF,GAAmCF,IACrC,IAAA,CAAK,MAAA,CAAO;gBAAE,GAAGA,CAAAA;gBAAM,SAAAE;YAAQ,CAAC;QAGzC,IAAA,CAAA,KAAA,GAAQ,CAACA,GAAmCF,IACnC,IAAA,CAAK,MAAA,CAAO;gBAAE,GAAGA,CAAAA;gBAAM,SAAAE;gBAAS,MAAM;YAAQ,CAAC;QAGxD,IAAA,CAAA,OAAA,GAAU,CAACA,GAAmCF,IACrC,IAAA,CAAK,MAAA,CAAO;gBAAE,GAAGA,CAAAA;gBAAM,MAAM;gBAAW,SAAAE;YAAQ,CAAC;QAG1D,IAAA,CAAA,IAAA,GAAO,CAACA,GAAmCF,IAClC,IAAA,CAAK,MAAA,CAAO;gBAAE,GAAGA,CAAAA;gBAAM,MAAM;gBAAQ,SAAAE;YAAQ,CAAC;QAGvD,IAAA,CAAA,OAAA,GAAU,CAACA,GAAmCF,IACrC,IAAA,CAAK,MAAA,CAAO;gBAAE,GAAGA,CAAAA;gBAAM,MAAM;gBAAW,SAAAE;YAAQ,CAAC;QAG1D,IAAA,CAAA,OAAA,GAAU,CAACA,GAAmCF,IACrC,IAAA,CAAK,MAAA,CAAO;gBAAE,GAAGA,CAAAA;gBAAM,MAAM;gBAAW,SAAAE;YAAQ,CAAC;QAG1D,IAAA,CAAA,OAAA,GAAU,CAAYM,GAA8BR,IAAkC;YACpF,IAAI,CAACA,GAEH;YAGF,IAAII;YACAJ,EAAK,OAAA,KAAY,KAAA,KAAA,CACnBI,IAAK,IAAA,CAAK,MAAA,CAAO;gBACf,GAAGJ,CAAAA;gBACH,SAAAQ;gBACA,MAAM;gBACN,SAASR,EAAK,OAAA;gBACd,aAAa,OAAOA,EAAK,WAAA,IAAgB,aAAaA,EAAK,WAAA,GAAc,KAAA;YAC3E,CAAC,CAAA;YAGH,IAAMS,IAAID,aAAmB,UAAUA,IAAUA,EAAQ,GAErDE,IAAgBN,MAAO,KAAA,GACvBO,GAEEC,IAAkBH,EACrB,IAAA,CAAK,OAAOI,GAAa;gBAGxB,IAFAF,IAAS;oBAAC;oBAAWE,CAAQ;iBAAA,wMACElB,UAAAA,CAAM,cAAA,CAAekB,CAAQ,GAE1DH,IAAgB,CAAA,GAChB,IAAA,CAAK,MAAA,CAAO;oBAAE,IAAAN;oBAAI,MAAM;oBAAW,SAASS;gBAAS,CAAC;qBAAA,IAC7CC,GAAeD,CAAQ,KAAK,CAACA,EAAS,EAAA,EAAI;oBACnDH,IAAgB,CAAA;oBAChB,IAAMR,IACJ,OAAOF,EAAK,KAAA,IAAU,aAAa,MAAMA,EAAK,KAAA,CAAM,CAAA,oBAAA,EAAuBa,EAAS,MAAA,EAAQ,IAAIb,EAAK,KAAA,EACjGe,IACJ,OAAOf,EAAK,WAAA,IAAgB,aACxB,MAAMA,EAAK,WAAA,CAAY,CAAA,oBAAA,EAAuBa,EAAS,MAAA,EAAQ,IAC/Db,EAAK,WAAA;oBACX,IAAA,CAAK,MAAA,CAAO;wBAAE,IAAAI;wBAAI,MAAM;wBAAS,SAAAF;wBAAS,aAAAa;oBAAY,CAAC;gBAAA,OAAA,IAC9Cf,EAAK,OAAA,KAAY,KAAA,GAAW;oBACrCU,IAAgB,CAAA;oBAChB,IAAMR,IAAU,OAAOF,EAAK,OAAA,IAAY,aAAa,MAAMA,EAAK,OAAA,CAAQa,CAAQ,IAAIb,EAAK,OAAA,EACnFe,IACJ,OAAOf,EAAK,WAAA,IAAgB,aAAa,MAAMA,EAAK,WAAA,CAAYa,CAAQ,IAAIb,EAAK,WAAA;oBACnF,IAAA,CAAK,MAAA,CAAO;wBAAE,IAAAI;wBAAI,MAAM;wBAAW,SAAAF;wBAAS,aAAAa;oBAAY,CAAC;gBAAA;YAE7D,CAAC,EACA,KAAA,CAAM,OAAOC,GAAU;gBAEtB,IADAL,IAAS;oBAAC;oBAAUK,CAAK;iBAAA,EACrBhB,EAAK,KAAA,KAAU,KAAA,GAAW;oBAC5BU,IAAgB,CAAA;oBAChB,IAAMR,IAAU,OAAOF,EAAK,KAAA,IAAU,aAAa,MAAMA,EAAK,KAAA,CAAMgB,CAAK,IAAIhB,EAAK,KAAA,EAC5Ee,IAAc,OAAOf,EAAK,WAAA,IAAgB,aAAa,MAAMA,EAAK,WAAA,CAAYgB,CAAK,IAAIhB,EAAK,WAAA;oBAClG,IAAA,CAAK,MAAA,CAAO;wBAAE,IAAAI;wBAAI,MAAM;wBAAS,SAAAF;wBAAS,aAAAa;oBAAY,CAAC;gBAAA;YAE3D,CAAC,EACA,OAAA,CAAQ,IAAM;gBA1KrB,IAAAd;gBA2KYS,KAAAA,CAEF,IAAA,CAAK,OAAA,CAAQN,CAAE,GACfA,IAAK,KAAA,CAAA,GAAA,CAGPH,IAAAD,EAAK,OAAA,KAAL,QAAAC,EAAA,IAAA,CAAAD;YACF,CAAC,GAEGiB,IAAS,IACb,IAAI,QAAmB,CAACC,GAASC,IAC/BP,EAAgB,IAAA,CAAK,IAAOD,CAAAA,CAAO,CAAC,CAAA,KAAM,WAAWQ,EAAOR,CAAAA,CAAO,CAAC,CAAC,IAAIO,EAAQP,CAAAA,CAAO,CAAC,CAAC,CAAE,EAAE,KAAA,CAAMQ,CAAM,CAC5G;YAEF,OAAI,OAAOf,KAAO,YAAY,OAAOA,KAAO,WAEnC;gBAAE,QAAAa;YAAO,IAET,OAAO,MAAA,CAAOb,GAAI;gBAAE,QAAAa;YAAO,CAAC;QAEvC;QAEA,IAAA,CAAA,MAAA,GAAS,CAACG,GAAkDpB,IAAyB;YACnF,IAAMI,IAAAA,CAAKJ,KAAA,OAAA,KAAA,IAAAA,EAAM,EAAA,KAAMJ;YACvB,OAAA,IAAA,CAAK,MAAA,CAAO;gBAAE,KAAKwB,EAAIhB,CAAE;gBAAG,IAAAA;gBAAI,GAAGJ,CAAK,CAAC;gBAClCI;QACT;QAEA,IAAA,CAAA,eAAA,GAAkB,IACT,IAAA,CAAK,MAAA,CAAO,MAAA,EAAQE,IAAU,CAAC,IAAA,CAAK,eAAA,CAAgB,GAAA,CAAIA,EAAM,EAAE,CAAC;QA1LxE,IAAA,CAAK,WAAA,GAAc,CAAC,CAAA,EACpB,IAAA,CAAK,MAAA,GAAS,CAAC,CAAA,EACf,IAAA,CAAK,eAAA,GAAkB,IAAI;IAC7B;AAyLF,GAEae,IAAa,IAAIxB,IAGxByB,KAAgB,CAACpB,GAAiBF,IAAyB;IAC/D,IAAMI,IAAAA,CAAKJ,KAAA,OAAA,KAAA,IAAAA,EAAM,EAAA,KAAMJ;IAEvB,OAAAyB,EAAW,QAAA,CAAS;QAClB,OAAOnB;QACP,GAAGF,CAAAA;QACH,IAAAI;IACF,CAAC,GACMA;AACT,GAEMU,MAAkBd,IAEpBA,KACA,OAAOA,KAAS,YAChB,QAAQA,KACR,OAAOA,EAAK,EAAA,IAAO,aACnB,YAAYA,KACZ,OAAOA,EAAK,MAAA,IAAW,UAIrBuB,KAAaD,IAEbE,KAAa,IAAMH,EAAW,MAAA,EAC9BI,KAAY,IAAMJ,EAAW,eAAA,CAAgB,GAGtCf,KAAQ,OAAO,MAAA,CAC1BiB,IACA;IACE,SAASF,EAAW,OAAA;IACpB,MAAMA,EAAW,IAAA;IACjB,SAASA,EAAW,OAAA;IACpB,OAAOA,EAAW,KAAA;IAClB,QAAQA,EAAW,MAAA;IACnB,SAASA,EAAW,OAAA;IACpB,SAASA,EAAW,OAAA;IACpB,SAASA,EAAW,OAAA;IACpB,SAASA,EAAW;AACtB,GACA;IAAE,YAAAG;IAAY,WAAAC;AAAU,CAC1B;ACxPyB,SAARC,GAA6BC,CAAAA,EAAK,EAAE,UAAAC,CAAS,EAAA,GAAI,CAAC,CAAA,CAAG;IAC1D,IAAI,CAACD,KAAO,OAAO,YAAa,aAAa;IAE7C,IAAME,IAAO,SAAS,IAAA,IAAQ,SAAS,oBAAA,CAAqB,MAAM,CAAA,CAAE,CAAC,CAAA,EAC/DC,IAAQ,SAAS,aAAA,CAAc,OAAO;IAC5CA,EAAM,IAAA,GAAO,YAETF,MAAa,SACXC,EAAK,UAAA,GACPA,EAAK,YAAA,CAAaC,GAAOD,EAAK,UAAU,IAK1CA,EAAK,WAAA,CAAYC,CAAK,GAGpBA,EAAM,UAAA,GACRA,EAAM,UAAA,CAAW,OAAA,GAAUH,IAE3BG,EAAM,WAAA,CAAY,SAAS,cAAA,CAAeH,CAAG,CAAC;AAElD;ACvB8BI,GAAY,CAAA;AAAA,CAAs4c;ACkFn7c,SAASC,GAASC,CAAAA,CAAoD;IAC3E,OAAQA,EAAkB,KAAA,KAAU,KAAA;AACtC;AN/DA,IAAMC,KAAwB,GAGxBC,KAAkB,QAGlBC,KAAyB,QAGzBC,KAAiB,KAGjBC,KAAc,KAGdC,KAAM,IAGNC,KAAkB,IAGlBC,KAAsB;AAE5B,SAASC,EAAAA,GAAMC,CAAAA,CAAiC;IAC9C,OAAOA,EAAQ,MAAA,CAAO,OAAO,EAAE,IAAA,CAAK,GAAG;AACzC;AAEA,SAASC,GAA0BC,CAAAA,CAAyC;IAC1E,IAAM,CAACC,GAAGC,CAAC,CAAA,GAAIF,EAAS,KAAA,CAAM,GAAG,GAC3BG,IAAoC,CAAC,CAAA;IAE3C,OAAIF,KACFE,EAAW,IAAA,CAAKF,CAAmB,GAGjCC,KACFC,EAAW,IAAA,CAAKD,CAAmB,GAG9BC;AACT;AAEA,IAAMC,MAASC,GAAsB;IA/DrC,IAAAC,IAAAC,IAAAC,IAAAC,IAAAC,IAAAC,IAAAC,IAAAC,IAAAC,IAAAC,IAAAC;IAgEE,IAAM,EACJ,QAAQC,CAAAA,EACR,OAAAC,CAAAA,EACA,UAAAC,CAAAA,EACA,aAAAC,CAAAA,EACA,YAAAC,CAAAA,EACA,eAAAC,CAAAA,EACA,SAAAC,CAAAA,EACA,OAAAC,CAAAA,EACA,QAAAC,CAAAA,EACA,UAAAC,CAAAA,EACA,aAAAC,CAAAA,EACA,mBAAAC,CAAAA,EACA,aAAaC,EAAAA,EACb,OAAAC,EAAAA,EACA,mBAAAC,EAAAA,EACA,mBAAAC,CAAAA,EACA,WAAAC,KAAY,EAAA,EACZ,sBAAAC,KAAuB,EAAA,EACvB,UAAUC,CAAAA,EACV,UAAAnC,EAAAA,EACA,KAAAoC,EAAAA,EACA,aAAaC,EAAAA,EACb,iBAAAC,CAAAA,EACA,YAAAC,CAAAA,EACA,OAAAC,CAAAA,EACA,sBAAAC,KAAuB,aAAA,EACvB,uBAAAC,EACF,EAAA,GAAIrC,GACE,CAACsC,GAAgBC,CAAiB,CAAA,yMAAIC,UAAAA,CAAM,QAAA,CAA2B,IAAI,GAC3E,CAACC,IAAmBC,CAAoB,CAAA,yMAAIF,UAAAA,CAAM,QAAA,CAAkD,IAAI,GACxG,CAACG,GAASC,CAAU,CAAA,yMAAIJ,UAAAA,CAAM,QAAA,CAAS,CAAA,CAAK,GAC5C,CAACK,GAASC,EAAU,CAAA,yMAAIN,UAAAA,CAAM,QAAA,CAAS,CAAA,CAAK,GAC5C,CAACO,GAASC,CAAU,CAAA,yMAAIR,UAAAA,CAAM,QAAA,CAAS,CAAA,CAAK,GAC5C,CAACS,IAAUC,CAAW,CAAA,yMAAIV,UAAAA,CAAM,QAAA,CAAS,CAAA,CAAK,GAC9C,CAACW,GAAUC,CAAW,CAAA,yMAAIZ,UAAAA,CAAM,QAAA,CAAS,CAAA,CAAK,GAC9C,CAACa,GAAoBC,CAAqB,CAAA,yMAAId,UAAAA,CAAM,QAAA,CAAS,CAAC,GAC9D,CAACe,GAAeC,CAAgB,CAAA,yMAAIhB,UAAAA,CAAM,QAAA,CAAS,CAAC,GACpDiB,0MAAgBjB,UAAAA,CAAM,MAAA,CAAO3B,EAAM,QAAA,IAAYiB,KAAuB3C,EAAc,GACpFuE,IAAgBlB,gNAAAA,CAAM,MAAA,CAAoB,IAAI,GAC9CmB,0MAAWnB,UAAAA,CAAM,MAAA,CAAsB,IAAI,GAC3CoB,KAAUzC,MAAU,GACpB0C,KAAY1C,IAAQ,KAAKF,GACzB6C,IAAYjD,EAAM,IAAA,EAClBkD,IAAclD,EAAM,WAAA,KAAgB,CAAA,GACpCmD,KAAiBnD,EAAM,SAAA,IAAa,IACpCoD,KAA4BpD,EAAM,oBAAA,IAAwB,IAE1DqD,2MAAc1B,UAAAA,CAAM,OAAA,CACxB,IAAMtB,EAAQ,SAAA,EAAWiD,IAAWA,EAAO,OAAA,KAAYtD,EAAM,EAAE,KAAK,GACpE;QAACK;QAASL,EAAM,EAAE;KACpB,GACMuD,2MAAc5B,UAAAA,CAAM,OAAA,CACxB,IAAG;QArHP,IAAAvC;QAqHU,OAAA,CAAAA,IAAAY,EAAM,WAAA,KAAN,OAAAZ,IAAqBuB;IAAAA,GAC3B;QAACX,EAAM,WAAA;QAAaW,EAAsB;KAC5C,GACM6C,2MAAW7B,UAAAA,CAAM,OAAA,CACrB,IAAM3B,EAAM,QAAA,IAAYiB,KAAuB3C,IAC/C;QAAC0B,EAAM,QAAA;QAAUiB,CAAmB;KACtC,GACMwC,2MAAyB9B,UAAAA,CAAM,MAAA,CAAO,CAAC,GACvC+B,0MAAS/B,UAAAA,CAAM,MAAA,CAAO,CAAC,GACvBgC,2MAA6BhC,UAAAA,CAAM,MAAA,CAAO,CAAC,GAC3CiC,0MAAkBjC,UAAAA,CAAM,MAAA,CAAwC,IAAI,GACpE,CAAC5C,IAAGC,EAAC,CAAA,GAAIF,GAAS,KAAA,CAAM,GAAG,GAC3B+E,2MAAqBlC,UAAAA,CAAM,OAAA,CAAQ,IAChCtB,EAAQ,MAAA,CAAO,CAACyD,GAAMC,GAAMC,IAE7BA,KAAgBX,KACXS,IAGFA,IAAOC,EAAK,MAAA,EAClB,CAAC,GACH;QAAC1D;QAASgD,EAAW;KAAC,GACnBY,KAAmBC,GAAoB,GAEvCC,KAASnE,EAAM,MAAA,IAAUD,GACzBqE,KAAWnB,MAAc;IAE/BS,EAAO,OAAA,yMAAU/B,UAAAA,CAAM,OAAA,CAAQ,IAAM0B,KAAcnC,KAAM2C,IAAoB;QAACR;QAAaQ,EAAkB;KAAC,yMAE9GlC,UAAAA,CAAM,SAAA,CAAU,IAAM;QACpBiB,EAAc,OAAA,GAAUY;IAC1B,GAAG;QAACA,EAAQ;KAAC,yMAEb7B,UAAAA,CAAM,SAAA,CAAU,IAAM;QAEpBI,EAAW,CAAA,CAAI;IACjB,GAAG,CAAC,CAAC,yMAELJ,UAAAA,CAAM,SAAA,CAAU,IAAM;QACpB,IAAM0C,IAAYvB,EAAS,OAAA;QAC3B,IAAIuB,GAAW;YACb,IAAMf,IAASe,EAAU,qBAAA,CAAsB,EAAE,MAAA;YAEjD,OAAA1B,EAAiBW,CAAM,GACvBnD,EAAYmE,KAAM;oBAAC;wBAAE,SAAStE,EAAM,EAAA;wBAAI,QAAAsD;wBAAQ,UAAUtD,EAAM;oBAAS,EAAG;uBAAGsE,CAAC;iBAAC,GAC1E,IAAMnE,GAAYmE,IAAMA,EAAE,MAAA,EAAQhB,IAAWA,EAAO,OAAA,KAAYtD,EAAM,EAAE,CAAC;QAAA;IAEpF,GAAG;QAACG;QAAYH,EAAM,EAAE;KAAC,yMAEzB2B,UAAAA,CAAM,eAAA,CAAgB,IAAM;QAC1B,IAAI,CAACG,GAAS;QACd,IAAMuC,IAAYvB,EAAS,OAAA,EACrByB,IAAiBF,EAAU,KAAA,CAAM,MAAA;QACvCA,EAAU,KAAA,CAAM,MAAA,GAAS;QACzB,IAAMG,IAAYH,EAAU,qBAAA,CAAsB,EAAE,MAAA;QACpDA,EAAU,KAAA,CAAM,MAAA,GAASE,GAEzB5B,EAAiB6B,CAAS,GAE1BrE,GAAYE,IACYA,EAAQ,IAAA,EAAMiD,IAAWA,EAAO,OAAA,KAAYtD,EAAM,EAAE,IAIjEK,EAAQ,GAAA,EAAKiD,IAAYA,EAAO,OAAA,KAAYtD,EAAM,EAAA,GAAK;oBAAE,GAAGsD,CAAAA;oBAAQ,QAAQkB;gBAAU,IAAIlB,CAAO,IAFjG;gBAAC;oBAAE,SAAStD,EAAM,EAAA;oBAAI,QAAQwE;oBAAW,UAAUxE,EAAM;gBAAS,EAAG;mBAAGK,CAAO;aAIzF;IACH,GAAG;QAACyB;QAAS9B,EAAM,KAAA;QAAOA,EAAM,WAAA;QAAaG;QAAYH,EAAM,EAAE;KAAC;IAElE,IAAMyE,0MAAc9C,UAAAA,CAAM,WAAA,CAAY,IAAM;QAE1CM,GAAW,CAAA,CAAI,GACfQ,EAAsBiB,EAAO,OAAO,GACpCvD,GAAYmE,IAAMA,EAAE,MAAA,EAAQhB,IAAWA,EAAO,OAAA,KAAYtD,EAAM,EAAE,CAAC,GAEnE,WAAW,IAAM;YACfS,EAAYT,CAAK;QACnB,GAAGtB,EAAmB;IACxB,GAAG;QAACsB;QAAOS;QAAaN;QAAYuD,CAAM;KAAC;0MAE3C/B,UAAAA,CAAM,SAAA,CAAU,IAAM;QACpB,IAAK3B,EAAM,OAAA,IAAWiD,MAAc,aAAcjD,EAAM,QAAA,KAAa,IAAA,KAAYA,EAAM,IAAA,KAAS,WAAW;QAC3G,IAAI0E;QA6BJ,OAAIlE,KAAYN,KAAgBsB,MAAyByC,KAAAA,CA1BtC,IAAM;YACvB,IAAIN,GAA2B,OAAA,GAAUF,GAAuB,OAAA,EAAS;gBAEvE,IAAMkB,IAAc,IAAI,KAAK,EAAE,OAAA,CAAQ,IAAIlB,GAAuB,OAAA;gBAElEb,EAAc,OAAA,GAAUA,EAAc,OAAA,GAAU+B;YAAAA;YAGlDhB,GAA2B,OAAA,GAAU,IAAI,KAAK,EAAE,OAAA,CAAQ;QAC1D,CAAA,EAkBa,IAAA,CAhBM,IAAM;YAInBf,EAAc,OAAA,KAAY,IAAA,KAAA,CAE9Ba,GAAuB,OAAA,GAAU,IAAI,KAAK,EAAE,OAAA,CAAQ,GAGpDiB,IAAY,WAAW,IAAM;gBA9NnC,IAAAtF;gBAAAA,CA+NQA,IAAAY,EAAM,WAAA,KAAN,QAAAZ,EAAA,IAAA,CAAAY,GAAoBA,IACpByE,EAAY;YACd,GAAG7B,EAAc,OAAO,CAAA;QAC1B,CAAA,EAKa,GAGN,IAAM,aAAa8B,CAAS;IACrC,GAAG;QAAClE;QAAUN;QAAaF;QAAOiD;QAAWzB;QAAuByC;QAAkBQ,CAAW;KAAC,yMAElG9C,UAAAA,CAAM,SAAA,CAAU,IAAM;QAChB3B,EAAM,MAAA,IACRyE,EAAY;IAEhB,GAAG;QAACA;QAAazE,EAAM,MAAM;KAAC;IAE9B,SAAS4E,IAAiB;QAnP5B,IAAAxF,GAAAC,GAAAC;QAoPI,OAAIgC,KAAA,QAAAA,EAAO,OAAA,yMAEPK,UAAAA,CAAA,aAAA,CAAC,OAAA;YACC,WAAWhD,EAAG0C,KAAA,OAAA,KAAA,IAAAA,EAAY,MAAA,EAAA,CAAQjC,IAAAY,KAAA,OAAA,KAAA,IAAAA,EAAO,UAAA,KAAP,OAAA,KAAA,IAAAZ,EAAmB,MAAA,EAAQ,eAAe;YAC5E,gBAAc6D,MAAc;QAAA,GAE3B3B,EAAM,OACT,IAIAH,2MAEAQ,UAAAA,CAAA,aAAA,CAAC,OAAA;YACC,WAAWhD,EAAG0C,KAAA,OAAA,KAAA,IAAAA,EAAY,MAAA,EAAA,CAAQhC,IAAAW,KAAA,OAAA,KAAA,IAAAA,EAAO,UAAA,KAAP,OAAA,KAAA,IAAAX,EAAmB,MAAA,EAAQ,eAAe;YAC5E,gBAAc4D,MAAc;QAAA,GAE3B9B,EACH,0MAGGQ,UAAAA,CAAA,aAAA,CAACkD,IAAA;YAAO,WAAWlG,EAAG0C,KAAA,OAAA,KAAA,IAAAA,EAAY,MAAA,EAAA,CAAQ/B,IAAAU,KAAA,OAAA,KAAA,IAAAA,EAAO,UAAA,KAAP,OAAA,KAAA,IAAAV,EAAmB,MAAM;YAAG,SAAS2D,MAAc;QAAA,CAAW;IACjH;IAEA,6MACEtB,UAAAA,CAAA,aAAA,CAAC,MAAA;QACC,UAAU;QACV,KAAKmB;QACL,WAAWnE,EACToC,IACAoC,IACA9B,KAAA,OAAA,KAAA,IAAAA,EAAY,KAAA,EAAA,CACZjC,KAAAY,KAAA,OAAA,KAAA,IAAAA,EAAO,UAAA,KAAP,OAAA,KAAA,IAAAZ,GAAmB,KAAA,EACnBiC,KAAA,OAAA,KAAA,IAAAA,EAAY,OAAA,EACZA,KAAA,OAAA,KAAA,IAAAA,CAAAA,CAAa4B,EAAAA,EAAAA,CACb5D,KAAAW,KAAA,OAAA,KAAA,IAAAA,EAAO,UAAA,KAAP,OAAA,KAAA,IAAAX,EAAAA,CAAoB4D,EACtB;QACA,qBAAkB;QAClB,oBAAA,CAAkB3D,KAAAU,EAAM,UAAA,KAAN,OAAAV,KAAoBoB;QACtC,eAAa,CAAA,CAASV,EAAM,GAAA,IAAOA,EAAM,QAAA,IAAYC,CAAAA;QACrD,gBAAc6B;QACd,gBAAc,CAAA,CAAQ9B,EAAM,OAAA;QAC5B,eAAasC;QACb,gBAAcN;QACd,gBAAcgB;QACd,mBAAiBjE;QACjB,mBAAiBC;QACjB,cAAYsB;QACZ,cAAYyC;QACZ,gBAAcb;QACd,oBAAkBgB;QAClB,aAAWD;QACX,eAAakB;QACb,kBAAgB/B;QAChB,wBAAsBR;QACtB,iBAAe,CAAA,CAAA,CAAQpB,KAAaY,KAAmBU,CAAAA;QACvD,OACE;YACE,WAAWxB;YACX,mBAAmBA;YACnB,aAAaC,EAAO,MAAA,GAASD;YAC7B,YAAY,GAAG0B,IAAUQ,IAAqBkB,EAAO,OAAA,CAAA,EAAA,CAAA;YACrD,oBAAoBtC,IAAkB,SAAS,GAAGsB,EAAAA,EAAAA,CAAAA;YAClD,GAAG9B,EAAAA;YACH,GAAGZ,EAAM;QACX;QAEF,WAAW,IAAM;YACfmC,EAAW,CAAA,CAAK,GAChBT,EAAkB,IAAI,GACtBkC,EAAgB,OAAA,GAAU;QAC5B;QACA,gBAAgBkB,GAAU;YACpBV,MAAY,CAAClB,KAAAA,CACjBL,EAAc,OAAA,GAAU,IAAI,MAC5BJ,EAAsBiB,EAAO,OAAO,GAEnCoB,EAAM,MAAA,CAAuB,iBAAA,CAAkBA,EAAM,SAAS,GAC1DA,EAAM,MAAA,CAAuB,OAAA,KAAY,YAAA,CAC9C3C,EAAW,CAAA,CAAI,GACfyB,EAAgB,OAAA,GAAU;gBAAE,GAAGkB,EAAM,OAAA;gBAAS,GAAGA,EAAM;YAAQ,CAAA,CAAA;QACjE;QACA,aAAa,IAAM;YAtUzB,IAAA1F,GAAAC,GAAAC,GAAAC;YAuUQ,IAAI6C,MAAY,CAACc,GAAa;YAE9BU,EAAgB,OAAA,GAAU;YAC1B,IAAMmB,IAAe,OAAA,CAAA,CACnB3F,IAAA0D,EAAS,OAAA,KAAT,OAAA,KAAA,IAAA1D,EAAkB,KAAA,CAAM,gBAAA,CAAiB,oBAAoB,OAAA,CAAQ,MAAM,GAAA,KAAO,CACpF,GACM4F,IAAe,OAAA,CAAA,CACnB3F,IAAAyD,EAAS,OAAA,KAAT,OAAA,KAAA,IAAAzD,EAAkB,KAAA,CAAM,gBAAA,CAAiB,oBAAoB,OAAA,CAAQ,MAAM,GAAA,KAAO,CACpF,GACM4F,IAAY,IAAI,KAAK,EAAE,OAAA,CAAQ,IAAA,CAAA,CAAI3F,IAAAuD,EAAc,OAAA,KAAd,OAAA,KAAA,IAAAvD,EAAuB,OAAA,EAAA,GAE1D4F,IAAczD,MAAmB,MAAMsD,IAAeC,GACtDG,IAAW,KAAK,GAAA,CAAID,CAAW,IAAID;YAEzC,IAAI,KAAK,GAAA,CAAIC,CAAW,KAAKzG,MAAmB0G,IAAW,KAAM;gBAC/D1C,EAAsBiB,EAAO,OAAO,GAAA,CACpCnE,IAAAS,EAAM,SAAA,KAAN,QAAAT,EAAA,IAAA,CAAAS,GAAkBA,IAGhB6B,EADEJ,MAAmB,MACAsD,IAAe,IAAI,UAAU,SAE7BC,IAAe,IAAI,SAAS,IAFO,GAK1DP,EAAY,GACZpC,EAAY,CAAA,CAAI,GAChBE,EAAY,CAAA,CAAK;gBACjB;YAAA;YAGFJ,EAAW,CAAA,CAAK,GAChBT,EAAkB,IAAI;QACxB;QACA,gBAAgBoD,GAAU;YAxWhC,IAAA1F,GAAAC,GAAAC,GAAAC;YA4WQ,IAHI,CAACqE,EAAgB,OAAA,IAAW,CAACV,KAAAA,CAAAA,CAEX9D,IAAA,OAAO,YAAA,CAAa,CAAA,KAApB,OAAA,KAAA,IAAAA,EAAuB,QAAA,GAAW,MAAA,IAAS,GAC9C;YAEnB,IAAMgG,IAASN,EAAM,OAAA,GAAUlB,EAAgB,OAAA,CAAQ,CAAA,EACjDyB,IAASP,EAAM,OAAA,GAAUlB,EAAgB,OAAA,CAAQ,CAAA,EAEjD0B,IAAAA,CAAkBjG,IAAAF,EAAM,eAAA,KAAN,OAAAE,IAAyBR,GAA0BC,EAAQ;YAG/E,CAAC2C,KAAAA,CAAmB,KAAK,GAAA,CAAI4D,CAAM,IAAI,KAAK,KAAK,GAAA,CAAID,CAAM,IAAI,CAAA,KACjE1D,EAAkB,KAAK,GAAA,CAAI2D,CAAM,IAAI,KAAK,GAAA,CAAID,CAAM,IAAI,MAAM,GAAG;YAGnE,IAAIF,IAAc;gBAAE,GAAG;gBAAG,GAAG;YAAE;YAG3BzD,MAAmB,MAAA,CAEjB6D,EAAgB,QAAA,CAAS,KAAK,KAAKA,EAAgB,QAAA,CAAS,QAAQ,CAAA,KAAA,CAClEA,EAAgB,QAAA,CAAS,KAAK,KAAKF,IAAS,KAErCE,EAAgB,QAAA,CAAS,QAAQ,KAAKF,IAAS,CAAA,KAAA,CACxDF,EAAY,CAAA,GAAIE,CAAAA,IAGX3D,MAAmB,OAAA,CAExB6D,EAAgB,QAAA,CAAS,MAAM,KAAKA,EAAgB,QAAA,CAAS,OAAO,CAAA,KAAA,CAClEA,EAAgB,QAAA,CAAS,MAAM,KAAKD,IAAS,KAEtCC,EAAgB,QAAA,CAAS,OAAO,KAAKD,IAAS,CAAA,KAAA,CACvDH,EAAY,CAAA,GAAIG,CAAAA,GAAAA,CAKlB,KAAK,GAAA,CAAIH,EAAY,CAAC,IAAI,KAAK,KAAK,GAAA,CAAIA,EAAY,CAAC,IAAI,CAAA,KAC3D3C,EAAY,CAAA,CAAI,GAAA,CAIlBjD,IAAAwD,EAAS,OAAA,KAAT,QAAAxD,EAAkB,KAAA,CAAM,WAAA,CAAY,oBAAoB,GAAG4F,EAAY,CAAA,CAAA,EAAA,CAAA,GAAA,CACvE3F,KAAAuD,EAAS,OAAA,KAAT,QAAAvD,GAAkB,KAAA,CAAM,WAAA,CAAY,oBAAoB,GAAG2F,EAAY,CAAA,CAAA,EAAA,CAAA;QACzE;IAAA,GAEC3B,MAAe,CAACvD,EAAM,GAAA,yMACrB2B,UAAAA,CAAA,aAAA,CAAC,UAAA;QACC,cAAYJ;QACZ,iBAAe6C;QACf,qBAAiB,CAAA;QACjB,SACEA,MAAY,CAAClB,IACT,IAAM,CAAC,IACP,IAAM;YAhatB,IAAA9D;YAiakBqF,EAAY,GAAA,CACZrF,IAAAY,EAAM,SAAA,KAAN,QAAAZ,EAAA,IAAA,CAAAY,GAAkBA;QACpB;QAEN,WAAWrB,EAAG0C,KAAA,OAAA,KAAA,IAAAA,EAAY,WAAA,EAAA,CAAa9B,KAAAS,KAAA,OAAA,KAAA,IAAAA,EAAO,UAAA,KAAP,OAAA,KAAA,IAAAT,GAAmB,WAAW;IAAA,GAAA,CAEpEC,KAAA8B,KAAA,OAAA,KAAA,IAAAA,EAAO,KAAA,KAAP,OAAA9B,KAAgB+F,EACnB,IACE,MAEHvF,EAAM,GAAA,8MAAOwF,iBAAAA,EAAexF,EAAM,KAAK,IACtCA,EAAM,GAAA,GACJA,EAAM,GAAA,GACJ,OAAOA,EAAM,KAAA,IAAU,aACzBA,EAAM,KAAA,CAAM,IAEZA,EAAM,KAAA,wMAGR2B,WAAAA,CAAA,aAAA,uMAAAA,UAAAA,CAAA,QAAA,EAAA,MACGsB,KAAajD,EAAM,IAAA,IAAQA,EAAM,OAAA,yMAChC2B,UAAAA,CAAA,aAAA,CAAC,OAAA;QAAI,aAAU;QAAG,WAAWhD,EAAG0C,KAAA,OAAA,KAAA,IAAAA,EAAY,IAAA,EAAA,CAAM5B,KAAAO,KAAA,OAAA,KAAA,IAAAA,EAAO,UAAA,KAAP,OAAA,KAAA,IAAAP,GAAmB,IAAI;IAAA,GACtEO,EAAM,OAAA,IAAYA,EAAM,IAAA,KAAS,aAAa,CAACA,EAAM,IAAA,GAAQA,EAAM,IAAA,IAAQ4E,GAAe,IAAI,MAC9F5E,EAAM,IAAA,KAAS,YAAYA,EAAM,IAAA,IAAA,CAAQsB,KAAA,OAAA,KAAA,IAAAA,CAAAA,CAAQ2B,EAAAA,KAAcwC,GAASxC,CAAS,IAAI,IACxF,IACE,4MAEJtB,UAAAA,CAAA,aAAA,CAAC,OAAA;QAAI,gBAAa;QAAG,WAAWhD,EAAG0C,KAAA,OAAA,KAAA,IAAAA,EAAY,OAAA,EAAA,CAAS3B,KAAAM,KAAA,OAAA,KAAA,IAAAA,EAAO,UAAA,KAAP,OAAA,KAAA,IAAAN,GAAmB,OAAO;IAAA,yMAChFiC,UAAAA,CAAA,aAAA,CAAC,OAAA;QAAI,cAAW;QAAG,WAAWhD,EAAG0C,KAAA,OAAA,KAAA,IAAAA,EAAY,KAAA,EAAA,CAAO1B,KAAAK,KAAA,OAAA,KAAA,IAAAA,EAAO,UAAA,KAAP,OAAA,KAAA,IAAAL,GAAmB,KAAK;IAAA,GACzE,OAAOK,EAAM,KAAA,IAAU,aAAaA,EAAM,KAAA,CAAM,IAAIA,EAAM,KAC7D,GACCA,EAAM,WAAA,yMACL2B,UAAAA,CAAA,aAAA,CAAC,OAAA;QACC,oBAAiB;QACjB,WAAWhD,EACTqC,IACAoC,IACA/B,KAAA,OAAA,KAAA,IAAAA,EAAY,WAAA,EAAA,CACZzB,KAAAI,KAAA,OAAA,KAAA,IAAAA,EAAO,UAAA,KAAP,OAAA,KAAA,IAAAJ,GAAmB,WACrB;IAAA,GAEC,OAAOI,EAAM,WAAA,IAAgB,aAAaA,EAAM,WAAA,CAAY,IAAIA,EAAM,WACzE,IACE,IACN,6MACCwF,iBAAAA,EAAexF,EAAM,MAAM,IAC1BA,EAAM,MAAA,GACJA,EAAM,MAAA,IAAU0F,GAAS1F,EAAM,MAAM,0MACvC2B,UAAAA,CAAA,aAAA,CAAC,UAAA;QACC,eAAW,CAAA;QACX,eAAW,CAAA;QACX,OAAO3B,EAAM,iBAAA,IAAqBa;QAClC,UAAUiE,GAAU;YArdlC,IAAA1F,GAAAC;YAudqBqG,GAAS1F,EAAM,MAAM,KACrBkD,KAAAA,CAAAA,CACL7D,IAAAA,CAAAD,IAAAY,EAAM,MAAA,EAAO,OAAA,KAAb,QAAAX,EAAA,IAAA,CAAAD,GAAuB0F,IACvBL,EAAY,CAAA;QACd;QACA,WAAW9F,EAAG0C,KAAA,OAAA,KAAA,IAAAA,EAAY,YAAA,EAAA,CAAcxB,KAAAG,KAAA,OAAA,KAAA,IAAAA,EAAO,UAAA,KAAP,OAAA,KAAA,IAAAH,GAAmB,YAAY;IAAA,GAEtEG,EAAM,MAAA,CAAO,KAChB,IACE,gNACHwF,iBAAAA,EAAexF,EAAM,MAAM,IAC1BA,EAAM,MAAA,GACJA,EAAM,MAAA,IAAU0F,GAAS1F,EAAM,MAAM,IACvC2B,gNAAAA,CAAA,aAAA,CAAC,UAAA;QACC,eAAW,CAAA;QACX,eAAW,CAAA;QACX,OAAO3B,EAAM,iBAAA,IAAqBc;QAClC,UAAUgE,GAAU;YAxelC,IAAA1F,GAAAC;YA0eqBqG,GAAS1F,EAAM,MAAM,KAAA,CAAA,CAC1BX,IAAAA,CAAAD,IAAAY,EAAM,MAAA,EAAO,OAAA,KAAb,QAAAX,EAAA,IAAA,CAAAD,GAAuB0F,IACnB,CAAAA,EAAM,gBAAA,IACVL,EAAY,CAAA;QACd;QACA,WAAW9F,EAAG0C,KAAA,OAAA,KAAA,IAAAA,EAAY,YAAA,EAAA,CAAcvB,KAAAE,KAAA,OAAA,KAAA,IAAAA,EAAO,UAAA,KAAP,OAAA,KAAA,IAAAF,GAAmB,YAAY;IAAA,GAEtEE,EAAM,MAAA,CAAO,KAChB,IACE,IACN,CAEJ;AAEJ;AAEA,SAAS2F,IAA4C;IAEnD,IADI,OAAO,UAAW,eAClB,OAAO,YAAa,aAAa,OAAO;IAE5C,IAAMC,IAAe,SAAS,eAAA,CAAgB,YAAA,CAAa,KAAK;IAEhE,OAAIA,MAAiB,UAAU,CAACA,IACvB,OAAO,gBAAA,CAAiB,SAAS,eAAe,EAAE,SAAA,GAGpDA;AACT;AAEA,SAASC,GAAaC,CAAAA,EAAuCC,CAAAA,CAA4C;IACvG,IAAMC,IAAS,CAAC;IAEhB,OAAA;QAACF;QAAeC,CAAY;KAAA,CAAE,OAAA,CAAQ,CAACrC,GAAQpD,IAAU;QACvD,IAAM2F,IAAW3F,MAAU,GACrB4F,IAASD,IAAW,oBAAoB,YACxCE,IAAeF,IAAW5H,KAAyBD;QAEzD,SAASgI,EAAU1C,CAAAA,CAAyB;YAC1C;gBAAC;gBAAO;gBAAS;gBAAU,MAAM;aAAA,CAAE,OAAA,EAAS2C,GAAQ;gBAClDL,CAAAA,CAAO,GAAGE,EAAAA,CAAAA,EAAUG,GAAK,CAAA,GAAI,OAAO3C,KAAW,WAAW,GAAGA,EAAAA,EAAAA,CAAAA,GAAaA;YAC5E,CAAC;QACH;QAEI,OAAOA,KAAW,YAAY,OAAOA,KAAW,WAClD0C,EAAU1C,CAAM,IACP,OAAOA,KAAW,WAC3B;YAAC;YAAO;YAAS;YAAU,MAAM;SAAA,CAAE,OAAA,EAAS2C,GAAQ;YAC9C3C,CAAAA,CAAO2C,CAAG,CAAA,KAAM,KAAA,IAClBL,CAAAA,CAAO,GAAGE,EAAAA,CAAAA,EAAUG,GAAK,CAAA,GAAIF,IAE7BH,CAAAA,CAAO,GAAGE,EAAAA,CAAAA,EAAUG,GAAK,CAAA,GAAI,OAAO3C,CAAAA,CAAO2C,CAAG,CAAA,IAAM,WAAW,GAAG3C,CAAAA,CAAO2C,CAAG,CAAA,CAAA,EAAA,CAAA,GAAQ3C,CAAAA,CAAO2C,CAAG;QAElG,CAAC,IAEDD,EAAUD,CAAY;IAE1B,CAAC,GAEMH;AACT;AAEA,SAASM,IAAY;IACnB,IAAM,CAACC,GAAcC,CAAe,CAAA,yMAAI7E,UAAAA,CAAM,QAAA,CAAmB,CAAC,CAAC;IAEnE,6MAAAA,UAAAA,CAAM,SAAA,CAAU,IACP8E,EAAW,SAAA,EAAWzG,GAAU;YACrC,IAAKA,EAAyB,OAAA,EAAS;gBACrC,WAAW,IAAM;gOACf0G,WAAAA,CAAS,SAAA,CAAU,IAAM;wBACvBF,GAAiBjG,IAAWA,EAAO,MAAA,EAAQoG,IAAMA,EAAE,EAAA,KAAO3G,EAAM,EAAE,CAAC;oBACrE,CAAC;gBACH,CAAC;gBACD;YAAA;YAIF,WAAW,IAAM;4NACf0G,WAAAA,CAAS,SAAA,CAAU,IAAM;oBACvBF,GAAiBjG,GAAW;wBAC1B,IAAMqG,IAAuBrG,EAAO,SAAA,EAAWoG,IAAMA,EAAE,EAAA,KAAO3G,EAAM,EAAE;wBAGtE,OAAI4G,MAAyB,CAAA,IACpB,CACL;+BAAGrG,EAAO,KAAA,CAAM,GAAGqG,CAAoB;4BACvC;gCAAE,GAAGrG,CAAAA,CAAOqG,CAAoB,CAAA;gCAAG,GAAG5G;4BAAM,EAC5C;+BAAGO,EAAO,KAAA,CAAMqG,IAAuB,CAAC,CAC1C;yBAAA,GAGK;4BAAC5G,EAAO;+BAAGO,CAAM;;oBAC1B,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC,GACA,CAAC,CAAC,GAEE;QACL,QAAQgG;IACV;AACF;AAEA,IAAMM,SAAUC,mNAAAA,EAAsC,SAAiB3H,CAAAA,EAAO4H,CAAAA,CAAK;IACjF,IAAM,EACJ,QAAA5C,CAAAA,EACA,UAAArF,IAAW,cAAA,EACX,QAAAkI,IAAS;QAAC;QAAU,MAAM;KAAA,EAC1B,QAAAC,CAAAA,EACA,aAAA1D,CAAAA,EACA,WAAAxC,CAAAA,EACA,QAAA2C,CAAAA,EACA,cAAAqC,CAAAA,EACA,OAAAmB,IAAQ,OAAA,EACR,YAAAC,CAAAA,EACA,UAAA3D,EAAAA,EACA,OAAA5C,EAAAA,EACA,eAAAR,KAAgBjC,EAAAA,EAChB,cAAAiJ,CAAAA,EACA,KAAAC,KAAM1B,GAAqB,CAAA,EAC3B,KAAAzE,KAAM1C,EAAAA,EACN,aAAA8I,CAAAA,EACA,OAAAhG,EAAAA,EACA,oBAAAiG,KAAqB,eAAA,EACrB,uBAAA/F,EACF,EAAA,GAAIrC,GACE,CAACoB,GAAQiH,CAAS,CAAA,GAAI7F,gNAAAA,CAAM,QAAA,CAAmB,CAAC,CAAC,GACjD8F,0MAAoB9F,UAAAA,CAAM,OAAA,CAAQ,IAC/B,MAAM,IAAA,CACX,IAAI,IAAI;YAAC7C,CAAQ;SAAA,CAAE,MAAA,CAAOyB,EAAO,MAAA,EAAQP,IAAUA,EAAM,QAAQ,EAAE,GAAA,EAAKA,IAAUA,EAAM,QAAQ,CAAC,CAAC,CACpG,GACC;QAACO;QAAQzB,CAAQ;KAAC,GACf,CAACuB,IAASF,EAAU,CAAA,yMAAIwB,UAAAA,CAAM,QAAA,CAAoB,CAAC,CAAC,GACpD,CAACnB,GAAUkH,CAAW,CAAA,yMAAI/F,UAAAA,CAAM,QAAA,CAAS,CAAA,CAAK,GAC9C,CAACzB,IAAayH,CAAc,CAAA,GAAIhG,gNAAAA,CAAM,QAAA,CAAS,CAAA,CAAK,GACpD,CAACiG,GAAaC,CAAc,CAAA,yMAAIlG,UAAAA,CAAM,QAAA,CAC1CuF,MAAU,WACNA,IACA,OAAO,UAAW,eAClB,OAAO,UAAA,IAAc,OAAO,UAAA,CAAW,8BAA8B,EAAE,OAAA,GACrE,SAEF,OACN,GAEMY,0MAAUnG,UAAAA,CAAM,MAAA,CAAyB,IAAI,GAC7CoG,KAAcf,EAAO,IAAA,CAAK,GAAG,EAAE,OAAA,CAAQ,QAAQ,EAAE,EAAE,OAAA,CAAQ,UAAU,EAAE,GACvEgB,IAAwBrG,gNAAAA,CAAM,MAAA,CAAoB,IAAI,GACtDsG,0MAAmBtG,UAAAA,CAAM,MAAA,CAAO,CAAA,CAAK,GAErClB,2MAAckB,UAAAA,CAAM,WAAA,EAAauG,GAA0B;QAC/DV,GAAWjH,GAAW;YAhoB1B,IAAAnB;YAioBM,OAAA,CAAKA,IAAAmB,EAAO,IAAA,EAAMP,IAAUA,EAAM,EAAA,KAAOkI,EAAc,EAAE,CAAA,KAApD,QAAA9I,EAAuD,MAAA,IAC1DqH,EAAW,OAAA,CAAQyB,EAAc,EAAE,GAG9B3H,EAAO,MAAA,CAAO,CAAC,EAAE,IAAA4H,CAAG,EAAA,GAAMA,MAAOD,EAAc,EAAE;QAC1D,CAAC;IACH,GAAG,CAAC,CAAC;IAEL,6MAAAvG,UAAAA,CAAM,SAAA,CAAU,IACP8E,EAAW,SAAA,EAAWzG,GAAU;YACrC,IAAKA,EAAyB,OAAA,EAAS;gBACrCwH,GAAWjH,IAAWA,EAAO,GAAA,EAAKoG,IAAOA,EAAE,EAAA,KAAO3G,EAAM,EAAA,GAAK;4BAAE,GAAG2G,CAAAA;4BAAG,QAAQ,CAAA;wBAAK,IAAIA,CAAE,CAAC;gBACzF;YAAA;YAIF,WAAW,IAAM;6NACfD,UAAAA,CAAS,SAAA,CAAU,IAAM;oBACvBc,GAAWjH,GAAW;wBACpB,IAAMqG,IAAuBrG,EAAO,SAAA,EAAWoG,IAAMA,EAAE,EAAA,KAAO3G,EAAM,EAAE;wBAGtE,OAAI4G,MAAyB,CAAA,IACpB,CACL;+BAAGrG,EAAO,KAAA,CAAM,GAAGqG,CAAoB;4BACvC;gCAAE,GAAGrG,CAAAA,CAAOqG,CAAoB,CAAA;gCAAG,GAAG5G;4BAAM,EAC5C;+BAAGO,EAAO,KAAA,CAAMqG,IAAuB,CAAC,CAC1C;yBAAA,GAGK;4BAAC5G,EAAO;+BAAGO,CAAM;;oBAC1B,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC,GACA,CAAC,CAAC,yMAELoB,UAAAA,CAAM,SAAA,CAAU,IAAM;QACpB,IAAIuF,MAAU,UAAU;YACtBW,EAAeX,CAAK;YACpB;QAAA;QAcF,IAXIA,MAAU,YAAA,CAER,OAAO,UAAA,IAAc,OAAO,UAAA,CAAW,8BAA8B,EAAE,OAAA,GAEzEW,EAAe,MAAM,IAGrBA,EAAe,OAAO,CAAA,GAItB,OAAO,UAAW,aAAa;QACnC,IAAMO,IAAiB,OAAO,UAAA,CAAW,8BAA8B;QAEvE,IAAI;YAEFA,EAAe,gBAAA,CAAiB,UAAU,CAAC,EAAE,SAAAC,CAAQ,EAAA,GAAM;gBAEvDR,EADEQ,IACa,SAEA,OAFM;YAIzB,CAAC;QACH,EAAA,OAASC,GAAP;YAEAF,EAAe,WAAA,CAAY,CAAC,EAAE,SAAAC,CAAQ,EAAA,GAAM;gBAC1C,IAAI;oBAEAR,EADEQ,IACa,SAEA,OAFM;gBAIzB,EAAA,OAASE,GAAP;oBACA,QAAQ,KAAA,CAAMA,CAAC;gBACjB;YACF,CAAC;QACH;IACF,GAAG;QAACrB,CAAK;KAAC,yMAEVvF,UAAAA,CAAM,SAAA,CAAU,IAAM;QAEhBpB,EAAO,MAAA,IAAU,KACnBmH,EAAY,CAAA,CAAK;IAErB,GAAG;QAACnH,CAAM;KAAC,yMAEXoB,UAAAA,CAAM,SAAA,CAAU,IAAM;QACpB,IAAM6G,KAAiB1D,GAAyB;YA3tBpD,IAAA1F,GAAAC;YA4tB8B2H,EAAO,KAAA,CAAOX,KAASvB,CAAAA,CAAcuB,CAAG,CAAA,IAAKvB,EAAM,IAAA,KAASuB,CAAG,KAAA,CAGrFqB,EAAY,CAAA,CAAI,GAAA,CAChBtI,IAAA0I,EAAQ,OAAA,KAAR,QAAA1I,EAAiB,KAAA,EAAA,GAIjB0F,EAAM,IAAA,KAAS,YAAA,CACd,SAAS,aAAA,KAAkBgD,EAAQ,OAAA,IAAA,CAAWzI,IAAAyI,EAAQ,OAAA,KAAR,QAAAzI,EAAiB,QAAA,CAAS,SAAS,aAAA,CAAA,KAElFqI,EAAY,CAAA,CAAK;QAErB;QACA,OAAA,SAAS,gBAAA,CAAiB,WAAWc,CAAa,GAE3C,IAAM,SAAS,mBAAA,CAAoB,WAAWA,CAAa;IACpE,GAAG;QAACxB,CAAM;KAAC,yMAEXrF,UAAAA,CAAM,SAAA,CAAU,IAAM;QACpB,IAAImG,EAAQ,OAAA,EACV,OAAO,IAAM;YACPE,EAAsB,OAAA,IAAA,CACxBA,EAAsB,OAAA,CAAQ,KAAA,CAAM;gBAAE,eAAe,CAAA;YAAK,CAAC,GAC3DA,EAAsB,OAAA,GAAU,MAChCC,EAAiB,OAAA,GAAU,CAAA,CAAA;QAE/B;IAEJ,GAAG;QAACH,EAAQ,OAAO;KAAC,yMAIlBnG,UAAAA,CAAA,aAAA,CAAC,WAAA;QACC,KAAKoF;QACL,cAAY,GAAGQ,GAAAA,CAAAA,EAAsBQ,IAAAA;QACrC,UAAU,CAAA;QACV,aAAU;QACV,iBAAc;QACd,eAAY;QACZ,0BAAwB,CAAA;IAAA,GAEvBN,EAAkB,GAAA,CAAI,CAAC3I,GAAUwB,IAAU;QAtwBlD,IAAAlB;QAuwBQ,IAAM,CAAC,GAAGJ,CAAC,CAAA,GAAIF,EAAS,KAAA,CAAM,GAAG;QAEjC,OAAKyB,EAAO,MAAA,yMAGVoB,UAAAA,CAAA,aAAA,CAAC,MAAA;YACC,KAAK7C;YACL,KAAKuI,OAAQ,SAAS1B,GAAqB,IAAI0B;YAC/C,UAAU,CAAA;YACV,KAAKS;YACL,WAAW/G;YACX,uBAAmB,CAAA;YACnB,cAAY6G;YACZ,mBAAiB;YACjB,eAAapH,KAAYD,EAAO,MAAA,GAAS,KAAK,CAAC0G;YAC/C,mBAAiBjI;YACjB,OACE;gBACE,wBAAwB,GAAA,CAAA,CAAGI,IAAAiB,EAAAA,CAAQ,CAAC,CAAA,KAAT,OAAA,KAAA,IAAAjB,EAAY,MAAA,KAAU,EAAA,EAAA,CAAA;gBACjD,WAAW,GAAGb,GAAAA,EAAAA,CAAAA;gBACd,SAAS,GAAG2C,GAAAA,EAAAA,CAAAA;gBACZ,GAAGN,EAAAA;gBACH,GAAGiF,GAAanC,GAAQqC,CAAY;YACtC;YAEF,SAASjB,GAAU;gBACbmD,EAAiB,OAAA,IAAW,CAACnD,EAAM,aAAA,CAAc,QAAA,CAASA,EAAM,aAAa,KAAA,CAC/EmD,EAAiB,OAAA,GAAU,CAAA,GACvBD,EAAsB,OAAA,IAAA,CACxBA,EAAsB,OAAA,CAAQ,KAAA,CAAM;oBAAE,eAAe,CAAA;gBAAK,CAAC,GAC3DA,EAAsB,OAAA,GAAU,IAAA,CAAA;YAGtC;YACA,UAAUlD,GAAU;gBAEhBA,EAAM,MAAA,YAAkB,eAAeA,EAAM,MAAA,CAAO,OAAA,CAAQ,WAAA,KAAgB,WAIzEmD,EAAiB,OAAA,IAAA,CACpBA,EAAiB,OAAA,GAAU,CAAA,GAC3BD,EAAsB,OAAA,GAAUlD,EAAM,aAAA;YAE1C;YACA,cAAc,IAAM4C,EAAY,CAAA,CAAI;YACpC,aAAa,IAAMA,EAAY,CAAA,CAAI;YACnC,cAAc,IAAM;gBAEbxH,MACHwH,EAAY,CAAA,CAAK;YAErB;YACA,WAAW,IAAMA,EAAY,CAAA,CAAK;YAClC,gBAAgB5C,GAAU;gBAEtBA,EAAM,MAAA,YAAkB,eAAeA,EAAM,MAAA,CAAO,OAAA,CAAQ,WAAA,KAAgB,WAG9E6C,EAAe,CAAA,CAAI;YACrB;YACA,aAAa,IAAMA,EAAe,CAAA,CAAK;QAAA,GAEtCpH,EACE,MAAA,EAAQP,IAAW,CAACA,EAAM,QAAA,IAAYM,MAAU,KAAMN,EAAM,QAAA,KAAalB,CAAQ,EACjF,GAAA,CAAI,CAACkB,GAAOM,IAAO;YAx0BlC,IAAAlB,GAAAC;YAy0BgB,6MAAAsC,UAAAA,CAAA,aAAA,CAACzC,IAAA;gBACC,KAAKc,EAAM,EAAA;gBACX,OAAOsB;gBACP,OAAOhB;gBACP,OAAON;gBACP,mBAAmBmH;gBACnB,UAAA,CAAU/H,IAAAgI,KAAA,OAAA,KAAA,IAAAA,EAAc,QAAA,KAAd,OAAAhI,IAA0BoE;gBACpC,WAAW4D,KAAA,OAAA,KAAA,IAAAA,EAAc,SAAA;gBACzB,sBAAsBA,KAAA,OAAA,KAAA,IAAAA,EAAc,oBAAA;gBACpC,QAAQjD;gBACR,eAAe/D;gBACf,aAAA,CAAaf,IAAA+H,KAAA,OAAA,KAAA,IAAAA,EAAc,WAAA,KAAd,OAAA/H,IAA6BkE;gBAC1C,aAAarD;gBACb,UAAUpB;gBACV,OAAOsI,KAAA,OAAA,KAAA,IAAAA,EAAc,KAAA;gBACrB,UAAUA,KAAA,OAAA,KAAA,IAAAA,EAAc,QAAA;gBACxB,YAAYA,KAAA,OAAA,KAAA,IAAAA,EAAc,UAAA;gBAC1B,mBAAmBA,KAAA,OAAA,KAAA,IAAAA,EAAc,iBAAA;gBACjC,mBAAmBA,KAAA,OAAA,KAAA,IAAAA,EAAc,iBAAA;gBACjC,aAAa3G;gBACb,QAAQF,EAAO,MAAA,EAAQoG,IAAMA,EAAE,QAAA,IAAY3G,EAAM,QAAQ;gBACzD,SAASK,GAAQ,MAAA,EAAQiE,IAAMA,EAAE,QAAA,IAAYtE,EAAM,QAAQ;gBAC3D,YAAYG;gBACZ,iBAAiB8G;gBACjB,KAAK/F;gBACL,aAAaoG;gBACb,UAAU9G;gBACV,uBAAuBgB;gBACvB,iBAAiBrC,EAAM,eAAA;YAAA,CACzB;QAAA,CACD,CACL,IA/FyB;IAiG7B,CAAC,CACH;AAEJ,CAAC", "ignoreList": [0, 1, 2, 3, 4, 5, 6]}}, {"offset": {"line": 3103, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}
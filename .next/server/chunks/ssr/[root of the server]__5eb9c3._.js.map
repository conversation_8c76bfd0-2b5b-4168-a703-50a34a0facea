{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/src/components/ui/backgroundEffect.tsx"], "sourcesContent": ["\"use client\";\n\nimport { memo } from \"react\";\n\nconst BackgroundEffect = () => {\n  return (\n    <div className=\"absolute hidden inset-0 isolate z-10 contain-strict max-md:hidden\">\n      <div className=\"absolute left-0 top-0 h-[1280px] w-[560px] -translate-y-[350px] -rotate-45 rounded-full bg-[radial-gradient(68.54%_68.72%_at_55.02%_31.46%,hsla(0,0%,85%,.08)_0,hsla(0,0%,55%,.02)_50%,hsla(0,0%,45%,0)_80%)]\"></div>\n      <div className=\"absolute left-0 top-0 h-[1280px] w-[240px] -rotate-45 rounded-full bg-[radial-gradient(50%_50%_at_50%_50%,hsla(0,0%,85%,.06)_0,hsla(0,0%,45%,.02)_80%,transparent_100%)] [translate:5%_-50%]\"></div>\n      <div className=\"absolute left-0 top-0 h-[1280px] w-[240px] -translate-y-[350px] -rotate-45 bg-[radial-gradient(50%_50%_at_50%_50%,hsla(0,0%,85%,.04)_0,hsla(0,0%,45%,.02)_80%,transparent_100%)]\"></div>\n    </div>\n  );\n};\n\n// Memoize the component to prevent unnecessary re-renders\nexport default memo(BackgroundEffect);\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIA,MAAM,mBAAmB;IACvB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;;;;;0BACf,8OAAC;gBAAI,WAAU;;;;;;0BACf,8OAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB;qDAGe,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EAAE"}}, {"offset": {"line": 56, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/src/components/ui/FloatingActionButton.tsx"], "sourcesContent": ["\"use client\";\n\nimport { AnimatePresence, motion } from \"framer-motion\";\nimport { Briefcase, FileText, Home, Mail, Menu, X } from \"lucide-react\";\nimport Link from \"next/link\";\nimport { useEffect, useState } from \"react\";\n\ninterface FloatingActionButtonProps {\n  threshold?: number;\n}\n\nexport default function FloatingActionButton({\n  threshold = 300,\n}: FloatingActionButtonProps) {\n  const [isVisible, setIsVisible] = useState(false);\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      if (window.scrollY > threshold) {\n        setIsVisible(true);\n      } else {\n        setIsVisible(false);\n        if (isMenuOpen) setIsMenuOpen(false);\n      }\n    };\n\n    window.addEventListener(\"scroll\", handleScroll);\n    return () => window.removeEventListener(\"scroll\", handleScroll);\n  }, [threshold, isMenuOpen]);\n\n  const menuItems = [\n    { icon: <Home className=\"h-5 w-5\" />, label: \"Home\", href: \"/\" },\n    { icon: <Briefcase className=\"h-5 w-5\" />, label: \"Work\", href: \"/#work\" },\n    {\n      icon: <FileText className=\"h-5 w-5\" />,\n      label: \"Projects\",\n      href: \"/projects\",\n    },\n    { icon: <Mail className=\"h-5 w-5\" />, label: \"Contact\", href: \"/contact\" },\n  ];\n\n  const handleMenuItemClick = (href: string) => {\n    setIsMenuOpen(false);\n    if (href === \"/#work\") {\n      const workSection = document.getElementById(\"work\");\n      if (workSection) {\n        workSection.scrollIntoView({ behavior: \"smooth\" });\n      }\n    }\n  };\n\n  return (\n    <AnimatePresence>\n      {isVisible && (\n        <motion.div\n          className=\"fixed bottom-6 right-6 z-30 flex flex-col items-end hidden\"\n          initial={{ opacity: 0, scale: 0.8 }}\n          animate={{ opacity: 1, scale: 1 }}\n          exit={{ opacity: 0, scale: 0.8 }}\n          transition={{ duration: 0.3 }}\n        >\n          {/* Menu items */}\n          <AnimatePresence>\n            {isMenuOpen && (\n              <motion.div\n                className=\"mb-4 flex flex-col gap-3\"\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                exit={{ opacity: 0, y: 20 }}\n                transition={{ duration: 0.3 }}\n              >\n                {menuItems.map((item, index) => (\n                  <motion.div\n                    key={item.label}\n                    initial={{ opacity: 0, x: 20 }}\n                    animate={{ opacity: 1, x: 0 }}\n                    transition={{ delay: index * 0.05 }}\n                  >\n                    <Link\n                      href={item.href}\n                      onClick={() => handleMenuItemClick(item.href)}\n                      className=\"flex items-center gap-2 rounded-full bg-[var(--card-background)] px-4 py-2 text-sm text-[var(--headline)] shadow-md hover:bg-[var(--link-color)] hover:text-white transition-colors\"\n                    >\n                      {item.icon}\n                      <span>{item.label}</span>\n                    </Link>\n                  </motion.div>\n                ))}\n              </motion.div>\n            )}\n          </AnimatePresence>\n\n          {/* Main button */}\n          <motion.button\n            className=\"flex h-12 w-12 items-center justify-center rounded-full bg-[var(--link-color)] text-white shadow-lg hover:bg-[var(--button)] transition-colors\"\n            onClick={() =>\n              isMenuOpen ? setIsMenuOpen(false) : setIsMenuOpen(true)\n            }\n            whileTap={{ scale: 0.9 }}\n            aria-label={isMenuOpen ? \"Close menu\" : \"Open menu\"}\n          >\n            {isMenuOpen ? (\n              <X className=\"h-5 w-5\" />\n            ) : (\n              <Menu className=\"h-5 w-5\" />\n            )}\n          </motion.button>\n\n          {/* Back to top button */}\n        </motion.div>\n      )}\n    </AnimatePresence>\n  );\n}\n"], "names": [], "mappings": ";;;;AAIA;AACA;AAFA;AAAA;AAAA;AAAA;AADA;AAAA;AACA;AAAA;AAHA;;;;;;AAWe,SAAS,qBAAqB,EAC3C,YAAY,GAAG,EACW;IAC1B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,IAAI,OAAO,OAAO,GAAG,WAAW;gBAC9B,aAAa;YACf,OAAO;gBACL,aAAa;gBACb,IAAI,YAAY,cAAc;YAChC;QACF;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG;QAAC;QAAW;KAAW;IAE1B,MAAM,YAAY;QAChB;YAAE,oBAAM,8OAAC,mMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;YAAc,OAAO;YAAQ,MAAM;QAAI;QAC/D;YAAE,oBAAM,8OAAC,4MAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;YAAc,OAAO;YAAQ,MAAM;QAAS;QACzE;YACE,oBAAM,8OAAC,8MAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;YAC1B,OAAO;YACP,MAAM;QACR;QACA;YAAE,oBAAM,8OAAC,kMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;YAAc,OAAO;YAAW,MAAM;QAAW;KAC1E;IAED,MAAM,sBAAsB,CAAC;QAC3B,cAAc;QACd,IAAI,SAAS,UAAU;YACrB,MAAM,cAAc,SAAS,cAAc,CAAC;YAC5C,IAAI,aAAa;gBACf,YAAY,cAAc,CAAC;oBAAE,UAAU;gBAAS;YAClD;QACF;IACF;IAEA,qBACE,8OAAC,yLAAA,CAAA,kBAAe;kBACb,2BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,WAAU;YACV,SAAS;gBAAE,SAAS;gBAAG,OAAO;YAAI;YAClC,SAAS;gBAAE,SAAS;gBAAG,OAAO;YAAE;YAChC,MAAM;gBAAE,SAAS;gBAAG,OAAO;YAAI;YAC/B,YAAY;gBAAE,UAAU;YAAI;;8BAG5B,8OAAC,yLAAA,CAAA,kBAAe;8BACb,4BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,MAAM;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC1B,YAAY;4BAAE,UAAU;wBAAI;kCAE3B,UAAU,GAAG,CAAC,CAAC,MAAM,sBACpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,OAAO,QAAQ;gCAAK;0CAElC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAM,KAAK,IAAI;oCACf,SAAS,IAAM,oBAAoB,KAAK,IAAI;oCAC5C,WAAU;;wCAET,KAAK,IAAI;sDACV,8OAAC;sDAAM,KAAK,KAAK;;;;;;;;;;;;+BAXd,KAAK,KAAK;;;;;;;;;;;;;;;8BAoBzB,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oBACZ,WAAU;oBACV,SAAS,IACP,aAAa,cAAc,SAAS,cAAc;oBAEpD,UAAU;wBAAE,OAAO;oBAAI;oBACvB,cAAY,aAAa,eAAe;8BAEvC,2BACC,8OAAC,4LAAA,CAAA,IAAC;wBAAC,WAAU;;;;;6CAEb,8OAAC,kMAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;AAS9B"}}, {"offset": {"line": 277, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 307, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\";\nimport { twMerge } from \"tailwind-merge\";\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB"}}, {"offset": {"line": 317, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 323, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/src/components/ui/navigation-menu.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva } from \"class-variance-authority\"\nimport { ChevronDownIcon } from \"lucide-react\"\nimport { NavigationMenu as NavigationMenuPrimitive } from \"radix-ui\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction NavigationMenu({\n  className,\n  children,\n  viewport = true,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Root> & {\n  viewport?: boolean\n}) {\n  return (\n    <NavigationMenuPrimitive.Root\n      data-slot=\"navigation-menu\"\n      data-viewport={viewport}\n      className={cn(\n        \"group/navigation-menu relative flex max-w-max flex-1 items-center justify-center\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      {viewport && <NavigationMenuViewport />}\n    </NavigationMenuPrimitive.Root>\n  )\n}\n\nfunction NavigationMenuList({\n  className,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.List>) {\n  return (\n    <NavigationMenuPrimitive.List\n      data-slot=\"navigation-menu-list\"\n      className={cn(\n        \"group flex flex-1 list-none items-center justify-center gap-1\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction NavigationMenuItem({\n  className,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Item>) {\n  return (\n    <NavigationMenuPrimitive.Item\n      data-slot=\"navigation-menu-item\"\n      className={cn(\"relative\", className)}\n      {...props}\n    />\n  )\n}\n\nconst navigationMenuTriggerStyle = cva(\n  \"group inline-flex h-9 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground disabled:pointer-events-none disabled:opacity-50 data-[state=open]:hover:bg-accent data-[state=open]:text-accent-foreground data-[state=open]:focus:bg-accent data-[state=open]:bg-accent/50 focus-visible:ring-ring/50 outline-none transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1\"\n)\n\nfunction NavigationMenuTrigger({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Trigger>) {\n  return (\n    <NavigationMenuPrimitive.Trigger\n      data-slot=\"navigation-menu-trigger\"\n      className={cn(navigationMenuTriggerStyle(), \"group\", className)}\n      {...props}\n    >\n      {children}{\" \"}\n      <ChevronDownIcon\n        className=\"relative top-[1px] ml-1 size-3 transition duration-300 group-data-[state=open]:rotate-180\"\n        aria-hidden=\"true\"\n      />\n    </NavigationMenuPrimitive.Trigger>\n  )\n}\n\nfunction NavigationMenuContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Content>) {\n  return (\n    <NavigationMenuPrimitive.Content\n      data-slot=\"navigation-menu-content\"\n      className={cn(\n        \"data-[motion^=from-]:animate-in data-[motion^=to-]:animate-out data-[motion^=from-]:fade-in data-[motion^=to-]:fade-out data-[motion=from-end]:slide-in-from-right-52 data-[motion=from-start]:slide-in-from-left-52 data-[motion=to-end]:slide-out-to-right-52 data-[motion=to-start]:slide-out-to-left-52 top-0 left-0 w-full p-2 pr-2.5 md:absolute md:w-auto\",\n        \"group-data-[viewport=false]/navigation-menu:bg-popover group-data-[viewport=false]/navigation-menu:text-popover-foreground group-data-[viewport=false]/navigation-menu:data-[state=open]:animate-in group-data-[viewport=false]/navigation-menu:data-[state=closed]:animate-out group-data-[viewport=false]/navigation-menu:data-[state=closed]:zoom-out-95 group-data-[viewport=false]/navigation-menu:data-[state=open]:zoom-in-95 group-data-[viewport=false]/navigation-menu:data-[state=open]:fade-in-0 group-data-[viewport=false]/navigation-menu:data-[state=closed]:fade-out-0 group-data-[viewport=false]/navigation-menu:top-full group-data-[viewport=false]/navigation-menu:mt-1.5 group-data-[viewport=false]/navigation-menu:overflow-hidden group-data-[viewport=false]/navigation-menu:rounded-md group-data-[viewport=false]/navigation-menu:border group-data-[viewport=false]/navigation-menu:shadow group-data-[viewport=false]/navigation-menu:duration-200 **:data-[slot=navigation-menu-link]:focus:ring-0 **:data-[slot=navigation-menu-link]:focus:outline-none\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction NavigationMenuViewport({\n  className,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Viewport>) {\n  return (\n    <div\n      className={cn(\n        \"absolute top-full left-0 isolate z-50 flex justify-center\"\n      )}\n    >\n      <NavigationMenuPrimitive.Viewport\n        data-slot=\"navigation-menu-viewport\"\n        className={cn(\n          \"origin-top-center bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-90 relative mt-1.5 h-[var(--radix-navigation-menu-viewport-height)] w-full overflow-hidden rounded-md border shadow md:w-[var(--radix-navigation-menu-viewport-width)]\",\n          className\n        )}\n        {...props}\n      />\n    </div>\n  )\n}\n\nfunction NavigationMenuLink({\n  className,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Link>) {\n  return (\n    <NavigationMenuPrimitive.Link\n      data-slot=\"navigation-menu-link\"\n      className={cn(\n        \"data-[active]:focus:bg-accent data-[active]:hover:bg-accent data-[active]:bg-accent data-[active]:text-accent-foreground hover:bg-accent focus:bg-accent focus:text-accent-foreground focus-visible:ring-ring/50 [&_svg:not([class*='text-'])]:text-muted-foreground flex flex-col gap-1 rounded-sm p-2 text-sm transition-all outline-none focus-visible:ring-[3px] focus-visible:outline-1 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction NavigationMenuIndicator({\n  className,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Indicator>) {\n  return (\n    <NavigationMenuPrimitive.Indicator\n      data-slot=\"navigation-menu-indicator\"\n      className={cn(\n        \"data-[state=visible]:animate-in data-[state=hidden]:animate-out data-[state=hidden]:fade-out data-[state=visible]:fade-in top-full z-[1] flex h-1.5 items-end justify-center overflow-hidden\",\n        className\n      )}\n      {...props}\n    >\n      <div className=\"bg-border relative top-[60%] h-2 w-2 rotate-45 rounded-tl-sm shadow-md\" />\n    </NavigationMenuPrimitive.Indicator>\n  )\n}\n\nexport {\n  NavigationMenu,\n  NavigationMenuList,\n  NavigationMenuItem,\n  NavigationMenuContent,\n  NavigationMenuTrigger,\n  NavigationMenuLink,\n  NavigationMenuIndicator,\n  NavigationMenuViewport,\n  navigationMenuTriggerStyle,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AACA;AAIA;AAFA;AADA;;;;;;AAKA,SAAS,eAAe,EACtB,SAAS,EACT,QAAQ,EACR,WAAW,IAAI,EACf,GAAG,OAGJ;IACC,qBACE,8OAAC,wNAAA,CAAA,iBAAuB,CAAC,IAAI;QAC3B,aAAU;QACV,iBAAe;QACf,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oFACA;QAED,GAAG,KAAK;;YAER;YACA,0BAAY,8OAAC;;;;;;;;;;;AAGpB;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACuD;IAC1D,qBACE,8OAAC,wNAAA,CAAA,iBAAuB,CAAC,IAAI;QAC3B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACuD;IAC1D,qBACE,8OAAC,wNAAA,CAAA,iBAAuB,CAAC,IAAI;QAC3B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QACzB,GAAG,KAAK;;;;;;AAGf;AAEA,MAAM,6BAA6B,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACnC;AAGF,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,8OAAC,wNAAA,CAAA,iBAAuB,CAAC,OAAO;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B,SAAS;QACpD,GAAG,KAAK;;YAER;YAAU;0BACX,8OAAC,wNAAA,CAAA,kBAAe;gBACd,WAAU;gBACV,eAAY;;;;;;;;;;;;AAIpB;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,8OAAC,wNAAA,CAAA,iBAAuB,CAAC,OAAO;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oWACA,6hCACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV;kBAGF,cAAA,8OAAC,wNAAA,CAAA,iBAAuB,CAAC,QAAQ;YAC/B,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sVACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACuD;IAC1D,qBACE,8OAAC,wNAAA,CAAA,iBAAuB,CAAC,IAAI;QAC3B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qaACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,wBAAwB,EAC/B,SAAS,EACT,GAAG,OAC4D;IAC/D,qBACE,8OAAC,wNAAA,CAAA,iBAAuB,CAAC,SAAS;QAChC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gMACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC;YAAI,WAAU;;;;;;;;;;;AAGrB"}}, {"offset": {"line": 469, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 475, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport { cva, type VariantProps } from \"class-variance-authority\";\nimport { Slot } from \"@radix-ui/react-slot\";\n\nimport { cn } from \"@/lib/utils\";\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-[8px] text-sm font-medium transition-[color,box-shadow] disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 [&_svg]:shrink-0 outline-none focus-visible:border-[var(--highlight)] focus-visible:ring-[var(--highlight)]/50 focus-visible:ring-[3px]\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-[var(--button)] text-[var(--button-text)] shadow-sm hover:bg-[color-mix(in_srgb,var(--button),#00000020)]\",\n        destructive:\n          \"bg-[var(--tertiary)] text-[var(--button-text)] shadow-xs hover:bg-[color-mix(in_srgb,var(--tertiary),#00000020)] focus-visible:ring-[var(--tertiary)]/20\",\n        outline:\n          \"border border-[var(--input-border-color)] bg-[var(--background)] shadow-xs hover:bg-[var(--card-hover)] hover:text-[var(--highlight)]\",\n        secondary:\n          \"bg-[var(--secondary)] text-[var(--button-text)] shadow-xs hover:bg-[color-mix(in_srgb,var(--secondary),#00000020)]\",\n        ghost: \"hover:bg-[var(--card-hover)] hover:text-[var(--highlight)]\",\n        link: \"text-[var(--link-color)] underline-offset-4 hover:text-[var(--link-hover)] hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2\",\n        sm: \"h-8 rounded-[8px] px-3 text-xs\",\n        lg: \"h-10 rounded-[8px] px-8\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n);\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean;\n  }) {\n  const Comp = asChild ? Slot : \"button\";\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  );\n}\n\nexport { Button, buttonVariants };\n"], "names": [], "mappings": ";;;;;AACA;AAGA;AAFA;;;;;AAIA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,6XACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf"}}, {"offset": {"line": 526, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 532, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/src/components/common/Logo.tsx"], "sourcesContent": ["export default function Logo() {\n  return (\n    <div>\n      <h1>Logo</h1>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAe,SAAS;IACtB,qBACE,8OAAC;kBACC,cAAA,8OAAC;sBAAG;;;;;;;;;;;AAGV"}}, {"offset": {"line": 552, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 558, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\";\n\nimport { cn } from \"@/lib/utils\";\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-[12px] border-2 border-[var(--input-border-color)] bg-[var(--input-background)] px-3 py-2 text-sm text-[var(--input-text)] placeholder-[var(--paragraph)] ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className,\n        )}\n        ref={ref}\n        {...props}\n      />\n    );\n  },\n);\nInput.displayName = \"Input\";\n\nexport { Input };\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAKA,MAAM,sBAAQ,sMAAM,UAAU,CAC5B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,8OAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iaACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG"}}, {"offset": {"line": 581, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 587, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport { cva, type VariantProps } from \"class-variance-authority\";\nimport { cn } from \"@/lib/utils\";\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"flex items-center justify-center gap-2 border-transparent bg-[var(--badge-background)]  text-[var(--badge-text)] \",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground bg-[var(--badge-background)] text-[var(--badge-text)]  \",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground  hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  },\n);\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  );\n}\n\nexport { Badge, badgeVariants };\n"], "names": [], "mappings": ";;;;;AACA;AACA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE"}}, {"offset": {"line": 623, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 629, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/src/components/ui/unified-search-input.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useRef, useEffect } from \"react\";\nimport { Input } from \"@/components/ui/input\";\nimport { Button } from \"@/components/ui/button\";\nimport { Badge } from \"@/components/ui/badge\";\nimport {\n  Search,\n  X,\n  Filter,\n  SortAsc,\n  SortDesc,\n  Loader2,\n  Command,\n} from \"lucide-react\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { cn } from \"@/lib/utils\";\n\ninterface UnifiedSearchInputProps {\n  placeholder?: string;\n  value?: string;\n  onChange?: (value: string) => void;\n  onSearch?: (value: string) => void;\n  onClear?: () => void;\n  className?: string;\n  disabled?: boolean;\n  loading?: boolean;\n  showClearButton?: boolean;\n  showSearchButton?: boolean;\n  size?: \"sm\" | \"md\" | \"lg\";\n  variant?: \"default\" | \"ghost\" | \"outline\";\n  autoFocus?: boolean;\n  debounceMs?: number;\n  resultCount?: number;\n  showResultCount?: boolean;\n}\n\nexport function UnifiedSearchInput({\n  placeholder = \"Search...\",\n  value = \"\",\n  onChange,\n  onSearch,\n  onClear,\n  className = \"\",\n  disabled = false,\n  loading = false,\n  showClearButton = true,\n  showSearchButton = false,\n  size = \"md\",\n  variant = \"default\",\n  autoFocus = false,\n  debounceMs = 300,\n  resultCount,\n  showResultCount = false,\n}: UnifiedSearchInputProps) {\n  const [localValue, setLocalValue] = useState(value);\n  const [isFocused, setIsFocused] = useState(false);\n  const inputRef = useRef<HTMLInputElement>(null);\n  const debounceRef = useRef<NodeJS.Timeout | null>(null);\n\n  // Sync with external value\n  useEffect(() => {\n    setLocalValue(value);\n  }, [value]);\n\n  // Auto focus\n  useEffect(() => {\n    if (autoFocus && inputRef.current) {\n      inputRef.current.focus();\n    }\n  }, [autoFocus]);\n\n  // Debounced search\n  useEffect(() => {\n    if (debounceRef.current) {\n      clearTimeout(debounceRef.current);\n    }\n\n    debounceRef.current = setTimeout(() => {\n      if (onChange && localValue !== value) {\n        onChange(localValue);\n      }\n    }, debounceMs);\n\n    return () => {\n      if (debounceRef.current) {\n        clearTimeout(debounceRef.current);\n      }\n    };\n  }, [localValue, onChange, value, debounceMs]);\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const newValue = e.target.value;\n    setLocalValue(newValue);\n  };\n\n  const handleSearch = () => {\n    if (onSearch) {\n      onSearch(localValue);\n    }\n  };\n\n  const handleClear = () => {\n    setLocalValue(\"\");\n    if (onChange) {\n      onChange(\"\");\n    }\n    if (onClear) {\n      onClear();\n    }\n    if (inputRef.current) {\n      inputRef.current.focus();\n    }\n  };\n\n  const handleKeyDown = (e: React.KeyboardEvent) => {\n    if (e.key === \"Enter\") {\n      e.preventDefault();\n      handleSearch();\n    }\n    if (e.key === \"Escape\") {\n      handleClear();\n    }\n  };\n\n  // Size variants\n  const sizeClasses = {\n    sm: \"h-8 text-sm\",\n    md: \"h-10 text-base\",\n    lg: \"h-12 text-lg\",\n  };\n\n  // Padding based on buttons\n  const getPaddingClasses = () => {\n    let leftPadding = \"pl-10\"; // For search icon\n    let rightPadding = \"pr-4\";\n\n    if (showClearButton && localValue) {\n      rightPadding = showSearchButton ? \"pr-20\" : \"pr-10\";\n    } else if (showSearchButton) {\n      rightPadding = \"pr-12\";\n    }\n\n    return `${leftPadding} ${rightPadding}`;\n  };\n\n  // Variant styles\n  const variantClasses = {\n    default:\n      \"bg-[var(--input-background)] border-[var(--input-border-color)] text-[var(--input-text)]\",\n    ghost: \"border-transparent bg-transparent hover:bg-[var(--card-hover)]\",\n    outline: \"border-[var(--card-border-color)] bg-transparent\",\n  };\n\n  return (\n    <div className={cn(\"relative w-full\", className)}>\n      {/* Search Input */}\n      <div className=\"relative\">\n        {/* Search Icon */}\n        <Search\n          className={cn(\n            \"absolute left-3 top-1/2 transform -translate-y-1/2 text-[var(--paragraph)] transition-colors\",\n            size === \"sm\" ? \"h-3 w-3\" : size === \"lg\" ? \"h-5 w-5\" : \"h-4 w-4\",\n            isFocused && \"text-[var(--link-color)]\",\n          )}\n        />\n\n        {/* Input Field */}\n        <Input\n          ref={inputRef}\n          type=\"text\"\n          placeholder={placeholder}\n          value={localValue}\n          onChange={handleInputChange}\n          onKeyDown={handleKeyDown}\n          onFocus={() => setIsFocused(true)}\n          onBlur={() => setIsFocused(false)}\n          disabled={disabled || loading}\n          className={cn(\n            sizeClasses[size],\n            getPaddingClasses(),\n            variantClasses[variant],\n            \"transition-all duration-200\",\n            isFocused && \"ring-2 ring-[var(--link-color)] ring-opacity-20\",\n            loading && \"cursor-wait\",\n          )}\n        />\n\n        {/* Right Side Buttons */}\n        <div className=\"absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center gap-1\">\n          {/* Loading Spinner */}\n          {loading && (\n            <Loader2\n              className={cn(\n                \"animate-spin text-[var(--paragraph)]\",\n                size === \"sm\"\n                  ? \"h-3 w-3\"\n                  : size === \"lg\"\n                    ? \"h-5 w-5\"\n                    : \"h-4 w-4\",\n              )}\n            />\n          )}\n\n          {/* Clear Button */}\n          <AnimatePresence>\n            {showClearButton && localValue && !loading && (\n              <motion.div\n                initial={{ opacity: 0, scale: 0.8 }}\n                animate={{ opacity: 1, scale: 1 }}\n                exit={{ opacity: 0, scale: 0.8 }}\n                transition={{ duration: 0.15 }}\n              >\n                <Button\n                  type=\"button\"\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  onClick={handleClear}\n                  disabled={disabled}\n                  className={cn(\n                    \"h-6 w-6 p-0 hover:bg-[var(--card-hover)] rounded-full\",\n                    size === \"sm\" && \"h-5 w-5\",\n                    size === \"lg\" && \"h-7 w-7\",\n                  )}\n                  aria-label=\"Clear search\"\n                >\n                  <X\n                    className={cn(\n                      size === \"sm\"\n                        ? \"h-3 w-3\"\n                        : size === \"lg\"\n                          ? \"h-4 w-4\"\n                          : \"h-3 w-3\",\n                    )}\n                  />\n                </Button>\n              </motion.div>\n            )}\n          </AnimatePresence>\n\n          {/* Search Button */}\n          {showSearchButton && (\n            <Button\n              type=\"button\"\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={handleSearch}\n              disabled={disabled || loading || !localValue.trim()}\n              className={cn(\n                \"h-6 w-6 p-0 hover:bg-[var(--card-hover)] rounded-full\",\n                size === \"sm\" && \"h-5 w-5\",\n                size === \"lg\" && \"h-7 w-7\",\n              )}\n              aria-label=\"Search\"\n            >\n              <Command\n                className={cn(\n                  size === \"sm\"\n                    ? \"h-3 w-3\"\n                    : size === \"lg\"\n                      ? \"h-4 w-4\"\n                      : \"h-3 w-3\",\n                )}\n              />\n            </Button>\n          )}\n        </div>\n      </div>\n\n      {/* Result Count */}\n      {showResultCount && resultCount !== undefined && (\n        <motion.div\n          initial={{ opacity: 0, y: -10 }}\n          animate={{ opacity: 1, y: 0 }}\n          className=\"absolute top-full left-0 mt-1 z-10\"\n        >\n          <Badge variant=\"secondary\" className=\"text-xs\">\n            {resultCount} {resultCount === 1 ? \"result\" : \"results\"}\n          </Badge>\n        </motion.div>\n      )}\n\n      {/* Keyboard Shortcuts Hint */}\n      {isFocused && (\n        <motion.div\n          initial={{ opacity: 0, y: -10 }}\n          animate={{ opacity: 1, y: 0 }}\n          exit={{ opacity: 0, y: -10 }}\n          className=\"absolute top-full right-0 mt-1 z-10\"\n        >\n          <div className=\"flex items-center gap-1 text-xs text-[var(--paragraph)] bg-[var(--card-background)] border border-[var(--card-border-color)] rounded px-2 py-1\">\n            <kbd className=\"px-1 py-0.5 bg-[var(--card-hover)] rounded text-xs\">\n              Enter\n            </kbd>\n            <span>to search</span>\n            <kbd className=\"px-1 py-0.5 bg-[var(--card-hover)] rounded text-xs ml-2\">\n              Esc\n            </kbd>\n            <span>to clear</span>\n          </div>\n        </motion.div>\n      )}\n    </div>\n  );\n}\n\n// Export variants for easy use\nexport const SearchInput = (props: UnifiedSearchInputProps) => (\n  <UnifiedSearchInput {...props} />\n);\n\nexport const CompactSearchInput = (props: UnifiedSearchInputProps) => (\n  <UnifiedSearchInput {...props} size=\"sm\" variant=\"ghost\" />\n);\n\nexport const LargeSearchInput = (props: UnifiedSearchInputProps) => (\n  <UnifiedSearchInput {...props} size=\"lg\" showSearchButton />\n);\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AACA;AACA;AAWA;AAVA;AAAA;AASA;AAAA;AATA;AAAA;AANA;;;;;;;;;AAqCO,SAAS,mBAAmB,EACjC,cAAc,WAAW,EACzB,QAAQ,EAAE,EACV,QAAQ,EACR,QAAQ,EACR,OAAO,EACP,YAAY,EAAE,EACd,WAAW,KAAK,EAChB,UAAU,KAAK,EACf,kBAAkB,IAAI,EACtB,mBAAmB,KAAK,EACxB,OAAO,IAAI,EACX,UAAU,SAAS,EACnB,YAAY,KAAK,EACjB,aAAa,GAAG,EAChB,WAAW,EACX,kBAAkB,KAAK,EACC;IACxB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAyB;IAElD,2BAA2B;IAC3B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,cAAc;IAChB,GAAG;QAAC;KAAM;IAEV,aAAa;IACb,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,aAAa,SAAS,OAAO,EAAE;YACjC,SAAS,OAAO,CAAC,KAAK;QACxB;IACF,GAAG;QAAC;KAAU;IAEd,mBAAmB;IACnB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,YAAY,OAAO,EAAE;YACvB,aAAa,YAAY,OAAO;QAClC;QAEA,YAAY,OAAO,GAAG,WAAW;YAC/B,IAAI,YAAY,eAAe,OAAO;gBACpC,SAAS;YACX;QACF,GAAG;QAEH,OAAO;YACL,IAAI,YAAY,OAAO,EAAE;gBACvB,aAAa,YAAY,OAAO;YAClC;QACF;IACF,GAAG;QAAC;QAAY;QAAU;QAAO;KAAW;IAE5C,MAAM,oBAAoB,CAAC;QACzB,MAAM,WAAW,EAAE,MAAM,CAAC,KAAK;QAC/B,cAAc;IAChB;IAEA,MAAM,eAAe;QACnB,IAAI,UAAU;YACZ,SAAS;QACX;IACF;IAEA,MAAM,cAAc;QAClB,cAAc;QACd,IAAI,UAAU;YACZ,SAAS;QACX;QACA,IAAI,SAAS;YACX;QACF;QACA,IAAI,SAAS,OAAO,EAAE;YACpB,SAAS,OAAO,CAAC,KAAK;QACxB;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,EAAE,GAAG,KAAK,SAAS;YACrB,EAAE,cAAc;YAChB;QACF;QACA,IAAI,EAAE,GAAG,KAAK,UAAU;YACtB;QACF;IACF;IAEA,gBAAgB;IAChB,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,2BAA2B;IAC3B,MAAM,oBAAoB;QACxB,IAAI,cAAc,SAAS,kBAAkB;QAC7C,IAAI,eAAe;QAEnB,IAAI,mBAAmB,YAAY;YACjC,eAAe,mBAAmB,UAAU;QAC9C,OAAO,IAAI,kBAAkB;YAC3B,eAAe;QACjB;QAEA,OAAO,GAAG,YAAY,CAAC,EAAE,cAAc;IACzC;IAEA,iBAAiB;IACjB,MAAM,iBAAiB;QACrB,SACE;QACF,OAAO;QACP,SAAS;IACX;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;;0BAEpC,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,sMAAA,CAAA,SAAM;wBACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gGACA,SAAS,OAAO,YAAY,SAAS,OAAO,YAAY,WACxD,aAAa;;;;;;kCAKjB,8OAAC,iIAAA,CAAA,QAAK;wBACJ,KAAK;wBACL,MAAK;wBACL,aAAa;wBACb,OAAO;wBACP,UAAU;wBACV,WAAW;wBACX,SAAS,IAAM,aAAa;wBAC5B,QAAQ,IAAM,aAAa;wBAC3B,UAAU,YAAY;wBACtB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,WAAW,CAAC,KAAK,EACjB,qBACA,cAAc,CAAC,QAAQ,EACvB,+BACA,aAAa,mDACb,WAAW;;;;;;kCAKf,8OAAC;wBAAI,WAAU;;4BAEZ,yBACC,8OAAC,iNAAA,CAAA,UAAO;gCACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wCACA,SAAS,OACL,YACA,SAAS,OACP,YACA;;;;;;0CAMZ,8OAAC,yLAAA,CAAA,kBAAe;0CACb,mBAAmB,cAAc,CAAC,yBACjC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAI;oCAClC,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAE;oCAChC,MAAM;wCAAE,SAAS;wCAAG,OAAO;oCAAI;oCAC/B,YAAY;wCAAE,UAAU;oCAAK;8CAE7B,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,UAAU;wCACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yDACA,SAAS,QAAQ,WACjB,SAAS,QAAQ;wCAEnB,cAAW;kDAEX,cAAA,8OAAC,4LAAA,CAAA,IAAC;4CACA,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,SAAS,OACL,YACA,SAAS,OACP,YACA;;;;;;;;;;;;;;;;;;;;;4BASjB,kCACC,8OAAC,kIAAA,CAAA,SAAM;gCACL,MAAK;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,UAAU,YAAY,WAAW,CAAC,WAAW,IAAI;gCACjD,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yDACA,SAAS,QAAQ,WACjB,SAAS,QAAQ;gCAEnB,cAAW;0CAEX,cAAA,8OAAC,wMAAA,CAAA,UAAO;oCACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,SAAS,OACL,YACA,SAAS,OACP,YACA;;;;;;;;;;;;;;;;;;;;;;;YASjB,mBAAmB,gBAAgB,2BAClC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG,CAAC;gBAAG;gBAC9B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,WAAU;0BAEV,cAAA,8OAAC,iIAAA,CAAA,QAAK;oBAAC,SAAQ;oBAAY,WAAU;;wBAClC;wBAAY;wBAAE,gBAAgB,IAAI,WAAW;;;;;;;;;;;;YAMnD,2BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG,CAAC;gBAAG;gBAC9B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,MAAM;oBAAE,SAAS;oBAAG,GAAG,CAAC;gBAAG;gBAC3B,WAAU;0BAEV,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCAAqD;;;;;;sCAGpE,8OAAC;sCAAK;;;;;;sCACN,8OAAC;4BAAI,WAAU;sCAA0D;;;;;;sCAGzE,8OAAC;sCAAK;;;;;;;;;;;;;;;;;;;;;;;AAMlB;AAGO,MAAM,cAAc,CAAC,sBAC1B,8OAAC;QAAoB,GAAG,KAAK;;;;;;AAGxB,MAAM,qBAAqB,CAAC,sBACjC,8OAAC;QAAoB,GAAG,KAAK;QAAE,MAAK;QAAK,SAAQ;;;;;;AAG5C,MAAM,mBAAmB,CAAC,sBAC/B,8OAAC;QAAoB,GAAG,KAAK;QAAE,MAAK;QAAK,gBAAgB"}}, {"offset": {"line": 985, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 991, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/src/components/ui/search-bar.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { Button } from \"@/components/ui/button\";\nimport { Input } from \"@/components/ui/input\";\nimport { UnifiedSearchInput } from \"@/components/ui/unified-search-input\";\nimport { Filter, X } from \"lucide-react\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport {\n  Popover,\n  PopoverContent,\n  PopoverTrigger,\n} from \"@/components/ui/popover\";\nimport { Badge } from \"@/components/ui/badge\";\n\ninterface SearchBarProps {\n  placeholder?: string;\n  onSearch: (query: string) => void;\n  onFilterChange?: (filters: Record<string, any>) => void;\n  filters?: Array<{\n    key: string;\n    label: string;\n    type: \"select\" | \"multiselect\" | \"date\" | \"range\";\n    options?: Array<{ value: string; label: string }>;\n  }>;\n  className?: string;\n  showFilters?: boolean;\n  debounceMs?: number;\n}\n\nexport function SearchBar({\n  placeholder = \"Search...\",\n  onSearch,\n  onFilterChange,\n  filters = [],\n  className = \"\",\n  showFilters = true,\n  debounceMs = 300,\n}: SearchBarProps) {\n  const [query, setQuery] = useState(\"\");\n  const [activeFilters, setActiveFilters] = useState<Record<string, any>>({});\n  const [showFilterPanel, setShowFilterPanel] = useState(false);\n\n  // Debounced search\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      onSearch(query);\n    }, debounceMs);\n\n    return () => clearTimeout(timer);\n  }, [query, onSearch, debounceMs]);\n\n  // Handle filter changes\n  useEffect(() => {\n    if (onFilterChange) {\n      onFilterChange(activeFilters);\n    }\n  }, [activeFilters, onFilterChange]);\n\n  const handleFilterChange = (key: string, value: any) => {\n    setActiveFilters((prev) => ({\n      ...prev,\n      [key]: value,\n    }));\n  };\n\n  const removeFilter = (key: string) => {\n    setActiveFilters((prev) => {\n      const newFilters = { ...prev };\n      delete newFilters[key];\n      return newFilters;\n    });\n  };\n\n  const clearAllFilters = () => {\n    setActiveFilters({});\n  };\n\n  const clearSearch = () => {\n    setQuery(\"\");\n  };\n\n  const activeFilterCount = Object.keys(activeFilters).filter(\n    (key) => activeFilters[key] !== undefined && activeFilters[key] !== \"\",\n  ).length;\n\n  return (\n    <div className={`space-y-3 ${className}`}>\n      {/* Unified Search Input */}\n      <div className=\"flex items-center gap-2\">\n        <UnifiedSearchInput\n          placeholder={placeholder}\n          value={query}\n          onChange={setQuery}\n          onSearch={onSearch}\n          className=\"flex-1\"\n        />\n\n        {showFilters && filters.length > 0 && (\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={() => setShowFilterPanel(!showFilterPanel)}\n            className=\"flex items-center gap-2\"\n          >\n            <Filter className=\"h-4 w-4\" />\n            Filters\n            {activeFilterCount > 0 && (\n              <Badge variant=\"secondary\" className=\"ml-1\">\n                {activeFilterCount}\n              </Badge>\n            )}\n          </Button>\n        )}\n      </div>\n\n      {/* Filter Panel */}\n      {showFilters && filters.length > 0 && showFilterPanel && (\n        <motion.div\n          initial={{ opacity: 0, height: 0 }}\n          animate={{ opacity: 1, height: \"auto\" }}\n          exit={{ opacity: 0, height: 0 }}\n          className=\"border border-[var(--card-border-color)] rounded-[12px] p-4 bg-[var(--card-background)]\"\n        >\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center justify-between\">\n              <h4 className=\"font-medium\">Filters</h4>\n              {activeFilterCount > 0 && (\n                <Button\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  onClick={clearAllFilters}\n                  className=\"text-xs\"\n                >\n                  Clear All\n                </Button>\n              )}\n            </div>\n\n            {filters.map((filter) => (\n              <div key={filter.key} className=\"space-y-2\">\n                <label className=\"text-sm font-medium\">{filter.label}</label>\n\n                {filter.type === \"select\" && (\n                  <select\n                    value={activeFilters[filter.key] || \"\"}\n                    onChange={(e) =>\n                      handleFilterChange(filter.key, e.target.value)\n                    }\n                    className=\"w-full p-2 border rounded-md bg-[var(--input-background)] border-[var(--input-border-color)]\"\n                  >\n                    <option value=\"\">All</option>\n                    {filter.options?.map((option) => (\n                      <option key={option.value} value={option.value}>\n                        {option.label}\n                      </option>\n                    ))}\n                  </select>\n                )}\n\n                {filter.type === \"multiselect\" && (\n                  <div className=\"space-y-1\">\n                    {filter.options?.map((option) => (\n                      <label\n                        key={option.value}\n                        className=\"flex items-center space-x-2\"\n                      >\n                        <input\n                          type=\"checkbox\"\n                          checked={(activeFilters[filter.key] || []).includes(\n                            option.value,\n                          )}\n                          onChange={(e) => {\n                            const currentValues =\n                              activeFilters[filter.key] || [];\n                            if (e.target.checked) {\n                              handleFilterChange(filter.key, [\n                                ...currentValues,\n                                option.value,\n                              ]);\n                            } else {\n                              handleFilterChange(\n                                filter.key,\n                                currentValues.filter(\n                                  (v: string) => v !== option.value,\n                                ),\n                              );\n                            }\n                          }}\n                          className=\"rounded\"\n                        />\n                        <span className=\"text-sm\">{option.label}</span>\n                      </label>\n                    ))}\n                  </div>\n                )}\n\n                {filter.type === \"date\" && (\n                  <Input\n                    type=\"date\"\n                    value={activeFilters[filter.key] || \"\"}\n                    onChange={(e) =>\n                      handleFilterChange(filter.key, e.target.value)\n                    }\n                    className=\"bg-[var(--input-background)] border-[var(--input-border-color)]\"\n                  />\n                )}\n              </div>\n            ))}\n          </div>\n        </motion.div>\n      )}\n\n      {/* Active Filters */}\n      <AnimatePresence>\n        {activeFilterCount > 0 && (\n          <motion.div\n            initial={{ opacity: 0, height: 0 }}\n            animate={{ opacity: 1, height: \"auto\" }}\n            exit={{ opacity: 0, height: 0 }}\n            className=\"flex flex-wrap gap-2\"\n          >\n            {Object.entries(activeFilters).map(([key, value]) => {\n              if (!value || (Array.isArray(value) && value.length === 0))\n                return null;\n\n              const filter = filters.find((f) => f.key === key);\n              if (!filter) return null;\n\n              const displayValue = Array.isArray(value)\n                ? value.join(\", \")\n                : filter.options?.find((opt) => opt.value === value)?.label ||\n                  value;\n\n              return (\n                <motion.div\n                  key={key}\n                  initial={{ opacity: 0, scale: 0.8 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  exit={{ opacity: 0, scale: 0.8 }}\n                >\n                  <Badge\n                    variant=\"secondary\"\n                    className=\"flex items-center gap-1 pr-1\"\n                  >\n                    <span className=\"text-xs\">\n                      {filter.label}: {displayValue}\n                    </span>\n                    <button\n                      onClick={() => removeFilter(key)}\n                      className=\"ml-1 hover:bg-[var(--card-hover)] rounded-full p-0.5\"\n                    >\n                      <X className=\"h-3 w-3\" />\n                    </button>\n                  </Badge>\n                </motion.div>\n              );\n            })}\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAQA;AAPA;AACA;AAAA;AADA;AANA;;;;;;;;;AA8BO,SAAS,UAAU,EACxB,cAAc,WAAW,EACzB,QAAQ,EACR,cAAc,EACd,UAAU,EAAE,EACZ,YAAY,EAAE,EACd,cAAc,IAAI,EAClB,aAAa,GAAG,EACD;IACf,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB,CAAC;IACzE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,mBAAmB;IACnB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,QAAQ,WAAW;YACvB,SAAS;QACX,GAAG;QAEH,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;QAAO;QAAU;KAAW;IAEhC,wBAAwB;IACxB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,gBAAgB;YAClB,eAAe;QACjB;IACF,GAAG;QAAC;QAAe;KAAe;IAElC,MAAM,qBAAqB,CAAC,KAAa;QACvC,iBAAiB,CAAC,OAAS,CAAC;gBAC1B,GAAG,IAAI;gBACP,CAAC,IAAI,EAAE;YACT,CAAC;IACH;IAEA,MAAM,eAAe,CAAC;QACpB,iBAAiB,CAAC;YAChB,MAAM,aAAa;gBAAE,GAAG,IAAI;YAAC;YAC7B,OAAO,UAAU,CAAC,IAAI;YACtB,OAAO;QACT;IACF;IAEA,MAAM,kBAAkB;QACtB,iBAAiB,CAAC;IACpB;IAEA,MAAM,cAAc;QAClB,SAAS;IACX;IAEA,MAAM,oBAAoB,OAAO,IAAI,CAAC,eAAe,MAAM,CACzD,CAAC,MAAQ,aAAa,CAAC,IAAI,KAAK,aAAa,aAAa,CAAC,IAAI,KAAK,IACpE,MAAM;IAER,qBACE,8OAAC;QAAI,WAAW,CAAC,UAAU,EAAE,WAAW;;0BAEtC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,sJAAA,CAAA,qBAAkB;wBACjB,aAAa;wBACb,OAAO;wBACP,UAAU;wBACV,UAAU;wBACV,WAAU;;;;;;oBAGX,eAAe,QAAQ,MAAM,GAAG,mBAC/B,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS,IAAM,mBAAmB,CAAC;wBACnC,WAAU;;0CAEV,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;4BAAY;4BAE7B,oBAAoB,mBACnB,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAY,WAAU;0CAClC;;;;;;;;;;;;;;;;;;YAQV,eAAe,QAAQ,MAAM,GAAG,KAAK,iCACpC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,QAAQ;gBAAE;gBACjC,SAAS;oBAAE,SAAS;oBAAG,QAAQ;gBAAO;gBACtC,MAAM;oBAAE,SAAS;oBAAG,QAAQ;gBAAE;gBAC9B,WAAU;0BAEV,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAc;;;;;;gCAC3B,oBAAoB,mBACnB,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;wBAMJ,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC;gCAAqB,WAAU;;kDAC9B,8OAAC;wCAAM,WAAU;kDAAuB,OAAO,KAAK;;;;;;oCAEnD,OAAO,IAAI,KAAK,0BACf,8OAAC;wCACC,OAAO,aAAa,CAAC,OAAO,GAAG,CAAC,IAAI;wCACpC,UAAU,CAAC,IACT,mBAAmB,OAAO,GAAG,EAAE,EAAE,MAAM,CAAC,KAAK;wCAE/C,WAAU;;0DAEV,8OAAC;gDAAO,OAAM;0DAAG;;;;;;4CAChB,OAAO,OAAO,EAAE,IAAI,CAAC,uBACpB,8OAAC;oDAA0B,OAAO,OAAO,KAAK;8DAC3C,OAAO,KAAK;mDADF,OAAO,KAAK;;;;;;;;;;;oCAO9B,OAAO,IAAI,KAAK,+BACf,8OAAC;wCAAI,WAAU;kDACZ,OAAO,OAAO,EAAE,IAAI,CAAC,uBACpB,8OAAC;gDAEC,WAAU;;kEAEV,8OAAC;wDACC,MAAK;wDACL,SAAS,CAAC,aAAa,CAAC,OAAO,GAAG,CAAC,IAAI,EAAE,EAAE,QAAQ,CACjD,OAAO,KAAK;wDAEd,UAAU,CAAC;4DACT,MAAM,gBACJ,aAAa,CAAC,OAAO,GAAG,CAAC,IAAI,EAAE;4DACjC,IAAI,EAAE,MAAM,CAAC,OAAO,EAAE;gEACpB,mBAAmB,OAAO,GAAG,EAAE;uEAC1B;oEACH,OAAO,KAAK;iEACb;4DACH,OAAO;gEACL,mBACE,OAAO,GAAG,EACV,cAAc,MAAM,CAClB,CAAC,IAAc,MAAM,OAAO,KAAK;4DAGvC;wDACF;wDACA,WAAU;;;;;;kEAEZ,8OAAC;wDAAK,WAAU;kEAAW,OAAO,KAAK;;;;;;;+CA3BlC,OAAO,KAAK;;;;;;;;;;oCAiCxB,OAAO,IAAI,KAAK,wBACf,8OAAC,iIAAA,CAAA,QAAK;wCACJ,MAAK;wCACL,OAAO,aAAa,CAAC,OAAO,GAAG,CAAC,IAAI;wCACpC,UAAU,CAAC,IACT,mBAAmB,OAAO,GAAG,EAAE,EAAE,MAAM,CAAC,KAAK;wCAE/C,WAAU;;;;;;;+BAhEN,OAAO,GAAG;;;;;;;;;;;;;;;;0BA0E5B,8OAAC,yLAAA,CAAA,kBAAe;0BACb,oBAAoB,mBACnB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBACjC,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAO;oBACtC,MAAM;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBAC9B,WAAU;8BAET,OAAO,OAAO,CAAC,eAAe,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM;wBAC9C,IAAI,CAAC,SAAU,MAAM,OAAO,CAAC,UAAU,MAAM,MAAM,KAAK,GACtD,OAAO;wBAET,MAAM,SAAS,QAAQ,IAAI,CAAC,CAAC,IAAM,EAAE,GAAG,KAAK;wBAC7C,IAAI,CAAC,QAAQ,OAAO;wBAEpB,MAAM,eAAe,MAAM,OAAO,CAAC,SAC/B,MAAM,IAAI,CAAC,QACX,OAAO,OAAO,EAAE,KAAK,CAAC,MAAQ,IAAI,KAAK,KAAK,QAAQ,SACpD;wBAEJ,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAI;4BAClC,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAE;4BAChC,MAAM;gCAAE,SAAS;gCAAG,OAAO;4BAAI;sCAE/B,cAAA,8OAAC,iIAAA,CAAA,QAAK;gCACJ,SAAQ;gCACR,WAAU;;kDAEV,8OAAC;wCAAK,WAAU;;4CACb,OAAO,KAAK;4CAAC;4CAAG;;;;;;;kDAEnB,8OAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAU;kDAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;;2BAhBZ;;;;;oBAqBX;;;;;;;;;;;;;;;;;AAMZ"}}, {"offset": {"line": 1358, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1364, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/src/components/common/navbar/DesktopNavbar.tsx"], "sourcesContent": ["\"use client\";\n\nimport Link from \"next/link\";\nimport {\n  NavigationMenu,\n  NavigationMenuContent,\n  NavigationMenuItem,\n  NavigationMenuLink,\n  NavigationMenuList,\n  NavigationMenuTrigger,\n} from \"@/components/ui/navigation-menu\";\n\nimport { cn } from \"@/lib/utils\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { BookOpenIcon, InfoIcon, LifeBuoyIcon } from \"lucide-react\";\nimport Logo from \"../Logo\";\nimport { SearchBar } from \"@/components/ui/search-bar\";\n\ntype NavLinkItem = {\n  href: string;\n  label: string;\n  description?: string;\n  icon?: \"BookOpenIcon\" | \"LifeBuoyIcon\" | \"InfoIcon\";\n};\n\ntype NavLink = {\n  href?: string;\n  label: string;\n  submenu?: boolean;\n  type?: \"description\" | \"simple\" | \"icon\";\n  items?: NavLinkItem[];\n};\n\nconst navigationLinks: NavLink[] = [\n  { href: \"#\", label: \"Home\" },\n  {\n    label: \"Features\",\n    submenu: true,\n    type: \"description\",\n    items: [\n      {\n        href: \"#\",\n        label: \"Components\",\n        description: \"Browse all components in the library.\",\n      },\n      {\n        href: \"#\",\n        label: \"Documentation\",\n        description: \"Learn how to use the library.\",\n      },\n      {\n        href: \"#\",\n        label: \"Templates\",\n        description: \"Pre-built layouts for common use cases.\",\n      },\n    ],\n  },\n  {\n    label: \"Pricing\",\n    submenu: true,\n    type: \"simple\",\n    items: [\n      { href: \"#\", label: \"Product A\" },\n      { href: \"#\", label: \"Product B\" },\n      { href: \"#\", label: \"Product C\" },\n      { href: \"#\", label: \"Product D\" },\n    ],\n  },\n  {\n    label: \"About\",\n    submenu: true,\n    type: \"icon\",\n    items: [\n      { href: \"#\", label: \"Getting Started\", icon: \"BookOpenIcon\" },\n      { href: \"#\", label: \"Tutorials\", icon: \"LifeBuoyIcon\" },\n      { href: \"#\", label: \"About Us\", icon: \"InfoIcon\" },\n    ],\n  },\n];\n\nconst renderIcon = (icon?: NavLinkItem[\"icon\"]) => {\n  switch (icon) {\n    case \"BookOpenIcon\":\n      return (\n        <BookOpenIcon\n          size={16}\n          className=\"text-[var(--menu-color)] opacity-60\"\n          aria-hidden=\"true\"\n        />\n      );\n    case \"LifeBuoyIcon\":\n      return (\n        <LifeBuoyIcon\n          size={16}\n          className=\"text-[var(--menu-color)] opacity-60\"\n          aria-hidden=\"true\"\n        />\n      );\n    case \"InfoIcon\":\n      return (\n        <InfoIcon\n          size={16}\n          className=\"text-[var(--menu-color)] opacity-60\"\n          aria-hidden=\"true\"\n        />\n      );\n    default:\n      return null;\n  }\n};\n\nconst renderSubmenuItems = (link: NavLink) => (\n  <ul className={cn(link.type === \"description\" ? \"min-w-64\" : \"min-w-48\")}>\n    {link.items?.map((item, idx) => (\n      <li key={idx}>\n        <NavigationMenuLink\n          href={item.href}\n          className={cn(\n            \"py-1.5 text-[var(--paragraph)] hover:text-[var(--highlight)] flex items-center gap-2\",\n            link.type === \"icon\" && \"pl-1\"\n          )}\n        >\n          {link.type === \"icon\" && item.icon && renderIcon(item.icon)}\n          <span>{item.label}</span>\n          {item.description && (\n            <span className=\"ml-2 text-xs text-[var(--muted)]\">\n              {item.description}\n            </span>\n          )}\n        </NavigationMenuLink>\n      </li>\n    ))}\n  </ul>\n);\n\nexport default function DesktopNavbar() {\n  return (\n    <div className=\"hidden md:flex h-16 items-center justify-between gap-4\">\n      {/* Left: Logo + Menu */}\n      <div className=\"flex items-center gap-6 flex-1\">\n        <Link\n          href=\"/home\"\n          aria-label=\"Cosmos\"\n          className=\"text-[var(--link-color)] hover:text-[var(--link-hover)]\"\n        >\n          <Logo />\n        </Link>\n\n        <NavigationMenu viewport={false}>\n          <NavigationMenuList className=\"gap-2 flex\">\n            {navigationLinks.map((link, index) => (\n              <NavigationMenuItem key={index}>\n                {link.submenu ? (\n                  <>\n                    <NavigationMenuTrigger className=\"text-[var(--nav-item)] hover:text-[var(--highlight)] bg-transparent px-2 py-1.5 font-medium *:[svg]:-me-0.5 *:[svg]:size-3.5\">\n                      {link.label}\n                    </NavigationMenuTrigger>\n                    <NavigationMenuContent className=\"z-50 p-1 bg-[var(--background)] border border-[var(--border)]\">\n                      {renderSubmenuItems(link)}\n                    </NavigationMenuContent>\n                  </>\n                ) : (\n                  <NavigationMenuLink\n                    href={link.href}\n                    className=\"text-[var(--nav-item)] hover:text-[var(--highlight)] py-1.5 font-medium\"\n                  >\n                    {link.label}\n                  </NavigationMenuLink>\n                )}\n              </NavigationMenuItem>\n            ))}\n          </NavigationMenuList>\n        </NavigationMenu>\n      </div>\n\n      {/* Center: Search Bar */}\n      <div className=\"flex-1 flex justify-center\">\n        <SearchBar />\n      </div>\n\n      {/* Right: Auth Buttons */}\n      <div className=\"flex items-center gap-2 flex-1 justify-end\">\n        <Button\n          asChild\n          variant=\"ghost\"\n          size=\"sm\"\n          className=\"text-sm text-[var(--button-text)] hover:bg-[var(--card-hover)]\"\n        >\n          <Link href=\"#\">Sign In</Link>\n        </Button>\n        <Button\n          asChild\n          size=\"sm\"\n          className=\"text-sm bg-[var(--button)] text-[var(--button-text)]\"\n        >\n          <Link href=\"#\">Get Started</Link>\n        </Button>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AASA;AACA;AAEA;AACA;AAFA;AAAA;AAAA;AAdA;;;;;;;;;AAiCA,MAAM,kBAA6B;IACjC;QAAE,MAAM;QAAK,OAAO;IAAO;IAC3B;QACE,OAAO;QACP,SAAS;QACT,MAAM;QACN,OAAO;YACL;gBACE,MAAM;gBACN,OAAO;gBACP,aAAa;YACf;YACA;gBACE,MAAM;gBACN,OAAO;gBACP,aAAa;YACf;YACA;gBACE,MAAM;gBACN,OAAO;gBACP,aAAa;YACf;SACD;IACH;IACA;QACE,OAAO;QACP,SAAS;QACT,MAAM;QACN,OAAO;YACL;gBAAE,MAAM;gBAAK,OAAO;YAAY;YAChC;gBAAE,MAAM;gBAAK,OAAO;YAAY;YAChC;gBAAE,MAAM;gBAAK,OAAO;YAAY;YAChC;gBAAE,MAAM;gBAAK,OAAO;YAAY;SACjC;IACH;IACA;QACE,OAAO;QACP,SAAS;QACT,MAAM;QACN,OAAO;YACL;gBAAE,MAAM;gBAAK,OAAO;gBAAmB,MAAM;YAAe;YAC5D;gBAAE,MAAM;gBAAK,OAAO;gBAAa,MAAM;YAAe;YACtD;gBAAE,MAAM;gBAAK,OAAO;gBAAY,MAAM;YAAW;SAClD;IACH;CACD;AAED,MAAM,aAAa,CAAC;IAClB,OAAQ;QACN,KAAK;YACH,qBACE,8OAAC,kNAAA,CAAA,eAAY;gBACX,MAAM;gBACN,WAAU;gBACV,eAAY;;;;;;QAGlB,KAAK;YACH,qBACE,8OAAC,kNAAA,CAAA,eAAY;gBACX,MAAM;gBACN,WAAU;gBACV,eAAY;;;;;;QAGlB,KAAK;YACH,qBACE,8OAAC,sMAAA,CAAA,WAAQ;gBACP,MAAM;gBACN,WAAU;gBACV,eAAY;;;;;;QAGlB;YACE,OAAO;IACX;AACF;AAEA,MAAM,qBAAqB,CAAC,qBAC1B,8OAAC;QAAG,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,KAAK,IAAI,KAAK,gBAAgB,aAAa;kBAC1D,KAAK,KAAK,EAAE,IAAI,CAAC,MAAM,oBACtB,8OAAC;0BACC,cAAA,8OAAC,8IAAA,CAAA,qBAAkB;oBACjB,MAAM,KAAK,IAAI;oBACf,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wFACA,KAAK,IAAI,KAAK,UAAU;;wBAGzB,KAAK,IAAI,KAAK,UAAU,KAAK,IAAI,IAAI,WAAW,KAAK,IAAI;sCAC1D,8OAAC;sCAAM,KAAK,KAAK;;;;;;wBAChB,KAAK,WAAW,kBACf,8OAAC;4BAAK,WAAU;sCACb,KAAK,WAAW;;;;;;;;;;;;eAZhB;;;;;;;;;;AAqBA,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,4JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,cAAW;wBACX,WAAU;kCAEV,cAAA,8OAAC,oIAAA,CAAA,UAAI;;;;;;;;;;kCAGP,8OAAC,8IAAA,CAAA,iBAAc;wBAAC,UAAU;kCACxB,cAAA,8OAAC,8IAAA,CAAA,qBAAkB;4BAAC,WAAU;sCAC3B,gBAAgB,GAAG,CAAC,CAAC,MAAM,sBAC1B,8OAAC,8IAAA,CAAA,qBAAkB;8CAChB,KAAK,OAAO,iBACX;;0DACE,8OAAC,8IAAA,CAAA,wBAAqB;gDAAC,WAAU;0DAC9B,KAAK,KAAK;;;;;;0DAEb,8OAAC,8IAAA,CAAA,wBAAqB;gDAAC,WAAU;0DAC9B,mBAAmB;;;;;;;qEAIxB,8OAAC,8IAAA,CAAA,qBAAkB;wCACjB,MAAM,KAAK,IAAI;wCACf,WAAU;kDAET,KAAK,KAAK;;;;;;mCAfQ;;;;;;;;;;;;;;;;;;;;;0BAyBjC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,yIAAA,CAAA,YAAS;;;;;;;;;;0BAIZ,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,kIAAA,CAAA,SAAM;wBACL,OAAO;wBACP,SAAQ;wBACR,MAAK;wBACL,WAAU;kCAEV,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;sCAAI;;;;;;;;;;;kCAEjB,8OAAC,kIAAA,CAAA,SAAM;wBACL,OAAO;wBACP,MAAK;wBACL,WAAU;kCAEV,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;sCAAI;;;;;;;;;;;;;;;;;;;;;;;AAKzB"}}, {"offset": {"line": 1672, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1678, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/src/components/common/navbar/MobileNavbar.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport Link from \"next/link\";\nimport { FiMenu, FiX, FiChevronDown, FiChevronUp } from \"react-icons/fi\";\nimport { motion, AnimatePresence } from \"framer-motion\";\n\nimport {\n  NavigationMenuList,\n  NavigationMenuLink,\n} from \"@/components/ui/navigation-menu\";\nimport { cn } from \"@/lib/utils\";\nimport { BookOpenIcon, InfoIcon, LifeBuoyIcon } from \"lucide-react\";\nimport Logo from \"../Logo\";\nimport { SearchBar } from \"@/components/ui/search-bar\";\n\ntype NavLinkItem = {\n  href: string;\n  label: string;\n  description?: string;\n  icon?: \"BookOpenIcon\" | \"LifeBuoyIcon\" | \"InfoIcon\";\n};\n\ntype NavLink = {\n  href?: string;\n  label: string;\n  submenu?: boolean;\n  type?: \"description\" | \"simple\" | \"icon\";\n  items?: NavLinkItem[];\n};\n\nconst navigationLinks: NavLink[] = [\n  { href: \"#\", label: \"Home\" },\n  {\n    label: \"Features\",\n    submenu: true,\n    type: \"description\",\n    items: [\n      {\n        href: \"#\",\n        label: \"Components\",\n        description: \"Browse all components in the library.\",\n      },\n      {\n        href: \"#\",\n        label: \"Documentation\",\n        description: \"Learn how to use the library.\",\n      },\n      {\n        href: \"#\",\n        label: \"Templates\",\n        description: \"Pre-built layouts for common use cases.\",\n      },\n    ],\n  },\n  {\n    label: \"Pricing\",\n    submenu: true,\n    type: \"simple\",\n    items: [\n      { href: \"#\", label: \"Product A\" },\n      { href: \"#\", label: \"Product B\" },\n      { href: \"#\", label: \"Product C\" },\n      { href: \"#\", label: \"Product D\" },\n    ],\n  },\n  {\n    label: \"About\",\n    submenu: true,\n    type: \"icon\",\n    items: [\n      { href: \"#\", label: \"Getting Started\", icon: \"BookOpenIcon\" },\n      { href: \"#\", label: \"Tutorials\", icon: \"LifeBuoyIcon\" },\n      { href: \"#\", label: \"About Us\", icon: \"InfoIcon\" },\n    ],\n  },\n];\n\nconst renderIcon = (icon?: NavLinkItem[\"icon\"]) => {\n  switch (icon) {\n    case \"BookOpenIcon\":\n      return (\n        <BookOpenIcon\n          size={16}\n          className=\"text-[var(--menu-color)] opacity-60\"\n          aria-hidden=\"true\"\n        />\n      );\n    case \"LifeBuoyIcon\":\n      return (\n        <LifeBuoyIcon\n          size={16}\n          className=\"text-[var(--menu-color)] opacity-60\"\n          aria-hidden=\"true\"\n        />\n      );\n    case \"InfoIcon\":\n      return (\n        <InfoIcon\n          size={16}\n          className=\"text-[var(--menu-color)] opacity-60\"\n          aria-hidden=\"true\"\n        />\n      );\n    default:\n      return null;\n  }\n};\n\ninterface MobileNavbarProps {\n  isOpen: boolean;\n  setIsOpen: (open: boolean) => void;\n}\n\nexport default function MobileNavbar({ isOpen, setIsOpen }: MobileNavbarProps) {\n  const [openDropdownIndex, setOpenDropdownIndex] = useState<number | null>(\n    null\n  );\n\n  const toggleDropdown = (index: number) => {\n    setOpenDropdownIndex((prev) => (prev === index ? null : index));\n  };\n\n  return (\n    <>\n      {/* Top bar with logo, search, menu toggle */}\n      <div className=\"flex items-center justify-between h-16 md:hidden gap-2\">\n        <Link\n          href=\"/home\"\n          aria-label=\"Cosmos\"\n          data-testid=\"TopNavBar_CosmosLogo\"\n          className=\"text-[var(--link-color)]\"\n        >\n          <Logo />\n        </Link>\n\n        <SearchBar />\n\n        <button\n          aria-label={isOpen ? \"Close menu\" : \"Open menu\"}\n          className=\"text-[var(--menu-color)]\"\n          onClick={() => setIsOpen(!isOpen)}\n        >\n          {isOpen ? <FiX size={24} /> : <FiMenu size={24} />}\n        </button>\n      </div>\n\n      {/* Animated sliding panel */}\n      <AnimatePresence>\n        {isOpen && (\n          <motion.nav\n            key=\"mobile-menu\"\n            initial={{ x: \"100%\", opacity: 0 }}\n            animate={{ x: 0, opacity: 1 }}\n            exit={{ x: \"100%\", opacity: 0 }}\n            transition={{ duration: 0.3, ease: \"easeInOut\" }}\n            className=\"fixed top-16 right-0 bottom-0 w-72 bg-[var(--background)] border-l border-[var(--border)] shadow-lg z-50 overflow-y-auto\"\n            aria-label=\"Mobile navigation\"\n          >\n            <NavigationMenuList className=\"flex flex-col gap-3 p-4\">\n              {navigationLinks.map((link, i) => (\n                <div key={i} className=\"flex flex-col\">\n                  {link.submenu ? (\n                    <>\n                      <button\n                        onClick={() => toggleDropdown(i)}\n                        className=\"flex justify-between items-center w-full text-[var(--nav-item)] px-2 py-2 text-sm font-medium hover:bg-[var(--card-hover)] rounded\"\n                        aria-expanded={openDropdownIndex === i}\n                      >\n                        {link.label}\n                        {openDropdownIndex === i ? (\n                          <FiChevronUp size={18} />\n                        ) : (\n                          <FiChevronDown size={18} />\n                        )}\n                      </button>\n                      <AnimatePresence>\n                        {openDropdownIndex === i && (\n                          <motion.ul\n                            initial={{ height: 0, opacity: 0 }}\n                            animate={{ height: \"auto\", opacity: 1 }}\n                            exit={{ height: 0, opacity: 0 }}\n                            transition={{ duration: 0.2 }}\n                            className={cn(\n                              \"overflow-hidden pl-4 flex flex-col gap-1\",\n                              link.type === \"description\"\n                                ? \"min-w-64\"\n                                : \"min-w-48\"\n                            )}\n                          >\n                            {link.items?.map((item, idx) => (\n                              <li key={idx}>\n                                <NavigationMenuLink\n                                  href={item.href}\n                                  className=\"block py-1.5 text-[var(--paragraph)] hover:text-[var(--highlight)]\"\n                                >\n                                  {link.type === \"icon\" &&\n                                    item.icon &&\n                                    renderIcon(item.icon)}\n                                  {item.label}\n                                  {item.description && (\n                                    <span className=\"ml-2 text-xs text-[var(--muted)]\">\n                                      {item.description}\n                                    </span>\n                                  )}\n                                </NavigationMenuLink>\n                              </li>\n                            ))}\n                          </motion.ul>\n                        )}\n                      </AnimatePresence>\n                    </>\n                  ) : (\n                    <NavigationMenuLink\n                      href={link.href}\n                      className=\"py-2 px-2 text-[var(--paragraph)] hover:text-[var(--highlight)] block font-medium\"\n                    >\n                      {link.label}\n                    </NavigationMenuLink>\n                  )}\n                </div>\n              ))}\n            </NavigationMenuList>\n          </motion.nav>\n        )}\n      </AnimatePresence>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAIA;AAIA;AAEA;AACA;AAFA;AAAA;AAAA;AARA;AACA;AAAA;AALA;;;;;;;;;;;AA+BA,MAAM,kBAA6B;IACjC;QAAE,MAAM;QAAK,OAAO;IAAO;IAC3B;QACE,OAAO;QACP,SAAS;QACT,MAAM;QACN,OAAO;YACL;gBACE,MAAM;gBACN,OAAO;gBACP,aAAa;YACf;YACA;gBACE,MAAM;gBACN,OAAO;gBACP,aAAa;YACf;YACA;gBACE,MAAM;gBACN,OAAO;gBACP,aAAa;YACf;SACD;IACH;IACA;QACE,OAAO;QACP,SAAS;QACT,MAAM;QACN,OAAO;YACL;gBAAE,MAAM;gBAAK,OAAO;YAAY;YAChC;gBAAE,MAAM;gBAAK,OAAO;YAAY;YAChC;gBAAE,MAAM;gBAAK,OAAO;YAAY;YAChC;gBAAE,MAAM;gBAAK,OAAO;YAAY;SACjC;IACH;IACA;QACE,OAAO;QACP,SAAS;QACT,MAAM;QACN,OAAO;YACL;gBAAE,MAAM;gBAAK,OAAO;gBAAmB,MAAM;YAAe;YAC5D;gBAAE,MAAM;gBAAK,OAAO;gBAAa,MAAM;YAAe;YACtD;gBAAE,MAAM;gBAAK,OAAO;gBAAY,MAAM;YAAW;SAClD;IACH;CACD;AAED,MAAM,aAAa,CAAC;IAClB,OAAQ;QACN,KAAK;YACH,qBACE,8OAAC,kNAAA,CAAA,eAAY;gBACX,MAAM;gBACN,WAAU;gBACV,eAAY;;;;;;QAGlB,KAAK;YACH,qBACE,8OAAC,kNAAA,CAAA,eAAY;gBACX,MAAM;gBACN,WAAU;gBACV,eAAY;;;;;;QAGlB,KAAK;YACH,qBACE,8OAAC,sMAAA,CAAA,WAAQ;gBACP,MAAM;gBACN,WAAU;gBACV,eAAY;;;;;;QAGlB;YACE,OAAO;IACX;AACF;AAOe,SAAS,aAAa,EAAE,MAAM,EAAE,SAAS,EAAqB;IAC3E,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EACvD;IAGF,MAAM,iBAAiB,CAAC;QACtB,qBAAqB,CAAC,OAAU,SAAS,QAAQ,OAAO;IAC1D;IAEA,qBACE;;0BAEE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,4JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,cAAW;wBACX,eAAY;wBACZ,WAAU;kCAEV,cAAA,8OAAC,oIAAA,CAAA,UAAI;;;;;;;;;;kCAGP,8OAAC,yIAAA,CAAA,YAAS;;;;;kCAEV,8OAAC;wBACC,cAAY,SAAS,eAAe;wBACpC,WAAU;wBACV,SAAS,IAAM,UAAU,CAAC;kCAEzB,uBAAS,8OAAC,8IAAA,CAAA,MAAG;4BAAC,MAAM;;;;;iDAAS,8OAAC,8IAAA,CAAA,SAAM;4BAAC,MAAM;;;;;;;;;;;;;;;;;0BAKhD,8OAAC,yLAAA,CAAA,kBAAe;0BACb,wBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBAET,SAAS;wBAAE,GAAG;wBAAQ,SAAS;oBAAE;oBACjC,SAAS;wBAAE,GAAG;wBAAG,SAAS;oBAAE;oBAC5B,MAAM;wBAAE,GAAG;wBAAQ,SAAS;oBAAE;oBAC9B,YAAY;wBAAE,UAAU;wBAAK,MAAM;oBAAY;oBAC/C,WAAU;oBACV,cAAW;8BAEX,cAAA,8OAAC,8IAAA,CAAA,qBAAkB;wBAAC,WAAU;kCAC3B,gBAAgB,GAAG,CAAC,CAAC,MAAM,kBAC1B,8OAAC;gCAAY,WAAU;0CACpB,KAAK,OAAO,iBACX;;sDACE,8OAAC;4CACC,SAAS,IAAM,eAAe;4CAC9B,WAAU;4CACV,iBAAe,sBAAsB;;gDAEpC,KAAK,KAAK;gDACV,sBAAsB,kBACrB,8OAAC,8IAAA,CAAA,cAAW;oDAAC,MAAM;;;;;yEAEnB,8OAAC,8IAAA,CAAA,gBAAa;oDAAC,MAAM;;;;;;;;;;;;sDAGzB,8OAAC,yLAAA,CAAA,kBAAe;sDACb,sBAAsB,mBACrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;gDACR,SAAS;oDAAE,QAAQ;oDAAG,SAAS;gDAAE;gDACjC,SAAS;oDAAE,QAAQ;oDAAQ,SAAS;gDAAE;gDACtC,MAAM;oDAAE,QAAQ;oDAAG,SAAS;gDAAE;gDAC9B,YAAY;oDAAE,UAAU;gDAAI;gDAC5B,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4CACA,KAAK,IAAI,KAAK,gBACV,aACA;0DAGL,KAAK,KAAK,EAAE,IAAI,CAAC,MAAM,oBACtB,8OAAC;kEACC,cAAA,8OAAC,8IAAA,CAAA,qBAAkB;4DACjB,MAAM,KAAK,IAAI;4DACf,WAAU;;gEAET,KAAK,IAAI,KAAK,UACb,KAAK,IAAI,IACT,WAAW,KAAK,IAAI;gEACrB,KAAK,KAAK;gEACV,KAAK,WAAW,kBACf,8OAAC;oEAAK,WAAU;8EACb,KAAK,WAAW;;;;;;;;;;;;uDAXhB;;;;;;;;;;;;;;;;iEAsBnB,8OAAC,8IAAA,CAAA,qBAAkB;oCACjB,MAAM,KAAK,IAAI;oCACf,WAAU;8CAET,KAAK,KAAK;;;;;;+BAxDP;;;;;;;;;;mBAVV;;;;;;;;;;;;AA6EhB"}}, {"offset": {"line": 2010, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2016, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/src/components/common/navbar/Navbar.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport DesktopNavbar from \"./DesktopNavbar\";\nimport MobileNavbar from \"./MobileNavbar\";\n\nexport default function Navbar() {\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);\n\n  return (\n    <header className=\"border-b border-[var(--border)] bg-[var(--background)]\">\n      <div className=\"container mx-auto px-4\">\n        {/* Show Mobile or Desktop Nav based on screen */}\n        <MobileNavbar isOpen={mobileMenuOpen} setIsOpen={setMobileMenuOpen} />\n        <DesktopNavbar />\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,sJAAA,CAAA,UAAY;oBAAC,QAAQ;oBAAgB,WAAW;;;;;;8BACjD,8OAAC,uJAAA,CAAA,UAAa;;;;;;;;;;;;;;;;AAItB"}}, {"offset": {"line": 2060, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2066, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/src/contexts/NavbarContext.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { createContext, useContext, useState, ReactNode } from \"react\";\n\ntype NavbarContextType = {\n  mobileMenuOpen: boolean;\n  setMobileMenuOpen: (open: boolean) => void;\n  openDropdownIndex: number | null;\n  setOpenDropdownIndex: (index: number | null) => void;\n};\n\nconst NavbarContext = createContext<NavbarContextType | undefined>(undefined);\n\nexport function NavbarProvider({ children }: { children: ReactNode }) {\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);\n  const [openDropdownIndex, setOpenDropdownIndex] = useState<number | null>(\n    null\n  );\n\n  return (\n    <NavbarContext.Provider\n      value={{\n        mobileMenuOpen,\n        setMobileMenuOpen,\n        openDropdownIndex,\n        setOpenDropdownIndex,\n      }}\n    >\n      {children}\n    </NavbarContext.Provider>\n  );\n}\n\nexport function useNavbarContext() {\n  const context = useContext(NavbarContext);\n  if (!context) {\n    throw new Error(\"useNavbarContext must be used within a NavbarProvider\");\n  }\n  return context;\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAWA,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAiC;AAE5D,SAAS,eAAe,EAAE,QAAQ,EAA2B;IAClE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EACvD;IAGF,qBACE,8OAAC,cAAc,QAAQ;QACrB,OAAO;YACL;YACA;YACA;YACA;QACF;kBAEC;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT"}}, {"offset": {"line": 2100, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}
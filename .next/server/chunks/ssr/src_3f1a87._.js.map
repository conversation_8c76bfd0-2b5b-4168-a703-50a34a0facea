{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/src/components/ui/backgroundEffect.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ui/backgroundEffect.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/backgroundEffect.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA0S,GACvU,wEACA"}}, {"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/src/components/ui/backgroundEffect.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ui/backgroundEffect.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/backgroundEffect.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAsR,GACnT,oDACA"}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 35, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 45, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/src/components/ui/FloatingActionButton.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ui/FloatingActionButton.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/FloatingActionButton.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA8S,GAC3U,4EACA"}}, {"offset": {"line": 53, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 59, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/src/components/ui/FloatingActionButton.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ui/FloatingActionButton.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/FloatingActionButton.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA0R,GACvT,wDACA"}}, {"offset": {"line": 67, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 73, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 77, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 83, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\";\nimport { twMerge } from \"tailwind-merge\";\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB"}}, {"offset": {"line": 93, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 99, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport { cva, type VariantProps } from \"class-variance-authority\";\nimport { Slot } from \"@radix-ui/react-slot\";\n\nimport { cn } from \"@/lib/utils\";\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-[8px] text-sm font-medium transition-[color,box-shadow] disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 [&_svg]:shrink-0 outline-none focus-visible:border-[var(--highlight)] focus-visible:ring-[var(--highlight)]/50 focus-visible:ring-[3px]\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-[var(--button)] text-[var(--button-text)] shadow-sm hover:bg-[color-mix(in_srgb,var(--button),#00000020)]\",\n        destructive:\n          \"bg-[var(--tertiary)] text-[var(--button-text)] shadow-xs hover:bg-[color-mix(in_srgb,var(--tertiary),#00000020)] focus-visible:ring-[var(--tertiary)]/20\",\n        outline:\n          \"border border-[var(--input-border-color)] bg-[var(--background)] shadow-xs hover:bg-[var(--card-hover)] hover:text-[var(--highlight)]\",\n        secondary:\n          \"bg-[var(--secondary)] text-[var(--button-text)] shadow-xs hover:bg-[color-mix(in_srgb,var(--secondary),#00000020)]\",\n        ghost: \"hover:bg-[var(--card-hover)] hover:text-[var(--highlight)]\",\n        link: \"text-[var(--link-color)] underline-offset-4 hover:text-[var(--link-hover)] hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2\",\n        sm: \"h-8 rounded-[8px] px-3 text-xs\",\n        lg: \"h-10 rounded-[8px] px-8\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n);\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean;\n  }) {\n  const Comp = asChild ? Slot : \"button\";\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  );\n}\n\nexport { Button, buttonVariants };\n"], "names": [], "mappings": ";;;;;AACA;AAGA;AAFA;;;;;AAIA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,6XACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf"}}, {"offset": {"line": 150, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 156, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/src/components/common/Navbar.tsx"], "sourcesContent": ["import Link from \"next/link\";\nimport { useState } from \"react\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { FiSearch } from \"react-icons/fi\";\n\nexport default function Navbar() {\n  const [search, setSearch] = useState(\"\");\n\n  return (\n    <header id=\"header-container\" className=\"border-b border-gray-200 bg-white\">\n      <div className=\"flex justify-between items-center px-6 py-3 max-w-[1200px] mx-auto\">\n        {/* Left side: logo + nav links */}\n        <div className=\"flex items-center gap-8\">\n          {/* Logo linking to /discover */}\n          <Link\n            href=\"/discover\"\n            aria-label=\"Cosmos\"\n            data-testid=\"TopNavBar_CosmosLogo\"\n            className=\"inline-block\"\n          >\n            <svg\n              width=\"90\"\n              height=\"14.169\"\n              viewBox=\"0 0 90 14.169\"\n              fill=\"none\"\n              xmlns=\"http://www.w3.org/2000/svg\"\n              className=\"text-black\"\n            >\n              <g\n                clipPath=\"url(#clip0_109_8737)\"\n                transform=\"translate(0 -3.006)\"\n              >\n                {/* Paste your full original path here */}\n                <path\n                  id=\"Path_10213\"\n                  data-name=\"Path 10213\"\n                  d=\"M.1.6a4.012,4.012,0,0,0,4-4,4.005,4.005,0,0,0-4-4,4.012,4.012,0,0,0-4,4A4.018,4.018,0,0,0,.1.6Zm0-.738A3.257,3.257,0,0,1-3.131-3.4,3.257,3.257,0,0,1,.1-6.662,3.257,3.257,0,0,1,3.331-3.4,3.257,3.257,0,0,1,.1-.138ZM.141-1.431c1.026,0,1.59-.5,1.641-1.374H.992a.733.733,0,0,1-.81.656H.1c-.667,0-.974-.595-.974-1.313S-.567-4.774.1-4.774H.182a.741.741,0,0,1,.81.656h.79C1.731-4.99,1.167-5.492.141-5.492c-1.077,0-1.815.7-1.815,2.031S-.936-1.431.141-1.431Z\"\n                  transform=\"translate(85.9 10.461)\"\n                  fill=\"currentColor\"\n                />\n                <g\n                  id=\"Group_2060\"\n                  data-name=\"Group 2060\"\n                  transform=\"translate(0 3.006)\"\n                >\n                  <path\n                    id=\"Path_10205\"\n                    data-name=\"Path 10205\"\n                    d=\"M-742.168-3903.765c0,4.428-2.862,7.085-6.6,7.085s-6.6-2.657-6.6-7.085,2.862-7.085,6.6-7.085S-742.168-3908.192-742.168-3903.765Zm-6.5,5.314c2.558,0,4.535-1.968,4.535-5.314s-1.976-5.313-4.535-5.313h-.2c-2.558,0-4.534,1.968-4.534,5.313s1.976,5.314,4.534,5.314Z\"\n                    transform=\"translate(768.296 3910.85)\"\n                    fill=\"currentColor\"\n                  />\n                  <path\n                    id=\"Path_10206\"\n                    data-name=\"Path 10206\"\n                    d=\"M56.475-3903.765c0,4.428-2.862,7.085-6.6,7.085s-6.6-2.657-6.6-7.085,2.862-7.085,6.6-7.085S56.475-3908.192,56.475-3903.765Zm-6.5,5.314c2.558,0,4.534-1.968,4.534-5.314s-1.976-5.313-4.534-5.313h-.2c-2.558,0-4.535,1.968-4.535,5.313s1.976,5.314,4.535,5.314Z\"\n                    transform=\"translate(12.227 3910.85)\"\n                    fill=\"currentColor\"\n                  />\n                  <path\n                    id=\"Path_10207\"\n                    data-name=\"Path 10207\"\n                    d=\"M-985.635-3901.206c-.193,3.012-2.884,4.526-5.617,4.526-3.739,0-6.6-2.657-6.6-7.085s2.862-7.085,6.6-7.085c2.954,0,5.406,1.613,5.617,4.527H-987.6c-.071-1.5-1.517-2.755-3.551-2.755h-.2c-2.558,0-4.534,1.968-4.534,5.313s1.976,5.314,4.534,5.314h.2c1.615,0,3.419-.868,3.551-2.755Z\"\n                    transform=\"translate(997.853 3910.85)\"\n                    fill=\"currentColor\"\n                  />\n                  <path\n                    id=\"Path_10208\"\n                    data-name=\"Path 10208\"\n                    d=\"M-489.865-3909.078h-.2c-1.712,0-2.854.925-2.854,2.145,0,1.063.728,1.692,1.889,1.928l2.2.433c2.381.473,3.759,1.732,3.759,3.975,0,2.42-2.047,3.916-5.1,3.916-3.247,0-5.313-1.574-5.412-4.625h1.968c0,1.869,1.456,2.854,3.523,2.854h.2c1.85,0,2.853-.925,2.853-2.067,0-1.121-.708-1.771-2.165-2.066l-2.2-.453c-2.2-.452-3.483-1.692-3.483-3.876,0-2.165,1.948-3.936,4.92-3.936,3.05,0,5,1.672,5.1,4.33h-1.968A2.777,2.777,0,0,0-489.865-3909.078Z\"\n                    transform=\"translate(522.347 3910.85)\"\n                    fill=\"currentColor\"\n                  />\n                  <path\n                    id=\"Path_10209\"\n                    data-name=\"Path 10209\"\n                    d=\"M-260.966-3905.426v12.043h-1.968v-13.776h3.267l4.113,11.926,4.034-11.926h3.247v13.776h-1.968v-12.1l-4.054,12.1h-2.519Z\"\n                    transform=\"translate(302.11 3907.355)\"\n                    fill=\"currentColor\"\n                  />\n                  <path\n                    id=\"Path_10210\"\n                    data-name=\"Path 10210\"\n                    d=\"M307.755-3909.078h-.2c-1.712,0-2.854.925-2.854,2.145,0,1.063.728,1.692,1.889,1.928l2.2.433c2.381.473,3.759,1.732,3.759,3.975,0,2.42-2.047,3.916-5.1,3.916-3.247,0-5.313-1.574-5.412-4.625h1.968c0,1.869,1.456,2.854,3.523,2.854h.2c1.85,0,2.854-.925,2.854-2.067,0-1.121-.708-1.771-2.165-2.066l-2.2-.453c-2.2-.452-3.483-1.692-3.483-3.876,0-2.165,1.948-3.936,4.92-3.936,3.05,0,5,1.672,5.1,4.33h-1.968A2.777,2.777,0,0,0,307.755-3909.078Z\"\n                    transform=\"translate(-232.754 3910.85)\"\n                    fill=\"currentColor\"\n                  />\n                </g>\n              </g>\n              <defs>\n                <clipPath id=\"clip0_109_8737\">\n                  <rect width=\"410.238\" height=\"68.013\" fill=\"currentColor\" />\n                </clipPath>\n              </defs>\n            </svg>\n          </Link>\n\n          {/* Nav links */}\n          <nav className=\"flex gap-6 items-center\">\n            <Link\n              href=\"/discover\"\n              data-testid=\"TopNavBar_DiscoverBtn\"\n              className=\"text-sm font-medium text-gray-800 hover:text-indigo-600\"\n            >\n              Discover\n            </Link>\n            <Link\n              href=\"/shop\"\n              data-testid=\"TopNavBar_HomeBtn\"\n              className=\"text-sm font-medium text-gray-800 hover:text-indigo-600\"\n            >\n              Shop\n            </Link>\n          </nav>\n        </div>\n\n        {/* Search bar */}\n        <div\n          role=\"combobox\"\n          aria-expanded=\"false\"\n          aria-controls=\"search-content\"\n          aria-label=\"Search\"\n          className=\"relative max-w-[460px] flex-grow mx-6\"\n        >\n          <div className=\"flex items-center bg-gray-100 rounded-md px-3 py-2 shadow-sm\">\n            <FiSearch className=\"text-gray-500 w-5 h-5\" aria-hidden=\"true\" />\n            <input\n              id=\"search-input\"\n              type=\"search\"\n              placeholder=\"Search Cosmos...\"\n              data-testid=\"Search_Input\"\n              autoComplete=\"off\"\n              value={search}\n              onChange={(e) => setSearch(e.target.value)}\n              className=\"bg-transparent border-none focus:ring-0 focus:outline-none flex-grow ml-2 placeholder-gray-500\"\n            />\n            <button\n              data-testid=\"TopNavBar_SearchPalette\"\n              aria-label=\"Filter by color\"\n              aria-expanded=\"false\"\n              className=\"ml-3 p-1 rounded-md hover:bg-gray-200\"\n              type=\"button\"\n            >\n              <svg\n                width=\"18\"\n                height=\"18\"\n                viewBox=\"0 0 18 18\"\n                fill=\"none\"\n                xmlns=\"http://www.w3.org/2000/svg\"\n                aria-hidden=\"true\"\n                focusable=\"false\"\n                className=\"text-gray-600\"\n              >\n                <path\n                  d=\"M12.589,13.571a13.131,13.131,0,0,1-.111,1.842A3.217,3.217,0,0,1,9.215,18a8.909,8.909,0,0,1-8.138-4.818A8.865,8.865,0,0,1,4.937,1.028,9.394,9.394,0,0,1,15.819,2.7a7.186,7.186,0,0,1,2.087,3.69,5.852,5.852,0,0,1,.064,1.647,2.032,2.032,0,0,1-2.028,1.786,7.718,7.718,0,0,0-1.48.092,2.39,2.39,0,0,0-1.9,2.3c-.016.448,0,.9,0,1.347h.025M4.436,9.427a1.625,1.625,0,1,0,1.62,1.619,1.627,1.627,0,0,0-1.62-1.619M8.516,12.7a1.622,1.622,0,0,0-1.633,1.608,1.631,1.631,0,1,0,3.262,0A1.615,1.615,0,0,0,8.516,12.7m4.074-6.158a1.631,1.631,0,0,0,.03-3.263,1.631,1.631,0,1,0-.03,3.263M5.64,6.16A1.622,1.622,0,0,0,4.024,4.511,1.642,1.642,0,0,0,2.387,6.123a1.624,1.624,0,0,0,1.6,1.639A1.6,1.6,0,0,0,5.64,6.16m2.466-.852A1.6,1.6,0,0,0,9.728,3.7a1.624,1.624,0,0,0-1.62-1.645A1.642,1.642,0,0,0,6.477,3.7a1.621,1.621,0,0,0,1.628,1.61\"\n                  fill=\"currentColor\"\n                />\n              </svg>\n            </button>\n          </div>\n        </div>\n\n        {/* Right side buttons */}\n        <div className=\"flex gap-4 items-center\">\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            data-testid=\"LoggedOutTopNavBar_SignInBtn\"\n            className=\"text-gray-700 hover:text-indigo-600\"\n          >\n            Log In\n          </Button>\n          <Button\n            size=\"sm\"\n            data-testid=\"LoggedOutTopNavBar_SignUpBtn\"\n            className=\"bg-indigo-600 text-white hover:bg-indigo-700\"\n          >\n            Sign Up\n          </Button>\n        </div>\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;;AAEe,SAAS;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,qBACE,8OAAC;QAAO,IAAG;QAAmB,WAAU;kBACtC,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,cAAW;4BACX,eAAY;4BACZ,WAAU;sCAEV,cAAA,8OAAC;gCACC,OAAM;gCACN,QAAO;gCACP,SAAQ;gCACR,MAAK;gCACL,OAAM;gCACN,WAAU;;kDAEV,8OAAC;wCACC,UAAS;wCACT,WAAU;;0DAGV,8OAAC;gDACC,IAAG;gDACH,aAAU;gDACV,GAAE;gDACF,WAAU;gDACV,MAAK;;;;;;0DAEP,8OAAC;gDACC,IAAG;gDACH,aAAU;gDACV,WAAU;;kEAEV,8OAAC;wDACC,IAAG;wDACH,aAAU;wDACV,GAAE;wDACF,WAAU;wDACV,MAAK;;;;;;kEAEP,8OAAC;wDACC,IAAG;wDACH,aAAU;wDACV,GAAE;wDACF,WAAU;wDACV,MAAK;;;;;;kEAEP,8OAAC;wDACC,IAAG;wDACH,aAAU;wDACV,GAAE;wDACF,WAAU;wDACV,MAAK;;;;;;kEAEP,8OAAC;wDACC,IAAG;wDACH,aAAU;wDACV,GAAE;wDACF,WAAU;wDACV,MAAK;;;;;;kEAEP,8OAAC;wDACC,IAAG;wDACH,aAAU;wDACV,GAAE;wDACF,WAAU;wDACV,MAAK;;;;;;kEAEP,8OAAC;wDACC,IAAG;wDACH,aAAU;wDACV,GAAE;wDACF,WAAU;wDACV,MAAK;;;;;;;;;;;;;;;;;;kDAIX,8OAAC;kDACC,cAAA,8OAAC;4CAAS,IAAG;sDACX,cAAA,8OAAC;gDAAK,OAAM;gDAAU,QAAO;gDAAS,MAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOnD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,eAAY;oCACZ,WAAU;8CACX;;;;;;8CAGD,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,eAAY;oCACZ,WAAU;8CACX;;;;;;;;;;;;;;;;;;8BAOL,8OAAC;oBACC,MAAK;oBACL,iBAAc;oBACd,iBAAc;oBACd,cAAW;oBACX,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,8IAAA,CAAA,WAAQ;gCAAC,WAAU;gCAAwB,eAAY;;;;;;0CACxD,8OAAC;gCACC,IAAG;gCACH,MAAK;gCACL,aAAY;gCACZ,eAAY;gCACZ,cAAa;gCACb,OAAO;gCACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;gCACzC,WAAU;;;;;;0CAEZ,8OAAC;gCACC,eAAY;gCACZ,cAAW;gCACX,iBAAc;gCACd,WAAU;gCACV,MAAK;0CAEL,cAAA,8OAAC;oCACC,OAAM;oCACN,QAAO;oCACP,SAAQ;oCACR,MAAK;oCACL,OAAM;oCACN,eAAY;oCACZ,WAAU;oCACV,WAAU;8CAEV,cAAA,8OAAC;wCACC,GAAE;wCACF,MAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQf,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,eAAY;4BACZ,WAAU;sCACX;;;;;;sCAGD,8OAAC,kIAAA,CAAA,SAAM;4BACL,MAAK;4BACL,eAAY;4BACZ,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AAOX"}}, {"offset": {"line": 476, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 482, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/src/components/common/Footer.tsx"], "sourcesContent": ["import React from \"react\";\n\nexport default function Footer() {\n  return (\n    <div>\n      <h1>footer</h1>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;kBACC,cAAA,8OAAC;sBAAG;;;;;;;;;;;AAGV"}}, {"offset": {"line": 502, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 508, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/src/app/layout.tsx"], "sourcesContent": ["import type { <PERSON>ada<PERSON>, Viewport } from \"next\";\nimport \"./globals.css\";\n\nimport BackgroundEffect from \"@/components/ui/backgroundEffect\";\nimport { CustomDialogProvider } from \"@/components/ui/custom-dialog\";\nimport FloatingActionButton from \"@/components/ui/FloatingActionButton\";\nimport { Analytics } from \"@vercel/analytics/react\";\nimport { SpeedInsights } from \"@vercel/speed-insights/next\";\nimport { Toaster } from \"sonner\";\nimport Navbar from \"@/components/common/Navbar\";\nimport Footer from \"@/components/common/Footer\";\n\nconst domain = \"templgen.com\";\nconst websitePath = {\n  main: `https://${domain}`,\n};\nconst webImage = `${websitePath.main}/images/profile.jpg`; // Adjust this image path as needed\nconst email = \"<EMAIL>\"; // Replace with your actual email\n\nconst keywords = [\n  \"templgen\",\n  \"website templates\",\n  \"mobile templates\",\n  \"React templates\",\n  \"Next.js templates\",\n  \"MERN templates\",\n  \"UI templates\",\n  \"frontend templates\",\n  \"backend templates\",\n  \"software templates\",\n  \"web development\",\n  \"template marketplace\",\n  \"responsive templates\",\n];\n\nexport const metadata: Metadata = {\n  metadataBase: new URL(websitePath.main),\n  title: {\n    template: \"TemplGen - %s\",\n    default: \"TemplGen - Website & Mobile Templates Marketplace\",\n  },\n  description:\n    \"TemplGen offers premium website and mobile templates for developers and businesses. Explore React, Next.js, and MERN templates to accelerate your projects.\",\n  keywords: keywords.join(\", \"),\n  authors: [{ name: \"TemplGen\", url: websitePath.main }],\n  creator: \"TemplGen\",\n  publisher: \"TemplGen\",\n  formatDetection: {\n    email: false,\n    address: false,\n    telephone: false,\n  },\n  alternates: {\n    canonical: websitePath.main,\n    languages: {\n      en: `${websitePath.main}/en`,\n      ar: `${websitePath.main}/ar`,\n    },\n  },\n  openGraph: {\n    type: \"website\",\n    locale: \"en_US\",\n    alternateLocale: \"ar_SA\",\n    title: \"TemplGen Marketplace\",\n    description:\n      \"Browse and purchase premium website and mobile templates from TemplGen. High-quality React, Next.js, and MERN templates ready to use.\",\n    url: websitePath.main,\n    siteName: \"TemplGen\",\n    images: [\n      {\n        url: webImage,\n        width: 400,\n        height: 400,\n        alt: \"TemplGen Logo\",\n      },\n    ],\n    countryName: \"Global\",\n    emails: [email],\n  },\n  twitter: {\n    card: \"summary_large_image\",\n    title: \"TemplGen Marketplace\",\n    description:\n      \"Discover and buy premium templates for web and mobile development at TemplGen.\",\n    images: webImage,\n    creator: \"@templgen\",\n  },\n  robots: {\n    index: true,\n    follow: true,\n    googleBot: {\n      index: true,\n      follow: true,\n      \"max-image-preview\": \"large\",\n      \"max-snippet\": -1,\n    },\n  },\n  verification: {\n    // Add verification tokens here if any\n  },\n};\n\nexport const viewport: Viewport = {\n  themeColor: \"#16161a\",\n  width: \"device-width\",\n  initialScale: 1,\n  maximumScale: 5,\n};\n\nexport default function RootLayout({\n  children,\n}: {\n  children: React.ReactNode;\n}) {\n  return (\n    <html lang=\"en\" dir=\"ltr\">\n      <head>\n        <link rel=\"preconnect\" href=\"https://fonts.googleapis.com\" />\n        <link\n          rel=\"preconnect\"\n          href=\"https://fonts.gstatic.com\"\n          crossOrigin=\"anonymous\"\n        />\n        <meta name=\"apple-mobile-web-app-title\" content=\"TemplGen\" />\n\n        <script\n          dangerouslySetInnerHTML={{\n            __html: `\n              (function() {\n                const observer = new MutationObserver((mutations) => {\n                  mutations.forEach(({ target }) => {\n                    if (target.nodeType === 1) {\n                      const elem = target;\n                      if (elem.hasAttribute('data-gr-ext-installed') ||\n                          elem.hasAttribute('data-new-gr-c-s-check-loaded')) {\n                        elem.removeAttribute('data-gr-ext-installed');\n                        elem.removeAttribute('data-new-gr-c-s-check-loaded');\n                      }\n                    }\n                  });\n                });\n                observer.observe(document.documentElement, {\n                  attributes: true,\n                  subtree: true,\n                  attributeFilter: ['data-gr-ext-installed', 'data-new-gr-c-s-check-loaded']\n                });\n              })();\n            `,\n          }}\n        />\n\n        <script\n          type=\"application/ld+json\"\n          dangerouslySetInnerHTML={{\n            __html: JSON.stringify({\n              \"@context\": \"https://schema.org\",\n              \"@type\": \"Organization\",\n              name: \"TemplGen\",\n              url: websitePath.main,\n              logo: webImage,\n              sameAs: [\n                \"https://github.com/templgen\",\n                \"https://www.linkedin.com/company/templgen\",\n                \"https://www.youtube.com/@templgen\",\n              ],\n              description:\n                \"Marketplace for premium website and mobile templates including React, Next.js, and MERN stacks.\",\n            }),\n          }}\n        />\n      </head>\n      <body className=\"flex relative dark flex-col min-h-screen\">\n        <SpeedInsights />\n        <Analytics />\n        {children}\n        <Toaster />\n        <Navbar />\n        <BackgroundEffect />\n        <main className=\"z-40 max-md:z-30 mx-auto w-full flex-grow\">\n          {children}\n        </main>\n        <Footer />\n        <FloatingActionButton threshold={400} />\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;AAEA,MAAM,SAAS;AACf,MAAM,cAAc;IAClB,MAAM,CAAC,QAAQ,EAAE,QAAQ;AAC3B;AACA,MAAM,WAAW,GAAG,YAAY,IAAI,CAAC,mBAAmB,CAAC,EAAE,mCAAmC;AAC9F,MAAM,QAAQ,wBAAwB,iCAAiC;AAEvE,MAAM,WAAW;IACf;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEM,MAAM,WAAqB;IAChC,cAAc,IAAI,IAAI,YAAY,IAAI;IACtC,OAAO;QACL,UAAU;QACV,SAAS;IACX;IACA,aACE;IACF,UAAU,SAAS,IAAI,CAAC;IACxB,SAAS;QAAC;YAAE,MAAM;YAAY,KAAK,YAAY,IAAI;QAAC;KAAE;IACtD,SAAS;IACT,WAAW;IACX,iBAAiB;QACf,OAAO;QACP,SAAS;QACT,WAAW;IACb;IACA,YAAY;QACV,WAAW,YAAY,IAAI;QAC3B,WAAW;YACT,IAAI,GAAG,YAAY,IAAI,CAAC,GAAG,CAAC;YAC5B,IAAI,GAAG,YAAY,IAAI,CAAC,GAAG,CAAC;QAC9B;IACF;IACA,WAAW;QACT,MAAM;QACN,QAAQ;QACR,iBAAiB;QACjB,OAAO;QACP,aACE;QACF,KAAK,YAAY,IAAI;QACrB,UAAU;QACV,QAAQ;YACN;gBACE,KAAK;gBACL,OAAO;gBACP,QAAQ;gBACR,KAAK;YACP;SACD;QACD,aAAa;QACb,QAAQ;YAAC;SAAM;IACjB;IACA,SAAS;QACP,MAAM;QACN,OAAO;QACP,aACE;QACF,QAAQ;QACR,SAAS;IACX;IACA,QAAQ;QACN,OAAO;QACP,QAAQ;QACR,WAAW;YACT,OAAO;YACP,QAAQ;YACR,qBAAqB;YACrB,eAAe,CAAC;QAClB;IACF;IACA,cAAc;IAEd;AACF;AAEO,MAAM,WAAqB;IAChC,YAAY;IACZ,OAAO;IACP,cAAc;IACd,cAAc;AAChB;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGT;IACC,qBACE,8OAAC;QAAK,MAAK;QAAK,KAAI;;0BAClB,8OAAC;;kCACC,8OAAC;wBAAK,KAAI;wBAAa,MAAK;;;;;;kCAC5B,8OAAC;wBACC,KAAI;wBACJ,MAAK;wBACL,aAAY;;;;;;kCAEd,8OAAC;wBAAK,MAAK;wBAA6B,SAAQ;;;;;;kCAEhD,8OAAC;wBACC,yBAAyB;4BACvB,QAAQ,CAAC;;;;;;;;;;;;;;;;;;;;YAoBT,CAAC;wBACH;;;;;;kCAGF,8OAAC;wBACC,MAAK;wBACL,yBAAyB;4BACvB,QAAQ,KAAK,SAAS,CAAC;gCACrB,YAAY;gCACZ,SAAS;gCACT,MAAM;gCACN,KAAK,YAAY,IAAI;gCACrB,MAAM;gCACN,QAAQ;oCACN;oCACA;oCACA;iCACD;gCACD,aACE;4BACJ;wBACF;;;;;;;;;;;;0BAGJ,8OAAC;gBAAK,WAAU;;kCACd,8OAAC,uKAAA,CAAA,gBAAa;;;;;kCACd,8OAAC,gKAAA,CAAA,YAAS;;;;;oBACT;kCACD,8OAAC,wIAAA,CAAA,UAAO;;;;;kCACR,8OAAC,sIAAA,CAAA,UAAM;;;;;kCACP,8OAAC,4IAAA,CAAA,UAAgB;;;;;kCACjB,8OAAC;wBAAK,WAAU;kCACb;;;;;;kCAEH,8OAAC,sIAAA,CAAA,UAAM;;;;;kCACP,8OAAC,gJAAA,CAAA,UAAoB;wBAAC,WAAW;;;;;;;;;;;;;;;;;;AAIzC"}}, {"offset": {"line": 777, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}
{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/Work/Repositories/templgen/src/components/common/Footer.tsx"], "sourcesContent": ["'use client';\n\nimport { usePathname } from 'next/navigation';\n\nexport default function Footer() {\n  const pathname = usePathname();\n  const isAuthRoute = pathname?.startsWith('/auth');\n\n  if (isAuthRoute) return null;\n\n  return (\n    <footer className=\"mt-auto border-t border-[var(--footer-border-color)] bg-[var(--background)] text-[var(--footer-text)]\">\n      <div className=\"mx-auto max-w-7xl px-4 py-8 text-sm\">\n        <h1 className=\"sr-only\">footer</h1>\n        <p>© {new Date().getFullYear()} TemplGen. All rights reserved.</p>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,cAAc,UAAU,WAAW;IAEzC,IAAI,aAAa,OAAO;IAExB,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAAU;;;;;;8BACxB,8OAAC;;wBAAE;wBAAG,IAAI,OAAO,WAAW;wBAAG;;;;;;;;;;;;;;;;;;AAIvC"}}, {"offset": {"line": 87, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 93, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/Work/Repositories/templgen/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\";\nimport { twMerge } from \"tailwind-merge\";\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB"}}, {"offset": {"line": 103, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 109, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/Work/Repositories/templgen/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport { cva, type VariantProps } from \"class-variance-authority\";\nimport { Slot } from \"@radix-ui/react-slot\";\n\nimport { cn } from \"@/lib/utils\";\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-[8px] text-sm font-medium transition-[color,box-shadow] disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 [&_svg]:shrink-0 outline-none focus-visible:border-[var(--highlight)] focus-visible:ring-[var(--highlight)]/50 focus-visible:ring-[3px]\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-[var(--button)] text-[var(--button-text)] shadow-sm hover:bg-[color-mix(in_srgb,var(--button),#00000020)]\",\n        destructive:\n          \"bg-[var(--tertiary)] text-[var(--button-text)] shadow-xs hover:bg-[color-mix(in_srgb,var(--tertiary),#00000020)] focus-visible:ring-[var(--tertiary)]/20\",\n        outline:\n          \"border border-[var(--input-border-color)] bg-[var(--background)] shadow-xs hover:bg-[var(--card-hover)] hover:text-[var(--highlight)]\",\n        secondary:\n          \"bg-[var(--secondary)] text-[var(--button-text)] shadow-xs hover:bg-[color-mix(in_srgb,var(--secondary),#00000020)]\",\n        ghost: \"hover:bg-[var(--card-hover)] hover:text-[var(--highlight)]\",\n        link: \"text-[var(--link-color)] underline-offset-4 hover:text-[var(--link-hover)] hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2\",\n        sm: \"h-8 rounded-[8px] px-3 text-xs\",\n        lg: \"h-10 rounded-[8px] px-8\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n);\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean;\n  }) {\n  const Comp = asChild ? Slot : \"button\";\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  );\n}\n\nexport { Button, buttonVariants };\n"], "names": [], "mappings": ";;;;;AACA;AAGA;AAFA;;;;;AAIA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,6XACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf"}}, {"offset": {"line": 160, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 166, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/Work/Repositories/templgen/src/components/common/navbar/AuthButtons.tsx"], "sourcesContent": ["\"use client\";\nimport Link from \"next/link\";\nimport { But<PERSON> } from \"@/components/ui/button\";\n\nexport default function AuthButtons() {\n  return (\n    <>\n      <Button\n        asChild\n        variant=\"ghost\"\n        size=\"sm\"\n        className=\"text-sm text-[var(--button-text)] hover:bg-[var(--card-hover)]\"\n      >\n        <Link href=\"#\">Sign In</Link>\n      </Button>\n      <Button\n        asChild\n        size=\"sm\"\n        className=\"text-sm bg-[var(--button)] text-[var(--button-text)]\"\n      >\n        <Link href=\"#\">Signup</Link>\n      </Button>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AAFA;;;;AAIe,SAAS;IACtB,qBACE;;0BACE,8OAAC,kIAAA,CAAA,SAAM;gBACL,OAAO;gBACP,SAAQ;gBACR,MAAK;gBACL,WAAU;0BAEV,cAAA,8OAAC,4JAAA,CAAA,UAAI;oBAAC,MAAK;8BAAI;;;;;;;;;;;0BAEjB,8OAAC,kIAAA,CAAA,SAAM;gBACL,OAAO;gBACP,MAAK;gBACL,WAAU;0BAEV,cAAA,8OAAC,4JAAA,CAAA,UAAI;oBAAC,MAAK;8BAAI;;;;;;;;;;;;;AAIvB"}}, {"offset": {"line": 217, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 223, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/Work/Repositories/templgen/src/components/common/Logo.tsx"], "sourcesContent": ["export default function Logo() {\n  return (\n    <div>\n      <h1>Logo</h1>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAe,SAAS;IACtB,qBACE,8OAAC;kBACC,cAAA,8OAAC;sBAAG;;;;;;;;;;;AAGV"}}, {"offset": {"line": 243, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 249, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/Work/Repositories/templgen/src/components/common/navbar/LogoLink.tsx"], "sourcesContent": ["\"use client\";\nimport Link from \"next/link\";\nimport Logo from \"../Logo\";\n\nexport default function LogoLink() {\n  return (\n    <Link\n      href=\"/home\"\n      aria-label=\"Cosmos\"\n      className=\"text-[var(--link-color)] hover:text-[var(--link-hover)]\"\n    >\n      <Logo />\n    </Link>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AAFA;;;;AAIe,SAAS;IACtB,qBACE,8OAAC,4JAAA,CAAA,UAAI;QACH,MAAK;QACL,cAAW;QACX,WAAU;kBAEV,cAAA,8OAAC,oIAAA,CAAA,UAAI;;;;;;;;;;AAGX"}}, {"offset": {"line": 275, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 281, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/Work/Repositories/templgen/src/components/ui/navigation-menu.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, VariantProps } from \"class-variance-authority\"\nimport { ChevronDownIcon } from \"lucide-react\"\nimport * as NavigationMenuPrimitive from \"@radix-ui/react-navigation-menu\"\n\nimport { cn } from \"@/lib/utils\"\n\ninterface NavigationMenuProps\n  extends React.ComponentProps<typeof NavigationMenuPrimitive.Root> {\n  viewport?: boolean\n}\n\nfunction NavigationMenu({\n  className,\n  children,\n  viewport = true,\n  ...props\n}: NavigationMenuProps) {\n  return (\n    <NavigationMenuPrimitive.Root\n      data-slot=\"navigation-menu\"\n      data-viewport={viewport}\n      className={cn(\n        \"group/navigation-menu relative flex max-w-max flex-1 items-center justify-center\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      {viewport && <NavigationMenuViewport />}\n    </NavigationMenuPrimitive.Root>\n  )\n}\n\ntype NavigationMenuListProps = React.ComponentProps<\n  typeof NavigationMenuPrimitive.List\n>\n\nfunction NavigationMenuList({ className, ...props }: NavigationMenuListProps) {\n  return (\n    <NavigationMenuPrimitive.List\n      data-slot=\"navigation-menu-list\"\n      className={cn(\n        \"group flex flex-1 list-none items-center justify-center gap-1\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\ntype NavigationMenuItemProps = React.ComponentProps<\n  typeof NavigationMenuPrimitive.Item\n>\n\nfunction NavigationMenuItem({ className, ...props }: NavigationMenuItemProps) {\n  return (\n    <NavigationMenuPrimitive.Item\n      data-slot=\"navigation-menu-item\"\n      className={cn(\"relative\", className)}\n      {...props}\n    />\n  )\n}\n\nconst navigationMenuTriggerStyle = cva(\n  \"group inline-flex h-9 w-max items-center justify-center rounded-md px-4 py-2 text-sm font-medium outline-none transition-[color,box-shadow]\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-[var(--background)] text-[var(--headline)]\",\n      },\n      state: {\n        hover:\n          \"hover:bg-[var(--button)] hover:text-[var(--button-text)] focus:bg-[var(--button)] focus:text-[var(--button-text)]\",\n        disabled: \"disabled:pointer-events-none disabled:opacity-50\",\n        open:\n          \"data-[state=open]:bg-[var(--button2)] data-[state=open]:hover:bg-[var(--button)] data-[state=open]:text-[var(--button-text)] focus-visible:ring-[3px] focus-visible:ring-[var(--button)]\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\ntype NavigationMenuTriggerProps = React.ComponentProps<\n  typeof NavigationMenuPrimitive.Trigger\n>\n\nfunction NavigationMenuTrigger({\n  className,\n  children,\n  ...props\n}: NavigationMenuTriggerProps) {\n  return (\n    <NavigationMenuPrimitive.Trigger\n      data-slot=\"navigation-menu-trigger\"\n      className={cn(navigationMenuTriggerStyle(), \"group\", className)}\n      {...props}\n    >\n      {children}{\" \"}\n      <ChevronDownIcon\n        className=\"relative top-[1px] ml-1 h-3 w-3 transition duration-300 group-data-[state=open]:rotate-180 text-[var(--button)]\"\n        aria-hidden=\"true\"\n      />\n    </NavigationMenuPrimitive.Trigger>\n  )\n}\n\ntype NavigationMenuContentProps = React.ComponentProps<\n  typeof NavigationMenuPrimitive.Content\n>\n\nfunction NavigationMenuContent({ className, ...props }: NavigationMenuContentProps) {\n  return (\n    <NavigationMenuPrimitive.Content\n      data-slot=\"navigation-menu-content\"\n      className={cn(\n        \"data-[motion^=from-]:animate-in data-[motion^=to-]:animate-out data-[motion^=from-]:fade-in data-[motion^=to-]:fade-out data-[motion=from-end]:slide-in-from-right-52 data-[motion=from-start]:slide-in-from-left-52 data-[motion=to-end]:slide-out-to-right-52 data-[motion=to-start]:slide-out-to-left-52 top-0 left-0 w-full p-2 pr-2.5 md:absolute md:w-auto\",\n        \"group-data-[viewport=false]/navigation-menu:bg-[var(--card-background)] group-data-[viewport=false]/navigation-menu:text-[var(--headline)] group-data-[viewport=false]/navigation-menu:data-[state=open]:animate-in group-data-[viewport=false]/navigation-menu:data-[state=closed]:animate-out group-data-[viewport=false]/navigation-menu:data-[state=closed]:zoom-out-95 group-data-[viewport=false]/navigation-menu:data-[state=open]:zoom-in-95 group-data-[viewport=false]/navigation-menu:data-[state=open]:fade-in-0 group-data-[viewport=false]/navigation-menu:data-[state=closed]:fade-out-0 group-data-[viewport=false]/navigation-menu:top-full group-data-[viewport=false]/navigation-menu:mt-1.5 group-data-[viewport=false]/navigation-menu:overflow-hidden group-data-[viewport=false]/navigation-menu:rounded-md group-data-[viewport=false]/navigation-menu:border group-data-[viewport=false]/navigation-menu:border-[var(--card-border-color)] group-data-[viewport=false]/navigation-menu:shadow group-data-[viewport=false]/navigation-menu:duration-200 **:data-[slot=navigation-menu-link]:focus:ring-0 **:data-[slot=navigation-menu-link]:focus:outline-none\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\ntype NavigationMenuViewportProps = React.ComponentProps<\n  typeof NavigationMenuPrimitive.Viewport\n>\n\nfunction NavigationMenuViewport({ className, ...props }: NavigationMenuViewportProps) {\n  return (\n    <div className={cn(\"absolute top-full left-0 isolate z-50 flex justify-center\")}>\n      <NavigationMenuPrimitive.Viewport\n        data-slot=\"navigation-menu-viewport\"\n        className={cn(\n          \"origin-top-center bg-[var(--card-background)] text-[var(--headline)] data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-90 relative mt-1.5 h-[var(--radix-navigation-menu-viewport-height)] w-full overflow-hidden rounded-md border border-[var(--card-border-color)] shadow md:w-[var(--radix-navigation-menu-viewport-width)]\",\n          className\n        )}\n        {...props}\n      />\n    </div>\n  )\n}\n\ntype NavigationMenuLinkProps = React.ComponentProps<\n  typeof NavigationMenuPrimitive.Link\n>\n\nfunction NavigationMenuLink({ className, ...props }: NavigationMenuLinkProps) {\n  return (\n    <NavigationMenuPrimitive.Link\n      data-slot=\"navigation-menu-link\"\n      className={cn(\n        \"data-[active]:focus:bg-[var(--button)] data-[active]:hover:bg-[var(--button)] data-[active]:bg-[var(--button)] data-[active]:text-[var(--button-text)] hover:bg-[var(--button2)] focus:bg-[var(--button)] focus:text-[var(--button-text)] focus-visible:ring-[3px] focus-visible:ring-[var(--button)] flex flex-col gap-1 rounded-sm p-2 text-sm outline-none transition-all [&_svg:not([class*='text-'])]:text-[var(--menu-color)] [&_svg:not([class*='size-'])]:h-4 [&_svg:not([class*='size-'])]:w-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\ntype NavigationMenuIndicatorProps = React.ComponentProps<\n  typeof NavigationMenuPrimitive.Indicator\n>\n\nfunction NavigationMenuIndicator({ className, ...props }: NavigationMenuIndicatorProps) {\n  return (\n    <NavigationMenuPrimitive.Indicator\n      data-slot=\"navigation-menu-indicator\"\n      className={cn(\n        \"data-[state=visible]:animate-in data-[state=hidden]:animate-out data-[state=hidden]:fade-out data-[state=visible]:fade-in top-full z-[1] flex h-1.5 items-end justify-center overflow-hidden\",\n        className\n      )}\n      {...props}\n    >\n      <div className=\"bg-[var(--border)] relative top-[60%] h-2 w-2 rotate-45 rounded-tl-sm shadow-md\" />\n    </NavigationMenuPrimitive.Indicator>\n  )\n}\n\nexport {\n  NavigationMenu,\n  NavigationMenuList,\n  NavigationMenuItem,\n  NavigationMenuContent,\n  NavigationMenuTrigger,\n  NavigationMenuLink,\n  NavigationMenuIndicator,\n  NavigationMenuViewport,\n  navigationMenuTriggerStyle,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AACA;AAIA;AAFA;AADA;;;;;;AAUA,SAAS,eAAe,EACtB,SAAS,EACT,QAAQ,EACR,WAAW,IAAI,EACf,GAAG,OACiB;IACpB,qBACE,8OAAC,+KAAwB,IAAI;QAC3B,aAAU;QACV,iBAAe;QACf,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oFACA;QAED,GAAG,KAAK;;YAER;YACA,0BAAY,8OAAC;;;;;;;;;;;AAGpB;AAMA,SAAS,mBAAmB,EAAE,SAAS,EAAE,GAAG,OAAgC;IAC1E,qBACE,8OAAC,+KAAwB,IAAI;QAC3B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAMA,SAAS,mBAAmB,EAAE,SAAS,EAAE,GAAG,OAAgC;IAC1E,qBACE,8OAAC,+KAAwB,IAAI;QAC3B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QACzB,GAAG,KAAK;;;;;;AAGf;AAEA,MAAM,6BAA6B,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACnC,+IACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;QACX;QACA,OAAO;YACL,OACE;YACF,UAAU;YACV,MACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OACwB;IAC3B,qBACE,8OAAC,+KAAwB,OAAO;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B,SAAS;QACpD,GAAG,KAAK;;YAER;YAAU;0BACX,8OAAC,wNAAA,CAAA,kBAAe;gBACd,WAAU;gBACV,eAAY;;;;;;;;;;;;AAIpB;AAMA,SAAS,sBAAsB,EAAE,SAAS,EAAE,GAAG,OAAmC;IAChF,qBACE,8OAAC,+KAAwB,OAAO;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oWACA,2nCACA;QAED,GAAG,KAAK;;;;;;AAGf;AAMA,SAAS,uBAAuB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAClF,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE;kBACjB,cAAA,8OAAC,+KAAwB,QAAQ;YAC/B,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wYACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAMA,SAAS,mBAAmB,EAAE,SAAS,EAAE,GAAG,OAAgC;IAC1E,qBACE,8OAAC,+KAAwB,IAAI;QAC3B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2eACA;QAED,GAAG,KAAK;;;;;;AAGf;AAMA,SAAS,wBAAwB,EAAE,SAAS,EAAE,GAAG,OAAqC;IACpF,qBACE,8OAAC,+KAAwB,SAAS;QAChC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gMACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC;YAAI,WAAU;;;;;;;;;;;AAGrB"}}, {"offset": {"line": 441, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 447, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/Work/Repositories/templgen/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\";\nimport { Check, ChevronRight, Circle } from \"lucide-react\";\n\nimport { cn } from \"@/lib/utils\";\n\nconst DropdownMenu = DropdownMenuPrimitive.Root;\n\nconst DropdownMenuTrigger = DropdownMenuPrimitive.Trigger;\n\nconst DropdownMenuGroup = DropdownMenuPrimitive.Group;\n\nconst DropdownMenuPortal = DropdownMenuPrimitive.Portal;\n\nconst DropdownMenuSub = DropdownMenuPrimitive.Sub;\n\nconst DropdownMenuRadioGroup = DropdownMenuPrimitive.RadioGroup;\n\nconst DropdownMenuSubTrigger = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.SubTrigger>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubTrigger> & {\n    inset?: boolean;\n  }\n>(({ className, inset, children, ...props }, ref) => (\n  <DropdownMenuPrimitive.SubTrigger\n    ref={ref}\n    className={cn(\n      \"flex cursor-default  select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent\",\n      inset && \"pl-8\",\n      className,\n    )}\n    {...props}\n  >\n    {children}\n    <ChevronRight className=\"ml-auto h-4 w-4\" />\n  </DropdownMenuPrimitive.SubTrigger>\n));\nDropdownMenuSubTrigger.displayName =\n  DropdownMenuPrimitive.SubTrigger.displayName;\n\nconst DropdownMenuSubContent = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.SubContent>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubContent>\n>(({ className, ...props }, ref) => (\n  <DropdownMenuPrimitive.SubContent\n    ref={ref}\n    className={cn(\n      \"z-50 min-w-[8rem] overflow-hidden rounded-md border p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n      className,\n    )}\n    {...props}\n  />\n));\nDropdownMenuSubContent.displayName =\n  DropdownMenuPrimitive.SubContent.displayName;\n\nconst DropdownMenuContent = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Content>\n>(({ className, sideOffset = 4, ...props }, ref) => (\n  <DropdownMenuPrimitive.Portal>\n    <DropdownMenuPrimitive.Content\n      ref={ref}\n      sideOffset={sideOffset}\n      className={cn(\n        \"z-50 min-w-[8rem] bg-[var(--card-background)]  overflow-hidden  border  p-1 text-[var(--paragraph)] border-[var(--border)] rounded-[12px] shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        className,\n      )}\n      {...props}\n    />\n  </DropdownMenuPrimitive.Portal>\n));\nDropdownMenuContent.displayName = DropdownMenuPrimitive.Content.displayName;\n\nconst DropdownMenuItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Item> & {\n    inset?: boolean;\n  }\n>(({ className, inset, ...props }, ref) => (\n  <DropdownMenuPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex hover:bg-[var(--card-hover)]  cursor-pointer select-none flex-row  items-start justify-start rounded-sm px-2 py-1.5 text-start text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      inset && \"pl-8\",\n      className,\n    )}\n    {...props}\n  />\n));\nDropdownMenuItem.displayName = DropdownMenuPrimitive.Item.displayName;\n\nconst DropdownMenuCheckboxItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.CheckboxItem>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.CheckboxItem>\n>(({ className, children, checked, ...props }, ref) => (\n  <DropdownMenuPrimitive.CheckboxItem\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className,\n    )}\n    checked={checked}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <DropdownMenuPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </DropdownMenuPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </DropdownMenuPrimitive.CheckboxItem>\n));\nDropdownMenuCheckboxItem.displayName =\n  DropdownMenuPrimitive.CheckboxItem.displayName;\n\nconst DropdownMenuRadioItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.RadioItem>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.RadioItem>\n>(({ className, children, ...props }, ref) => (\n  <DropdownMenuPrimitive.RadioItem\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className,\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <DropdownMenuPrimitive.ItemIndicator>\n        <Circle className=\"h-2 w-2 fill-current\" />\n      </DropdownMenuPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </DropdownMenuPrimitive.RadioItem>\n));\nDropdownMenuRadioItem.displayName = DropdownMenuPrimitive.RadioItem.displayName;\n\nconst DropdownMenuLabel = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Label> & {\n    inset?: boolean;\n  }\n>(({ className, inset, ...props }, ref) => (\n  <DropdownMenuPrimitive.Label\n    ref={ref}\n    className={cn(\n      \"px-2 py-1.5 text-sm font-semibold\",\n      inset && \"pl-8\",\n      className,\n    )}\n    {...props}\n  />\n));\nDropdownMenuLabel.displayName = DropdownMenuPrimitive.Label.displayName;\n\nconst DropdownMenuSeparator = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <DropdownMenuPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-[var(--border)]\", className)}\n    {...props}\n  />\n));\nDropdownMenuSeparator.displayName = DropdownMenuPrimitive.Separator.displayName;\n\nconst DropdownMenuShortcut = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLSpanElement>) => {\n  return (\n    <span\n      className={cn(\"ml-auto  text-xs tracking-widest opacity-60\", className)}\n      {...props}\n    />\n  );\n};\nDropdownMenuShortcut.displayName = \"DropdownMenuShortcut\";\n\nexport {\n  DropdownMenu,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuGroup,\n  DropdownMenuPortal,\n  DropdownMenuSub,\n  DropdownMenuSubContent,\n  DropdownMenuSubTrigger,\n  DropdownMenuRadioGroup,\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAIA;AAHA;AACA;AAAA;AAAA;;;;;;AAIA,MAAM,eAAe,6KAAsB,IAAI;AAE/C,MAAM,sBAAsB,6KAAsB,OAAO;AAEzD,MAAM,oBAAoB,6KAAsB,KAAK;AAErD,MAAM,qBAAqB,6KAAsB,MAAM;AAEvD,MAAM,kBAAkB,6KAAsB,GAAG;AAEjD,MAAM,yBAAyB,6KAAsB,UAAU;AAE/D,MAAM,uCAAyB,sMAAM,UAAU,CAK7C,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAC3C,8OAAC,6KAAsB,UAAU;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yIACA,SAAS,QACT;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,sNAAA,CAAA,eAAY;gBAAC,WAAU;;;;;;;;;;;;AAG5B,uBAAuB,WAAW,GAChC,6KAAsB,UAAU,CAAC,WAAW;AAE9C,MAAM,uCAAyB,sMAAM,UAAU,CAG7C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,6KAAsB,UAAU;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8aACA;QAED,GAAG,KAAK;;;;;;AAGb,uBAAuB,WAAW,GAChC,6KAAsB,UAAU,CAAC,WAAW;AAE9C,MAAM,oCAAsB,sMAAM,UAAU,CAG1C,CAAC,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC1C,8OAAC,6KAAsB,MAAM;kBAC3B,cAAA,8OAAC,6KAAsB,OAAO;YAC5B,KAAK;YACL,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,weACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIf,oBAAoB,WAAW,GAAG,6KAAsB,OAAO,CAAC,WAAW;AAE3E,MAAM,iCAAmB,sMAAM,UAAU,CAKvC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,8OAAC,6KAAsB,IAAI;QACzB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mSACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;AAGb,iBAAiB,WAAW,GAAG,6KAAsB,IAAI,CAAC,WAAW;AAErE,MAAM,yCAA2B,sMAAM,UAAU,CAG/C,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBAC7C,8OAAC,6KAAsB,YAAY;QACjC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wOACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,6KAAsB,aAAa;8BAClC,cAAA,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGpB;;;;;;;AAGL,yBAAyB,WAAW,GAClC,6KAAsB,YAAY,CAAC,WAAW;AAEhD,MAAM,sCAAwB,sMAAM,UAAU,CAG5C,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,6KAAsB,SAAS;QAC9B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wOACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,6KAAsB,aAAa;8BAClC,cAAA,8OAAC,sMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGrB;;;;;;;AAGL,sBAAsB,WAAW,GAAG,6KAAsB,SAAS,CAAC,WAAW;AAE/E,MAAM,kCAAoB,sMAAM,UAAU,CAKxC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,8OAAC,6KAAsB,KAAK;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qCACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG,6KAAsB,KAAK,CAAC,WAAW;AAEvE,MAAM,sCAAwB,sMAAM,UAAU,CAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,6KAAsB,SAAS;QAC9B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGb,sBAAsB,WAAW,GAAG,6KAAsB,SAAS,CAAC,WAAW;AAE/E,MAAM,uBAAuB,CAAC,EAC5B,SAAS,EACT,GAAG,OACmC;IACtC,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,+CAA+C;QAC5D,GAAG,KAAK;;;;;;AAGf;AACA,qBAAqB,WAAW,GAAG"}}, {"offset": {"line": 638, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 644, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/Work/Repositories/templgen/src/components/common/navbar/NavItems.tsx"], "sourcesContent": ["'use client';\n\nimport {\n  NavigationMenu,\n  NavigationMenuItem,\n  NavigationMenuLink,\n  NavigationMenuList,\n  NavigationMenuTrigger,\n} from '@/components/ui/navigation-menu';\n\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuItem,\n  DropdownMenuTrigger as DropdownTrigger,\n} from '@/components/ui/dropdown-menu';\n\nimport { MdKeyboardArrowRight } from 'react-icons/md';\nimport { cn } from '@/lib/utils';\nimport { AnimatePresence, motion } from 'framer-motion';\nimport { BookOpenIcon, InfoIcon, LifeBuoyIcon } from 'lucide-react';\nimport { useState } from 'react';\nimport { FiArrowLeft } from 'react-icons/fi';\n\ntype NavLinkItem = {\n  href: string;\n  label: string;\n  description?: string;\n  icon?: 'BookOpenIcon' | 'LifeBuoyIcon' | 'InfoIcon';\n};\n\ntype NavLink = {\n  href?: string;\n  label: string;\n  submenu?: boolean;\n  type?: 'description' | 'simple' | 'icon' | 'mega';\n  items?: NavLinkItem[];\n};\n\nconst CATEGORIES: { href: string; label: string; count: number }[] = [\n  { href: '/category/ui-kits', label: 'UI Kits', count: 4824 },\n  { href: '/category/coded-templates', label: 'Coded Templates', count: 239 },\n  { href: '/category/no-code', label: 'No-code Assets', count: 347 },\n  { href: '/category/illustrations', label: 'Illustrations', count: 1289 },\n  { href: '/category/fonts', label: 'Fonts', count: 715 },\n  { href: '/category/wireframe-kits', label: 'Wireframes', count: 161 },\n  { href: '/category/presentation', label: 'Presentation', count: 482 },\n  { href: '/category/mockups', label: 'Mockups', count: 723 },\n  { href: '/category/3d-assets', label: '3D Assets', count: 1340 },\n  { href: '/category/icons', label: 'Icon Sets', count: 1301 },\n  { href: '/category/themes', label: 'Themes', count: 799 },\n  { href: '/category/freebies', label: 'Freebies', count: 1968 },\n];\n\nconst POPULAR_APPS: { alt: string; img: string; onClick?: () => void }[] = [\n  { alt: 'Figma', img: '/img/app-icons/figma-prog.svg' },\n  { alt: 'Framer', img: '/img/app-icons/framer-prog.svg' },\n  { alt: 'React', img: '/img/app-icons/react-prog.svg' },\n  { alt: 'HTML/CSS', img: '/img/app-icons/html-prog.svg' },\n  { alt: 'Photoshop', img: '/img/app-icons/ps-prog.svg' },\n  { alt: 'Blender', img: '/img/app-icons/blender-prog.svg' },\n];\n\nconst navigationLinks: NavLink[] = [\n  { href: '/', label: 'Home' },\n  {\n    label: 'About',\n    submenu: true,\n    type: 'icon',\n    items: [\n      { href: '#', label: 'Getting Started', icon: 'BookOpenIcon' },\n      { href: '#', label: 'Tutorials', icon: 'LifeBuoyIcon' },\n      { href: '#', label: 'About Us', icon: 'InfoIcon' },\n    ],\n  },\n  {\n    label: 'Browse',\n    submenu: true,\n    type: 'mega',\n    // items optional for mega; we’re rendering custom sections\n  },\n];\n\nconst renderIcon = (icon?: NavLinkItem['icon']) => {\n  switch (icon) {\n    case 'BookOpenIcon':\n      return <BookOpenIcon size={16} className=\"text-[var(--menu-color)] opacity-60\" aria-hidden=\"true\" />;\n    case 'LifeBuoyIcon':\n      return <LifeBuoyIcon size={16} className=\"text-[var(--menu-color)] opacity-60\" aria-hidden=\"true\" />;\n    case 'InfoIcon':\n      return <InfoIcon size={16} className=\"text-[var(--menu-color)] opacity-60\" aria-hidden=\"true\" />;\n    default:\n      return null;\n  }\n};\n\nexport default function NavItems({ isMobile = false }: { isMobile?: boolean }) {\n  const [openDropdownIndex, setOpenDropdownIndex] = useState<number | null>(null);\n  const [hoverIndex, setHoverIndex] = useState<number | null>(null);\n\n  if (!isMobile) {\n    return (\n      <NavigationMenu viewport={false}>\n        <NavigationMenuList className=\"gap-2 flex\">\n          {navigationLinks.map((link, index) =>\n            link.submenu ? (\n              <NavigationMenuItem\n                key={index}\n                onMouseEnter={() => setHoverIndex(index)}\n                onMouseLeave={() => setHoverIndex((prev) => (prev === index ? null : prev))}\n                className=\"relative\"\n              >\n                {link.type !== 'mega' ? (\n                  <DropdownMenu>\n                    <DropdownTrigger asChild>\n                      <NavigationMenuTrigger className=\"text-[var(--nav-item)] bg-transparent px-2 py-1.5 font-medium cursor-pointer *:[svg]:-me-0.5 *:[svg]:size-3.5 inline-flex items-center gap-1\">\n                        {link.label}\n                      </NavigationMenuTrigger>\n                    </DropdownTrigger>\n\n                    <DropdownMenuContent\n                      side=\"bottom\"\n                      align=\"start\"\n                      className={cn(\n                        'bg-[var(--background)] border border-[var(--border)] p-2 min-w-[12rem]'\n                      )}\n                    >\n                      {link.type === 'description' && (\n                        <DropdownMenuGroup>\n                          {link.items?.map((item, idx) => (\n                            <DropdownMenuItem\n                              key={idx}\n                              asChild\n                              className=\"flex flex-col items-start gap-0.5 py-2 px-3 rounded cursor-pointer\"\n                            >\n                              <a href={item.href} className=\"w-full text-[var(--paragraph)]\">\n                                <span>{item.label}</span>\n                                {item.description && (\n                                  <span className=\"text-xs text-[var(--paragraph)] opacity-70\">\n                                    {item.description}\n                                  </span>\n                                )}\n                              </a>\n                            </DropdownMenuItem>\n                          ))}\n                        </DropdownMenuGroup>\n                      )}\n\n                      {link.type === 'simple' && (\n                        <DropdownMenuGroup>\n                          {link.items?.map((item, idx) => (\n                            <DropdownMenuItem key={idx} asChild className=\"py-2 px-3 rounded cursor-pointer\">\n                              <a href={item.href} className=\"text-[var(--paragraph)] block w-full\">\n                                {item.label}\n                              </a>\n                            </DropdownMenuItem>\n                          ))}\n                        </DropdownMenuGroup>\n                      )}\n\n                      {link.type === 'icon' && (\n                        <DropdownMenuGroup>\n                          {link.items?.map((item, idx) => (\n                            <DropdownMenuItem\n                              key={idx}\n                              asChild\n                              className=\"flex items-center gap-2 py-2 px-3 rounded cursor-pointer\"\n                            >\n                              <a href={item.href} className=\"flex items-center gap-2 w-full text-[var(--paragraph)]\">\n                                {item.icon && renderIcon(item.icon)}\n                                {item.label}\n                              </a>\n                            </DropdownMenuItem>\n                          ))}\n                        </DropdownMenuGroup>\n                      )}\n                    </DropdownMenuContent>\n                  </DropdownMenu>\n                ) : (\n                  // Mega / Browse\n                  <div className=\"relative\">\n                    <button\n                      className=\"text-[var(--nav-item)] bg-transparent px-2 py-1.5 font-medium inline-flex items-center gap-1\"\n                      aria-haspopup=\"true\"\n                      aria-expanded={hoverIndex === index}\n                    >\n                      {link.label}\n                    </button>\n\n                    <AnimatePresence>\n                      {hoverIndex === index && (\n                        <motion.div\n                          key={`hover-panel-${index}`}\n                          initial={{ opacity: 0 }}\n                          animate={{ opacity: 1 }}\n                          exit={{ opacity: 0 }}\n                          transition={{ duration: 0.18, ease: 'easeOut' }}\n                          className=\"absolute left-0 top-full mt-2 w-[900px] max-w-[95vw] z-50\"\n                          onMouseLeave={() => setHoverIndex(null)}\n                        >\n                          <div className=\"bg-[var(--background)] border border-[var(--border)] rounded-lg shadow-lg p-4\">\n                            {/* 3-column grid with fixed gaps */}\n                            <div className=\"grid grid-cols-12 gap-x-6 gap-y-4\">\n                              {/* Categories (span 6) */}\n                              <div className=\"col-span-12 md:col-span-6\">\n                                <div className=\"text-xs uppercase tracking-wide text-[var(--paragraph)] mb-3\">\n                                  Categories\n                                </div>\n                                <ul className=\"grid grid-cols-2 gap-x-4 gap-y-2\">\n                                  {CATEGORIES.map((c) => (\n                                    <li key={c.href}>\n                                      <a\n                                        href={c.href}\n                                        className=\"flex items-center justify-between rounded-md px-2 py-2 hover:bg-[var(--card-hover)] transition-colors\"\n                                      >\n                                        <span className=\"text-[var(--main)] text-sm\">{c.label}</span>\n                                        <span className=\"text-[var(--paragraph)] text-xs opacity-80\">\n                                          {c.count.toLocaleString()}\n                                        </span>\n                                      </a>\n                                    </li>\n                                  ))}\n                                </ul>\n                              </div>\n\n                              {/* Space / Banner (span 3) */}\n                              <div className=\"col-span-12 md:col-span-3\">\n                                <div className=\"text-xs uppercase tracking-wide text-[var(--paragraph)] mb-3\">\n                                  Featured\n                                </div>\n                                <a\n                                  href=\"https://studio.ui8.net\"\n                                  target=\"_blank\"\n                                  rel=\"noopener noreferrer\"\n                                  className=\"block\"\n                                >\n                                  <img\n                                    src=\"/img/banners/studio-menu.avif\"\n                                    alt=\"UI8 Studio\"\n                                    className=\"w-full rounded-md border border-[var(--card-border-color)]\"\n                                  />\n                                </a>\n                              </div>\n\n                              {/* Formats (span 3) */}\n                              <div className=\"col-span-12 md:col-span-3\">\n                                <div className=\"text-xs uppercase tracking-wide text-[var(--paragraph)] mb-3\">\n                                  Browse by most popular formats\n                                </div>\n                                <ul className=\"grid grid-cols-3 gap-3\">\n                                  {POPULAR_APPS.map((app, i) => (\n                                    <li key={i}>\n                                      <button\n                                        type=\"button\"\n                                        onClick={app.onClick}\n                                        className=\"w-12 h-12 rounded-md bg-[var(--card-background)] border border-[var(--card-border-color)] hover:bg-[var(--card-hover)] flex items-center justify-center transition-colors\"\n                                        aria-label={app.alt}\n                                        title={app.alt}\n                                      >\n                                        <img src={app.img} alt={app.alt} className=\"w-6 h-6\" />\n                                      </button>\n                                    </li>\n                                  ))}\n                                </ul>\n                              </div>\n                            </div>\n                          </div>\n                        </motion.div>\n                      )}\n                    </AnimatePresence>\n                  </div>\n                )}\n              </NavigationMenuItem>\n            ) : (\n              <NavigationMenuItem key={index}>\n                <NavigationMenuLink href={link.href} className=\"text-[var(--nav-item)] py-1.5 font-medium\">\n                  {link.label}\n                </NavigationMenuLink>\n              </NavigationMenuItem>\n            )\n          )}\n        </NavigationMenuList>\n      </NavigationMenu>\n    );\n  }\n\n  // Mobile drilldown (Browse renders like a submenu list)\n  return (\n    <NavigationMenu\n      className=\"fixed top-0 left-0 w-full h-screen z-50 bg-[var(--background)] p-4 overflow-hidden\"\n      role=\"navigation\"\n      aria-label=\"Mobile navigation\"\n    >\n      <AnimatePresence initial={false} mode=\"wait\">\n        {openDropdownIndex === null ? (\n          <motion.div\n            key=\"main-menu\"\n            initial={{ x: 300, opacity: 0 }}\n            animate={{ x: 0, opacity: 1 }}\n            exit={{ x: -300, opacity: 0 }}\n            transition={{ duration: 0.2 }}\n            className=\"flex flex-col h-full w-screen\"\n          >\n            <header>\n              <h1 className=\"text-[var(--main)]\">Menu</h1>\n            </header>\n            <NavigationMenuList className=\"flex flex-col gap-3 w-full overflow-auto\">\n              {navigationLinks.map((link, i) => (\n                <div key={i} className=\"flex flex-col w-full\">\n                  {link.submenu ? (\n                    <button\n                      onClick={() => setOpenDropdownIndex(i)}\n                      aria-label={`Open submenu for ${link.label}`}\n                      className=\"flex justify-between items-center w-full text-[var(--nav-item)] px-3 py-4 text-base font-medium rounded touch-manipulation\"\n                      style={{ touchAction: 'manipulation' }}\n                    >\n                      <span>{link.label}</span>\n                      <span aria-hidden=\"true\" style={{ fontSize: 20 }}>\n                        <MdKeyboardArrowRight />\n                      </span>\n                    </button>\n                  ) : (\n                    <NavigationMenuLink\n                      href={link.href}\n                      tabIndex={0}\n                      className=\"py-4 px-3 text-[var(--paragraph)] block font-medium w-full\"\n                    >\n                      {link.label}\n                    </NavigationMenuLink>\n                  )}\n                </div>\n              ))}\n            </NavigationMenuList>\n          </motion.div>\n        ) : (\n          <motion.div\n            key=\"submenu\"\n            initial={{ x: 300, opacity: 0 }}\n            animate={{ x: 0, opacity: 1 }}\n            exit={{ x: 300, opacity: 0 }}\n            transition={{ duration: 0.2 }}\n            className=\"flex flex-col w-full h-full\"\n          >\n            <button\n              onClick={() => setOpenDropdownIndex(null)}\n              aria-label=\"Go back to main menu\"\n              className=\"flex items-center gap-2 text-[var(--nav-item)] mb-4 px-3 py-3 font-medium rounded touch-manipulation w-full\"\n              style={{ touchAction: 'manipulation' }}\n            >\n              <FiArrowLeft size={20} aria-hidden=\"true\" />\n            </button>\n            {/* Browse mobile list (Categories only for brevity) */}\n            <NavigationMenuList className=\"flex flex-col gap-1 overflow-auto\">\n              {navigationLinks[openDropdownIndex].type === 'mega' ? (\n                <>\n                  <div className=\"px-3 py-2 text-xs uppercase tracking-wide text-[var(--paragraph)]\">\n                    Categories\n                  </div>\n                  {CATEGORIES.map((c) => (\n                    <NavigationMenuLink\n                      key={c.href}\n                      href={c.href}\n                      className=\"px-3 py-3 text-[var(--paragraph)] font-medium flex items-center justify-between w-full hover:bg-[var(--card-hover)] rounded\"\n                    >\n                      <span className=\"text-[var(--main)]\">{c.label}</span>\n                      <span className=\"text-xs text-[var(--paragraph)] opacity-80\">\n                        {c.count.toLocaleString()}\n                      </span>\n                    </NavigationMenuLink>\n                  ))}\n                </>\n              ) : (\n                navigationLinks[openDropdownIndex].items?.map((item, idx) => (\n                  <NavigationMenuLink\n                    key={idx}\n                    href={item.href}\n                    tabIndex={0}\n                    className=\"py-4 px-3 text-[var(--paragraph)] font-medium flex items-center gap-2 w-full\"\n                  >\n                    {navigationLinks[openDropdownIndex].type === 'icon' && item.icon && renderIcon(item.icon)}\n                    <div className=\"flex flex-col\">\n                      <span>{item.label}</span>\n                      {item.description && (\n                        <span className=\"text-xs text-[var(--paragraph)] opacity-70\">{item.description}</span>\n                      )}\n                    </div>\n                  </NavigationMenuLink>\n                ))\n              )}\n            </NavigationMenuList>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </NavigationMenu>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAQA;AASA;AAGA;AADA;AAAA;AAAA;AADA;AAAA;AAGA;AALA;AAlBA;;;;;;;;;;AAwCA,MAAM,aAA+D;IACnE;QAAE,MAAM;QAAqB,OAAO;QAAW,OAAO;IAAK;IAC3D;QAAE,MAAM;QAA6B,OAAO;QAAmB,OAAO;IAAI;IAC1E;QAAE,MAAM;QAAqB,OAAO;QAAkB,OAAO;IAAI;IACjE;QAAE,MAAM;QAA2B,OAAO;QAAiB,OAAO;IAAK;IACvE;QAAE,MAAM;QAAmB,OAAO;QAAS,OAAO;IAAI;IACtD;QAAE,MAAM;QAA4B,OAAO;QAAc,OAAO;IAAI;IACpE;QAAE,MAAM;QAA0B,OAAO;QAAgB,OAAO;IAAI;IACpE;QAAE,MAAM;QAAqB,OAAO;QAAW,OAAO;IAAI;IAC1D;QAAE,MAAM;QAAuB,OAAO;QAAa,OAAO;IAAK;IAC/D;QAAE,MAAM;QAAmB,OAAO;QAAa,OAAO;IAAK;IAC3D;QAAE,MAAM;QAAoB,OAAO;QAAU,OAAO;IAAI;IACxD;QAAE,MAAM;QAAsB,OAAO;QAAY,OAAO;IAAK;CAC9D;AAED,MAAM,eAAqE;IACzE;QAAE,KAAK;QAAS,KAAK;IAAgC;IACrD;QAAE,KAAK;QAAU,KAAK;IAAiC;IACvD;QAAE,KAAK;QAAS,KAAK;IAAgC;IACrD;QAAE,KAAK;QAAY,KAAK;IAA+B;IACvD;QAAE,KAAK;QAAa,KAAK;IAA6B;IACtD;QAAE,KAAK;QAAW,KAAK;IAAkC;CAC1D;AAED,MAAM,kBAA6B;IACjC;QAAE,MAAM;QAAK,OAAO;IAAO;IAC3B;QACE,OAAO;QACP,SAAS;QACT,MAAM;QACN,OAAO;YACL;gBAAE,MAAM;gBAAK,OAAO;gBAAmB,MAAM;YAAe;YAC5D;gBAAE,MAAM;gBAAK,OAAO;gBAAa,MAAM;YAAe;YACtD;gBAAE,MAAM;gBAAK,OAAO;gBAAY,MAAM;YAAW;SAClD;IACH;IACA;QACE,OAAO;QACP,SAAS;QACT,MAAM;IAER;CACD;AAED,MAAM,aAAa,CAAC;IAClB,OAAQ;QACN,KAAK;YACH,qBAAO,8OAAC,kNAAA,CAAA,eAAY;gBAAC,MAAM;gBAAI,WAAU;gBAAsC,eAAY;;;;;;QAC7F,KAAK;YACH,qBAAO,8OAAC,kNAAA,CAAA,eAAY;gBAAC,MAAM;gBAAI,WAAU;gBAAsC,eAAY;;;;;;QAC7F,KAAK;YACH,qBAAO,8OAAC,sMAAA,CAAA,WAAQ;gBAAC,MAAM;gBAAI,WAAU;gBAAsC,eAAY;;;;;;QACzF;YACE,OAAO;IACX;AACF;AAEe,SAAS,SAAS,EAAE,WAAW,KAAK,EAA0B;IAC3E,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC1E,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAE5D,IAAI,CAAC,UAAU;QACb,qBACE,8OAAC,8IAAA,CAAA,iBAAc;YAAC,UAAU;sBACxB,cAAA,8OAAC,8IAAA,CAAA,qBAAkB;gBAAC,WAAU;0BAC3B,gBAAgB,GAAG,CAAC,CAAC,MAAM,QAC1B,KAAK,OAAO,iBACV,8OAAC,8IAAA,CAAA,qBAAkB;wBAEjB,cAAc,IAAM,cAAc;wBAClC,cAAc,IAAM,cAAc,CAAC,OAAU,SAAS,QAAQ,OAAO;wBACrE,WAAU;kCAET,KAAK,IAAI,KAAK,uBACb,8OAAC,4IAAA,CAAA,eAAY;;8CACX,8OAAC,4IAAA,CAAA,sBAAe;oCAAC,OAAO;8CACtB,cAAA,8OAAC,8IAAA,CAAA,wBAAqB;wCAAC,WAAU;kDAC9B,KAAK,KAAK;;;;;;;;;;;8CAIf,8OAAC,4IAAA,CAAA,sBAAmB;oCAClB,MAAK;oCACL,OAAM;oCACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV;;wCAGD,KAAK,IAAI,KAAK,+BACb,8OAAC,4IAAA,CAAA,oBAAiB;sDACf,KAAK,KAAK,EAAE,IAAI,CAAC,MAAM,oBACtB,8OAAC,4IAAA,CAAA,mBAAgB;oDAEf,OAAO;oDACP,WAAU;8DAEV,cAAA,8OAAC;wDAAE,MAAM,KAAK,IAAI;wDAAE,WAAU;;0EAC5B,8OAAC;0EAAM,KAAK,KAAK;;;;;;4DAChB,KAAK,WAAW,kBACf,8OAAC;gEAAK,WAAU;0EACb,KAAK,WAAW;;;;;;;;;;;;mDARlB;;;;;;;;;;wCAiBZ,KAAK,IAAI,KAAK,0BACb,8OAAC,4IAAA,CAAA,oBAAiB;sDACf,KAAK,KAAK,EAAE,IAAI,CAAC,MAAM,oBACtB,8OAAC,4IAAA,CAAA,mBAAgB;oDAAW,OAAO;oDAAC,WAAU;8DAC5C,cAAA,8OAAC;wDAAE,MAAM,KAAK,IAAI;wDAAE,WAAU;kEAC3B,KAAK,KAAK;;;;;;mDAFQ;;;;;;;;;;wCAS5B,KAAK,IAAI,KAAK,wBACb,8OAAC,4IAAA,CAAA,oBAAiB;sDACf,KAAK,KAAK,EAAE,IAAI,CAAC,MAAM,oBACtB,8OAAC,4IAAA,CAAA,mBAAgB;oDAEf,OAAO;oDACP,WAAU;8DAEV,cAAA,8OAAC;wDAAE,MAAM,KAAK,IAAI;wDAAE,WAAU;;4DAC3B,KAAK,IAAI,IAAI,WAAW,KAAK,IAAI;4DACjC,KAAK,KAAK;;;;;;;mDANR;;;;;;;;;;;;;;;;;;;;;mCAejB,gBAAgB;sCAChB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,WAAU;oCACV,iBAAc;oCACd,iBAAe,eAAe;8CAE7B,KAAK,KAAK;;;;;;8CAGb,8OAAC,yLAAA,CAAA,kBAAe;8CACb,eAAe,uBACd,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;wCAAE;wCACtB,SAAS;4CAAE,SAAS;wCAAE;wCACtB,MAAM;4CAAE,SAAS;wCAAE;wCACnB,YAAY;4CAAE,UAAU;4CAAM,MAAM;wCAAU;wCAC9C,WAAU;wCACV,cAAc,IAAM,cAAc;kDAElC,cAAA,8OAAC;4CAAI,WAAU;sDAEb,cAAA,8OAAC;gDAAI,WAAU;;kEAEb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EAA+D;;;;;;0EAG9E,8OAAC;gEAAG,WAAU;0EACX,WAAW,GAAG,CAAC,CAAC,kBACf,8OAAC;kFACC,cAAA,8OAAC;4EACC,MAAM,EAAE,IAAI;4EACZ,WAAU;;8FAEV,8OAAC;oFAAK,WAAU;8FAA8B,EAAE,KAAK;;;;;;8FACrD,8OAAC;oFAAK,WAAU;8FACb,EAAE,KAAK,CAAC,cAAc;;;;;;;;;;;;uEAPpB,EAAE,IAAI;;;;;;;;;;;;;;;;kEAgBrB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EAA+D;;;;;;0EAG9E,8OAAC;gEACC,MAAK;gEACL,QAAO;gEACP,KAAI;gEACJ,WAAU;0EAEV,cAAA,8OAAC;oEACC,KAAI;oEACJ,KAAI;oEACJ,WAAU;;;;;;;;;;;;;;;;;kEAMhB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EAA+D;;;;;;0EAG9E,8OAAC;gEAAG,WAAU;0EACX,aAAa,GAAG,CAAC,CAAC,KAAK,kBACtB,8OAAC;kFACC,cAAA,8OAAC;4EACC,MAAK;4EACL,SAAS,IAAI,OAAO;4EACpB,WAAU;4EACV,cAAY,IAAI,GAAG;4EACnB,OAAO,IAAI,GAAG;sFAEd,cAAA,8OAAC;gFAAI,KAAK,IAAI,GAAG;gFAAE,KAAK,IAAI,GAAG;gFAAE,WAAU;;;;;;;;;;;uEARtC;;;;;;;;;;;;;;;;;;;;;;;;;;;uCA3Dd,CAAC,YAAY,EAAE,OAAO;;;;;;;;;;;;;;;;uBArFhC;;;;6CAuKP,8OAAC,8IAAA,CAAA,qBAAkB;kCACjB,cAAA,8OAAC,8IAAA,CAAA,qBAAkB;4BAAC,MAAM,KAAK,IAAI;4BAAE,WAAU;sCAC5C,KAAK,KAAK;;;;;;uBAFU;;;;;;;;;;;;;;;IAUrC;IAEA,wDAAwD;IACxD,qBACE,8OAAC,8IAAA,CAAA,iBAAc;QACb,WAAU;QACV,MAAK;QACL,cAAW;kBAEX,cAAA,8OAAC,yLAAA,CAAA,kBAAe;YAAC,SAAS;YAAO,MAAK;sBACnC,sBAAsB,qBACrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBAET,SAAS;oBAAE,GAAG;oBAAK,SAAS;gBAAE;gBAC9B,SAAS;oBAAE,GAAG;oBAAG,SAAS;gBAAE;gBAC5B,MAAM;oBAAE,GAAG,CAAC;oBAAK,SAAS;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,WAAU;;kCAEV,8OAAC;kCACC,cAAA,8OAAC;4BAAG,WAAU;sCAAqB;;;;;;;;;;;kCAErC,8OAAC,8IAAA,CAAA,qBAAkB;wBAAC,WAAU;kCAC3B,gBAAgB,GAAG,CAAC,CAAC,MAAM,kBAC1B,8OAAC;gCAAY,WAAU;0CACpB,KAAK,OAAO,iBACX,8OAAC;oCACC,SAAS,IAAM,qBAAqB;oCACpC,cAAY,CAAC,iBAAiB,EAAE,KAAK,KAAK,EAAE;oCAC5C,WAAU;oCACV,OAAO;wCAAE,aAAa;oCAAe;;sDAErC,8OAAC;sDAAM,KAAK,KAAK;;;;;;sDACjB,8OAAC;4CAAK,eAAY;4CAAO,OAAO;gDAAE,UAAU;4CAAG;sDAC7C,cAAA,8OAAC,8IAAA,CAAA,uBAAoB;;;;;;;;;;;;;;;yDAIzB,8OAAC,8IAAA,CAAA,qBAAkB;oCACjB,MAAM,KAAK,IAAI;oCACf,UAAU;oCACV,WAAU;8CAET,KAAK,KAAK;;;;;;+BAnBP;;;;;;;;;;;eAZV;;;;qCAuCN,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBAET,SAAS;oBAAE,GAAG;oBAAK,SAAS;gBAAE;gBAC9B,SAAS;oBAAE,GAAG;oBAAG,SAAS;gBAAE;gBAC5B,MAAM;oBAAE,GAAG;oBAAK,SAAS;gBAAE;gBAC3B,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,WAAU;;kCAEV,8OAAC;wBACC,SAAS,IAAM,qBAAqB;wBACpC,cAAW;wBACX,WAAU;wBACV,OAAO;4BAAE,aAAa;wBAAe;kCAErC,cAAA,8OAAC,8IAAA,CAAA,cAAW;4BAAC,MAAM;4BAAI,eAAY;;;;;;;;;;;kCAGrC,8OAAC,8IAAA,CAAA,qBAAkB;wBAAC,WAAU;kCAC3B,eAAe,CAAC,kBAAkB,CAAC,IAAI,KAAK,uBAC3C;;8CACE,8OAAC;oCAAI,WAAU;8CAAoE;;;;;;gCAGlF,WAAW,GAAG,CAAC,CAAC,kBACf,8OAAC,8IAAA,CAAA,qBAAkB;wCAEjB,MAAM,EAAE,IAAI;wCACZ,WAAU;;0DAEV,8OAAC;gDAAK,WAAU;0DAAsB,EAAE,KAAK;;;;;;0DAC7C,8OAAC;gDAAK,WAAU;0DACb,EAAE,KAAK,CAAC,cAAc;;;;;;;uCANpB,EAAE,IAAI;;;;;;2CAYjB,eAAe,CAAC,kBAAkB,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,oBACnD,8OAAC,8IAAA,CAAA,qBAAkB;gCAEjB,MAAM,KAAK,IAAI;gCACf,UAAU;gCACV,WAAU;;oCAET,eAAe,CAAC,kBAAkB,CAAC,IAAI,KAAK,UAAU,KAAK,IAAI,IAAI,WAAW,KAAK,IAAI;kDACxF,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;0DAAM,KAAK,KAAK;;;;;;4CAChB,KAAK,WAAW,kBACf,8OAAC;gDAAK,WAAU;0DAA8C,KAAK,WAAW;;;;;;;;;;;;;+BAT7E;;;;;;;;;;;eAtCT;;;;;;;;;;;;;;;AA2DhB"}}, {"offset": {"line": 1433, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1439, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/Work/Repositories/templgen/src/components/ui/RotatingTextRef.tsx"], "sourcesContent": ["import React, {\n  forwardRef,\n  useCallback,\n  useEffect,\n  useImperative<PERSON>andle,\n  useMemo,\n  useState,\n} from \"react\";\nimport {\n  motion,\n  AnimatePresence,\n  Transition,\n  type VariantLabels,\n  type Target,\n  type AnimationControls,\n  type TargetAndTransition,\n} from \"framer-motion\";\n\nfunction cn(...classes: (string | undefined | null | boolean)[]): string {\n  return classes.filter(Boolean).join(\" \");\n}\n\nexport interface RotatingTextRef {\n  next: () => void;\n  previous: () => void;\n  jumpTo: (index: number) => void;\n  reset: () => void;\n}\n\nexport interface RotatingTextProps\n  extends Omit<\n    React.ComponentPropsWithoutRef<typeof motion.span>,\n    \"children\" | \"transition\" | \"initial\" | \"animate\" | \"exit\"\n  > {\n  texts: string[];\n  transition?: Transition;\n  initial?: boolean | Target | VariantLabels;\n  animate?: boolean | VariantLabels | AnimationControls | TargetAndTransition;\n  exit?: Target | VariantLabels;\n  animatePresenceMode?: \"sync\" | \"wait\";\n  animatePresenceInitial?: boolean;\n  rotationInterval?: number;\n  staggerDuration?: number;\n  staggerFrom?: \"first\" | \"last\" | \"center\" | \"random\" | number;\n  loop?: boolean;\n  auto?: boolean;\n  splitBy?: string;\n  onNext?: (index: number) => void;\n  mainClassName?: string;\n  splitLevelClassName?: string;\n  elementLevelClassName?: string;\n}\n\nconst RotatingText = forwardRef<RotatingTextRef, RotatingTextProps>(\n  (\n    {\n      texts,\n      transition = { type: \"spring\", damping: 25, stiffness: 300 },\n      initial = { y: \"100%\", opacity: 0 },\n      animate = { y: 0, opacity: 1 },\n      exit = { y: \"-120%\", opacity: 0 },\n      animatePresenceMode = \"wait\",\n      animatePresenceInitial = false,\n      rotationInterval = 2000,\n      staggerDuration = 0,\n      staggerFrom = \"first\",\n      loop = true,\n      auto = true,\n      splitBy = \"characters\",\n      onNext,\n      mainClassName,\n      splitLevelClassName,\n      elementLevelClassName,\n      ...rest\n    },\n    ref\n  ) => {\n    const [currentTextIndex, setCurrentTextIndex] = useState<number>(0);\n\n    const splitIntoCharacters = (text: string): string[] => {\n      if (typeof Intl !== \"undefined\" && Intl.Segmenter) {\n        const segmenter = new Intl.Segmenter(\"en\", { granularity: \"grapheme\" });\n        return Array.from(\n          segmenter.segment(text),\n          (segment) => segment.segment\n        );\n      }\n      return Array.from(text);\n    };\n\n    const elements = useMemo(() => {\n      const currentText: string = texts[currentTextIndex];\n      if (splitBy === \"characters\") {\n        const words = currentText.split(\" \");\n        return words.map((word, i) => ({\n          characters: splitIntoCharacters(word),\n          needsSpace: i !== words.length - 1,\n        }));\n      }\n      if (splitBy === \"words\") {\n        return currentText.split(\" \").map((word, i, arr) => ({\n          characters: [word],\n          needsSpace: i !== arr.length - 1,\n        }));\n      }\n      if (splitBy === \"lines\") {\n        return currentText.split(\"\\n\").map((line, i, arr) => ({\n          characters: [line],\n          needsSpace: i !== arr.length - 1,\n        }));\n      }\n\n      return currentText.split(splitBy).map((part, i, arr) => ({\n        characters: [part],\n        needsSpace: i !== arr.length - 1,\n      }));\n    }, [texts, currentTextIndex, splitBy]);\n\n    const getStaggerDelay = useCallback(\n      (index: number, totalChars: number): number => {\n        const total = totalChars;\n        if (staggerFrom === \"first\") return index * staggerDuration;\n        if (staggerFrom === \"last\")\n          return (total - 1 - index) * staggerDuration;\n        if (staggerFrom === \"center\") {\n          const center = Math.floor(total / 2);\n          return Math.abs(center - index) * staggerDuration;\n        }\n        if (staggerFrom === \"random\") {\n          const randomIndex = Math.floor(Math.random() * total);\n          return Math.abs(randomIndex - index) * staggerDuration;\n        }\n        return Math.abs((staggerFrom as number) - index) * staggerDuration;\n      },\n      [staggerFrom, staggerDuration]\n    );\n\n    const handleIndexChange = useCallback(\n      (newIndex: number) => {\n        setCurrentTextIndex(newIndex);\n        if (onNext) onNext(newIndex);\n      },\n      [onNext]\n    );\n\n    const next = useCallback(() => {\n      const nextIndex =\n        currentTextIndex === texts.length - 1\n          ? loop\n            ? 0\n            : currentTextIndex\n          : currentTextIndex + 1;\n      if (nextIndex !== currentTextIndex) {\n        handleIndexChange(nextIndex);\n      }\n    }, [currentTextIndex, texts.length, loop, handleIndexChange]);\n\n    const previous = useCallback(() => {\n      const prevIndex =\n        currentTextIndex === 0\n          ? loop\n            ? texts.length - 1\n            : currentTextIndex\n          : currentTextIndex - 1;\n      if (prevIndex !== currentTextIndex) {\n        handleIndexChange(prevIndex);\n      }\n    }, [currentTextIndex, texts.length, loop, handleIndexChange]);\n\n    const jumpTo = useCallback(\n      (index: number) => {\n        const validIndex = Math.max(0, Math.min(index, texts.length - 1));\n        if (validIndex !== currentTextIndex) {\n          handleIndexChange(validIndex);\n        }\n      },\n      [texts.length, currentTextIndex, handleIndexChange]\n    );\n\n    const reset = useCallback(() => {\n      if (currentTextIndex !== 0) {\n        handleIndexChange(0);\n      }\n    }, [currentTextIndex, handleIndexChange]);\n\n    useImperativeHandle(\n      ref,\n      () => ({\n        next,\n        previous,\n        jumpTo,\n        reset,\n      }),\n      [next, previous, jumpTo, reset]\n    );\n\n    useEffect(() => {\n      if (!auto) return;\n      const intervalId = setInterval(next, rotationInterval);\n      return () => clearInterval(intervalId);\n    }, [next, rotationInterval, auto]);\n\n    return (\n      <motion.span\n        className={cn(\n          \"flex flex-wrap whitespace-pre-wrap relative\",\n          mainClassName\n        )}\n        {...rest}\n        layout\n        transition={transition}\n      >\n        <span className=\"sr-only\">{texts[currentTextIndex]}</span>\n        <AnimatePresence\n          mode={animatePresenceMode}\n          initial={animatePresenceInitial}\n        >\n          <motion.span\n            key={currentTextIndex}\n            className={cn(\n              splitBy === \"lines\"\n                ? \"flex flex-col w-full\"\n                : \"flex flex-wrap whitespace-pre-wrap relative\"\n            )}\n            layout\n            aria-hidden=\"true\"\n          >\n            {elements.map((wordObj, wordIndex, array) => {\n              const previousCharsCount = array\n                .slice(0, wordIndex)\n                .reduce((sum, word) => sum + word.characters.length, 0);\n              return (\n                <span\n                  key={wordIndex}\n                  className={cn(\"inline-flex\", splitLevelClassName)}\n                >\n                  {wordObj.characters.map((char, charIndex) => (\n                    <motion.span\n                      key={charIndex}\n                      initial={initial}\n                      animate={animate}\n                      exit={exit}\n                      transition={{\n                        ...transition,\n                        delay: getStaggerDelay(\n                          previousCharsCount + charIndex,\n                          array.reduce(\n                            (sum, word) => sum + word.characters.length,\n                            0\n                          )\n                        ),\n                      }}\n                      className={cn(\"inline-block\", elementLevelClassName)}\n                    >\n                      {char}\n                    </motion.span>\n                  ))}\n                  {wordObj.needsSpace && (\n                    <span className=\"whitespace-pre\"> </span>\n                  )}\n                </span>\n              );\n            })}\n          </motion.span>\n        </AnimatePresence>\n      </motion.span>\n    );\n  }\n);\n\nRotatingText.displayName = \"RotatingText\";\nexport default RotatingText;\n"], "names": [], "mappings": ";;;;AAAA;AAQA;AAAA;;;;AAUA,SAAS,GAAG,GAAG,OAAgD;IAC7D,OAAO,QAAQ,MAAM,CAAC,SAAS,IAAI,CAAC;AACtC;AAiCA,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAC5B,CACE,EACE,KAAK,EACL,aAAa;IAAE,MAAM;IAAU,SAAS;IAAI,WAAW;AAAI,CAAC,EAC5D,UAAU;IAAE,GAAG;IAAQ,SAAS;AAAE,CAAC,EACnC,UAAU;IAAE,GAAG;IAAG,SAAS;AAAE,CAAC,EAC9B,OAAO;IAAE,GAAG;IAAS,SAAS;AAAE,CAAC,EACjC,sBAAsB,MAAM,EAC5B,yBAAyB,KAAK,EAC9B,mBAAmB,IAAI,EACvB,kBAAkB,CAAC,EACnB,cAAc,OAAO,EACrB,OAAO,IAAI,EACX,OAAO,IAAI,EACX,UAAU,YAAY,EACtB,MAAM,EACN,aAAa,EACb,mBAAmB,EACnB,qBAAqB,EACrB,GAAG,MACJ,EACD;IAEA,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAEjE,MAAM,sBAAsB,CAAC;QAC3B,IAAI,OAAO,SAAS,eAAe,KAAK,SAAS,EAAE;YACjD,MAAM,YAAY,IAAI,KAAK,SAAS,CAAC,MAAM;gBAAE,aAAa;YAAW;YACrE,OAAO,MAAM,IAAI,CACf,UAAU,OAAO,CAAC,OAClB,CAAC,UAAY,QAAQ,OAAO;QAEhC;QACA,OAAO,MAAM,IAAI,CAAC;IACpB;IAEA,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACvB,MAAM,cAAsB,KAAK,CAAC,iBAAiB;QACnD,IAAI,YAAY,cAAc;YAC5B,MAAM,QAAQ,YAAY,KAAK,CAAC;YAChC,OAAO,MAAM,GAAG,CAAC,CAAC,MAAM,IAAM,CAAC;oBAC7B,YAAY,oBAAoB;oBAChC,YAAY,MAAM,MAAM,MAAM,GAAG;gBACnC,CAAC;QACH;QACA,IAAI,YAAY,SAAS;YACvB,OAAO,YAAY,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,MAAM,GAAG,MAAQ,CAAC;oBACnD,YAAY;wBAAC;qBAAK;oBAClB,YAAY,MAAM,IAAI,MAAM,GAAG;gBACjC,CAAC;QACH;QACA,IAAI,YAAY,SAAS;YACvB,OAAO,YAAY,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,GAAG,MAAQ,CAAC;oBACpD,YAAY;wBAAC;qBAAK;oBAClB,YAAY,MAAM,IAAI,MAAM,GAAG;gBACjC,CAAC;QACH;QAEA,OAAO,YAAY,KAAK,CAAC,SAAS,GAAG,CAAC,CAAC,MAAM,GAAG,MAAQ,CAAC;gBACvD,YAAY;oBAAC;iBAAK;gBAClB,YAAY,MAAM,IAAI,MAAM,GAAG;YACjC,CAAC;IACH,GAAG;QAAC;QAAO;QAAkB;KAAQ;IAErC,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAChC,CAAC,OAAe;QACd,MAAM,QAAQ;QACd,IAAI,gBAAgB,SAAS,OAAO,QAAQ;QAC5C,IAAI,gBAAgB,QAClB,OAAO,CAAC,QAAQ,IAAI,KAAK,IAAI;QAC/B,IAAI,gBAAgB,UAAU;YAC5B,MAAM,SAAS,KAAK,KAAK,CAAC,QAAQ;YAClC,OAAO,KAAK,GAAG,CAAC,SAAS,SAAS;QACpC;QACA,IAAI,gBAAgB,UAAU;YAC5B,MAAM,cAAc,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;YAC/C,OAAO,KAAK,GAAG,CAAC,cAAc,SAAS;QACzC;QACA,OAAO,KAAK,GAAG,CAAC,AAAC,cAAyB,SAAS;IACrD,GACA;QAAC;QAAa;KAAgB;IAGhC,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAClC,CAAC;QACC,oBAAoB;QACpB,IAAI,QAAQ,OAAO;IACrB,GACA;QAAC;KAAO;IAGV,MAAM,OAAO,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACvB,MAAM,YACJ,qBAAqB,MAAM,MAAM,GAAG,IAChC,OACE,IACA,mBACF,mBAAmB;QACzB,IAAI,cAAc,kBAAkB;YAClC,kBAAkB;QACpB;IACF,GAAG;QAAC;QAAkB,MAAM,MAAM;QAAE;QAAM;KAAkB;IAE5D,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC3B,MAAM,YACJ,qBAAqB,IACjB,OACE,MAAM,MAAM,GAAG,IACf,mBACF,mBAAmB;QACzB,IAAI,cAAc,kBAAkB;YAClC,kBAAkB;QACpB;IACF,GAAG;QAAC;QAAkB,MAAM,MAAM;QAAE;QAAM;KAAkB;IAE5D,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACvB,CAAC;QACC,MAAM,aAAa,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,OAAO,MAAM,MAAM,GAAG;QAC9D,IAAI,eAAe,kBAAkB;YACnC,kBAAkB;QACpB;IACF,GACA;QAAC,MAAM,MAAM;QAAE;QAAkB;KAAkB;IAGrD,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACxB,IAAI,qBAAqB,GAAG;YAC1B,kBAAkB;QACpB;IACF,GAAG;QAAC;QAAkB;KAAkB;IAExC,CAAA,GAAA,qMAAA,CAAA,sBAAmB,AAAD,EAChB,KACA,IAAM,CAAC;YACL;YACA;YACA;YACA;QACF,CAAC,GACD;QAAC;QAAM;QAAU;QAAQ;KAAM;IAGjC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,MAAM;QACX,MAAM,aAAa,YAAY,MAAM;QACrC,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;QAAM;QAAkB;KAAK;IAEjC,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;QACV,WAAW,GACT,+CACA;QAED,GAAG,IAAI;QACR,MAAM;QACN,YAAY;;0BAEZ,8OAAC;gBAAK,WAAU;0BAAW,KAAK,CAAC,iBAAiB;;;;;;0BAClD,8OAAC,yLAAA,CAAA,kBAAe;gBACd,MAAM;gBACN,SAAS;0BAET,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;oBAEV,WAAW,GACT,YAAY,UACR,yBACA;oBAEN,MAAM;oBACN,eAAY;8BAEX,SAAS,GAAG,CAAC,CAAC,SAAS,WAAW;wBACjC,MAAM,qBAAqB,MACxB,KAAK,CAAC,GAAG,WACT,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,UAAU,CAAC,MAAM,EAAE;wBACvD,qBACE,8OAAC;4BAEC,WAAW,GAAG,eAAe;;gCAE5B,QAAQ,UAAU,CAAC,GAAG,CAAC,CAAC,MAAM,0BAC7B,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;wCAEV,SAAS;wCACT,SAAS;wCACT,MAAM;wCACN,YAAY;4CACV,GAAG,UAAU;4CACb,OAAO,gBACL,qBAAqB,WACrB,MAAM,MAAM,CACV,CAAC,KAAK,OAAS,MAAM,KAAK,UAAU,CAAC,MAAM,EAC3C;wCAGN;wCACA,WAAW,GAAG,gBAAgB;kDAE7B;uCAhBI;;;;;gCAmBR,QAAQ,UAAU,kBACjB,8OAAC;oCAAK,WAAU;8CAAiB;;;;;;;2BAzB9B;;;;;oBA6BX;mBA5CK;;;;;;;;;;;;;;;;AAiDf;AAGF,aAAa,WAAW,GAAG;uCACZ"}}, {"offset": {"line": 1670, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1676, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/Work/Repositories/templgen/src/components/common/navbar/SearchBarWrapper.tsx"], "sourcesContent": ["\"use client\";\n\nimport RotatingText from \"@/components/ui/RotatingTextRef\";\nimport { cn } from \"@/lib/utils\";\nimport { AnimatePresence, motion } from \"framer-motion\";\nimport Image from \"next/image\";\nimport { useRef, useState } from \"react\";\nimport { FiX } from \"react-icons/fi\";\n\nexport default function SearchBarWrapper() {\n  const [query, setQuery] = useState(\"\");\n  const [focused, setFocused] = useState(false);\n  const inputRef = useRef<HTMLInputElement>(null);\n\n  const animatedSuggestions = [\n    \"Search Cosmos...\",\n    \"Explore subtle feelings...\",\n    \"Discover blockchain ideas...\",\n    \"Find NFTs & decentralization...\",\n    \"Dive into melancholy thoughts...\",\n  ];\n\n  const suggestions = [\n    \"subtle feelings of melancholy\",\n    \"cosmos\",\n    \"blockchain\",\n    \"NFT\",\n    \"decentralized\",\n  ].filter((s) => s.toLowerCase().includes(query.toLowerCase()));\n\n  const selectSuggestion = (item: string) => {\n    setQuery(item);\n    setFocused(false);\n    inputRef.current?.focus();\n  };\n\n  const onFocus = () => setFocused(true);\n  const onBlur = () => setTimeout(() => setFocused(false), 150);\n\n  return (\n    <>\n      {/* Overlay with smooth fade */}\n      <AnimatePresence>\n        {focused && (\n          <motion.div\n            key=\"overlay\"\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 0.3 }}\n            exit={{ opacity: 0 }}\n            transition={{ duration: 0.3 }}\n            className=\"fixed inset-0 bg-black z-40\"\n            onClick={() => {\n              setFocused(false);\n              inputRef.current?.blur();\n            }}\n          />\n        )}\n      </AnimatePresence>\n\n      {/* Search Box Container */}\n      <div\n        role=\"combobox\"\n        aria-expanded={focused}\n        aria-controls=\"search-listbox\"\n        aria-haspopup=\"listbox\"\n        className={cn(\n          \"relative w-full max-md:w-full\",\n          focused ? \"md:max-w-xl z-50 relative\" : \"md:max-w-lg\"\n        )}\n      >\n        <div\n          className={cn(\n            \"relative flex items-center rounded-[12px] px-3 py-2 border transition-all duration-300 ease-in-out bg-[var(--input-background)]\",\n            focused\n              ? \"ring-2 ring-[var(--highlight)] border-[var(--input-border-color)]\"\n              : \"border-[var(--input-border-color)]\"\n          )}\n          style={{ position: \"relative\", zIndex: 50 }}\n        >\n          <Image\n            src=\"/icons/star.svg\"\n            alt=\"Star Icon\"\n            width={20}\n            height={20}\n            className=\"mr-2 flex-shrink-0 transition-transform duration-300 ease-in-out hover:scale-125 focus-within:scale-125\"\n          />\n          <input\n            ref={inputRef}\n            type=\"search\"\n            role=\"searchbox\"\n            aria-autocomplete=\"list\"\n            aria-controls=\"search-listbox\"\n            placeholder={focused || query ? \"\" : \" \"}\n            className=\"flex-grow bg-transparent text-[var(--input-text)] placeholder-transparent outline-none text-sm transition-colors duration-500\"\n            value={query}\n            onChange={(e) => setQuery(e.target.value)}\n            onFocus={onFocus}\n            onBlur={onBlur}\n            autoComplete=\"off\"\n          />\n\n          {/* Static 'Try' + RotatingText with fade effect */}\n          {!focused && !query && (\n            <div\n              className=\"pointer-events-none absolute left-[38px] top-1/2 -translate-y-1/2 flex items-center space-x-1 text-[var(--paragraph)] text-sm select-none overflow-hidden whitespace-nowrap\"\n              style={{ width: \"calc(100% - 38px)\" }}\n            >\n              <span>Try</span>\n              <RotatingText\n                texts={animatedSuggestions}\n                rotationInterval={4000}\n                mainClassName=\"inline-block\"\n                staggerFrom=\"first\"\n                staggerDuration={0}\n                initial={{ opacity: 0 }}\n                animate={{ opacity: 1 }}\n                exit={{ opacity: 0 }}\n                transition={{ duration: 1 }}\n                animatePresenceMode=\"wait\"\n                splitBy=\"lines\"\n              />\n            </div>\n          )}\n\n          {query && (\n            <button\n              aria-label=\"Clear search\"\n              onClick={() => {\n                setQuery(\"\");\n                inputRef.current?.focus();\n              }}\n              className=\"ml-2 text-[var(--menu-color)] hover:text-[var(--highlight)]\"\n            >\n              <FiX />\n            </button>\n          )}\n        </div>\n\n        {focused && suggestions.length > 0 && (\n          <ul\n            id=\"search-listbox\"\n            role=\"listbox\"\n            className=\"absolute z-50 mt-1 max-h-48 w-full overflow-auto rounded-[12px] border border-[var(--input-border-color)] bg-[var(--input-background)] shadow-lg\"\n          >\n            {suggestions.map((item, i) => (\n              <li\n                key={i}\n                role=\"option\"\n                tabIndex={-1}\n                className=\"cursor-pointer px-4 py-2 text-sm text-[var(--paragraph)] hover:bg-[var(--highlight)] hover:text-[var(--active-text)]\"\n                onPointerDown={(e) => {\n                  e.preventDefault();\n                  selectSuggestion(item);\n                }}\n              >\n                {item}\n              </li>\n            ))}\n          </ul>\n        )}\n      </div>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AACA;AAFA;AAAA;AAGA;AAPA;;;;;;;;AASe,SAAS;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAE1C,MAAM,sBAAsB;QAC1B;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,cAAc;QAClB;QACA;QACA;QACA;QACA;KACD,CAAC,MAAM,CAAC,CAAC,IAAM,EAAE,WAAW,GAAG,QAAQ,CAAC,MAAM,WAAW;IAE1D,MAAM,mBAAmB,CAAC;QACxB,SAAS;QACT,WAAW;QACX,SAAS,OAAO,EAAE;IACpB;IAEA,MAAM,UAAU,IAAM,WAAW;IACjC,MAAM,SAAS,IAAM,WAAW,IAAM,WAAW,QAAQ;IAEzD,qBACE;;0BAEE,8OAAC,yLAAA,CAAA,kBAAe;0BACb,yBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBAET,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAI;oBACxB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;oBACV,SAAS;wBACP,WAAW;wBACX,SAAS,OAAO,EAAE;oBACpB;mBATI;;;;;;;;;;0BAeV,8OAAC;gBACC,MAAK;gBACL,iBAAe;gBACf,iBAAc;gBACd,iBAAc;gBACd,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iCACA,UAAU,8BAA8B;;kCAG1C,8OAAC;wBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mIACA,UACI,sEACA;wBAEN,OAAO;4BAAE,UAAU;4BAAY,QAAQ;wBAAG;;0CAE1C,8OAAC,6HAAA,CAAA,UAAK;gCACJ,KAAI;gCACJ,KAAI;gCACJ,OAAO;gCACP,QAAQ;gCACR,WAAU;;;;;;0CAEZ,8OAAC;gCACC,KAAK;gCACL,MAAK;gCACL,MAAK;gCACL,qBAAkB;gCAClB,iBAAc;gCACd,aAAa,WAAW,QAAQ,KAAK;gCACrC,WAAU;gCACV,OAAO;gCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;gCACxC,SAAS;gCACT,QAAQ;gCACR,cAAa;;;;;;4BAId,CAAC,WAAW,CAAC,uBACZ,8OAAC;gCACC,WAAU;gCACV,OAAO;oCAAE,OAAO;gCAAoB;;kDAEpC,8OAAC;kDAAK;;;;;;kDACN,8OAAC,2IAAA,CAAA,UAAY;wCACX,OAAO;wCACP,kBAAkB;wCAClB,eAAc;wCACd,aAAY;wCACZ,iBAAiB;wCACjB,SAAS;4CAAE,SAAS;wCAAE;wCACtB,SAAS;4CAAE,SAAS;wCAAE;wCACtB,MAAM;4CAAE,SAAS;wCAAE;wCACnB,YAAY;4CAAE,UAAU;wCAAE;wCAC1B,qBAAoB;wCACpB,SAAQ;;;;;;;;;;;;4BAKb,uBACC,8OAAC;gCACC,cAAW;gCACX,SAAS;oCACP,SAAS;oCACT,SAAS,OAAO,EAAE;gCACpB;gCACA,WAAU;0CAEV,cAAA,8OAAC,8IAAA,CAAA,MAAG;;;;;;;;;;;;;;;;oBAKT,WAAW,YAAY,MAAM,GAAG,mBAC/B,8OAAC;wBACC,IAAG;wBACH,MAAK;wBACL,WAAU;kCAET,YAAY,GAAG,CAAC,CAAC,MAAM,kBACtB,8OAAC;gCAEC,MAAK;gCACL,UAAU,CAAC;gCACX,WAAU;gCACV,eAAe,CAAC;oCACd,EAAE,cAAc;oCAChB,iBAAiB;gCACnB;0CAEC;+BATI;;;;;;;;;;;;;;;;;;AAiBrB"}}, {"offset": {"line": 1893, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1899, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/Work/Repositories/templgen/src/components/common/navbar/Navbar.tsx"], "sourcesContent": ["'use client';\n\nimport { AnimatePresence, motion } from 'framer-motion';\nimport { usePathname } from 'next/navigation';\nimport { useEffect, useState } from 'react';\nimport { FiMenu, FiX } from 'react-icons/fi';\nimport AuthButtons from './AuthButtons';\nimport LogoLink from './LogoLink';\nimport NavItems from './NavItems';\nimport SearchBarWrapper from './SearchBarWrapper';\n\nexport default function Navbar() {\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);\n  const pathname = usePathname();\n  const isAuthRoute = pathname?.startsWith('/auth');\n\n  // Close mobile menu on route change\n  useEffect(() => {\n    if (mobileMenuOpen) setMobileMenuOpen(false);\n  }, [pathname]);\n\n  if (isAuthRoute) {\n    return null;\n  }\n\n  return (\n    <header className=\"border-b border-[var(--border)] bg-[var(--background)] text-[var(--main)]\">\n      <div className=\"container mx-auto px-4\">\n        {/* Mobile */}\n        <div className=\"flex items-center justify-between h-16 md:hidden gap-2\">\n          <LogoLink />\n          <SearchBarWrapper />\n          <button\n            aria-label={mobileMenuOpen ? 'Close menu' : 'Open menu'}\n            className=\"text-[var(--menu-color)]\"\n            onClick={() => setMobileMenuOpen((v) => !v)}\n          >\n            {mobileMenuOpen ? <FiX size={24} /> : <FiMenu size={24} />}\n          </button>\n        </div>\n\n        <AnimatePresence>\n          {mobileMenuOpen && (\n            <motion.nav\n              key=\"mobile-menu\"\n              initial={{ x: '100%', opacity: 0 }}\n              animate={{ x: 0, opacity: 1 }}\n              exit={{ x: '100%', opacity: 0 }}\n              transition={{ duration: 0.3, ease: 'easeInOut' }}\n              className=\"fixed top-0 left-0 w-full h-screen bg-[var(--background)] z-50 m-auto overflow-y-auto p-4 flex justify-center\"\n              aria-label=\"Mobile navigation\"\n            >\n              <div className=\"w-full max-w-md\">\n                <div className=\"flex justify-end mb-4\">\n                  <button\n                    aria-label=\"Close menu\"\n                    className=\"text-[var(--menu-color)]\"\n                    onClick={() => setMobileMenuOpen(false)}\n                  >\n                    <FiX size={28} />\n                  </button>\n                </div>\n                <NavItems isMobile />\n              </div>\n            </motion.nav>\n          )}\n        </AnimatePresence>\n\n        {/* Desktop */}\n        <div className=\"hidden md:flex h-16 items-center justify-between gap-4\">\n          <div className=\"flex items-center gap-6 flex-1\">\n            <LogoLink />\n            <NavItems />\n          </div>\n          <div className=\"flex-1 flex justify-center\">\n            <SearchBarWrapper />\n          </div>\n          <div className=\"flex items-center gap-2 flex-1 justify-end\">\n            <AuthButtons />\n          </div>\n        </div>\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAEA;AACA;AACA;AACA;AAJA;AAHA;AAAA;AAFA;;;;;;;;;;AAWe,SAAS;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,cAAc,UAAU,WAAW;IAEzC,oCAAoC;IACpC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,gBAAgB,kBAAkB;IACxC,GAAG;QAAC;KAAS;IAEb,IAAI,aAAa;QACf,OAAO;IACT;IAEA,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,kJAAA,CAAA,UAAQ;;;;;sCACT,8OAAC,0JAAA,CAAA,UAAgB;;;;;sCACjB,8OAAC;4BACC,cAAY,iBAAiB,eAAe;4BAC5C,WAAU;4BACV,SAAS,IAAM,kBAAkB,CAAC,IAAM,CAAC;sCAExC,+BAAiB,8OAAC,8IAAA,CAAA,MAAG;gCAAC,MAAM;;;;;qDAAS,8OAAC,8IAAA,CAAA,SAAM;gCAAC,MAAM;;;;;;;;;;;;;;;;;8BAIxD,8OAAC,yLAAA,CAAA,kBAAe;8BACb,gCACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,SAAS;4BAAE,GAAG;4BAAQ,SAAS;wBAAE;wBACjC,SAAS;4BAAE,GAAG;4BAAG,SAAS;wBAAE;wBAC5B,MAAM;4BAAE,GAAG;4BAAQ,SAAS;wBAAE;wBAC9B,YAAY;4BAAE,UAAU;4BAAK,MAAM;wBAAY;wBAC/C,WAAU;wBACV,cAAW;kCAEX,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,cAAW;wCACX,WAAU;wCACV,SAAS,IAAM,kBAAkB;kDAEjC,cAAA,8OAAC,8IAAA,CAAA,MAAG;4CAAC,MAAM;;;;;;;;;;;;;;;;8CAGf,8OAAC,kJAAA,CAAA,UAAQ;oCAAC,QAAQ;;;;;;;;;;;;uBAlBhB;;;;;;;;;;8BAyBV,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kJAAA,CAAA,UAAQ;;;;;8CACT,8OAAC,kJAAA,CAAA,UAAQ;;;;;;;;;;;sCAEX,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,0JAAA,CAAA,UAAgB;;;;;;;;;;sCAEnB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,qJAAA,CAAA,UAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMxB"}}, {"offset": {"line": 2114, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2120, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/Work/Repositories/templgen/src/components/ui/backgroundEffect.tsx"], "sourcesContent": ["\"use client\";\n\nimport { memo } from \"react\";\n\nconst BackgroundEffect = () => {\n  return (\n    <div className=\"absolute hidden inset-0 isolate z-10 contain-strict max-md:hidden\">\n      <div className=\"absolute left-0 top-0 h-[1280px] w-[560px] -translate-y-[350px] -rotate-45 rounded-full bg-[radial-gradient(68.54%_68.72%_at_55.02%_31.46%,hsla(0,0%,85%,.08)_0,hsla(0,0%,55%,.02)_50%,hsla(0,0%,45%,0)_80%)]\"></div>\n      <div className=\"absolute left-0 top-0 h-[1280px] w-[240px] -rotate-45 rounded-full bg-[radial-gradient(50%_50%_at_50%_50%,hsla(0,0%,85%,.06)_0,hsla(0,0%,45%,.02)_80%,transparent_100%)] [translate:5%_-50%]\"></div>\n      <div className=\"absolute left-0 top-0 h-[1280px] w-[240px] -translate-y-[350px] -rotate-45 bg-[radial-gradient(50%_50%_at_50%_50%,hsla(0,0%,85%,.04)_0,hsla(0,0%,45%,.02)_80%,transparent_100%)]\"></div>\n    </div>\n  );\n};\n\n// Memoize the component to prevent unnecessary re-renders\nexport default memo(BackgroundEffect);\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIA,MAAM,mBAAmB;IACvB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;;;;;0BACf,8OAAC;gBAAI,WAAU;;;;;;0BACf,8OAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB;qDAGe,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EAAE"}}, {"offset": {"line": 2161, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2167, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/Work/Repositories/templgen/src/components/ui/FloatingActionButton.tsx"], "sourcesContent": ["\"use client\";\n\nimport { AnimatePresence, motion } from \"framer-motion\";\nimport { Briefcase, FileText, Home, Mail, Menu, X } from \"lucide-react\";\nimport Link from \"next/link\";\nimport { useEffect, useState } from \"react\";\n\ninterface FloatingActionButtonProps {\n  threshold?: number;\n}\n\nexport default function FloatingActionButton({\n  threshold = 300,\n}: FloatingActionButtonProps) {\n  const [isVisible, setIsVisible] = useState(false);\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      if (window.scrollY > threshold) {\n        setIsVisible(true);\n      } else {\n        setIsVisible(false);\n        if (isMenuOpen) setIsMenuOpen(false);\n      }\n    };\n\n    window.addEventListener(\"scroll\", handleScroll);\n    return () => window.removeEventListener(\"scroll\", handleScroll);\n  }, [threshold, isMenuOpen]);\n\n  const menuItems = [\n    { icon: <Home className=\"h-5 w-5\" />, label: \"Home\", href: \"/\" },\n    { icon: <Briefcase className=\"h-5 w-5\" />, label: \"Work\", href: \"/#work\" },\n    {\n      icon: <FileText className=\"h-5 w-5\" />,\n      label: \"Projects\",\n      href: \"/projects\",\n    },\n    { icon: <Mail className=\"h-5 w-5\" />, label: \"Contact\", href: \"/contact\" },\n  ];\n\n  const handleMenuItemClick = (href: string) => {\n    setIsMenuOpen(false);\n    if (href === \"/#work\") {\n      const workSection = document.getElementById(\"work\");\n      if (workSection) {\n        workSection.scrollIntoView({ behavior: \"smooth\" });\n      }\n    }\n  };\n\n  return (\n    <AnimatePresence>\n      {isVisible && (\n        <motion.div\n          className=\"fixed bottom-6 right-6 z-30 flex flex-col items-end hidden\"\n          initial={{ opacity: 0, scale: 0.8 }}\n          animate={{ opacity: 1, scale: 1 }}\n          exit={{ opacity: 0, scale: 0.8 }}\n          transition={{ duration: 0.3 }}\n        >\n          {/* Menu items */}\n          <AnimatePresence>\n            {isMenuOpen && (\n              <motion.div\n                className=\"mb-4 flex flex-col gap-3\"\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                exit={{ opacity: 0, y: 20 }}\n                transition={{ duration: 0.3 }}\n              >\n                {menuItems.map((item, index) => (\n                  <motion.div\n                    key={item.label}\n                    initial={{ opacity: 0, x: 20 }}\n                    animate={{ opacity: 1, x: 0 }}\n                    transition={{ delay: index * 0.05 }}\n                  >\n                    <Link\n                      href={item.href}\n                      onClick={() => handleMenuItemClick(item.href)}\n                      className=\"flex items-center gap-2 rounded-full bg-[var(--card-background)] px-4 py-2 text-sm text-[var(--headline)] shadow-md hover:bg-[var(--link-color)] hover:text-white transition-colors\"\n                    >\n                      {item.icon}\n                      <span>{item.label}</span>\n                    </Link>\n                  </motion.div>\n                ))}\n              </motion.div>\n            )}\n          </AnimatePresence>\n\n          {/* Main button */}\n          <motion.button\n            className=\"flex h-12 w-12 items-center justify-center rounded-full bg-[var(--link-color)] text-white shadow-lg hover:bg-[var(--button)] transition-colors\"\n            onClick={() =>\n              isMenuOpen ? setIsMenuOpen(false) : setIsMenuOpen(true)\n            }\n            whileTap={{ scale: 0.9 }}\n            aria-label={isMenuOpen ? \"Close menu\" : \"Open menu\"}\n          >\n            {isMenuOpen ? (\n              <X className=\"h-5 w-5\" />\n            ) : (\n              <Menu className=\"h-5 w-5\" />\n            )}\n          </motion.button>\n\n          {/* Back to top button */}\n        </motion.div>\n      )}\n    </AnimatePresence>\n  );\n}\n"], "names": [], "mappings": ";;;;AAIA;AACA;AAFA;AAAA;AAAA;AAAA;AADA;AAAA;AACA;AAAA;AAHA;;;;;;AAWe,SAAS,qBAAqB,EAC3C,YAAY,GAAG,EACW;IAC1B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,IAAI,OAAO,OAAO,GAAG,WAAW;gBAC9B,aAAa;YACf,OAAO;gBACL,aAAa;gBACb,IAAI,YAAY,cAAc;YAChC;QACF;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG;QAAC;QAAW;KAAW;IAE1B,MAAM,YAAY;QAChB;YAAE,oBAAM,8OAAC,mMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;YAAc,OAAO;YAAQ,MAAM;QAAI;QAC/D;YAAE,oBAAM,8OAAC,4MAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;YAAc,OAAO;YAAQ,MAAM;QAAS;QACzE;YACE,oBAAM,8OAAC,8MAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;YAC1B,OAAO;YACP,MAAM;QACR;QACA;YAAE,oBAAM,8OAAC,kMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;YAAc,OAAO;YAAW,MAAM;QAAW;KAC1E;IAED,MAAM,sBAAsB,CAAC;QAC3B,cAAc;QACd,IAAI,SAAS,UAAU;YACrB,MAAM,cAAc,SAAS,cAAc,CAAC;YAC5C,IAAI,aAAa;gBACf,YAAY,cAAc,CAAC;oBAAE,UAAU;gBAAS;YAClD;QACF;IACF;IAEA,qBACE,8OAAC,yLAAA,CAAA,kBAAe;kBACb,2BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,WAAU;YACV,SAAS;gBAAE,SAAS;gBAAG,OAAO;YAAI;YAClC,SAAS;gBAAE,SAAS;gBAAG,OAAO;YAAE;YAChC,MAAM;gBAAE,SAAS;gBAAG,OAAO;YAAI;YAC/B,YAAY;gBAAE,UAAU;YAAI;;8BAG5B,8OAAC,yLAAA,CAAA,kBAAe;8BACb,4BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,MAAM;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC1B,YAAY;4BAAE,UAAU;wBAAI;kCAE3B,UAAU,GAAG,CAAC,CAAC,MAAM,sBACpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,OAAO,QAAQ;gCAAK;0CAElC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAM,KAAK,IAAI;oCACf,SAAS,IAAM,oBAAoB,KAAK,IAAI;oCAC5C,WAAU;;wCAET,KAAK,IAAI;sDACV,8OAAC;sDAAM,KAAK,KAAK;;;;;;;;;;;;+BAXd,KAAK,KAAK;;;;;;;;;;;;;;;8BAoBzB,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oBACZ,WAAU;oBACV,SAAS,IACP,aAAa,cAAc,SAAS,cAAc;oBAEpD,UAAU;wBAAE,OAAO;oBAAI;oBACvB,cAAY,aAAa,eAAe;8BAEvC,2BACC,8OAAC,4LAAA,CAAA,IAAC;wBAAC,WAAU;;;;;6CAEb,8OAAC,kMAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;AAS9B"}}, {"offset": {"line": 2382, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2388, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/Work/Repositories/templgen/src/contexts/NavbarContext.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { createContext, useContext, useState, ReactNode } from \"react\";\n\ntype NavbarContextType = {\n  mobileMenuOpen: boolean;\n  setMobileMenuOpen: (open: boolean) => void;\n  openDropdownIndex: number | null;\n  setOpenDropdownIndex: (index: number | null) => void;\n};\n\nconst NavbarContext = createContext<NavbarContextType | undefined>(undefined);\n\nexport function NavbarProvider({ children }: { children: ReactNode }) {\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);\n  const [openDropdownIndex, setOpenDropdownIndex] = useState<number | null>(\n    null\n  );\n\n  return (\n    <NavbarContext.Provider\n      value={{\n        mobileMenuOpen,\n        setMobileMenuOpen,\n        openDropdownIndex,\n        setOpenDropdownIndex,\n      }}\n    >\n      {children}\n    </NavbarContext.Provider>\n  );\n}\n\nexport function useNavbarContext() {\n  const context = useContext(NavbarContext);\n  if (!context) {\n    throw new Error(\"useNavbarContext must be used within a NavbarProvider\");\n  }\n  return context;\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAWA,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAiC;AAE5D,SAAS,eAAe,EAAE,QAAQ,EAA2B;IAClE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EACvD;IAGF,qBACE,8OAAC,cAAc,QAAQ;QACrB,OAAO;YACL;YACA;YACA;YACA;QACF;kBAEC;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT"}}, {"offset": {"line": 2422, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}
{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/src/components/ui/backgroundEffect.tsx"], "sourcesContent": ["\"use client\";\n\nimport { memo } from \"react\";\n\nconst BackgroundEffect = () => {\n  return (\n    <div className=\"absolute hidden inset-0 isolate z-10 contain-strict max-md:hidden\">\n      <div className=\"absolute left-0 top-0 h-[1280px] w-[560px] -translate-y-[350px] -rotate-45 rounded-full bg-[radial-gradient(68.54%_68.72%_at_55.02%_31.46%,hsla(0,0%,85%,.08)_0,hsla(0,0%,55%,.02)_50%,hsla(0,0%,45%,0)_80%)]\"></div>\n      <div className=\"absolute left-0 top-0 h-[1280px] w-[240px] -rotate-45 rounded-full bg-[radial-gradient(50%_50%_at_50%_50%,hsla(0,0%,85%,.06)_0,hsla(0,0%,45%,.02)_80%,transparent_100%)] [translate:5%_-50%]\"></div>\n      <div className=\"absolute left-0 top-0 h-[1280px] w-[240px] -translate-y-[350px] -rotate-45 bg-[radial-gradient(50%_50%_at_50%_50%,hsla(0,0%,85%,.04)_0,hsla(0,0%,45%,.02)_80%,transparent_100%)]\"></div>\n    </div>\n  );\n};\n\n// Memoize the component to prevent unnecessary re-renders\nexport default memo(BackgroundEffect);\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIA,MAAM,mBAAmB;IACvB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;;;;;0BACf,8OAAC;gBAAI,WAAU;;;;;;0BACf,8OAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB;qDAGe,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EAAE"}}, {"offset": {"line": 56, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/src/components/ui/FloatingActionButton.tsx"], "sourcesContent": ["\"use client\";\n\nimport { AnimatePresence, motion } from \"framer-motion\";\nimport { Briefcase, FileText, Home, Mail, Menu, X } from \"lucide-react\";\nimport Link from \"next/link\";\nimport { useEffect, useState } from \"react\";\n\ninterface FloatingActionButtonProps {\n  threshold?: number;\n}\n\nexport default function FloatingActionButton({\n  threshold = 300,\n}: FloatingActionButtonProps) {\n  const [isVisible, setIsVisible] = useState(false);\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      if (window.scrollY > threshold) {\n        setIsVisible(true);\n      } else {\n        setIsVisible(false);\n        if (isMenuOpen) setIsMenuOpen(false);\n      }\n    };\n\n    window.addEventListener(\"scroll\", handleScroll);\n    return () => window.removeEventListener(\"scroll\", handleScroll);\n  }, [threshold, isMenuOpen]);\n\n  const menuItems = [\n    { icon: <Home className=\"h-5 w-5\" />, label: \"Home\", href: \"/\" },\n    { icon: <Briefcase className=\"h-5 w-5\" />, label: \"Work\", href: \"/#work\" },\n    {\n      icon: <FileText className=\"h-5 w-5\" />,\n      label: \"Projects\",\n      href: \"/projects\",\n    },\n    { icon: <Mail className=\"h-5 w-5\" />, label: \"Contact\", href: \"/contact\" },\n  ];\n\n  const handleMenuItemClick = (href: string) => {\n    setIsMenuOpen(false);\n    if (href === \"/#work\") {\n      const workSection = document.getElementById(\"work\");\n      if (workSection) {\n        workSection.scrollIntoView({ behavior: \"smooth\" });\n      }\n    }\n  };\n\n  return (\n    <AnimatePresence>\n      {isVisible && (\n        <motion.div\n          className=\"fixed bottom-6 right-6 z-30 flex flex-col items-end hidden\"\n          initial={{ opacity: 0, scale: 0.8 }}\n          animate={{ opacity: 1, scale: 1 }}\n          exit={{ opacity: 0, scale: 0.8 }}\n          transition={{ duration: 0.3 }}\n        >\n          {/* Menu items */}\n          <AnimatePresence>\n            {isMenuOpen && (\n              <motion.div\n                className=\"mb-4 flex flex-col gap-3\"\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                exit={{ opacity: 0, y: 20 }}\n                transition={{ duration: 0.3 }}\n              >\n                {menuItems.map((item, index) => (\n                  <motion.div\n                    key={item.label}\n                    initial={{ opacity: 0, x: 20 }}\n                    animate={{ opacity: 1, x: 0 }}\n                    transition={{ delay: index * 0.05 }}\n                  >\n                    <Link\n                      href={item.href}\n                      onClick={() => handleMenuItemClick(item.href)}\n                      className=\"flex items-center gap-2 rounded-full bg-[var(--card-background)] px-4 py-2 text-sm text-[var(--headline)] shadow-md hover:bg-[var(--link-color)] hover:text-white transition-colors\"\n                    >\n                      {item.icon}\n                      <span>{item.label}</span>\n                    </Link>\n                  </motion.div>\n                ))}\n              </motion.div>\n            )}\n          </AnimatePresence>\n\n          {/* Main button */}\n          <motion.button\n            className=\"flex h-12 w-12 items-center justify-center rounded-full bg-[var(--link-color)] text-white shadow-lg hover:bg-[var(--button)] transition-colors\"\n            onClick={() =>\n              isMenuOpen ? setIsMenuOpen(false) : setIsMenuOpen(true)\n            }\n            whileTap={{ scale: 0.9 }}\n            aria-label={isMenuOpen ? \"Close menu\" : \"Open menu\"}\n          >\n            {isMenuOpen ? (\n              <X className=\"h-5 w-5\" />\n            ) : (\n              <Menu className=\"h-5 w-5\" />\n            )}\n          </motion.button>\n\n          {/* Back to top button */}\n        </motion.div>\n      )}\n    </AnimatePresence>\n  );\n}\n"], "names": [], "mappings": ";;;;AAIA;AACA;AAFA;AAAA;AAAA;AAAA;AADA;AAAA;AACA;AAAA;AAHA;;;;;;AAWe,SAAS,qBAAqB,EAC3C,YAAY,GAAG,EACW;IAC1B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,IAAI,OAAO,OAAO,GAAG,WAAW;gBAC9B,aAAa;YACf,OAAO;gBACL,aAAa;gBACb,IAAI,YAAY,cAAc;YAChC;QACF;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG;QAAC;QAAW;KAAW;IAE1B,MAAM,YAAY;QAChB;YAAE,oBAAM,8OAAC,mMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;YAAc,OAAO;YAAQ,MAAM;QAAI;QAC/D;YAAE,oBAAM,8OAAC,4MAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;YAAc,OAAO;YAAQ,MAAM;QAAS;QACzE;YACE,oBAAM,8OAAC,8MAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;YAC1B,OAAO;YACP,MAAM;QACR;QACA;YAAE,oBAAM,8OAAC,kMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;YAAc,OAAO;YAAW,MAAM;QAAW;KAC1E;IAED,MAAM,sBAAsB,CAAC;QAC3B,cAAc;QACd,IAAI,SAAS,UAAU;YACrB,MAAM,cAAc,SAAS,cAAc,CAAC;YAC5C,IAAI,aAAa;gBACf,YAAY,cAAc,CAAC;oBAAE,UAAU;gBAAS;YAClD;QACF;IACF;IAEA,qBACE,8OAAC,yLAAA,CAAA,kBAAe;kBACb,2BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,WAAU;YACV,SAAS;gBAAE,SAAS;gBAAG,OAAO;YAAI;YAClC,SAAS;gBAAE,SAAS;gBAAG,OAAO;YAAE;YAChC,MAAM;gBAAE,SAAS;gBAAG,OAAO;YAAI;YAC/B,YAAY;gBAAE,UAAU;YAAI;;8BAG5B,8OAAC,yLAAA,CAAA,kBAAe;8BACb,4BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,MAAM;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC1B,YAAY;4BAAE,UAAU;wBAAI;kCAE3B,UAAU,GAAG,CAAC,CAAC,MAAM,sBACpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,OAAO,QAAQ;gCAAK;0CAElC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAM,KAAK,IAAI;oCACf,SAAS,IAAM,oBAAoB,KAAK,IAAI;oCAC5C,WAAU;;wCAET,KAAK,IAAI;sDACV,8OAAC;sDAAM,KAAK,KAAK;;;;;;;;;;;;+BAXd,KAAK,KAAK;;;;;;;;;;;;;;;8BAoBzB,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oBACZ,WAAU;oBACV,SAAS,IACP,aAAa,cAAc,SAAS,cAAc;oBAEpD,UAAU;wBAAE,OAAO;oBAAI;oBACvB,cAAY,aAAa,eAAe;8BAEvC,2BACC,8OAAC,4LAAA,CAAA,IAAC;wBAAC,WAAU;;;;;6CAEb,8OAAC,kMAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;AAS9B"}}, {"offset": {"line": 277, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 307, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\";\nimport { twMerge } from \"tailwind-merge\";\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB"}}, {"offset": {"line": 317, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 323, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport { cva, type VariantProps } from \"class-variance-authority\";\nimport { Slot } from \"@radix-ui/react-slot\";\n\nimport { cn } from \"@/lib/utils\";\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-[8px] text-sm font-medium transition-[color,box-shadow] disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 [&_svg]:shrink-0 outline-none focus-visible:border-[var(--highlight)] focus-visible:ring-[var(--highlight)]/50 focus-visible:ring-[3px]\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-[var(--button)] text-[var(--button-text)] shadow-sm hover:bg-[color-mix(in_srgb,var(--button),#00000020)]\",\n        destructive:\n          \"bg-[var(--tertiary)] text-[var(--button-text)] shadow-xs hover:bg-[color-mix(in_srgb,var(--tertiary),#00000020)] focus-visible:ring-[var(--tertiary)]/20\",\n        outline:\n          \"border border-[var(--input-border-color)] bg-[var(--background)] shadow-xs hover:bg-[var(--card-hover)] hover:text-[var(--highlight)]\",\n        secondary:\n          \"bg-[var(--secondary)] text-[var(--button-text)] shadow-xs hover:bg-[color-mix(in_srgb,var(--secondary),#00000020)]\",\n        ghost: \"hover:bg-[var(--card-hover)] hover:text-[var(--highlight)]\",\n        link: \"text-[var(--link-color)] underline-offset-4 hover:text-[var(--link-hover)] hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2\",\n        sm: \"h-8 rounded-[8px] px-3 text-xs\",\n        lg: \"h-10 rounded-[8px] px-8\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n);\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean;\n  }) {\n  const Comp = asChild ? Slot : \"button\";\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  );\n}\n\nexport { Button, buttonVariants };\n"], "names": [], "mappings": ";;;;;AACA;AAGA;AAFA;;;;;AAIA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,6XACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf"}}, {"offset": {"line": 374, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 380, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/src/components/ui/navigation-menu.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva } from \"class-variance-authority\"\nimport { ChevronDownIcon } from \"lucide-react\"\nimport { NavigationMenu as NavigationMenuPrimitive } from \"radix-ui\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction NavigationMenu({\n  className,\n  children,\n  viewport = true,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Root> & {\n  viewport?: boolean\n}) {\n  return (\n    <NavigationMenuPrimitive.Root\n      data-slot=\"navigation-menu\"\n      data-viewport={viewport}\n      className={cn(\n        \"group/navigation-menu relative flex max-w-max flex-1 items-center justify-center\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      {viewport && <NavigationMenuViewport />}\n    </NavigationMenuPrimitive.Root>\n  )\n}\n\nfunction NavigationMenuList({\n  className,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.List>) {\n  return (\n    <NavigationMenuPrimitive.List\n      data-slot=\"navigation-menu-list\"\n      className={cn(\n        \"group flex flex-1 list-none items-center justify-center gap-1\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction NavigationMenuItem({\n  className,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Item>) {\n  return (\n    <NavigationMenuPrimitive.Item\n      data-slot=\"navigation-menu-item\"\n      className={cn(\"relative\", className)}\n      {...props}\n    />\n  )\n}\n\nconst navigationMenuTriggerStyle = cva(\n  \"group inline-flex h-9 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground disabled:pointer-events-none disabled:opacity-50 data-[state=open]:hover:bg-accent data-[state=open]:text-accent-foreground data-[state=open]:focus:bg-accent data-[state=open]:bg-accent/50 focus-visible:ring-ring/50 outline-none transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1\"\n)\n\nfunction NavigationMenuTrigger({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Trigger>) {\n  return (\n    <NavigationMenuPrimitive.Trigger\n      data-slot=\"navigation-menu-trigger\"\n      className={cn(navigationMenuTriggerStyle(), \"group\", className)}\n      {...props}\n    >\n      {children}{\" \"}\n      <ChevronDownIcon\n        className=\"relative top-[1px] ml-1 size-3 transition duration-300 group-data-[state=open]:rotate-180\"\n        aria-hidden=\"true\"\n      />\n    </NavigationMenuPrimitive.Trigger>\n  )\n}\n\nfunction NavigationMenuContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Content>) {\n  return (\n    <NavigationMenuPrimitive.Content\n      data-slot=\"navigation-menu-content\"\n      className={cn(\n        \"data-[motion^=from-]:animate-in data-[motion^=to-]:animate-out data-[motion^=from-]:fade-in data-[motion^=to-]:fade-out data-[motion=from-end]:slide-in-from-right-52 data-[motion=from-start]:slide-in-from-left-52 data-[motion=to-end]:slide-out-to-right-52 data-[motion=to-start]:slide-out-to-left-52 top-0 left-0 w-full p-2 pr-2.5 md:absolute md:w-auto\",\n        \"group-data-[viewport=false]/navigation-menu:bg-popover group-data-[viewport=false]/navigation-menu:text-popover-foreground group-data-[viewport=false]/navigation-menu:data-[state=open]:animate-in group-data-[viewport=false]/navigation-menu:data-[state=closed]:animate-out group-data-[viewport=false]/navigation-menu:data-[state=closed]:zoom-out-95 group-data-[viewport=false]/navigation-menu:data-[state=open]:zoom-in-95 group-data-[viewport=false]/navigation-menu:data-[state=open]:fade-in-0 group-data-[viewport=false]/navigation-menu:data-[state=closed]:fade-out-0 group-data-[viewport=false]/navigation-menu:top-full group-data-[viewport=false]/navigation-menu:mt-1.5 group-data-[viewport=false]/navigation-menu:overflow-hidden group-data-[viewport=false]/navigation-menu:rounded-md group-data-[viewport=false]/navigation-menu:border group-data-[viewport=false]/navigation-menu:shadow group-data-[viewport=false]/navigation-menu:duration-200 **:data-[slot=navigation-menu-link]:focus:ring-0 **:data-[slot=navigation-menu-link]:focus:outline-none\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction NavigationMenuViewport({\n  className,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Viewport>) {\n  return (\n    <div\n      className={cn(\n        \"absolute top-full left-0 isolate z-50 flex justify-center\"\n      )}\n    >\n      <NavigationMenuPrimitive.Viewport\n        data-slot=\"navigation-menu-viewport\"\n        className={cn(\n          \"origin-top-center bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-90 relative mt-1.5 h-[var(--radix-navigation-menu-viewport-height)] w-full overflow-hidden rounded-md border shadow md:w-[var(--radix-navigation-menu-viewport-width)]\",\n          className\n        )}\n        {...props}\n      />\n    </div>\n  )\n}\n\nfunction NavigationMenuLink({\n  className,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Link>) {\n  return (\n    <NavigationMenuPrimitive.Link\n      data-slot=\"navigation-menu-link\"\n      className={cn(\n        \"data-[active]:focus:bg-accent data-[active]:hover:bg-accent data-[active]:bg-accent data-[active]:text-accent-foreground hover:bg-accent focus:bg-accent focus:text-accent-foreground focus-visible:ring-ring/50 [&_svg:not([class*='text-'])]:text-muted-foreground flex flex-col gap-1 rounded-sm p-2 text-sm transition-all outline-none focus-visible:ring-[3px] focus-visible:outline-1 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction NavigationMenuIndicator({\n  className,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Indicator>) {\n  return (\n    <NavigationMenuPrimitive.Indicator\n      data-slot=\"navigation-menu-indicator\"\n      className={cn(\n        \"data-[state=visible]:animate-in data-[state=hidden]:animate-out data-[state=hidden]:fade-out data-[state=visible]:fade-in top-full z-[1] flex h-1.5 items-end justify-center overflow-hidden\",\n        className\n      )}\n      {...props}\n    >\n      <div className=\"bg-border relative top-[60%] h-2 w-2 rotate-45 rounded-tl-sm shadow-md\" />\n    </NavigationMenuPrimitive.Indicator>\n  )\n}\n\nexport {\n  NavigationMenu,\n  NavigationMenuList,\n  NavigationMenuItem,\n  NavigationMenuContent,\n  NavigationMenuTrigger,\n  NavigationMenuLink,\n  NavigationMenuIndicator,\n  NavigationMenuViewport,\n  navigationMenuTriggerStyle,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AACA;AAIA;AAFA;AADA;;;;;;AAKA,SAAS,eAAe,EACtB,SAAS,EACT,QAAQ,EACR,WAAW,IAAI,EACf,GAAG,OAGJ;IACC,qBACE,8OAAC,wNAAA,CAAA,iBAAuB,CAAC,IAAI;QAC3B,aAAU;QACV,iBAAe;QACf,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oFACA;QAED,GAAG,KAAK;;YAER;YACA,0BAAY,8OAAC;;;;;;;;;;;AAGpB;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACuD;IAC1D,qBACE,8OAAC,wNAAA,CAAA,iBAAuB,CAAC,IAAI;QAC3B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACuD;IAC1D,qBACE,8OAAC,wNAAA,CAAA,iBAAuB,CAAC,IAAI;QAC3B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QACzB,GAAG,KAAK;;;;;;AAGf;AAEA,MAAM,6BAA6B,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACnC;AAGF,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,8OAAC,wNAAA,CAAA,iBAAuB,CAAC,OAAO;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B,SAAS;QACpD,GAAG,KAAK;;YAER;YAAU;0BACX,8OAAC,wNAAA,CAAA,kBAAe;gBACd,WAAU;gBACV,eAAY;;;;;;;;;;;;AAIpB;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,8OAAC,wNAAA,CAAA,iBAAuB,CAAC,OAAO;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oWACA,6hCACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV;kBAGF,cAAA,8OAAC,wNAAA,CAAA,iBAAuB,CAAC,QAAQ;YAC/B,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sVACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACuD;IAC1D,qBACE,8OAAC,wNAAA,CAAA,iBAAuB,CAAC,IAAI;QAC3B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qaACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,wBAAwB,EAC/B,SAAS,EACT,GAAG,OAC4D;IAC/D,qBACE,8OAAC,wNAAA,CAAA,iBAAuB,CAAC,SAAS;QAChC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gMACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC;YAAI,WAAU;;;;;;;;;;;AAGrB"}}, {"offset": {"line": 526, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 532, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/src/components/common/Logo.tsx"], "sourcesContent": ["export default function Logo() {\n  return (\n    <div>\n      <h1>Logo</h1>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAe,SAAS;IACtB,qBACE,8OAAC;kBACC,cAAA,8OAAC;sBAAG;;;;;;;;;;;AAGV"}}, {"offset": {"line": 552, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 558, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/src/components/layouts/website/SearchBar.tsx"], "sourcesContent": ["import { useState, useRef } from \"react\";\nimport { cn } from \"@/lib/utils\"; // your classnames merging util\nimport { FiSearch, FiX } from \"react-icons/fi\"; // React Icons from Feather\n\nexport default function SearchBar() {\n  const [query, setQuery] = useState(\"\");\n  const [focused, setFocused] = useState(false);\n  const inputRef = useRef<HTMLInputElement>(null);\n\n  const suggestions = [\n    \"subtle feelings of melancholy\",\n    \"cosmos\",\n    \"blockchain\",\n    \"NFT\",\n    \"decentralized\",\n  ].filter((s) => s.toLowerCase().includes(query.toLowerCase()));\n\n  return (\n    <div\n      role=\"combobox\"\n      aria-expanded={focused}\n      aria-controls=\"search-listbox\"\n      aria-haspopup=\"listbox\"\n      className=\"relative w-full max-w-md md:max-w-lg\"\n    >\n      <div\n        className={cn(\n          \"flex items-center rounded-md px-3 py-2 border transition\",\n          focused\n            ? \"ring-2 ring-[var(--highlight)] border-[var(--input-border-color)]\"\n            : \"border-[var(--input-border-color)]\",\n          \"bg-[var(--input-background)]\"\n        )}\n      >\n        <FiSearch\n          className=\"w-5 h-5 text-[var(--menu-color)] mr-2 flex-shrink-0\"\n          aria-hidden=\"true\"\n          focusable=\"false\"\n        />\n        <input\n          ref={inputRef}\n          type=\"search\"\n          role=\"searchbox\"\n          aria-autocomplete=\"list\"\n          aria-controls=\"search-listbox\"\n          placeholder=\"Search Cosmos...\"\n          className=\"flex-grow bg-transparent text-[var(--input-text)] placeholder-[var(--paragraph)] outline-none text-sm\"\n          value={query}\n          onChange={(e) => setQuery(e.target.value)}\n          onFocus={() => setFocused(true)}\n          onBlur={() => {\n            setTimeout(() => setFocused(false), 150);\n          }}\n          autoComplete=\"off\"\n        />\n        {query && (\n          <button\n            aria-label=\"Clear search\"\n            onClick={() => {\n              setQuery(\"\");\n              inputRef.current?.focus();\n            }}\n            className=\"ml-2 text-[var(--menu-color)] hover:text-[var(--highlight)]\"\n          >\n            <FiX />\n          </button>\n        )}\n      </div>\n\n      {focused && suggestions.length > 0 && (\n        <ul\n          id=\"search-listbox\"\n          role=\"listbox\"\n          className=\"absolute z-50 mt-1 max-h-48 w-full overflow-auto rounded-md border border-[var(--input-border-color)] bg-[var(--input-background)] shadow-lg\"\n        >\n          {suggestions.map((item, i) => (\n            <li\n              key={i}\n              role=\"option\"\n              tabIndex={-1}\n              className=\"cursor-pointer px-4 py-2 text-sm text-[var(--paragraph)] hover:bg-[var(--highlight)] hover:text-[var(--active-text)]\"\n              onMouseDown={(e) => {\n                e.preventDefault();\n                setQuery(item);\n                setFocused(false);\n              }}\n            >\n              Try <span className=\"font-semibold\">‘{item}’</span>\n            </li>\n          ))}\n        </ul>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA,qMAAkC,+BAA+B;AACjE,qPAAgD,2BAA2B;;;;;AAE5D,SAAS;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAE1C,MAAM,cAAc;QAClB;QACA;QACA;QACA;QACA;KACD,CAAC,MAAM,CAAC,CAAC,IAAM,EAAE,WAAW,GAAG,QAAQ,CAAC,MAAM,WAAW;IAE1D,qBACE,8OAAC;QACC,MAAK;QACL,iBAAe;QACf,iBAAc;QACd,iBAAc;QACd,WAAU;;0BAEV,8OAAC;gBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4DACA,UACI,sEACA,sCACJ;;kCAGF,8OAAC,8IAAA,CAAA,WAAQ;wBACP,WAAU;wBACV,eAAY;wBACZ,WAAU;;;;;;kCAEZ,8OAAC;wBACC,KAAK;wBACL,MAAK;wBACL,MAAK;wBACL,qBAAkB;wBAClB,iBAAc;wBACd,aAAY;wBACZ,WAAU;wBACV,OAAO;wBACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;wBACxC,SAAS,IAAM,WAAW;wBAC1B,QAAQ;4BACN,WAAW,IAAM,WAAW,QAAQ;wBACtC;wBACA,cAAa;;;;;;oBAEd,uBACC,8OAAC;wBACC,cAAW;wBACX,SAAS;4BACP,SAAS;4BACT,SAAS,OAAO,EAAE;wBACpB;wBACA,WAAU;kCAEV,cAAA,8OAAC,8IAAA,CAAA,MAAG;;;;;;;;;;;;;;;;YAKT,WAAW,YAAY,MAAM,GAAG,mBAC/B,8OAAC;gBACC,IAAG;gBACH,MAAK;gBACL,WAAU;0BAET,YAAY,GAAG,CAAC,CAAC,MAAM,kBACtB,8OAAC;wBAEC,MAAK;wBACL,UAAU,CAAC;wBACX,WAAU;wBACV,aAAa,CAAC;4BACZ,EAAE,cAAc;4BAChB,SAAS;4BACT,WAAW;wBACb;;4BACD;0CACK,8OAAC;gCAAK,WAAU;;oCAAgB;oCAAE;oCAAK;;;;;;;;uBAVtC;;;;;;;;;;;;;;;;AAiBnB"}}, {"offset": {"line": 687, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 693, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/src/components/common/Navbar.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport Link from \"next/link\";\nimport { BookOpenIcon, InfoIcon, LifeBuoyIcon } from \"lucide-react\";\nimport { FiMenu, FiX } from \"react-icons/fi\";\nimport { motion, AnimatePresence } from \"framer-motion\";\n\nimport { cn } from \"@/lib/utils\";\nimport { Button } from \"@/components/ui/button\";\nimport {\n  NavigationMenu,\n  NavigationMenuContent,\n  NavigationMenuItem,\n  NavigationMenuLink,\n  NavigationMenuList,\n  NavigationMenuTrigger,\n} from \"@/components/ui/navigation-menu\";\nimport Logo from \"./Logo\";\nimport SearchBar from \"../layouts/website/SearchBar\";\n\ntype NavLinkItem = {\n  href: string;\n  label: string;\n  description?: string;\n  icon?: \"BookOpenIcon\" | \"LifeBuoyIcon\" | \"InfoIcon\";\n};\n\ntype NavLink = {\n  href?: string;\n  label: string;\n  submenu?: boolean;\n  type?: \"description\" | \"simple\" | \"icon\";\n  items?: NavLinkItem[];\n};\n\nconst navigationLinks: NavLink[] = [\n  { href: \"#\", label: \"Home\" },\n  {\n    label: \"Features\",\n    submenu: true,\n    type: \"description\",\n    items: [\n      {\n        href: \"#\",\n        label: \"Components\",\n        description: \"Browse all components in the library.\",\n      },\n      {\n        href: \"#\",\n        label: \"Documentation\",\n        description: \"Learn how to use the library.\",\n      },\n      {\n        href: \"#\",\n        label: \"Templates\",\n        description: \"Pre-built layouts for common use cases.\",\n      },\n    ],\n  },\n  {\n    label: \"Pricing\",\n    submenu: true,\n    type: \"simple\",\n    items: [\n      { href: \"#\", label: \"Product A\" },\n      { href: \"#\", label: \"Product B\" },\n      { href: \"#\", label: \"Product C\" },\n      { href: \"#\", label: \"Product D\" },\n    ],\n  },\n  {\n    label: \"About\",\n    submenu: true,\n    type: \"icon\",\n    items: [\n      { href: \"#\", label: \"Getting Started\", icon: \"BookOpenIcon\" },\n      { href: \"#\", label: \"Tutorials\", icon: \"LifeBuoyIcon\" },\n      { href: \"#\", label: \"About Us\", icon: \"InfoIcon\" },\n    ],\n  },\n];\n\n// Icon rendering helper\nconst renderIcon = (icon?: NavLinkItem[\"icon\"]) => {\n  switch (icon) {\n    case \"BookOpenIcon\":\n      return (\n        <BookOpenIcon\n          size={16}\n          className=\"text-[var(--menu-color)] opacity-60\"\n          aria-hidden=\"true\"\n        />\n      );\n    case \"LifeBuoyIcon\":\n      return (\n        <LifeBuoyIcon\n          size={16}\n          className=\"text-[var(--menu-color)] opacity-60\"\n          aria-hidden=\"true\"\n        />\n      );\n    case \"InfoIcon\":\n      return (\n        <InfoIcon\n          size={16}\n          className=\"text-[var(--menu-color)] opacity-60\"\n          aria-hidden=\"true\"\n        />\n      );\n    default:\n      return null;\n  }\n};\n\n// Render submenu items\nconst renderSubmenuItems = (link: NavLink) => (\n  <ul className={cn(link.type === \"description\" ? \"min-w-64\" : \"min-w-48\")}>\n    {link.items?.map((item, idx) => (\n      <li key={idx}>\n        <NavigationMenuLink\n          href={item.href}\n          className={cn(\n            \"py-1.5 text-[var(--paragraph)] hover:text-[var(--highlight)] flex items-center gap-2\",\n            link.type === \"icon\" && \"pl-1\"\n          )}\n        >\n          {link.type === \"icon\" && item.icon && renderIcon(item.icon)}\n          <span>{item.label}</span>\n          {item.description && (\n            <span className=\"ml-2 text-xs text-[var(--muted)]\">\n              {item.description}\n            </span>\n          )}\n        </NavigationMenuLink>\n      </li>\n    ))}\n  </ul>\n);\n\nexport default function Navbar() {\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);\n\n  return (\n    <header className=\"border-b border-[var(--border)] bg-[var(--background)]\">\n      <div className=\"container mx-auto px-4\">\n        {/* MOBILE NAV */}\n        <div className=\"flex items-center justify-between h-16 md:hidden gap-2\">\n          <Link\n            href=\"/home\"\n            aria-label=\"Cosmos\"\n            data-testid=\"TopNavBar_CosmosLogo\"\n            className=\"text-[var(--link-color)]\"\n          >\n            <Logo />\n          </Link>\n\n          <SearchBar />\n\n          <Button\n            aria-label={mobileMenuOpen ? \"Close menu\" : \"Open menu\"}\n            variant=\"ghost\"\n            size=\"icon\"\n            className=\"text-[var(--menu-color)]\"\n            onClick={() => setMobileMenuOpen((open) => !open)}\n          >\n            {mobileMenuOpen ? <FiX size={24} /> : <FiMenu size={24} />}\n          </Button>\n        </div>\n\n        {/* MOBILE MENU POPUP with framer-motion */}\n        <AnimatePresence>\n          {mobileMenuOpen && (\n            <motion.nav\n              key=\"mobile-menu\"\n              initial={{ height: 0, opacity: 0 }}\n              animate={{ height: \"auto\", opacity: 1 }}\n              exit={{ height: 0, opacity: 0 }}\n              transition={{ duration: 0.3, ease: \"easeInOut\" }}\n              className=\"md:hidden border-t border-[var(--border)] bg-[var(--background)] overflow-hidden\"\n              aria-label=\"Mobile navigation\"\n            >\n              <NavigationMenu className=\"max-w-none p-4\">\n                <NavigationMenuList className=\"flex flex-col gap-3\">\n                  {navigationLinks.map((link, i) => (\n                    <NavigationMenuItem key={i}>\n                      {link.submenu ? (\n                        <>\n                          <div className=\"text-[var(--nav-item)] px-2 py-1.5 text-xs font-medium\">\n                            {link.label}\n                          </div>\n                          <ul className=\"pl-4\">\n                            {link.items?.map((item, idx) => (\n                              <li key={idx}>\n                                <NavigationMenuLink\n                                  href={item.href}\n                                  className=\"py-1.5 text-[var(--paragraph)] hover:text-[var(--highlight)] block\"\n                                >\n                                  {link.type === \"icon\" &&\n                                    item.icon &&\n                                    renderIcon(item.icon)}\n                                  {item.label}\n                                </NavigationMenuLink>\n                              </li>\n                            ))}\n                          </ul>\n                        </>\n                      ) : (\n                        <NavigationMenuLink\n                          href={link.href}\n                          className=\"py-1.5 text-[var(--paragraph)] hover:text-[var(--highlight)] block font-medium\"\n                        >\n                          {link.label}\n                        </NavigationMenuLink>\n                      )}\n                    </NavigationMenuItem>\n                  ))}\n                </NavigationMenuList>\n              </NavigationMenu>\n            </motion.nav>\n          )}\n        </AnimatePresence>\n\n        {/* DESKTOP NAV */}\n        <div className=\"hidden md:flex h-16 items-center justify-between\">\n          {/* Left: Logo + Menu Items */}\n          <div className=\"flex items-center gap-6 flex-1\">\n            <Link\n              href=\"/home\"\n              aria-label=\"Cosmos\"\n              className=\"text-[var(--link-color)] hover:text-[var(--link-hover)]\"\n            >\n              <Logo />\n            </Link>\n\n            <NavigationMenu viewport={false}>\n              <NavigationMenuList className=\"gap-2 flex\">\n                {navigationLinks.map((link, index) => (\n                  <NavigationMenuItem key={index}>\n                    {link.submenu ? (\n                      <>\n                        <NavigationMenuTrigger className=\"text-[var(--nav-item)] hover:text-[var(--highlight)] bg-transparent px-2 py-1.5 font-medium *:[svg]:-me-0.5 *:[svg]:size-3.5\">\n                          {link.label}\n                        </NavigationMenuTrigger>\n                        <NavigationMenuContent className=\"z-50 p-1 bg-[var(--background)] border border-[var(--border)]\">\n                          {renderSubmenuItems(link)}\n                        </NavigationMenuContent>\n                      </>\n                    ) : (\n                      <NavigationMenuLink\n                        href={link.href}\n                        className=\"text-[var(--nav-item)] hover:text-[var(--highlight)] py-1.5 font-medium\"\n                      >\n                        {link.label}\n                      </NavigationMenuLink>\n                    )}\n                  </NavigationMenuItem>\n                ))}\n              </NavigationMenuList>\n            </NavigationMenu>\n          </div>\n\n          {/* Center: SearchBar */}\n          <div className=\"flex-1 flex justify-center\">\n            <SearchBar />\n          </div>\n\n          {/* Right: Auth Buttons */}\n          <div className=\"flex items-center gap-2 flex-1 justify-end\">\n            <Button\n              asChild\n              variant=\"ghost\"\n              size=\"sm\"\n              className=\"text-sm text-[var(--button-text)] hover:bg-[var(--card-hover)]\"\n            >\n              <Link href=\"#\">Sign In</Link>\n            </Button>\n            <Button\n              asChild\n              size=\"sm\"\n              className=\"text-sm bg-[var(--button)] text-[var(--button-text)]\"\n            >\n              <Link href=\"#\">Get Started</Link>\n            </Button>\n          </div>\n        </div>\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAKA;AACA;AACA;AAQA;AACA;AAfA;AAAA;AAAA;AACA;AACA;AAAA;AANA;;;;;;;;;;;;AAoCA,MAAM,kBAA6B;IACjC;QAAE,MAAM;QAAK,OAAO;IAAO;IAC3B;QACE,OAAO;QACP,SAAS;QACT,MAAM;QACN,OAAO;YACL;gBACE,MAAM;gBACN,OAAO;gBACP,aAAa;YACf;YACA;gBACE,MAAM;gBACN,OAAO;gBACP,aAAa;YACf;YACA;gBACE,MAAM;gBACN,OAAO;gBACP,aAAa;YACf;SACD;IACH;IACA;QACE,OAAO;QACP,SAAS;QACT,MAAM;QACN,OAAO;YACL;gBAAE,MAAM;gBAAK,OAAO;YAAY;YAChC;gBAAE,MAAM;gBAAK,OAAO;YAAY;YAChC;gBAAE,MAAM;gBAAK,OAAO;YAAY;YAChC;gBAAE,MAAM;gBAAK,OAAO;YAAY;SACjC;IACH;IACA;QACE,OAAO;QACP,SAAS;QACT,MAAM;QACN,OAAO;YACL;gBAAE,MAAM;gBAAK,OAAO;gBAAmB,MAAM;YAAe;YAC5D;gBAAE,MAAM;gBAAK,OAAO;gBAAa,MAAM;YAAe;YACtD;gBAAE,MAAM;gBAAK,OAAO;gBAAY,MAAM;YAAW;SAClD;IACH;CACD;AAED,wBAAwB;AACxB,MAAM,aAAa,CAAC;IAClB,OAAQ;QACN,KAAK;YACH,qBACE,8OAAC,kNAAA,CAAA,eAAY;gBACX,MAAM;gBACN,WAAU;gBACV,eAAY;;;;;;QAGlB,KAAK;YACH,qBACE,8OAAC,kNAAA,CAAA,eAAY;gBACX,MAAM;gBACN,WAAU;gBACV,eAAY;;;;;;QAGlB,KAAK;YACH,qBACE,8OAAC,sMAAA,CAAA,WAAQ;gBACP,MAAM;gBACN,WAAU;gBACV,eAAY;;;;;;QAGlB;YACE,OAAO;IACX;AACF;AAEA,uBAAuB;AACvB,MAAM,qBAAqB,CAAC,qBAC1B,8OAAC;QAAG,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,KAAK,IAAI,KAAK,gBAAgB,aAAa;kBAC1D,KAAK,KAAK,EAAE,IAAI,CAAC,MAAM,oBACtB,8OAAC;0BACC,cAAA,8OAAC,8IAAA,CAAA,qBAAkB;oBACjB,MAAM,KAAK,IAAI;oBACf,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wFACA,KAAK,IAAI,KAAK,UAAU;;wBAGzB,KAAK,IAAI,KAAK,UAAU,KAAK,IAAI,IAAI,WAAW,KAAK,IAAI;sCAC1D,8OAAC;sCAAM,KAAK,KAAK;;;;;;wBAChB,KAAK,WAAW,kBACf,8OAAC;4BAAK,WAAU;sCACb,KAAK,WAAW;;;;;;;;;;;;eAZhB;;;;;;;;;;AAqBA,SAAS;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,cAAW;4BACX,eAAY;4BACZ,WAAU;sCAEV,cAAA,8OAAC,oIAAA,CAAA,UAAI;;;;;;;;;;sCAGP,8OAAC,qJAAA,CAAA,UAAS;;;;;sCAEV,8OAAC,kIAAA,CAAA,SAAM;4BACL,cAAY,iBAAiB,eAAe;4BAC5C,SAAQ;4BACR,MAAK;4BACL,WAAU;4BACV,SAAS,IAAM,kBAAkB,CAAC,OAAS,CAAC;sCAE3C,+BAAiB,8OAAC,8IAAA,CAAA,MAAG;gCAAC,MAAM;;;;;qDAAS,8OAAC,8IAAA,CAAA,SAAM;gCAAC,MAAM;;;;;;;;;;;;;;;;;8BAKxD,8OAAC,yLAAA,CAAA,kBAAe;8BACb,gCACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,SAAS;4BAAE,QAAQ;4BAAG,SAAS;wBAAE;wBACjC,SAAS;4BAAE,QAAQ;4BAAQ,SAAS;wBAAE;wBACtC,MAAM;4BAAE,QAAQ;4BAAG,SAAS;wBAAE;wBAC9B,YAAY;4BAAE,UAAU;4BAAK,MAAM;wBAAY;wBAC/C,WAAU;wBACV,cAAW;kCAEX,cAAA,8OAAC,8IAAA,CAAA,iBAAc;4BAAC,WAAU;sCACxB,cAAA,8OAAC,8IAAA,CAAA,qBAAkB;gCAAC,WAAU;0CAC3B,gBAAgB,GAAG,CAAC,CAAC,MAAM,kBAC1B,8OAAC,8IAAA,CAAA,qBAAkB;kDAChB,KAAK,OAAO,iBACX;;8DACE,8OAAC;oDAAI,WAAU;8DACZ,KAAK,KAAK;;;;;;8DAEb,8OAAC;oDAAG,WAAU;8DACX,KAAK,KAAK,EAAE,IAAI,CAAC,MAAM,oBACtB,8OAAC;sEACC,cAAA,8OAAC,8IAAA,CAAA,qBAAkB;gEACjB,MAAM,KAAK,IAAI;gEACf,WAAU;;oEAET,KAAK,IAAI,KAAK,UACb,KAAK,IAAI,IACT,WAAW,KAAK,IAAI;oEACrB,KAAK,KAAK;;;;;;;2DARN;;;;;;;;;;;yEAef,8OAAC,8IAAA,CAAA,qBAAkB;4CACjB,MAAM,KAAK,IAAI;4CACf,WAAU;sDAET,KAAK,KAAK;;;;;;uCA3BQ;;;;;;;;;;;;;;;uBAX3B;;;;;;;;;;8BAkDV,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,cAAW;oCACX,WAAU;8CAEV,cAAA,8OAAC,oIAAA,CAAA,UAAI;;;;;;;;;;8CAGP,8OAAC,8IAAA,CAAA,iBAAc;oCAAC,UAAU;8CACxB,cAAA,8OAAC,8IAAA,CAAA,qBAAkB;wCAAC,WAAU;kDAC3B,gBAAgB,GAAG,CAAC,CAAC,MAAM,sBAC1B,8OAAC,8IAAA,CAAA,qBAAkB;0DAChB,KAAK,OAAO,iBACX;;sEACE,8OAAC,8IAAA,CAAA,wBAAqB;4DAAC,WAAU;sEAC9B,KAAK,KAAK;;;;;;sEAEb,8OAAC,8IAAA,CAAA,wBAAqB;4DAAC,WAAU;sEAC9B,mBAAmB;;;;;;;iFAIxB,8OAAC,8IAAA,CAAA,qBAAkB;oDACjB,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,KAAK;;;;;;+CAfQ;;;;;;;;;;;;;;;;;;;;;sCAyBjC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,qJAAA,CAAA,UAAS;;;;;;;;;;sCAIZ,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCACL,OAAO;oCACP,SAAQ;oCACR,MAAK;oCACL,WAAU;8CAEV,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;kDAAI;;;;;;;;;;;8CAEjB,8OAAC,kIAAA,CAAA,SAAM;oCACL,OAAO;oCACP,MAAK;oCACL,WAAU;8CAEV,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;kDAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO7B"}}, {"offset": {"line": 1175, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}
{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/Work/Repositories/templgen/src/app/auth/signup/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { useRouter } from 'next/navigation';\n\nfunction IconGoogle({ className }: { className?: string }) {\n  return (\n    <svg className={className} viewBox=\"0 0 48 48\" aria-hidden=\"true\" xmlns=\"http://www.w3.org/2000/svg\">\n      <path fill=\"#FFC107\" d=\"M43.6 20.5H42V20H24v8h11.3C33.9 32.6 29.3 36 24 36c-6.6 0-12-5.4-12-12S17.4 12 24 12c3 0 5.7 1.1 7.7 3l5.7-5.7C33.9 6.1 29.2 4 24 4 12.9 4 4 12.9 4 24s8.9 20 20 20 20-8.9 20-20c0-1.2-.1-2.3-.4-3.5z\"/>\n      <path fill=\"#FF3D00\" d=\"M6.3 14.7l6.6 4.8C14.6 16.2 18.9 12 24 12c3 0 5.7 1.1 7.7 3l5.7-5.7C33.9 6.1 29.2 4 24 4c-7.7 0-14.3 4.3-17.7 10.7z\"/>\n      <path fill=\"#4CAF50\" d=\"M24 44c5.2 0 9.9-2 13.3-5.3l-6.2-5.1C29.1 35.6 26.7 36 24 36c-5.3 0-9.9-3.4-11.3-8H6.3l-6.6 5C3.7 39.6 13 44 24 44z\"/>\n      <path fill=\"#1976D2\" d=\"M43.6 20.5H42V20H24v8h11.3c-1.3 3.8-5.1 6.5-9.3 6.5-5.3 0-9.9-3.4-11.3-8H6.3l-6.6 5C3.7 39.6 13 44 24 44c11 0 20-9 20-20 0-1.2-.1-2.3-.4-3.5z\"/>\n    </svg>\n  );\n}\n\nexport default function RegisterPage() {\n  const router = useRouter();\n  const [email, setEmail] = useState('');\n  const [touched, setTouched] = useState(false);\n\n  const isValidEmail = (value: string) =>\n    /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(value);\n\n  const showError = touched && !isValidEmail(email);\n\n  const onSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setTouched(true);\n    if (!isValidEmail(email)) return;\n\n    // TODO: handle registration request\n    // await fetch('/api/register', { method: 'POST', body: JSON.stringify({ email }) });\n\n    // Navigate to next step\n    router.push('/verify');\n  };\n\n  const continueWithGoogle = async () => {\n    // TODO: hook up OAuth (e.g., NextAuth signIn('google'))\n    router.push('/onboarding');\n  };\n\n  return (\n    <main className=\"min-h-screen w-full flex items-center justify-center bg-[var(--background)] text-[var(--main)] px-6\">\n      <div className=\"w-full max-w-md\">\n        {/* Logo + Title */}\n        <div className=\"mb-6\">\n          <div className=\"flex items-center gap-3\">\n            {/* Reference-like logo block */}\n            <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"30\" aria-hidden=\"true\">\n              <path d=\"M-.14-.003h20.28v10.006H10Z\" fill=\"#FFF\"></path>\n              <defs>\n                <linearGradient id=\"a\" x1=\".49\" x2=\".51\" y1=\"0\" y2=\"1\">\n                  <stop offset=\"0\" stopColor=\"#0AF\" />\n                  <stop offset=\"1\" stopColor=\"#09F\" />\n                </linearGradient>\n              </defs>\n              <path d=\"M-.14 10.003H10L20.14 20.01H-.14Z\" fill=\"url(#a)\"></path>\n              <defs>\n                <linearGradient id=\"b\" x1=\".50\" x2=\".50\" y1=\"0\" y2=\"1\">\n                  <stop offset=\"0\" stopColor=\"#03F\" />\n                  <stop offset=\"1\" stopColor=\"#05F\" />\n                </linearGradient>\n              </defs>\n              <path d=\"M-.14 20.01H10v10.006Z\" fill=\"url(#b)\"></path>\n            </svg>\n\n            <h5 className=\"text-lg font-medium text-[var(--headline)]\">\n              Welcome to Framer\n            </h5>\n          </div>\n        </div>\n\n        {/* Continue with Google */}\n        <div className=\"mb-6\">\n          <button\n            onClick={continueWithGoogle}\n            className=\"w-full inline-flex items-center justify-center gap-3 rounded-lg px-4 py-3 font-medium\n                       bg-[var(--button)] text-[var(--button-text)]\n                       hover:bg-[var(--highlight)]\n                       border border-[var(--button-border)]\n                       transition-colors\"\n          >\n            <IconGoogle className=\"w-5 h-5\" />\n            <span>Continue with Google</span>\n          </button>\n        </div>\n\n        {/* Email form with error like reference */}\n        <form noValidate onSubmit={onSubmit} className=\"space-y-3\">\n          <div>\n            <input\n              className={[\n                'w-full rounded-lg px-4 py-3 outline-none',\n                'bg-[var(--input-background)] text-[var(--input-text)]',\n                'placeholder-[var(--paragraph)]',\n                'border',\n                showError\n                  ? 'border-[var(--selectBox-border)] border-red-400'\n                  : 'border-[var(--input-border-color)] focus:border-[var(--selectBox-border)]',\n              ].join(' ')}\n              style={{\n                marginTop: 20,\n                backgroundColor: 'rgba(0,0,0,0.2)',\n                color: 'rgb(255,255,255)',\n                boxShadow: 'inset 0 0 0 9999px rgba(0,0,0,0)',\n              }}\n              type=\"email\"\n              placeholder=\"Email\"\n              value={email}\n              onChange={(e) => setEmail(e.target.value)}\n              onBlur={() => setTouched(true)}\n              autoCapitalize=\"none\"\n              autoCorrect=\"off\"\n              spellCheck={false}\n            />\n          </div>\n\n          {showError && (\n            <div>\n              <p\n                className=\"text-xs\"\n                style={{ color: 'var(--fraction-color-danger, #f36)' }}\n              >\n                Please enter a valid email address\n              </p>\n            </div>\n          )}\n\n          <button\n            type=\"submit\"\n            className=\"w-full rounded-lg px-4 py-3 font-medium\n                       bg-[var(--button)] text-[var(--button-text)]\n                       hover:bg-[var(--highlight)]\n                       border border-[var(--button-border)]\n                       transition-[background,border-color] duration-150\"\n          >\n            <span className=\"label\" aria-hidden=\"false\">Continue</span>\n          </button>\n        </form>\n      </div>\n    </main>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKA,SAAS,WAAW,EAAE,SAAS,EAA0B;IACvD,qBACE,8OAAC;QAAI,WAAW;QAAW,SAAQ;QAAY,eAAY;QAAO,OAAM;;0BACtE,8OAAC;gBAAK,MAAK;gBAAU,GAAE;;;;;;0BACvB,8OAAC;gBAAK,MAAK;gBAAU,GAAE;;;;;;0BACvB,8OAAC;gBAAK,MAAK;gBAAU,GAAE;;;;;;0BACvB,8OAAC;gBAAK,MAAK;gBAAU,GAAE;;;;;;;;;;;;AAG7B;AAEe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,eAAe,CAAC,QACpB,6BAA6B,IAAI,CAAC;IAEpC,MAAM,YAAY,WAAW,CAAC,aAAa;IAE3C,MAAM,WAAW,OAAO;QACtB,EAAE,cAAc;QAChB,WAAW;QACX,IAAI,CAAC,aAAa,QAAQ;QAE1B,oCAAoC;QACpC,qFAAqF;QAErF,wBAAwB;QACxB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,qBAAqB;QACzB,wDAAwD;QACxD,OAAO,IAAI,CAAC;IACd;IAEA,qBACE,8OAAC;QAAK,WAAU;kBACd,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,OAAM;gCAA6B,OAAM;gCAAK,QAAO;gCAAK,eAAY;;kDACzE,8OAAC;wCAAK,GAAE;wCAA8B,MAAK;;;;;;kDAC3C,8OAAC;kDACC,cAAA,8OAAC;4CAAe,IAAG;4CAAI,IAAG;4CAAM,IAAG;4CAAM,IAAG;4CAAI,IAAG;;8DACjD,8OAAC;oDAAK,QAAO;oDAAI,WAAU;;;;;;8DAC3B,8OAAC;oDAAK,QAAO;oDAAI,WAAU;;;;;;;;;;;;;;;;;kDAG/B,8OAAC;wCAAK,GAAE;wCAAoC,MAAK;;;;;;kDACjD,8OAAC;kDACC,cAAA,8OAAC;4CAAe,IAAG;4CAAI,IAAG;4CAAM,IAAG;4CAAM,IAAG;4CAAI,IAAG;;8DACjD,8OAAC;oDAAK,QAAO;oDAAI,WAAU;;;;;;8DAC3B,8OAAC;oDAAK,QAAO;oDAAI,WAAU;;;;;;;;;;;;;;;;;kDAG/B,8OAAC;wCAAK,GAAE;wCAAyB,MAAK;;;;;;;;;;;;0CAGxC,8OAAC;gCAAG,WAAU;0CAA6C;;;;;;;;;;;;;;;;;8BAO/D,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBACC,SAAS;wBACT,WAAU;;0CAMV,8OAAC;gCAAW,WAAU;;;;;;0CACtB,8OAAC;0CAAK;;;;;;;;;;;;;;;;;8BAKV,8OAAC;oBAAK,UAAU;oBAAC,UAAU;oBAAU,WAAU;;sCAC7C,8OAAC;sCACC,cAAA,8OAAC;gCACC,WAAW;oCACT;oCACA;oCACA;oCACA;oCACA,YACI,oDACA;iCACL,CAAC,IAAI,CAAC;gCACP,OAAO;oCACL,WAAW;oCACX,iBAAiB;oCACjB,OAAO;oCACP,WAAW;gCACb;gCACA,MAAK;gCACL,aAAY;gCACZ,OAAO;gCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;gCACxC,QAAQ,IAAM,WAAW;gCACzB,gBAAe;gCACf,aAAY;gCACZ,YAAY;;;;;;;;;;;wBAIf,2BACC,8OAAC;sCACC,cAAA,8OAAC;gCACC,WAAU;gCACV,OAAO;oCAAE,OAAO;gCAAqC;0CACtD;;;;;;;;;;;sCAML,8OAAC;4BACC,MAAK;4BACL,WAAU;sCAMV,cAAA,8OAAC;gCAAK,WAAU;gCAAQ,eAAY;0CAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMxD"}}, {"offset": {"line": 338, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}
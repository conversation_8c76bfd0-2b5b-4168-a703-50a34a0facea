{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/src/components/ui/FuzzyText.tsx"], "sourcesContent": ["import React, { useEffect, useRef } from \"react\";\n\ninterface FuzzyTextProps {\n  children: React.ReactNode;\n  fontSize?: number | string;\n  fontWeight?: string | number;\n  fontFamily?: string;\n  color?: string;\n  enableHover?: boolean;\n  baseIntensity?: number;\n  hoverIntensity?: number;\n}\n\nconst FuzzyText: React.FC<FuzzyTextProps> = ({\n  children,\n  fontSize = \"clamp(2rem, 8vw, 8rem)\",\n  fontWeight = 900,\n  fontFamily = \"inherit\",\n  color = \"#fff\",\n  enableHover = true,\n  baseIntensity = 0.18,\n  hoverIntensity = 0.5,\n}) => {\n  const canvasRef = useRef<\n    HTMLCanvasElement & { cleanupFuzzyText?: () => void }\n  >(null);\n\n  useEffect(() => {\n    let animationFrameId: number;\n    let isCancelled = false;\n    const canvas = canvasRef.current;\n    if (!canvas) return;\n\n    const init = async () => {\n      if (document.fonts?.ready) {\n        await document.fonts.ready;\n      }\n      if (isCancelled) return;\n\n      const ctx = canvas.getContext(\"2d\");\n      if (!ctx) return;\n\n      const computedFontFamily =\n        fontFamily === \"inherit\"\n          ? window.getComputedStyle(canvas).fontFamily || \"sans-serif\"\n          : fontFamily;\n\n      const fontSizeStr =\n        typeof fontSize === \"number\" ? `${fontSize}px` : fontSize;\n      let numericFontSize: number;\n      if (typeof fontSize === \"number\") {\n        numericFontSize = fontSize;\n      } else {\n        const temp = document.createElement(\"span\");\n        temp.style.fontSize = fontSize;\n        document.body.appendChild(temp);\n        const computedSize = window.getComputedStyle(temp).fontSize;\n        numericFontSize = parseFloat(computedSize);\n        document.body.removeChild(temp);\n      }\n\n      const text = React.Children.toArray(children).join(\"\");\n\n      const offscreen = document.createElement(\"canvas\");\n      const offCtx = offscreen.getContext(\"2d\");\n      if (!offCtx) return;\n\n      offCtx.font = `${fontWeight} ${fontSizeStr} ${computedFontFamily}`;\n      offCtx.textBaseline = \"alphabetic\";\n      const metrics = offCtx.measureText(text);\n\n      const actualLeft = metrics.actualBoundingBoxLeft ?? 0;\n      const actualRight = metrics.actualBoundingBoxRight ?? metrics.width;\n      const actualAscent = metrics.actualBoundingBoxAscent ?? numericFontSize;\n      const actualDescent =\n        metrics.actualBoundingBoxDescent ?? numericFontSize * 0.2;\n\n      const textBoundingWidth = Math.ceil(actualLeft + actualRight);\n      const tightHeight = Math.ceil(actualAscent + actualDescent);\n\n      const extraWidthBuffer = 10;\n      const offscreenWidth = textBoundingWidth + extraWidthBuffer;\n\n      offscreen.width = offscreenWidth;\n      offscreen.height = tightHeight;\n\n      const xOffset = extraWidthBuffer / 2;\n      offCtx.font = `${fontWeight} ${fontSizeStr} ${computedFontFamily}`;\n      offCtx.textBaseline = \"alphabetic\";\n      offCtx.fillStyle = color;\n      offCtx.fillText(text, xOffset - actualLeft, actualAscent);\n\n      const horizontalMargin = 50;\n      const verticalMargin = 0;\n      canvas.width = offscreenWidth + horizontalMargin * 2;\n      canvas.height = tightHeight + verticalMargin * 2;\n      ctx.translate(horizontalMargin, verticalMargin);\n\n      const interactiveLeft = horizontalMargin + xOffset;\n      const interactiveTop = verticalMargin;\n      const interactiveRight = interactiveLeft + textBoundingWidth;\n      const interactiveBottom = interactiveTop + tightHeight;\n\n      let isHovering = false;\n      const fuzzRange = 30;\n\n      const run = () => {\n        if (isCancelled) return;\n        ctx.clearRect(\n          -fuzzRange,\n          -fuzzRange,\n          offscreenWidth + 2 * fuzzRange,\n          tightHeight + 2 * fuzzRange\n        );\n        const intensity = isHovering ? hoverIntensity : baseIntensity;\n        for (let j = 0; j < tightHeight; j++) {\n          const dx = Math.floor(intensity * (Math.random() - 0.5) * fuzzRange);\n          ctx.drawImage(\n            offscreen,\n            0,\n            j,\n            offscreenWidth,\n            1,\n            dx,\n            j,\n            offscreenWidth,\n            1\n          );\n        }\n        animationFrameId = window.requestAnimationFrame(run);\n      };\n\n      run();\n\n      const isInsideTextArea = (x: number, y: number) =>\n        x >= interactiveLeft &&\n        x <= interactiveRight &&\n        y >= interactiveTop &&\n        y <= interactiveBottom;\n\n      const handleMouseMove = (e: MouseEvent) => {\n        if (!enableHover) return;\n        const rect = canvas.getBoundingClientRect();\n        const x = e.clientX - rect.left;\n        const y = e.clientY - rect.top;\n        isHovering = isInsideTextArea(x, y);\n      };\n\n      const handleMouseLeave = () => {\n        isHovering = false;\n      };\n\n      const handleTouchMove = (e: TouchEvent) => {\n        if (!enableHover) return;\n        e.preventDefault();\n        const rect = canvas.getBoundingClientRect();\n        const touch = e.touches[0];\n        const x = touch.clientX - rect.left;\n        const y = touch.clientY - rect.top;\n        isHovering = isInsideTextArea(x, y);\n      };\n\n      const handleTouchEnd = () => {\n        isHovering = false;\n      };\n\n      if (enableHover) {\n        canvas.addEventListener(\"mousemove\", handleMouseMove);\n        canvas.addEventListener(\"mouseleave\", handleMouseLeave);\n        canvas.addEventListener(\"touchmove\", handleTouchMove, {\n          passive: false,\n        });\n        canvas.addEventListener(\"touchend\", handleTouchEnd);\n      }\n\n      const cleanup = () => {\n        window.cancelAnimationFrame(animationFrameId);\n        if (enableHover) {\n          canvas.removeEventListener(\"mousemove\", handleMouseMove);\n          canvas.removeEventListener(\"mouseleave\", handleMouseLeave);\n          canvas.removeEventListener(\"touchmove\", handleTouchMove);\n          canvas.removeEventListener(\"touchend\", handleTouchEnd);\n        }\n      };\n\n      canvas.cleanupFuzzyText = cleanup;\n    };\n\n    init();\n\n    return () => {\n      isCancelled = true;\n      window.cancelAnimationFrame(animationFrameId);\n      if (canvas && canvas.cleanupFuzzyText) {\n        canvas.cleanupFuzzyText();\n      }\n    };\n  }, [\n    children,\n    fontSize,\n    fontWeight,\n    fontFamily,\n    color,\n    enableHover,\n    baseIntensity,\n    hoverIntensity,\n  ]);\n\n  return <canvas ref={canvasRef} />;\n};\n\nexport default FuzzyText;\n"], "names": [], "mappings": ";;;;AAAA;;;AAaA,MAAM,YAAsC,CAAC,EAC3C,QAAQ,EACR,WAAW,wBAAwB,EACnC,aAAa,GAAG,EAChB,aAAa,SAAS,EACtB,QAAQ,MAAM,EACd,cAAc,IAAI,EAClB,gBAAgB,IAAI,EACpB,iBAAiB,GAAG,EACrB;IACC,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAErB;IAEF,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI;QACJ,IAAI,cAAc;QAClB,MAAM,SAAS,UAAU,OAAO;QAChC,IAAI,CAAC,QAAQ;QAEb,MAAM,OAAO;YACX,IAAI,SAAS,KAAK,EAAE,OAAO;gBACzB,MAAM,SAAS,KAAK,CAAC,KAAK;YAC5B;YACA,IAAI,aAAa;YAEjB,MAAM,MAAM,OAAO,UAAU,CAAC;YAC9B,IAAI,CAAC,KAAK;YAEV,MAAM,qBACJ,eAAe,YACX,OAAO,gBAAgB,CAAC,QAAQ,UAAU,IAAI,eAC9C;YAEN,MAAM,cACJ,OAAO,aAAa,WAAW,GAAG,SAAS,EAAE,CAAC,GAAG;YACnD,IAAI;YACJ,IAAI,OAAO,aAAa,UAAU;gBAChC,kBAAkB;YACpB,OAAO;gBACL,MAAM,OAAO,SAAS,aAAa,CAAC;gBACpC,KAAK,KAAK,CAAC,QAAQ,GAAG;gBACtB,SAAS,IAAI,CAAC,WAAW,CAAC;gBAC1B,MAAM,eAAe,OAAO,gBAAgB,CAAC,MAAM,QAAQ;gBAC3D,kBAAkB,WAAW;gBAC7B,SAAS,IAAI,CAAC,WAAW,CAAC;YAC5B;YAEA,MAAM,OAAO,qMAAA,CAAA,UAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAU,IAAI,CAAC;YAEnD,MAAM,YAAY,SAAS,aAAa,CAAC;YACzC,MAAM,SAAS,UAAU,UAAU,CAAC;YACpC,IAAI,CAAC,QAAQ;YAEb,OAAO,IAAI,GAAG,GAAG,WAAW,CAAC,EAAE,YAAY,CAAC,EAAE,oBAAoB;YAClE,OAAO,YAAY,GAAG;YACtB,MAAM,UAAU,OAAO,WAAW,CAAC;YAEnC,MAAM,aAAa,QAAQ,qBAAqB,IAAI;YACpD,MAAM,cAAc,QAAQ,sBAAsB,IAAI,QAAQ,KAAK;YACnE,MAAM,eAAe,QAAQ,uBAAuB,IAAI;YACxD,MAAM,gBACJ,QAAQ,wBAAwB,IAAI,kBAAkB;YAExD,MAAM,oBAAoB,KAAK,IAAI,CAAC,aAAa;YACjD,MAAM,cAAc,KAAK,IAAI,CAAC,eAAe;YAE7C,MAAM,mBAAmB;YACzB,MAAM,iBAAiB,oBAAoB;YAE3C,UAAU,KAAK,GAAG;YAClB,UAAU,MAAM,GAAG;YAEnB,MAAM,UAAU,mBAAmB;YACnC,OAAO,IAAI,GAAG,GAAG,WAAW,CAAC,EAAE,YAAY,CAAC,EAAE,oBAAoB;YAClE,OAAO,YAAY,GAAG;YACtB,OAAO,SAAS,GAAG;YACnB,OAAO,QAAQ,CAAC,MAAM,UAAU,YAAY;YAE5C,MAAM,mBAAmB;YACzB,MAAM,iBAAiB;YACvB,OAAO,KAAK,GAAG,iBAAiB,mBAAmB;YACnD,OAAO,MAAM,GAAG,cAAc,iBAAiB;YAC/C,IAAI,SAAS,CAAC,kBAAkB;YAEhC,MAAM,kBAAkB,mBAAmB;YAC3C,MAAM,iBAAiB;YACvB,MAAM,mBAAmB,kBAAkB;YAC3C,MAAM,oBAAoB,iBAAiB;YAE3C,IAAI,aAAa;YACjB,MAAM,YAAY;YAElB,MAAM,MAAM;gBACV,IAAI,aAAa;gBACjB,IAAI,SAAS,CACX,CAAC,WACD,CAAC,WACD,iBAAiB,IAAI,WACrB,cAAc,IAAI;gBAEpB,MAAM,YAAY,aAAa,iBAAiB;gBAChD,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,IAAK;oBACpC,MAAM,KAAK,KAAK,KAAK,CAAC,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;oBAC1D,IAAI,SAAS,CACX,WACA,GACA,GACA,gBACA,GACA,IACA,GACA,gBACA;gBAEJ;gBACA,mBAAmB,OAAO,qBAAqB,CAAC;YAClD;YAEA;YAEA,MAAM,mBAAmB,CAAC,GAAW,IACnC,KAAK,mBACL,KAAK,oBACL,KAAK,kBACL,KAAK;YAEP,MAAM,kBAAkB,CAAC;gBACvB,IAAI,CAAC,aAAa;gBAClB,MAAM,OAAO,OAAO,qBAAqB;gBACzC,MAAM,IAAI,EAAE,OAAO,GAAG,KAAK,IAAI;gBAC/B,MAAM,IAAI,EAAE,OAAO,GAAG,KAAK,GAAG;gBAC9B,aAAa,iBAAiB,GAAG;YACnC;YAEA,MAAM,mBAAmB;gBACvB,aAAa;YACf;YAEA,MAAM,kBAAkB,CAAC;gBACvB,IAAI,CAAC,aAAa;gBAClB,EAAE,cAAc;gBAChB,MAAM,OAAO,OAAO,qBAAqB;gBACzC,MAAM,QAAQ,EAAE,OAAO,CAAC,EAAE;gBAC1B,MAAM,IAAI,MAAM,OAAO,GAAG,KAAK,IAAI;gBACnC,MAAM,IAAI,MAAM,OAAO,GAAG,KAAK,GAAG;gBAClC,aAAa,iBAAiB,GAAG;YACnC;YAEA,MAAM,iBAAiB;gBACrB,aAAa;YACf;YAEA,IAAI,aAAa;gBACf,OAAO,gBAAgB,CAAC,aAAa;gBACrC,OAAO,gBAAgB,CAAC,cAAc;gBACtC,OAAO,gBAAgB,CAAC,aAAa,iBAAiB;oBACpD,SAAS;gBACX;gBACA,OAAO,gBAAgB,CAAC,YAAY;YACtC;YAEA,MAAM,UAAU;gBACd,OAAO,oBAAoB,CAAC;gBAC5B,IAAI,aAAa;oBACf,OAAO,mBAAmB,CAAC,aAAa;oBACxC,OAAO,mBAAmB,CAAC,cAAc;oBACzC,OAAO,mBAAmB,CAAC,aAAa;oBACxC,OAAO,mBAAmB,CAAC,YAAY;gBACzC;YACF;YAEA,OAAO,gBAAgB,GAAG;QAC5B;QAEA;QAEA,OAAO;YACL,cAAc;YACd,OAAO,oBAAoB,CAAC;YAC5B,IAAI,UAAU,OAAO,gBAAgB,EAAE;gBACrC,OAAO,gBAAgB;YACzB;QACF;IACF,GAAG;QACD;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,qBAAO,8OAAC;QAAO,KAAK;;;;;;AACtB;uCAEe"}}, {"offset": {"line": 154, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 160, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/src/components/ui/text-randomized.tsx"], "sourcesContent": ["\"use client\";\nimport { useEffect, useState, useCallback } from \"react\";\n\nconst lettersAndSymbols = \"abcdefghijklmnopqrstuvwxyz!@#$%^&*-_+=;:<>,\";\n\ninterface AnimatedTextProps {\n  text: string;\n  className?: string;\n}\n\nexport function RandomizedTextEffect({\n  text,\n  className = \"\",\n}: AnimatedTextProps) {\n  const [animatedText, setAnimatedText] = useState(\"\");\n  const [hasSeenAnimation, setHasSeenAnimation] = useState<boolean>(false);\n\n  const getRandomChar = useCallback(\n    () =>\n      lettersAndSymbols[Math.floor(Math.random() * lettersAndSymbols.length)],\n    [],\n  );\n\n  const animateText = useCallback(async () => {\n    const duration = 30;\n    const revealDuration = 40;\n    const initialRandomDuration = 300;\n\n    const generateRandomText = () =>\n      text\n        .split(\"\")\n        .map(() => getRandomChar())\n        .join(\"\");\n\n    setAnimatedText(generateRandomText());\n\n    const endTime = Date.now() + initialRandomDuration;\n    while (Date.now() < endTime) {\n      await new Promise((resolve) => setTimeout(resolve, duration));\n      setAnimatedText(generateRandomText());\n    }\n\n    for (let i = 0; i < text.length; i++) {\n      await new Promise((resolve) => setTimeout(resolve, revealDuration));\n      setAnimatedText(\n        (prevText) =>\n          text.slice(0, i + 1) +\n          prevText\n            .slice(i + 1)\n            .split(\"\")\n            .map(() => getRandomChar())\n            .join(\"\"),\n      );\n    }\n  }, [text, getRandomChar]);\n\n  useEffect(() => {\n    const isFirstVisit = !localStorage.getItem(\"hasSeenAnimation\");\n\n    if (isFirstVisit) {\n      animateText();\n      localStorage.setItem(\"hasSeenAnimation\", \"true\");\n    } else {\n      setHasSeenAnimation(true);\n    }\n\n    const handleBeforeUnload = () => {\n      localStorage.removeItem(\"hasSeenAnimation\");\n    };\n\n    window.addEventListener(\"beforeunload\", handleBeforeUnload);\n\n    return () => {\n      window.removeEventListener(\"beforeunload\", handleBeforeUnload);\n    };\n  }, [animateText]);\n\n  return (\n    <div className={`relative inline-block ${className}`}>\n      {hasSeenAnimation ? text : animatedText}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AADA;;;AAGA,MAAM,oBAAoB;AAOnB,SAAS,qBAAqB,EACnC,IAAI,EACJ,YAAY,EAAE,EACI;IAClB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAElE,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC9B,IACE,iBAAiB,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,kBAAkB,MAAM,EAAE,EACzE,EAAE;IAGJ,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC9B,MAAM,WAAW;QACjB,MAAM,iBAAiB;QACvB,MAAM,wBAAwB;QAE9B,MAAM,qBAAqB,IACzB,KACG,KAAK,CAAC,IACN,GAAG,CAAC,IAAM,iBACV,IAAI,CAAC;QAEV,gBAAgB;QAEhB,MAAM,UAAU,KAAK,GAAG,KAAK;QAC7B,MAAO,KAAK,GAAG,KAAK,QAAS;YAC3B,MAAM,IAAI,QAAQ,CAAC,UAAY,WAAW,SAAS;YACnD,gBAAgB;QAClB;QAEA,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;YACpC,MAAM,IAAI,QAAQ,CAAC,UAAY,WAAW,SAAS;YACnD,gBACE,CAAC,WACC,KAAK,KAAK,CAAC,GAAG,IAAI,KAClB,SACG,KAAK,CAAC,IAAI,GACV,KAAK,CAAC,IACN,GAAG,CAAC,IAAM,iBACV,IAAI,CAAC;QAEd;IACF,GAAG;QAAC;QAAM;KAAc;IAExB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe,CAAC,aAAa,OAAO,CAAC;QAE3C,IAAI,cAAc;YAChB;YACA,aAAa,OAAO,CAAC,oBAAoB;QAC3C,OAAO;YACL,oBAAoB;QACtB;QAEA,MAAM,qBAAqB;YACzB,aAAa,UAAU,CAAC;QAC1B;QAEA,OAAO,gBAAgB,CAAC,gBAAgB;QAExC,OAAO;YACL,OAAO,mBAAmB,CAAC,gBAAgB;QAC7C;IACF,GAAG;QAAC;KAAY;IAEhB,qBACE,8OAAC;QAAI,WAAW,CAAC,sBAAsB,EAAE,WAAW;kBACjD,mBAAmB,OAAO;;;;;;AAGjC"}}, {"offset": {"line": 219, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 225, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/src/app/not-found.tsx"], "sourcesContent": ["\"use client\";\n\nimport { But<PERSON> } from \"@/components/ui\";\nimport FuzzyText from \"@/components/ui/FuzzyText\";\nimport { RandomizedTextEffect } from \"@/components/ui/text-randomized\";\nimport Link from \"next/link\";\nimport { IoArrowBack } from \"react-icons/io5\";\nexport default function NotFoundPage() {\n  return (\n    <>\n      {/* Add structured data for the 404 page */}\n\n      <div className=\"fixed w-screen h-screen bg-[var(--background)]  flex flex-col justify-center items-center  z-50 inset-0\">\n        <div className=\"flex flex-col items-center justify-center text-center\">\n          <FuzzyText\n            fontSize={50}\n            baseIntensity={0.2}\n            hoverIntensity={2}\n            enableHover={false}\n          >\n            404 | Page Not Found\n          </FuzzyText>\n\n          <RandomizedTextEffect\n            className=\"mt-4 text-xl text-[var(--paragraph)]\"\n            text={\"The page you are looking for doesn't exist.\"}\n          />\n          <div className=\"mt-8 w-max\">\n            <Link href=\"/\">\n              <Button>\n                <IoArrowBack />\n                <span>Back To HomePage</span>\n              </Button>\n            </Link>\n          </div>\n        </div>\n      </div>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAHA;AAIA;AANA;;;;;;;AAOe,SAAS;IACtB,qBACE;kBAGE,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,qIAAA,CAAA,UAAS;wBACR,UAAU;wBACV,eAAe;wBACf,gBAAgB;wBAChB,aAAa;kCACd;;;;;;kCAID,8OAAC,8IAAA,CAAA,uBAAoB;wBACnB,WAAU;wBACV,MAAM;;;;;;kCAER,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;sCACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;;kDACL,8OAAC,+IAAA,CAAA,cAAW;;;;;kDACZ,8OAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtB"}}, {"offset": {"line": 315, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}
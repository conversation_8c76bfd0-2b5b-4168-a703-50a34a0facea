{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/class-variance-authority/dist/index.mjs"], "sourcesContent": ["/**\n * Copyright 2022 Joe Bell. All rights reserved.\n *\n * This file is licensed to you under the Apache License, Version 2.0\n * (the \"License\"); you may not use this file except in compliance with the\n * License. You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS, WITHOUT\n * WARRANTIES OR REPRESENTATIONS OF ANY KIND, either express or implied. See the\n * License for the specific language governing permissions and limitations under\n * the License.\n */ import { clsx } from \"clsx\";\nconst falsyToString = (value)=>typeof value === \"boolean\" ? `${value}` : value === 0 ? \"0\" : value;\nexport const cx = clsx;\nexport const cva = (base, config)=>(props)=>{\n        var _config_compoundVariants;\n        if ((config === null || config === void 0 ? void 0 : config.variants) == null) return cx(base, props === null || props === void 0 ? void 0 : props.class, props === null || props === void 0 ? void 0 : props.className);\n        const { variants, defaultVariants } = config;\n        const getVariantClassNames = Object.keys(variants).map((variant)=>{\n            const variantProp = props === null || props === void 0 ? void 0 : props[variant];\n            const defaultVariantProp = defaultVariants === null || defaultVariants === void 0 ? void 0 : defaultVariants[variant];\n            if (variantProp === null) return null;\n            const variantKey = falsyToString(variantProp) || falsyToString(defaultVariantProp);\n            return variants[variant][variantKey];\n        });\n        const propsWithoutUndefined = props && Object.entries(props).reduce((acc, param)=>{\n            let [key, value] = param;\n            if (value === undefined) {\n                return acc;\n            }\n            acc[key] = value;\n            return acc;\n        }, {});\n        const getCompoundVariantClassNames = config === null || config === void 0 ? void 0 : (_config_compoundVariants = config.compoundVariants) === null || _config_compoundVariants === void 0 ? void 0 : _config_compoundVariants.reduce((acc, param)=>{\n            let { class: cvClass, className: cvClassName, ...compoundVariantOptions } = param;\n            return Object.entries(compoundVariantOptions).every((param)=>{\n                let [key, value] = param;\n                return Array.isArray(value) ? value.includes({\n                    ...defaultVariants,\n                    ...propsWithoutUndefined\n                }[key]) : ({\n                    ...defaultVariants,\n                    ...propsWithoutUndefined\n                })[key] === value;\n            }) ? [\n                ...acc,\n                cvClass,\n                cvClassName\n            ] : acc;\n        }, []);\n        return cx(base, getVariantClassNames, getCompoundVariantClassNames, props === null || props === void 0 ? void 0 : props.class, props === null || props === void 0 ? void 0 : props.className);\n    };\n\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;CAcC;;;;AAAG;;AACJ,MAAM,gBAAgB,CAAC,QAAQ,OAAO,UAAU,YAAY,GAAG,OAAO,GAAG,UAAU,IAAI,MAAM;AACtF,MAAM,KAAK,qIAAA,CAAA,OAAI;AACf,MAAM,MAAM,CAAC,MAAM,SAAS,CAAC;QAC5B,IAAI;QACJ,IAAI,CAAC,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,OAAO,QAAQ,KAAK,MAAM,OAAO,GAAG,MAAM,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,MAAM,KAAK,EAAE,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,MAAM,SAAS;QACvN,MAAM,EAAE,QAAQ,EAAE,eAAe,EAAE,GAAG;QACtC,MAAM,uBAAuB,OAAO,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;YACpD,MAAM,cAAc,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,KAAK,CAAC,QAAQ;YAChF,MAAM,qBAAqB,oBAAoB,QAAQ,oBAAoB,KAAK,IAAI,KAAK,IAAI,eAAe,CAAC,QAAQ;YACrH,IAAI,gBAAgB,MAAM,OAAO;YACjC,MAAM,aAAa,cAAc,gBAAgB,cAAc;YAC/D,OAAO,QAAQ,CAAC,QAAQ,CAAC,WAAW;QACxC;QACA,MAAM,wBAAwB,SAAS,OAAO,OAAO,CAAC,OAAO,MAAM,CAAC,CAAC,KAAK;YACtE,IAAI,CAAC,KAAK,MAAM,GAAG;YACnB,IAAI,UAAU,WAAW;gBACrB,OAAO;YACX;YACA,GAAG,CAAC,IAAI,GAAG;YACX,OAAO;QACX,GAAG,CAAC;QACJ,MAAM,+BAA+B,WAAW,QAAQ,WAAW,KAAK,IAAI,KAAK,IAAI,CAAC,2BAA2B,OAAO,gBAAgB,MAAM,QAAQ,6BAA6B,KAAK,IAAI,KAAK,IAAI,yBAAyB,MAAM,CAAC,CAAC,KAAK;YACvO,IAAI,EAAE,OAAO,OAAO,EAAE,WAAW,WAAW,EAAE,GAAG,wBAAwB,GAAG;YAC5E,OAAO,OAAO,OAAO,CAAC,wBAAwB,KAAK,CAAC,CAAC;gBACjD,IAAI,CAAC,KAAK,MAAM,GAAG;gBACnB,OAAO,MAAM,OAAO,CAAC,SAAS,MAAM,QAAQ,CAAC;oBACzC,GAAG,eAAe;oBAClB,GAAG,qBAAqB;gBAC5B,CAAC,CAAC,IAAI,IAAI,CAAC;oBACP,GAAG,eAAe;oBAClB,GAAG,qBAAqB;gBAC5B,CAAC,CAAC,CAAC,IAAI,KAAK;YAChB,KAAK;mBACE;gBACH;gBACA;aACH,GAAG;QACR,GAAG,EAAE;QACL,OAAO,GAAG,MAAM,sBAAsB,8BAA8B,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,MAAM,KAAK,EAAE,UAAU,QAAQ,UAAU,KAAK,IAAI,KAAK,IAAI,MAAM,SAAS;IAChM", "ignoreList": [0]}}, {"offset": {"line": 67, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 73, "column": 0}, "map": {"version": 3, "file": "loader-circle.js", "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/lucide-react/src/icons/loader-circle.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [['path', { d: 'M21 12a9 9 0 1 1-6.219-8.56', key: '13zald' }]];\n\n/**\n * @component @name LoaderCircle\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjEgMTJhOSA5IDAgMSAxLTYuMjE5LTguNTYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/loader-circle\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst LoaderCircle = createLucideIcon('LoaderCircle', __iconNode);\n\nexport default LoaderCircle;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAuB,CAAA,CAAA;IAAC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA+B;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAC;KAAC;CAAA,CAAA;AAa5F,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,YAAA,CAAe,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0]}}, {"offset": {"line": 96, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 112, "column": 0}, "map": {"version": 3, "file": "chevron-down.js", "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/lucide-react/src/icons/chevron-down.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [['path', { d: 'm6 9 6 6 6-6', key: 'qrunsl' }]];\n\n/**\n * @component @name ChevronDown\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtNiA5IDYgNiA2LTYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/chevron-down\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ChevronDown = createLucideIcon('ChevronDown', __iconNode);\n\nexport default ChevronDown;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAuB,CAAA,CAAA;IAAC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAC;KAAC;CAAA,CAAA;AAa7E,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,WAAA,CAAc,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAe,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0]}}, {"offset": {"line": 135, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 151, "column": 0}, "map": {"version": 3, "file": "chevron-up.js", "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/lucide-react/src/icons/chevron-up.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [['path', { d: 'm18 15-6-6-6 6', key: '153udz' }]];\n\n/**\n * @component @name ChevronUp\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTggMTUtNi02LTYgNiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/chevron-up\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ChevronUp = createLucideIcon('ChevronUp', __iconNode);\n\nexport default ChevronUp;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAuB,CAAA,CAAA;IAAC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAC;KAAC;CAAA,CAAA;AAa/E,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,SAAA,CAAY,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAa,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0]}}, {"offset": {"line": 174, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 190, "column": 0}, "map": {"version": 3, "file": "check.js", "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/lucide-react/src/icons/check.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [['path', { d: 'M20 6 9 17l-5-5', key: '1gmf2c' }]];\n\n/**\n * @component @name Check\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjAgNiA5IDE3bC01LTUiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/check\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Check = createLucideIcon('Check', __iconNode);\n\nexport default Check;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAuB,CAAA,CAAA;IAAC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmB;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAC;KAAC;CAAA,CAAA;AAahF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAQ,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0]}}, {"offset": {"line": 213, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 229, "column": 0}, "map": {"version": 3, "file": "circle.js", "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/lucide-react/src/icons/circle.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }]];\n\n/**\n * @component @name Circle\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/circle\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Circle = createLucideIcon('Circle', __iconNode);\n\nexport default Circle;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB,CAAA,CAAA,CAAA;IAAC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAE;YAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAC;CAAA,CAAA;AAazF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAS,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAU,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0]}}, {"offset": {"line": 254, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 270, "column": 0}, "map": {"version": 3, "file": "chevron-right.js", "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/lucide-react/src/icons/chevron-right.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [['path', { d: 'm9 18 6-6-6-6', key: 'mthhwq' }]];\n\n/**\n * @component @name ChevronRight\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtOSAxOCA2LTYtNi02IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/chevron-right\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ChevronRight = createLucideIcon('ChevronRight', __iconNode);\n\nexport default ChevronRight;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAuB,CAAA,CAAA;IAAC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAC;KAAC;CAAA,CAAA;AAa9E,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,YAAA,CAAe,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAiB,AAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0]}}, {"offset": {"line": 293, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 319, "column": 0}, "map": {"version": 3, "file": "upload.js", "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/lucide-react/src/icons/upload.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4', key: 'ih7n3h' }],\n  ['polyline', { points: '17 8 12 3 7 8', key: 't8dd8p' }],\n  ['line', { x1: '12', x2: '12', y1: '3', y2: '15', key: 'widbto' }],\n];\n\n/**\n * @component @name Upload\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjEgMTV2NGEyIDIgMCAwIDEtMiAySDVhMiAyIDAgMCAxLTItMnYtNCIgLz4KICA8cG9seWxpbmUgcG9pbnRzPSIxNyA4IDEyIDMgNyA4IiAvPgogIDxsaW5lIHgxPSIxMiIgeDI9IjEyIiB5MT0iMyIgeTI9IjE1IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/upload\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Upload = createLucideIcon('Upload', __iconNode);\n\nexport default Upload;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA6C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA,CAAA;IAC1E;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAA;QAAA,CAAA;YAAE,QAAQ,CAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA,CAAA;IACvD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAK,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CACnE,CAAA;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAS,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAU,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0]}}, {"offset": {"line": 359, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 375, "column": 0}, "map": {"version": 3, "file": "circle-check-big.js", "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/lucide-react/src/icons/circle-check-big.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M21.801 10A10 10 0 1 1 17 3.335', key: 'yps3ct' }],\n  ['path', { d: 'm9 11 3 3L22 4', key: '1pflzl' }],\n];\n\n/**\n * @component @name CircleCheckBig\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjEuODAxIDEwQTEwIDEwIDAgMSAxIDE3IDMuMzM1IiAvPgogIDxwYXRoIGQ9Im05IDExIDMgM0wyMiA0IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/circle-check-big\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CircleCheckBig = createLucideIcon('CircleCheckBig', __iconNode);\n\nexport default CircleCheckBig;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAmC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA,CAAA;IAChE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAkB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CACjD,CAAA;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,cAAA,CAAiB,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,AAAjB,CAAA,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAkB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0]}}, {"offset": {"line": 405, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 421, "column": 0}, "map": {"version": 3, "file": "circle-alert.js", "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/lucide-react/src/icons/circle-alert.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n  ['line', { x1: '12', x2: '12', y1: '8', y2: '12', key: '1pkeuh' }],\n  ['line', { x1: '12', x2: '12.01', y1: '16', y2: '16', key: '4dfq90' }],\n];\n\n/**\n * @component @name CircleAlert\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KICA8bGluZSB4MT0iMTIiIHgyPSIxMiIgeTE9IjgiIHkyPSIxMiIgLz4KICA8bGluZSB4MT0iMTIiIHgyPSIxMi4wMSIgeTE9IjE2IiB5Mj0iMTYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/circle-alert\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CircleAlert = createLucideIcon('CircleAlert', __iconNode);\n\nexport default CircleAlert;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA,CAAA;IAClC;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA,CAAA;IACzD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAK,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA,CAAA;IACjE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CACvE,CAAA;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,WAAA,CAAc,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAe,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0]}}, {"offset": {"line": 466, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 482, "column": 0}, "map": {"version": 3, "file": "file.js", "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/lucide-react/src/icons/file.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z', key: '1rqfz7' }],\n  ['path', { d: 'M14 2v4a2 2 0 0 0 2 2h4', key: 'tnqrlb' }],\n];\n\n/**\n * @component @name File\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTUgMkg2YTIgMiAwIDAgMC0yIDJ2MTZhMiAyIDAgMCAwIDIgMmgxMmEyIDIgMCAwIDAgMi0yVjdaIiAvPgogIDxwYXRoIGQ9Ik0xNCAydjRhMiAyIDAgMCAwIDIgMmg0IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/file\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst File = createLucideIcon('File', __iconNode);\n\nexport default File;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA8D,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA,CAAA;IAC3F;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA2B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAC1D,CAAA;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0]}}, {"offset": {"line": 512, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 528, "column": 0}, "map": {"version": 3, "file": "triangle-alert.js", "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/lucide-react/src/icons/triangle-alert.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'm21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3',\n      key: 'wmoenq',\n    },\n  ],\n  ['path', { d: 'M12 9v4', key: 'juzpu7' }],\n  ['path', { d: 'M12 17h.01', key: 'p32p05' }],\n];\n\n/**\n * @component @name TriangleAlert\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMjEuNzMgMTgtOC0xNGEyIDIgMCAwIDAtMy40OCAwbC04IDE0QTIgMiAwIDAgMCA0IDIxaDE2YTIgMiAwIDAgMCAxLjczLTMiIC8+CiAgPHBhdGggZD0iTTEyIDl2NCIgLz4KICA8cGF0aCBkPSJNMTIgMTdoLjAxIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/triangle-alert\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst TriangleAlert = createLucideIcon('TriangleAlert', __iconNode);\n\nexport default TriangleAlert;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA,CAAA;IAClC,CAAA;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA,CAAA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,CAAA,CAAA,CAAK,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACP,CAAA;KACF,CAAA;IACA;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA,CAAA;IACxC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAc,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAC7C,CAAA;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,aAAA,CAAgB,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAA,AAAjB,CAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAiB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0]}}, {"offset": {"line": 565, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 581, "column": 0}, "map": {"version": 3, "file": "info.js", "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/lucide-react/src/icons/info.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n  ['path', { d: 'M12 16v-4', key: '1dtifu' }],\n  ['path', { d: 'M12 8h.01', key: 'e9boi3' }],\n];\n\n/**\n * @component @name Info\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KICA8cGF0aCBkPSJNMTIgMTZ2LTQiIC8+CiAgPHBhdGggZD0iTTEyIDhoLjAxIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/info\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Info = createLucideIcon('Info', __iconNode);\n\nexport default Info;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA,CAAA;IAClC;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA,CAAA;IACzD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA,CAAA;IAC1C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAC5C,CAAA;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,AAAjB,CAAA,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0]}}, {"offset": {"line": 620, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 636, "column": 0}, "map": {"version": 3, "file": "shield.js", "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/lucide-react/src/icons/shield.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z',\n      key: 'oel41y',\n    },\n  ],\n];\n\n/**\n * @component @name Shield\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjAgMTNjMCA1LTMuNSA3LjUtNy42NiA4Ljk1YTEgMSAwIDAgMS0uNjctLjAxQzcuNSAyMC41IDQgMTggNCAxM1Y2YTEgMSAwIDAgMSAxLTFjMiAwIDQuNS0xLjIgNi4yNC0yLjcyYTEuMTcgMS4xNyAwIDAgMSAxLjUyIDBDMTQuNTEgMy44MSAxNyA1IDE5IDVhMSAxIDAgMCAxIDEgMXoiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/shield\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Shield = createLucideIcon('Shield', __iconNode);\n\nexport default Shield;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA,CAAA;IAClC,CAAA;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA,CAAA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,CAAA,CAAA,CAAK,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACP,CAAA;KACF;CACF,CAAA;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAS,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAU,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0]}}, {"offset": {"line": 659, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 675, "column": 0}, "map": {"version": 3, "file": "trash-2.js", "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/lucide-react/src/icons/trash-2.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M3 6h18', key: 'd0wm0j' }],\n  ['path', { d: 'M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6', key: '4alrt4' }],\n  ['path', { d: 'M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2', key: 'v07s0e' }],\n  ['line', { x1: '10', x2: '10', y1: '11', y2: '17', key: '1uufr5' }],\n  ['line', { x1: '14', x2: '14', y1: '11', y2: '17', key: 'xtxkd' }],\n];\n\n/**\n * @component @name Trash2\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMyA2aDE4IiAvPgogIDxwYXRoIGQ9Ik0xOSA2djE0YzAgMS0xIDItMiAySDdjLTEgMC0yLTEtMi0yVjYiIC8+CiAgPHBhdGggZD0iTTggNlY0YzAtMSAxLTIgMi0yaDRjMSAwIDIgMSAyIDJ2MiIgLz4KICA8bGluZSB4MT0iMTAiIHgyPSIxMCIgeTE9IjExIiB5Mj0iMTciIC8+CiAgPGxpbmUgeDE9IjE0IiB4Mj0iMTQiIHkxPSIxMSIgeTI9IjE3IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/trash-2\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Trash2 = createLucideIcon('Trash2', __iconNode);\n\nexport default Trash2;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA,CAAA;IACxC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAyC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA,CAAA;IACtE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAsC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA,CAAA;IACnE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA,CAAA;IAClE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAS;KAAA;CACnE,CAAA;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAS,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAU,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0]}}, {"offset": {"line": 732, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 748, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/%40radix-ui/react-label/src/label.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { Primitive } from '@radix-ui/react-primitive';\n\n/* -------------------------------------------------------------------------------------------------\n * Label\n * -----------------------------------------------------------------------------------------------*/\n\nconst NAME = 'Label';\n\ntype LabelElement = React.ComponentRef<typeof Primitive.label>;\ntype PrimitiveLabelProps = React.ComponentPropsWithoutRef<typeof Primitive.label>;\ninterface LabelProps extends PrimitiveLabelProps {}\n\nconst Label = React.forwardRef<LabelElement, LabelProps>((props, forwardedRef) => {\n  return (\n    <Primitive.label\n      {...props}\n      ref={forwardedRef}\n      onMouseDown={(event) => {\n        // only prevent text selection if clicking inside the label itself\n        const target = event.target as HTMLElement;\n        if (target.closest('button, input, select, textarea')) return;\n\n        props.onMouseDown?.(event);\n        // prevent text selection when double clicking label\n        if (!event.defaultPrevented && event.detail > 1) event.preventDefault();\n      }}\n    />\n  );\n});\n\nLabel.displayName = NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = Label;\n\nexport {\n  Label,\n  //\n  Root,\n};\nexport type { LabelProps };\n"], "names": [], "mappings": ";;;;;AAAA,YAAY,WAAW;AAenB;AAdJ,SAAS,iBAAiB;;;;;AAM1B,IAAM,OAAO;AAMb,IAAM,QAAc,sMAAA,UAAA,CAAqC,CAAC,OAAO,iBAAiB;IAChF,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,wKAAC,YAAA,CAAU,KAAA,EAAV;QACE,GAAG,KAAA;QACJ,KAAK;QACL,aAAa,CAAC,UAAU;YAEtB,MAAM,SAAS,MAAM,MAAA;YACrB,IAAI,OAAO,OAAA,CAAQ,iCAAiC,EAAG,CAAA;YAEvD,MAAM,WAAA,GAAc,KAAK;YAEzB,IAAI,CAAC,MAAM,gBAAA,IAAoB,MAAM,MAAA,GAAS,EAAG,CAAA,MAAM,cAAA,CAAe;QACxE;IAAA;AAGN,CAAC;AAED,MAAM,WAAA,GAAc;AAIpB,IAAM,OAAO", "ignoreList": [0]}}, {"offset": {"line": 777, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 783, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/%40radix-ui/number/src/number.ts"], "sourcesContent": ["function clamp(value: number, [min, max]: [number, number]): number {\n  return Math.min(max, Math.max(min, value));\n}\n\nexport { clamp };\n"], "names": [], "mappings": ";;;;AAAA,SAAS,MAAM,KAAA,EAAe,CAAC,KAAK,GAAG,CAAA,EAA6B;IAClE,OAAO,KAAK,GAAA,CAAI,KAAK,KAAK,GAAA,CAAI,KAAK,KAAK,CAAC;AAC3C", "ignoreList": [0]}}, {"offset": {"line": 792, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 798, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/%40radix-ui/react-select/src/select.tsx"], "sourcesContent": ["import * as React from 'react';\nimport * as ReactDOM from 'react-dom';\nimport { clamp } from '@radix-ui/number';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { createCollection } from '@radix-ui/react-collection';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useDirection } from '@radix-ui/react-direction';\nimport { DismissableLayer } from '@radix-ui/react-dismissable-layer';\nimport { useFocusGuards } from '@radix-ui/react-focus-guards';\nimport { FocusScope } from '@radix-ui/react-focus-scope';\nimport { useId } from '@radix-ui/react-id';\nimport * as PopperPrimitive from '@radix-ui/react-popper';\nimport { createPopperScope } from '@radix-ui/react-popper';\nimport { Portal as PortalPrimitive } from '@radix-ui/react-portal';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { createSlot } from '@radix-ui/react-slot';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\nimport { usePrevious } from '@radix-ui/react-use-previous';\nimport { VISUALLY_HIDDEN_STYLES } from '@radix-ui/react-visually-hidden';\nimport { hideOthers } from 'aria-hidden';\nimport { RemoveScroll } from 'react-remove-scroll';\n\nimport type { Scope } from '@radix-ui/react-context';\n\ntype Direction = 'ltr' | 'rtl';\n\nconst OPEN_KEYS = [' ', 'Enter', 'ArrowUp', 'ArrowDown'];\nconst SELECTION_KEYS = [' ', 'Enter'];\n\n/* -------------------------------------------------------------------------------------------------\n * Select\n * -----------------------------------------------------------------------------------------------*/\n\nconst SELECT_NAME = 'Select';\n\ntype ItemData = { value: string; disabled: boolean; textValue: string };\nconst [Collection, useCollection, createCollectionScope] = createCollection<\n  SelectItemElement,\n  ItemData\n>(SELECT_NAME);\n\ntype ScopedProps<P> = P & { __scopeSelect?: Scope };\nconst [createSelectContext, createSelectScope] = createContextScope(SELECT_NAME, [\n  createCollectionScope,\n  createPopperScope,\n]);\nconst usePopperScope = createPopperScope();\n\ntype SelectContextValue = {\n  trigger: SelectTriggerElement | null;\n  onTriggerChange(node: SelectTriggerElement | null): void;\n  valueNode: SelectValueElement | null;\n  onValueNodeChange(node: SelectValueElement): void;\n  valueNodeHasChildren: boolean;\n  onValueNodeHasChildrenChange(hasChildren: boolean): void;\n  contentId: string;\n  value: string | undefined;\n  onValueChange(value: string): void;\n  open: boolean;\n  required?: boolean;\n  onOpenChange(open: boolean): void;\n  dir: SelectProps['dir'];\n  triggerPointerDownPosRef: React.MutableRefObject<{ x: number; y: number } | null>;\n  disabled?: boolean;\n};\n\nconst [SelectProvider, useSelectContext] = createSelectContext<SelectContextValue>(SELECT_NAME);\n\ntype NativeOption = React.ReactElement<React.ComponentProps<'option'>>;\n\ntype SelectNativeOptionsContextValue = {\n  onNativeOptionAdd(option: NativeOption): void;\n  onNativeOptionRemove(option: NativeOption): void;\n};\nconst [SelectNativeOptionsProvider, useSelectNativeOptionsContext] =\n  createSelectContext<SelectNativeOptionsContextValue>(SELECT_NAME);\n\ninterface ControlledClearableSelectProps {\n  value: string | undefined;\n  defaultValue?: never;\n  onValueChange: (value: string | undefined) => void;\n}\n\ninterface ControlledUnclearableSelectProps {\n  value: string;\n  defaultValue?: never;\n  onValueChange: (value: string) => void;\n}\n\ninterface UncontrolledSelectProps {\n  value?: never;\n  defaultValue?: string;\n  onValueChange?: {\n    (value: string): void;\n    (value: string | undefined): void;\n  };\n}\n\ntype SelectControlProps =\n  | ControlledClearableSelectProps\n  | ControlledUnclearableSelectProps\n  | UncontrolledSelectProps;\n\ninterface SelectSharedProps {\n  children?: React.ReactNode;\n  open?: boolean;\n  defaultOpen?: boolean;\n  onOpenChange?(open: boolean): void;\n  dir?: Direction;\n  name?: string;\n  autoComplete?: string;\n  disabled?: boolean;\n  required?: boolean;\n  form?: string;\n}\n\n// TODO: Should improve typing somewhat, but this would be a breaking change.\n// Consider using in the next major version (along with some testing to be sure\n// it works as expected and doesn't cause problems)\ntype _FutureSelectProps = SelectSharedProps & SelectControlProps;\n\ntype SelectProps = SelectSharedProps & {\n  value?: string;\n  defaultValue?: string;\n  onValueChange?(value: string): void;\n};\n\nconst Select: React.FC<SelectProps> = (props: ScopedProps<SelectProps>) => {\n  const {\n    __scopeSelect,\n    children,\n    open: openProp,\n    defaultOpen,\n    onOpenChange,\n    value: valueProp,\n    defaultValue,\n    onValueChange,\n    dir,\n    name,\n    autoComplete,\n    disabled,\n    required,\n    form,\n  } = props;\n  const popperScope = usePopperScope(__scopeSelect);\n  const [trigger, setTrigger] = React.useState<SelectTriggerElement | null>(null);\n  const [valueNode, setValueNode] = React.useState<SelectValueElement | null>(null);\n  const [valueNodeHasChildren, setValueNodeHasChildren] = React.useState(false);\n  const direction = useDirection(dir);\n  const [open, setOpen] = useControllableState({\n    prop: openProp,\n    defaultProp: defaultOpen ?? false,\n    onChange: onOpenChange,\n    caller: SELECT_NAME,\n  });\n  const [value, setValue] = useControllableState({\n    prop: valueProp,\n    defaultProp: defaultValue,\n    onChange: onValueChange as any,\n    caller: SELECT_NAME,\n  });\n  const triggerPointerDownPosRef = React.useRef<{ x: number; y: number } | null>(null);\n\n  // We set this to true by default so that events bubble to forms without JS (SSR)\n  const isFormControl = trigger ? form || !!trigger.closest('form') : true;\n  const [nativeOptionsSet, setNativeOptionsSet] = React.useState(new Set<NativeOption>());\n\n  // The native `select` only associates the correct default value if the corresponding\n  // `option` is rendered as a child **at the same time** as itself.\n  // Because it might take a few renders for our items to gather the information to build\n  // the native `option`(s), we generate a key on the `select` to make sure React re-builds it\n  // each time the options change.\n  const nativeSelectKey = Array.from(nativeOptionsSet)\n    .map((option) => option.props.value)\n    .join(';');\n\n  return (\n    <PopperPrimitive.Root {...popperScope}>\n      <SelectProvider\n        required={required}\n        scope={__scopeSelect}\n        trigger={trigger}\n        onTriggerChange={setTrigger}\n        valueNode={valueNode}\n        onValueNodeChange={setValueNode}\n        valueNodeHasChildren={valueNodeHasChildren}\n        onValueNodeHasChildrenChange={setValueNodeHasChildren}\n        contentId={useId()}\n        value={value}\n        onValueChange={setValue}\n        open={open}\n        onOpenChange={setOpen}\n        dir={direction}\n        triggerPointerDownPosRef={triggerPointerDownPosRef}\n        disabled={disabled}\n      >\n        <Collection.Provider scope={__scopeSelect}>\n          <SelectNativeOptionsProvider\n            scope={props.__scopeSelect}\n            onNativeOptionAdd={React.useCallback((option) => {\n              setNativeOptionsSet((prev) => new Set(prev).add(option));\n            }, [])}\n            onNativeOptionRemove={React.useCallback((option) => {\n              setNativeOptionsSet((prev) => {\n                const optionsSet = new Set(prev);\n                optionsSet.delete(option);\n                return optionsSet;\n              });\n            }, [])}\n          >\n            {children}\n          </SelectNativeOptionsProvider>\n        </Collection.Provider>\n\n        {isFormControl ? (\n          <SelectBubbleInput\n            key={nativeSelectKey}\n            aria-hidden\n            required={required}\n            tabIndex={-1}\n            name={name}\n            autoComplete={autoComplete}\n            value={value}\n            // enable form autofill\n            onChange={(event) => setValue(event.target.value)}\n            disabled={disabled}\n            form={form}\n          >\n            {value === undefined ? <option value=\"\" /> : null}\n            {Array.from(nativeOptionsSet)}\n          </SelectBubbleInput>\n        ) : null}\n      </SelectProvider>\n    </PopperPrimitive.Root>\n  );\n};\n\nSelect.displayName = SELECT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst TRIGGER_NAME = 'SelectTrigger';\n\ntype SelectTriggerElement = React.ComponentRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = React.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface SelectTriggerProps extends PrimitiveButtonProps {}\n\nconst SelectTrigger = React.forwardRef<SelectTriggerElement, SelectTriggerProps>(\n  (props: ScopedProps<SelectTriggerProps>, forwardedRef) => {\n    const { __scopeSelect, disabled = false, ...triggerProps } = props;\n    const popperScope = usePopperScope(__scopeSelect);\n    const context = useSelectContext(TRIGGER_NAME, __scopeSelect);\n    const isDisabled = context.disabled || disabled;\n    const composedRefs = useComposedRefs(forwardedRef, context.onTriggerChange);\n    const getItems = useCollection(__scopeSelect);\n    const pointerTypeRef = React.useRef<React.PointerEvent['pointerType']>('touch');\n\n    const [searchRef, handleTypeaheadSearch, resetTypeahead] = useTypeaheadSearch((search) => {\n      const enabledItems = getItems().filter((item) => !item.disabled);\n      const currentItem = enabledItems.find((item) => item.value === context.value);\n      const nextItem = findNextItem(enabledItems, search, currentItem);\n      if (nextItem !== undefined) {\n        context.onValueChange(nextItem.value);\n      }\n    });\n\n    const handleOpen = (pointerEvent?: React.MouseEvent | React.PointerEvent) => {\n      if (!isDisabled) {\n        context.onOpenChange(true);\n        // reset typeahead when we open\n        resetTypeahead();\n      }\n\n      if (pointerEvent) {\n        context.triggerPointerDownPosRef.current = {\n          x: Math.round(pointerEvent.pageX),\n          y: Math.round(pointerEvent.pageY),\n        };\n      }\n    };\n\n    return (\n      <PopperPrimitive.Anchor asChild {...popperScope}>\n        <Primitive.button\n          type=\"button\"\n          role=\"combobox\"\n          aria-controls={context.contentId}\n          aria-expanded={context.open}\n          aria-required={context.required}\n          aria-autocomplete=\"none\"\n          dir={context.dir}\n          data-state={context.open ? 'open' : 'closed'}\n          disabled={isDisabled}\n          data-disabled={isDisabled ? '' : undefined}\n          data-placeholder={shouldShowPlaceholder(context.value) ? '' : undefined}\n          {...triggerProps}\n          ref={composedRefs}\n          // Enable compatibility with native label or custom `Label` \"click\" for Safari:\n          onClick={composeEventHandlers(triggerProps.onClick, (event) => {\n            // Whilst browsers generally have no issue focusing the trigger when clicking\n            // on a label, Safari seems to struggle with the fact that there's no `onClick`.\n            // We force `focus` in this case. Note: this doesn't create any other side-effect\n            // because we are preventing default in `onPointerDown` so effectively\n            // this only runs for a label \"click\"\n            event.currentTarget.focus();\n\n            // Open on click when using a touch or pen device\n            if (pointerTypeRef.current !== 'mouse') {\n              handleOpen(event);\n            }\n          })}\n          onPointerDown={composeEventHandlers(triggerProps.onPointerDown, (event) => {\n            pointerTypeRef.current = event.pointerType;\n\n            // prevent implicit pointer capture\n            // https://www.w3.org/TR/pointerevents3/#implicit-pointer-capture\n            const target = event.target as HTMLElement;\n            if (target.hasPointerCapture(event.pointerId)) {\n              target.releasePointerCapture(event.pointerId);\n            }\n\n            // only call handler if it's the left button (mousedown gets triggered by all mouse buttons)\n            // but not when the control key is pressed (avoiding MacOS right click); also not for touch\n            // devices because that would open the menu on scroll. (pen devices behave as touch on iOS).\n            if (event.button === 0 && event.ctrlKey === false && event.pointerType === 'mouse') {\n              handleOpen(event);\n              // prevent trigger from stealing focus from the active item after opening.\n              event.preventDefault();\n            }\n          })}\n          onKeyDown={composeEventHandlers(triggerProps.onKeyDown, (event) => {\n            const isTypingAhead = searchRef.current !== '';\n            const isModifierKey = event.ctrlKey || event.altKey || event.metaKey;\n            if (!isModifierKey && event.key.length === 1) handleTypeaheadSearch(event.key);\n            if (isTypingAhead && event.key === ' ') return;\n            if (OPEN_KEYS.includes(event.key)) {\n              handleOpen();\n              event.preventDefault();\n            }\n          })}\n        />\n      </PopperPrimitive.Anchor>\n    );\n  }\n);\n\nSelectTrigger.displayName = TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectValue\n * -----------------------------------------------------------------------------------------------*/\n\nconst VALUE_NAME = 'SelectValue';\n\ntype SelectValueElement = React.ComponentRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = React.ComponentPropsWithoutRef<typeof Primitive.span>;\ninterface SelectValueProps extends Omit<PrimitiveSpanProps, 'placeholder'> {\n  placeholder?: React.ReactNode;\n}\n\nconst SelectValue = React.forwardRef<SelectValueElement, SelectValueProps>(\n  (props: ScopedProps<SelectValueProps>, forwardedRef) => {\n    // We ignore `className` and `style` as this part shouldn't be styled.\n    const { __scopeSelect, className, style, children, placeholder = '', ...valueProps } = props;\n    const context = useSelectContext(VALUE_NAME, __scopeSelect);\n    const { onValueNodeHasChildrenChange } = context;\n    const hasChildren = children !== undefined;\n    const composedRefs = useComposedRefs(forwardedRef, context.onValueNodeChange);\n\n    useLayoutEffect(() => {\n      onValueNodeHasChildrenChange(hasChildren);\n    }, [onValueNodeHasChildrenChange, hasChildren]);\n\n    return (\n      <Primitive.span\n        {...valueProps}\n        ref={composedRefs}\n        // we don't want events from the portalled `SelectValue` children to bubble\n        // through the item they came from\n        style={{ pointerEvents: 'none' }}\n      >\n        {shouldShowPlaceholder(context.value) ? <>{placeholder}</> : children}\n      </Primitive.span>\n    );\n  }\n);\n\nSelectValue.displayName = VALUE_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectIcon\n * -----------------------------------------------------------------------------------------------*/\n\nconst ICON_NAME = 'SelectIcon';\n\ntype SelectIconElement = React.ComponentRef<typeof Primitive.span>;\ninterface SelectIconProps extends PrimitiveSpanProps {}\n\nconst SelectIcon = React.forwardRef<SelectIconElement, SelectIconProps>(\n  (props: ScopedProps<SelectIconProps>, forwardedRef) => {\n    const { __scopeSelect, children, ...iconProps } = props;\n    return (\n      <Primitive.span aria-hidden {...iconProps} ref={forwardedRef}>\n        {children || '▼'}\n      </Primitive.span>\n    );\n  }\n);\n\nSelectIcon.displayName = ICON_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectPortal\n * -----------------------------------------------------------------------------------------------*/\n\nconst PORTAL_NAME = 'SelectPortal';\n\ntype PortalProps = React.ComponentPropsWithoutRef<typeof PortalPrimitive>;\ninterface SelectPortalProps {\n  children?: React.ReactNode;\n  /**\n   * Specify a container element to portal the content into.\n   */\n  container?: PortalProps['container'];\n}\n\nconst SelectPortal: React.FC<SelectPortalProps> = (props: ScopedProps<SelectPortalProps>) => {\n  return <PortalPrimitive asChild {...props} />;\n};\n\nSelectPortal.displayName = PORTAL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'SelectContent';\n\ntype SelectContentElement = SelectContentImplElement;\ninterface SelectContentProps extends SelectContentImplProps {}\n\nconst SelectContent = React.forwardRef<SelectContentElement, SelectContentProps>(\n  (props: ScopedProps<SelectContentProps>, forwardedRef) => {\n    const context = useSelectContext(CONTENT_NAME, props.__scopeSelect);\n    const [fragment, setFragment] = React.useState<DocumentFragment>();\n\n    // setting the fragment in `useLayoutEffect` as `DocumentFragment` doesn't exist on the server\n    useLayoutEffect(() => {\n      setFragment(new DocumentFragment());\n    }, []);\n\n    if (!context.open) {\n      const frag = fragment as Element | undefined;\n      return frag\n        ? ReactDOM.createPortal(\n            <SelectContentProvider scope={props.__scopeSelect}>\n              <Collection.Slot scope={props.__scopeSelect}>\n                <div>{props.children}</div>\n              </Collection.Slot>\n            </SelectContentProvider>,\n            frag\n          )\n        : null;\n    }\n\n    return <SelectContentImpl {...props} ref={forwardedRef} />;\n  }\n);\n\nSelectContent.displayName = CONTENT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectContentImpl\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_MARGIN = 10;\n\ntype SelectContentContextValue = {\n  content?: SelectContentElement | null;\n  viewport?: SelectViewportElement | null;\n  onViewportChange?: (node: SelectViewportElement | null) => void;\n  itemRefCallback?: (node: SelectItemElement | null, value: string, disabled: boolean) => void;\n  selectedItem?: SelectItemElement | null;\n  onItemLeave?: () => void;\n  itemTextRefCallback?: (\n    node: SelectItemTextElement | null,\n    value: string,\n    disabled: boolean\n  ) => void;\n  focusSelectedItem?: () => void;\n  selectedItemText?: SelectItemTextElement | null;\n  position?: SelectContentProps['position'];\n  isPositioned?: boolean;\n  searchRef?: React.RefObject<string>;\n};\n\nconst [SelectContentProvider, useSelectContentContext] =\n  createSelectContext<SelectContentContextValue>(CONTENT_NAME);\n\nconst CONTENT_IMPL_NAME = 'SelectContentImpl';\n\ntype SelectContentImplElement = SelectPopperPositionElement | SelectItemAlignedPositionElement;\ntype DismissableLayerProps = React.ComponentPropsWithoutRef<typeof DismissableLayer>;\ntype FocusScopeProps = React.ComponentPropsWithoutRef<typeof FocusScope>;\n\ntype SelectPopperPrivateProps = { onPlaced?: PopperContentProps['onPlaced'] };\n\ninterface SelectContentImplProps\n  extends Omit<SelectPopperPositionProps, keyof SelectPopperPrivateProps>,\n    Omit<SelectItemAlignedPositionProps, keyof SelectPopperPrivateProps> {\n  /**\n   * Event handler called when auto-focusing on close.\n   * Can be prevented.\n   */\n  onCloseAutoFocus?: FocusScopeProps['onUnmountAutoFocus'];\n  /**\n   * Event handler called when the escape key is down.\n   * Can be prevented.\n   */\n  onEscapeKeyDown?: DismissableLayerProps['onEscapeKeyDown'];\n  /**\n   * Event handler called when the a `pointerdown` event happens outside of the `DismissableLayer`.\n   * Can be prevented.\n   */\n  onPointerDownOutside?: DismissableLayerProps['onPointerDownOutside'];\n\n  position?: 'item-aligned' | 'popper';\n}\n\nconst Slot = createSlot('SelectContent.RemoveScroll');\n\nconst SelectContentImpl = React.forwardRef<SelectContentImplElement, SelectContentImplProps>(\n  (props: ScopedProps<SelectContentImplProps>, forwardedRef) => {\n    const {\n      __scopeSelect,\n      position = 'item-aligned',\n      onCloseAutoFocus,\n      onEscapeKeyDown,\n      onPointerDownOutside,\n      //\n      // PopperContent props\n      side,\n      sideOffset,\n      align,\n      alignOffset,\n      arrowPadding,\n      collisionBoundary,\n      collisionPadding,\n      sticky,\n      hideWhenDetached,\n      avoidCollisions,\n      //\n      ...contentProps\n    } = props;\n    const context = useSelectContext(CONTENT_NAME, __scopeSelect);\n    const [content, setContent] = React.useState<SelectContentImplElement | null>(null);\n    const [viewport, setViewport] = React.useState<SelectViewportElement | null>(null);\n    const composedRefs = useComposedRefs(forwardedRef, (node) => setContent(node));\n    const [selectedItem, setSelectedItem] = React.useState<SelectItemElement | null>(null);\n    const [selectedItemText, setSelectedItemText] = React.useState<SelectItemTextElement | null>(\n      null\n    );\n    const getItems = useCollection(__scopeSelect);\n    const [isPositioned, setIsPositioned] = React.useState(false);\n    const firstValidItemFoundRef = React.useRef(false);\n\n    // aria-hide everything except the content (better supported equivalent to setting aria-modal)\n    React.useEffect(() => {\n      if (content) return hideOthers(content);\n    }, [content]);\n\n    // Make sure the whole tree has focus guards as our `Select` may be\n    // the last element in the DOM (because of the `Portal`)\n    useFocusGuards();\n\n    const focusFirst = React.useCallback(\n      (candidates: Array<HTMLElement | null>) => {\n        const [firstItem, ...restItems] = getItems().map((item) => item.ref.current);\n        const [lastItem] = restItems.slice(-1);\n\n        const PREVIOUSLY_FOCUSED_ELEMENT = document.activeElement;\n        for (const candidate of candidates) {\n          // if focus is already where we want to go, we don't want to keep going through the candidates\n          if (candidate === PREVIOUSLY_FOCUSED_ELEMENT) return;\n          candidate?.scrollIntoView({ block: 'nearest' });\n          // viewport might have padding so scroll to its edges when focusing first/last items.\n          if (candidate === firstItem && viewport) viewport.scrollTop = 0;\n          if (candidate === lastItem && viewport) viewport.scrollTop = viewport.scrollHeight;\n          candidate?.focus();\n          if (document.activeElement !== PREVIOUSLY_FOCUSED_ELEMENT) return;\n        }\n      },\n      [getItems, viewport]\n    );\n\n    const focusSelectedItem = React.useCallback(\n      () => focusFirst([selectedItem, content]),\n      [focusFirst, selectedItem, content]\n    );\n\n    // Since this is not dependent on layout, we want to ensure this runs at the same time as\n    // other effects across components. Hence why we don't call `focusSelectedItem` inside `position`.\n    React.useEffect(() => {\n      if (isPositioned) {\n        focusSelectedItem();\n      }\n    }, [isPositioned, focusSelectedItem]);\n\n    // prevent selecting items on `pointerup` in some cases after opening from `pointerdown`\n    // and close on `pointerup` outside.\n    const { onOpenChange, triggerPointerDownPosRef } = context;\n    React.useEffect(() => {\n      if (content) {\n        let pointerMoveDelta = { x: 0, y: 0 };\n\n        const handlePointerMove = (event: PointerEvent) => {\n          pointerMoveDelta = {\n            x: Math.abs(Math.round(event.pageX) - (triggerPointerDownPosRef.current?.x ?? 0)),\n            y: Math.abs(Math.round(event.pageY) - (triggerPointerDownPosRef.current?.y ?? 0)),\n          };\n        };\n        const handlePointerUp = (event: PointerEvent) => {\n          // If the pointer hasn't moved by a certain threshold then we prevent selecting item on `pointerup`.\n          if (pointerMoveDelta.x <= 10 && pointerMoveDelta.y <= 10) {\n            event.preventDefault();\n          } else {\n            // otherwise, if the event was outside the content, close.\n            if (!content.contains(event.target as HTMLElement)) {\n              onOpenChange(false);\n            }\n          }\n          document.removeEventListener('pointermove', handlePointerMove);\n          triggerPointerDownPosRef.current = null;\n        };\n\n        if (triggerPointerDownPosRef.current !== null) {\n          document.addEventListener('pointermove', handlePointerMove);\n          document.addEventListener('pointerup', handlePointerUp, { capture: true, once: true });\n        }\n\n        return () => {\n          document.removeEventListener('pointermove', handlePointerMove);\n          document.removeEventListener('pointerup', handlePointerUp, { capture: true });\n        };\n      }\n    }, [content, onOpenChange, triggerPointerDownPosRef]);\n\n    React.useEffect(() => {\n      const close = () => onOpenChange(false);\n      window.addEventListener('blur', close);\n      window.addEventListener('resize', close);\n      return () => {\n        window.removeEventListener('blur', close);\n        window.removeEventListener('resize', close);\n      };\n    }, [onOpenChange]);\n\n    const [searchRef, handleTypeaheadSearch] = useTypeaheadSearch((search) => {\n      const enabledItems = getItems().filter((item) => !item.disabled);\n      const currentItem = enabledItems.find((item) => item.ref.current === document.activeElement);\n      const nextItem = findNextItem(enabledItems, search, currentItem);\n      if (nextItem) {\n        /**\n         * Imperative focus during keydown is risky so we prevent React's batching updates\n         * to avoid potential bugs. See: https://github.com/facebook/react/issues/20332\n         */\n        setTimeout(() => (nextItem.ref.current as HTMLElement).focus());\n      }\n    });\n\n    const itemRefCallback = React.useCallback(\n      (node: SelectItemElement | null, value: string, disabled: boolean) => {\n        const isFirstValidItem = !firstValidItemFoundRef.current && !disabled;\n        const isSelectedItem = context.value !== undefined && context.value === value;\n        if (isSelectedItem || isFirstValidItem) {\n          setSelectedItem(node);\n          if (isFirstValidItem) firstValidItemFoundRef.current = true;\n        }\n      },\n      [context.value]\n    );\n    const handleItemLeave = React.useCallback(() => content?.focus(), [content]);\n    const itemTextRefCallback = React.useCallback(\n      (node: SelectItemTextElement | null, value: string, disabled: boolean) => {\n        const isFirstValidItem = !firstValidItemFoundRef.current && !disabled;\n        const isSelectedItem = context.value !== undefined && context.value === value;\n        if (isSelectedItem || isFirstValidItem) {\n          setSelectedItemText(node);\n        }\n      },\n      [context.value]\n    );\n\n    const SelectPosition = position === 'popper' ? SelectPopperPosition : SelectItemAlignedPosition;\n\n    // Silently ignore props that are not supported by `SelectItemAlignedPosition`\n    const popperContentProps =\n      SelectPosition === SelectPopperPosition\n        ? {\n            side,\n            sideOffset,\n            align,\n            alignOffset,\n            arrowPadding,\n            collisionBoundary,\n            collisionPadding,\n            sticky,\n            hideWhenDetached,\n            avoidCollisions,\n          }\n        : {};\n\n    return (\n      <SelectContentProvider\n        scope={__scopeSelect}\n        content={content}\n        viewport={viewport}\n        onViewportChange={setViewport}\n        itemRefCallback={itemRefCallback}\n        selectedItem={selectedItem}\n        onItemLeave={handleItemLeave}\n        itemTextRefCallback={itemTextRefCallback}\n        focusSelectedItem={focusSelectedItem}\n        selectedItemText={selectedItemText}\n        position={position}\n        isPositioned={isPositioned}\n        searchRef={searchRef}\n      >\n        <RemoveScroll as={Slot} allowPinchZoom>\n          <FocusScope\n            asChild\n            // we make sure we're not trapping once it's been closed\n            // (closed !== unmounted when animating out)\n            trapped={context.open}\n            onMountAutoFocus={(event) => {\n              // we prevent open autofocus because we manually focus the selected item\n              event.preventDefault();\n            }}\n            onUnmountAutoFocus={composeEventHandlers(onCloseAutoFocus, (event) => {\n              context.trigger?.focus({ preventScroll: true });\n              event.preventDefault();\n            })}\n          >\n            <DismissableLayer\n              asChild\n              disableOutsidePointerEvents\n              onEscapeKeyDown={onEscapeKeyDown}\n              onPointerDownOutside={onPointerDownOutside}\n              // When focus is trapped, a focusout event may still happen.\n              // We make sure we don't trigger our `onDismiss` in such case.\n              onFocusOutside={(event) => event.preventDefault()}\n              onDismiss={() => context.onOpenChange(false)}\n            >\n              <SelectPosition\n                role=\"listbox\"\n                id={context.contentId}\n                data-state={context.open ? 'open' : 'closed'}\n                dir={context.dir}\n                onContextMenu={(event) => event.preventDefault()}\n                {...contentProps}\n                {...popperContentProps}\n                onPlaced={() => setIsPositioned(true)}\n                ref={composedRefs}\n                style={{\n                  // flex layout so we can place the scroll buttons properly\n                  display: 'flex',\n                  flexDirection: 'column',\n                  // reset the outline by default as the content MAY get focused\n                  outline: 'none',\n                  ...contentProps.style,\n                }}\n                onKeyDown={composeEventHandlers(contentProps.onKeyDown, (event) => {\n                  const isModifierKey = event.ctrlKey || event.altKey || event.metaKey;\n\n                  // select should not be navigated using tab key so we prevent it\n                  if (event.key === 'Tab') event.preventDefault();\n\n                  if (!isModifierKey && event.key.length === 1) handleTypeaheadSearch(event.key);\n\n                  if (['ArrowUp', 'ArrowDown', 'Home', 'End'].includes(event.key)) {\n                    const items = getItems().filter((item) => !item.disabled);\n                    let candidateNodes = items.map((item) => item.ref.current!);\n\n                    if (['ArrowUp', 'End'].includes(event.key)) {\n                      candidateNodes = candidateNodes.slice().reverse();\n                    }\n                    if (['ArrowUp', 'ArrowDown'].includes(event.key)) {\n                      const currentElement = event.target as SelectItemElement;\n                      const currentIndex = candidateNodes.indexOf(currentElement);\n                      candidateNodes = candidateNodes.slice(currentIndex + 1);\n                    }\n\n                    /**\n                     * Imperative focus during keydown is risky so we prevent React's batching updates\n                     * to avoid potential bugs. See: https://github.com/facebook/react/issues/20332\n                     */\n                    setTimeout(() => focusFirst(candidateNodes));\n\n                    event.preventDefault();\n                  }\n                })}\n              />\n            </DismissableLayer>\n          </FocusScope>\n        </RemoveScroll>\n      </SelectContentProvider>\n    );\n  }\n);\n\nSelectContentImpl.displayName = CONTENT_IMPL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectItemAlignedPosition\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_ALIGNED_POSITION_NAME = 'SelectItemAlignedPosition';\n\ntype SelectItemAlignedPositionElement = React.ComponentRef<typeof Primitive.div>;\ninterface SelectItemAlignedPositionProps extends PrimitiveDivProps, SelectPopperPrivateProps {}\n\nconst SelectItemAlignedPosition = React.forwardRef<\n  SelectItemAlignedPositionElement,\n  SelectItemAlignedPositionProps\n>((props: ScopedProps<SelectItemAlignedPositionProps>, forwardedRef) => {\n  const { __scopeSelect, onPlaced, ...popperProps } = props;\n  const context = useSelectContext(CONTENT_NAME, __scopeSelect);\n  const contentContext = useSelectContentContext(CONTENT_NAME, __scopeSelect);\n  const [contentWrapper, setContentWrapper] = React.useState<HTMLDivElement | null>(null);\n  const [content, setContent] = React.useState<SelectItemAlignedPositionElement | null>(null);\n  const composedRefs = useComposedRefs(forwardedRef, (node) => setContent(node));\n  const getItems = useCollection(__scopeSelect);\n  const shouldExpandOnScrollRef = React.useRef(false);\n  const shouldRepositionRef = React.useRef(true);\n\n  const { viewport, selectedItem, selectedItemText, focusSelectedItem } = contentContext;\n  const position = React.useCallback(() => {\n    if (\n      context.trigger &&\n      context.valueNode &&\n      contentWrapper &&\n      content &&\n      viewport &&\n      selectedItem &&\n      selectedItemText\n    ) {\n      const triggerRect = context.trigger.getBoundingClientRect();\n\n      // -----------------------------------------------------------------------------------------\n      //  Horizontal positioning\n      // -----------------------------------------------------------------------------------------\n      const contentRect = content.getBoundingClientRect();\n      const valueNodeRect = context.valueNode.getBoundingClientRect();\n      const itemTextRect = selectedItemText.getBoundingClientRect();\n\n      if (context.dir !== 'rtl') {\n        const itemTextOffset = itemTextRect.left - contentRect.left;\n        const left = valueNodeRect.left - itemTextOffset;\n        const leftDelta = triggerRect.left - left;\n        const minContentWidth = triggerRect.width + leftDelta;\n        const contentWidth = Math.max(minContentWidth, contentRect.width);\n        const rightEdge = window.innerWidth - CONTENT_MARGIN;\n        const clampedLeft = clamp(left, [\n          CONTENT_MARGIN,\n          // Prevents the content from going off the starting edge of the\n          // viewport. It may still go off the ending edge, but this can be\n          // controlled by the user since they may want to manage overflow in a\n          // specific way.\n          // https://github.com/radix-ui/primitives/issues/2049\n          Math.max(CONTENT_MARGIN, rightEdge - contentWidth),\n        ]);\n\n        contentWrapper.style.minWidth = minContentWidth + 'px';\n        contentWrapper.style.left = clampedLeft + 'px';\n      } else {\n        const itemTextOffset = contentRect.right - itemTextRect.right;\n        const right = window.innerWidth - valueNodeRect.right - itemTextOffset;\n        const rightDelta = window.innerWidth - triggerRect.right - right;\n        const minContentWidth = triggerRect.width + rightDelta;\n        const contentWidth = Math.max(minContentWidth, contentRect.width);\n        const leftEdge = window.innerWidth - CONTENT_MARGIN;\n        const clampedRight = clamp(right, [\n          CONTENT_MARGIN,\n          Math.max(CONTENT_MARGIN, leftEdge - contentWidth),\n        ]);\n\n        contentWrapper.style.minWidth = minContentWidth + 'px';\n        contentWrapper.style.right = clampedRight + 'px';\n      }\n\n      // -----------------------------------------------------------------------------------------\n      // Vertical positioning\n      // -----------------------------------------------------------------------------------------\n      const items = getItems();\n      const availableHeight = window.innerHeight - CONTENT_MARGIN * 2;\n      const itemsHeight = viewport.scrollHeight;\n\n      const contentStyles = window.getComputedStyle(content);\n      const contentBorderTopWidth = parseInt(contentStyles.borderTopWidth, 10);\n      const contentPaddingTop = parseInt(contentStyles.paddingTop, 10);\n      const contentBorderBottomWidth = parseInt(contentStyles.borderBottomWidth, 10);\n      const contentPaddingBottom = parseInt(contentStyles.paddingBottom, 10);\n      const fullContentHeight = contentBorderTopWidth + contentPaddingTop + itemsHeight + contentPaddingBottom + contentBorderBottomWidth; // prettier-ignore\n      const minContentHeight = Math.min(selectedItem.offsetHeight * 5, fullContentHeight);\n\n      const viewportStyles = window.getComputedStyle(viewport);\n      const viewportPaddingTop = parseInt(viewportStyles.paddingTop, 10);\n      const viewportPaddingBottom = parseInt(viewportStyles.paddingBottom, 10);\n\n      const topEdgeToTriggerMiddle = triggerRect.top + triggerRect.height / 2 - CONTENT_MARGIN;\n      const triggerMiddleToBottomEdge = availableHeight - topEdgeToTriggerMiddle;\n\n      const selectedItemHalfHeight = selectedItem.offsetHeight / 2;\n      const itemOffsetMiddle = selectedItem.offsetTop + selectedItemHalfHeight;\n      const contentTopToItemMiddle = contentBorderTopWidth + contentPaddingTop + itemOffsetMiddle;\n      const itemMiddleToContentBottom = fullContentHeight - contentTopToItemMiddle;\n\n      const willAlignWithoutTopOverflow = contentTopToItemMiddle <= topEdgeToTriggerMiddle;\n\n      if (willAlignWithoutTopOverflow) {\n        const isLastItem =\n          items.length > 0 && selectedItem === items[items.length - 1]!.ref.current;\n        contentWrapper.style.bottom = 0 + 'px';\n        const viewportOffsetBottom =\n          content.clientHeight - viewport.offsetTop - viewport.offsetHeight;\n        const clampedTriggerMiddleToBottomEdge = Math.max(\n          triggerMiddleToBottomEdge,\n          selectedItemHalfHeight +\n            // viewport might have padding bottom, include it to avoid a scrollable viewport\n            (isLastItem ? viewportPaddingBottom : 0) +\n            viewportOffsetBottom +\n            contentBorderBottomWidth\n        );\n        const height = contentTopToItemMiddle + clampedTriggerMiddleToBottomEdge;\n        contentWrapper.style.height = height + 'px';\n      } else {\n        const isFirstItem = items.length > 0 && selectedItem === items[0]!.ref.current;\n        contentWrapper.style.top = 0 + 'px';\n        const clampedTopEdgeToTriggerMiddle = Math.max(\n          topEdgeToTriggerMiddle,\n          contentBorderTopWidth +\n            viewport.offsetTop +\n            // viewport might have padding top, include it to avoid a scrollable viewport\n            (isFirstItem ? viewportPaddingTop : 0) +\n            selectedItemHalfHeight\n        );\n        const height = clampedTopEdgeToTriggerMiddle + itemMiddleToContentBottom;\n        contentWrapper.style.height = height + 'px';\n        viewport.scrollTop = contentTopToItemMiddle - topEdgeToTriggerMiddle + viewport.offsetTop;\n      }\n\n      contentWrapper.style.margin = `${CONTENT_MARGIN}px 0`;\n      contentWrapper.style.minHeight = minContentHeight + 'px';\n      contentWrapper.style.maxHeight = availableHeight + 'px';\n      // -----------------------------------------------------------------------------------------\n\n      onPlaced?.();\n\n      // we don't want the initial scroll position adjustment to trigger \"expand on scroll\"\n      // so we explicitly turn it on only after they've registered.\n      requestAnimationFrame(() => (shouldExpandOnScrollRef.current = true));\n    }\n  }, [\n    getItems,\n    context.trigger,\n    context.valueNode,\n    contentWrapper,\n    content,\n    viewport,\n    selectedItem,\n    selectedItemText,\n    context.dir,\n    onPlaced,\n  ]);\n\n  useLayoutEffect(() => position(), [position]);\n\n  // copy z-index from content to wrapper\n  const [contentZIndex, setContentZIndex] = React.useState<string>();\n  useLayoutEffect(() => {\n    if (content) setContentZIndex(window.getComputedStyle(content).zIndex);\n  }, [content]);\n\n  // When the viewport becomes scrollable at the top, the scroll up button will mount.\n  // Because it is part of the normal flow, it will push down the viewport, thus throwing our\n  // trigger => selectedItem alignment off by the amount the viewport was pushed down.\n  // We wait for this to happen and then re-run the positining logic one more time to account for it.\n  const handleScrollButtonChange = React.useCallback(\n    (node: SelectScrollButtonImplElement | null) => {\n      if (node && shouldRepositionRef.current === true) {\n        position();\n        focusSelectedItem?.();\n        shouldRepositionRef.current = false;\n      }\n    },\n    [position, focusSelectedItem]\n  );\n\n  return (\n    <SelectViewportProvider\n      scope={__scopeSelect}\n      contentWrapper={contentWrapper}\n      shouldExpandOnScrollRef={shouldExpandOnScrollRef}\n      onScrollButtonChange={handleScrollButtonChange}\n    >\n      <div\n        ref={setContentWrapper}\n        style={{\n          display: 'flex',\n          flexDirection: 'column',\n          position: 'fixed',\n          zIndex: contentZIndex,\n        }}\n      >\n        <Primitive.div\n          {...popperProps}\n          ref={composedRefs}\n          style={{\n            // When we get the height of the content, it includes borders. If we were to set\n            // the height without having `boxSizing: 'border-box'` it would be too big.\n            boxSizing: 'border-box',\n            // We need to ensure the content doesn't get taller than the wrapper\n            maxHeight: '100%',\n            ...popperProps.style,\n          }}\n        />\n      </div>\n    </SelectViewportProvider>\n  );\n});\n\nSelectItemAlignedPosition.displayName = ITEM_ALIGNED_POSITION_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectPopperPosition\n * -----------------------------------------------------------------------------------------------*/\n\nconst POPPER_POSITION_NAME = 'SelectPopperPosition';\n\ntype SelectPopperPositionElement = React.ComponentRef<typeof PopperPrimitive.Content>;\ntype PopperContentProps = React.ComponentPropsWithoutRef<typeof PopperPrimitive.Content>;\ninterface SelectPopperPositionProps extends PopperContentProps, SelectPopperPrivateProps {}\n\nconst SelectPopperPosition = React.forwardRef<\n  SelectPopperPositionElement,\n  SelectPopperPositionProps\n>((props: ScopedProps<SelectPopperPositionProps>, forwardedRef) => {\n  const {\n    __scopeSelect,\n    align = 'start',\n    collisionPadding = CONTENT_MARGIN,\n    ...popperProps\n  } = props;\n  const popperScope = usePopperScope(__scopeSelect);\n\n  return (\n    <PopperPrimitive.Content\n      {...popperScope}\n      {...popperProps}\n      ref={forwardedRef}\n      align={align}\n      collisionPadding={collisionPadding}\n      style={{\n        // Ensure border-box for floating-ui calculations\n        boxSizing: 'border-box',\n        ...popperProps.style,\n        // re-namespace exposed content custom properties\n        ...{\n          '--radix-select-content-transform-origin': 'var(--radix-popper-transform-origin)',\n          '--radix-select-content-available-width': 'var(--radix-popper-available-width)',\n          '--radix-select-content-available-height': 'var(--radix-popper-available-height)',\n          '--radix-select-trigger-width': 'var(--radix-popper-anchor-width)',\n          '--radix-select-trigger-height': 'var(--radix-popper-anchor-height)',\n        },\n      }}\n    />\n  );\n});\n\nSelectPopperPosition.displayName = POPPER_POSITION_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectViewport\n * -----------------------------------------------------------------------------------------------*/\n\ntype SelectViewportContextValue = {\n  contentWrapper?: HTMLDivElement | null;\n  shouldExpandOnScrollRef?: React.RefObject<boolean>;\n  onScrollButtonChange?: (node: SelectScrollButtonImplElement | null) => void;\n};\n\nconst [SelectViewportProvider, useSelectViewportContext] =\n  createSelectContext<SelectViewportContextValue>(CONTENT_NAME, {});\n\nconst VIEWPORT_NAME = 'SelectViewport';\n\ntype SelectViewportElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface SelectViewportProps extends PrimitiveDivProps {\n  nonce?: string;\n}\n\nconst SelectViewport = React.forwardRef<SelectViewportElement, SelectViewportProps>(\n  (props: ScopedProps<SelectViewportProps>, forwardedRef) => {\n    const { __scopeSelect, nonce, ...viewportProps } = props;\n    const contentContext = useSelectContentContext(VIEWPORT_NAME, __scopeSelect);\n    const viewportContext = useSelectViewportContext(VIEWPORT_NAME, __scopeSelect);\n    const composedRefs = useComposedRefs(forwardedRef, contentContext.onViewportChange);\n    const prevScrollTopRef = React.useRef(0);\n    return (\n      <>\n        {/* Hide scrollbars cross-browser and enable momentum scroll for touch devices */}\n        <style\n          dangerouslySetInnerHTML={{\n            __html: `[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}`,\n          }}\n          nonce={nonce}\n        />\n        <Collection.Slot scope={__scopeSelect}>\n          <Primitive.div\n            data-radix-select-viewport=\"\"\n            role=\"presentation\"\n            {...viewportProps}\n            ref={composedRefs}\n            style={{\n              // we use position: 'relative' here on the `viewport` so that when we call\n              // `selectedItem.offsetTop` in calculations, the offset is relative to the viewport\n              // (independent of the scrollUpButton).\n              position: 'relative',\n              flex: 1,\n              // Viewport should only be scrollable in the vertical direction.\n              // This won't work in vertical writing modes, so we'll need to\n              // revisit this if/when that is supported\n              // https://developer.chrome.com/blog/vertical-form-controls\n              overflow: 'hidden auto',\n              ...viewportProps.style,\n            }}\n            onScroll={composeEventHandlers(viewportProps.onScroll, (event) => {\n              const viewport = event.currentTarget;\n              const { contentWrapper, shouldExpandOnScrollRef } = viewportContext;\n              if (shouldExpandOnScrollRef?.current && contentWrapper) {\n                const scrolledBy = Math.abs(prevScrollTopRef.current - viewport.scrollTop);\n                if (scrolledBy > 0) {\n                  const availableHeight = window.innerHeight - CONTENT_MARGIN * 2;\n                  const cssMinHeight = parseFloat(contentWrapper.style.minHeight);\n                  const cssHeight = parseFloat(contentWrapper.style.height);\n                  const prevHeight = Math.max(cssMinHeight, cssHeight);\n\n                  if (prevHeight < availableHeight) {\n                    const nextHeight = prevHeight + scrolledBy;\n                    const clampedNextHeight = Math.min(availableHeight, nextHeight);\n                    const heightDiff = nextHeight - clampedNextHeight;\n\n                    contentWrapper.style.height = clampedNextHeight + 'px';\n                    if (contentWrapper.style.bottom === '0px') {\n                      viewport.scrollTop = heightDiff > 0 ? heightDiff : 0;\n                      // ensure the content stays pinned to the bottom\n                      contentWrapper.style.justifyContent = 'flex-end';\n                    }\n                  }\n                }\n              }\n              prevScrollTopRef.current = viewport.scrollTop;\n            })}\n          />\n        </Collection.Slot>\n      </>\n    );\n  }\n);\n\nSelectViewport.displayName = VIEWPORT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectGroup\n * -----------------------------------------------------------------------------------------------*/\n\nconst GROUP_NAME = 'SelectGroup';\n\ntype SelectGroupContextValue = { id: string };\n\nconst [SelectGroupContextProvider, useSelectGroupContext] =\n  createSelectContext<SelectGroupContextValue>(GROUP_NAME);\n\ntype SelectGroupElement = React.ComponentRef<typeof Primitive.div>;\ninterface SelectGroupProps extends PrimitiveDivProps {}\n\nconst SelectGroup = React.forwardRef<SelectGroupElement, SelectGroupProps>(\n  (props: ScopedProps<SelectGroupProps>, forwardedRef) => {\n    const { __scopeSelect, ...groupProps } = props;\n    const groupId = useId();\n    return (\n      <SelectGroupContextProvider scope={__scopeSelect} id={groupId}>\n        <Primitive.div role=\"group\" aria-labelledby={groupId} {...groupProps} ref={forwardedRef} />\n      </SelectGroupContextProvider>\n    );\n  }\n);\n\nSelectGroup.displayName = GROUP_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectLabel\n * -----------------------------------------------------------------------------------------------*/\n\nconst LABEL_NAME = 'SelectLabel';\n\ntype SelectLabelElement = React.ComponentRef<typeof Primitive.div>;\ninterface SelectLabelProps extends PrimitiveDivProps {}\n\nconst SelectLabel = React.forwardRef<SelectLabelElement, SelectLabelProps>(\n  (props: ScopedProps<SelectLabelProps>, forwardedRef) => {\n    const { __scopeSelect, ...labelProps } = props;\n    const groupContext = useSelectGroupContext(LABEL_NAME, __scopeSelect);\n    return <Primitive.div id={groupContext.id} {...labelProps} ref={forwardedRef} />;\n  }\n);\n\nSelectLabel.displayName = LABEL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_NAME = 'SelectItem';\n\ntype SelectItemContextValue = {\n  value: string;\n  disabled: boolean;\n  textId: string;\n  isSelected: boolean;\n  onItemTextChange(node: SelectItemTextElement | null): void;\n};\n\nconst [SelectItemContextProvider, useSelectItemContext] =\n  createSelectContext<SelectItemContextValue>(ITEM_NAME);\n\ntype SelectItemElement = React.ComponentRef<typeof Primitive.div>;\ninterface SelectItemProps extends PrimitiveDivProps {\n  value: string;\n  disabled?: boolean;\n  textValue?: string;\n}\n\nconst SelectItem = React.forwardRef<SelectItemElement, SelectItemProps>(\n  (props: ScopedProps<SelectItemProps>, forwardedRef) => {\n    const {\n      __scopeSelect,\n      value,\n      disabled = false,\n      textValue: textValueProp,\n      ...itemProps\n    } = props;\n    const context = useSelectContext(ITEM_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(ITEM_NAME, __scopeSelect);\n    const isSelected = context.value === value;\n    const [textValue, setTextValue] = React.useState(textValueProp ?? '');\n    const [isFocused, setIsFocused] = React.useState(false);\n    const composedRefs = useComposedRefs(forwardedRef, (node) =>\n      contentContext.itemRefCallback?.(node, value, disabled)\n    );\n    const textId = useId();\n    const pointerTypeRef = React.useRef<React.PointerEvent['pointerType']>('touch');\n\n    const handleSelect = () => {\n      if (!disabled) {\n        context.onValueChange(value);\n        context.onOpenChange(false);\n      }\n    };\n\n    if (value === '') {\n      throw new Error(\n        'A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.'\n      );\n    }\n\n    return (\n      <SelectItemContextProvider\n        scope={__scopeSelect}\n        value={value}\n        disabled={disabled}\n        textId={textId}\n        isSelected={isSelected}\n        onItemTextChange={React.useCallback((node) => {\n          setTextValue((prevTextValue) => prevTextValue || (node?.textContent ?? '').trim());\n        }, [])}\n      >\n        <Collection.ItemSlot\n          scope={__scopeSelect}\n          value={value}\n          disabled={disabled}\n          textValue={textValue}\n        >\n          <Primitive.div\n            role=\"option\"\n            aria-labelledby={textId}\n            data-highlighted={isFocused ? '' : undefined}\n            // `isFocused` caveat fixes stuttering in VoiceOver\n            aria-selected={isSelected && isFocused}\n            data-state={isSelected ? 'checked' : 'unchecked'}\n            aria-disabled={disabled || undefined}\n            data-disabled={disabled ? '' : undefined}\n            tabIndex={disabled ? undefined : -1}\n            {...itemProps}\n            ref={composedRefs}\n            onFocus={composeEventHandlers(itemProps.onFocus, () => setIsFocused(true))}\n            onBlur={composeEventHandlers(itemProps.onBlur, () => setIsFocused(false))}\n            onClick={composeEventHandlers(itemProps.onClick, () => {\n              // Open on click when using a touch or pen device\n              if (pointerTypeRef.current !== 'mouse') handleSelect();\n            })}\n            onPointerUp={composeEventHandlers(itemProps.onPointerUp, () => {\n              // Using a mouse you should be able to do pointer down, move through\n              // the list, and release the pointer over the item to select it.\n              if (pointerTypeRef.current === 'mouse') handleSelect();\n            })}\n            onPointerDown={composeEventHandlers(itemProps.onPointerDown, (event) => {\n              pointerTypeRef.current = event.pointerType;\n            })}\n            onPointerMove={composeEventHandlers(itemProps.onPointerMove, (event) => {\n              // Remember pointer type when sliding over to this item from another one\n              pointerTypeRef.current = event.pointerType;\n              if (disabled) {\n                contentContext.onItemLeave?.();\n              } else if (pointerTypeRef.current === 'mouse') {\n                // even though safari doesn't support this option, it's acceptable\n                // as it only means it might scroll a few pixels when using the pointer.\n                event.currentTarget.focus({ preventScroll: true });\n              }\n            })}\n            onPointerLeave={composeEventHandlers(itemProps.onPointerLeave, (event) => {\n              if (event.currentTarget === document.activeElement) {\n                contentContext.onItemLeave?.();\n              }\n            })}\n            onKeyDown={composeEventHandlers(itemProps.onKeyDown, (event) => {\n              const isTypingAhead = contentContext.searchRef?.current !== '';\n              if (isTypingAhead && event.key === ' ') return;\n              if (SELECTION_KEYS.includes(event.key)) handleSelect();\n              // prevent page scroll if using the space key to select an item\n              if (event.key === ' ') event.preventDefault();\n            })}\n          />\n        </Collection.ItemSlot>\n      </SelectItemContextProvider>\n    );\n  }\n);\n\nSelectItem.displayName = ITEM_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectItemText\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_TEXT_NAME = 'SelectItemText';\n\ntype SelectItemTextElement = React.ComponentRef<typeof Primitive.span>;\ninterface SelectItemTextProps extends PrimitiveSpanProps {}\n\nconst SelectItemText = React.forwardRef<SelectItemTextElement, SelectItemTextProps>(\n  (props: ScopedProps<SelectItemTextProps>, forwardedRef) => {\n    // We ignore `className` and `style` as this part shouldn't be styled.\n    const { __scopeSelect, className, style, ...itemTextProps } = props;\n    const context = useSelectContext(ITEM_TEXT_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(ITEM_TEXT_NAME, __scopeSelect);\n    const itemContext = useSelectItemContext(ITEM_TEXT_NAME, __scopeSelect);\n    const nativeOptionsContext = useSelectNativeOptionsContext(ITEM_TEXT_NAME, __scopeSelect);\n    const [itemTextNode, setItemTextNode] = React.useState<SelectItemTextElement | null>(null);\n    const composedRefs = useComposedRefs(\n      forwardedRef,\n      (node) => setItemTextNode(node),\n      itemContext.onItemTextChange,\n      (node) => contentContext.itemTextRefCallback?.(node, itemContext.value, itemContext.disabled)\n    );\n\n    const textContent = itemTextNode?.textContent;\n    const nativeOption = React.useMemo(\n      () => (\n        <option key={itemContext.value} value={itemContext.value} disabled={itemContext.disabled}>\n          {textContent}\n        </option>\n      ),\n      [itemContext.disabled, itemContext.value, textContent]\n    );\n\n    const { onNativeOptionAdd, onNativeOptionRemove } = nativeOptionsContext;\n    useLayoutEffect(() => {\n      onNativeOptionAdd(nativeOption);\n      return () => onNativeOptionRemove(nativeOption);\n    }, [onNativeOptionAdd, onNativeOptionRemove, nativeOption]);\n\n    return (\n      <>\n        <Primitive.span id={itemContext.textId} {...itemTextProps} ref={composedRefs} />\n\n        {/* Portal the select item text into the trigger value node */}\n        {itemContext.isSelected && context.valueNode && !context.valueNodeHasChildren\n          ? ReactDOM.createPortal(itemTextProps.children, context.valueNode)\n          : null}\n      </>\n    );\n  }\n);\n\nSelectItemText.displayName = ITEM_TEXT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectItemIndicator\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_INDICATOR_NAME = 'SelectItemIndicator';\n\ntype SelectItemIndicatorElement = React.ComponentRef<typeof Primitive.span>;\ninterface SelectItemIndicatorProps extends PrimitiveSpanProps {}\n\nconst SelectItemIndicator = React.forwardRef<SelectItemIndicatorElement, SelectItemIndicatorProps>(\n  (props: ScopedProps<SelectItemIndicatorProps>, forwardedRef) => {\n    const { __scopeSelect, ...itemIndicatorProps } = props;\n    const itemContext = useSelectItemContext(ITEM_INDICATOR_NAME, __scopeSelect);\n    return itemContext.isSelected ? (\n      <Primitive.span aria-hidden {...itemIndicatorProps} ref={forwardedRef} />\n    ) : null;\n  }\n);\n\nSelectItemIndicator.displayName = ITEM_INDICATOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectScrollUpButton\n * -----------------------------------------------------------------------------------------------*/\n\nconst SCROLL_UP_BUTTON_NAME = 'SelectScrollUpButton';\n\ntype SelectScrollUpButtonElement = SelectScrollButtonImplElement;\ninterface SelectScrollUpButtonProps extends Omit<SelectScrollButtonImplProps, 'onAutoScroll'> {}\n\nconst SelectScrollUpButton = React.forwardRef<\n  SelectScrollUpButtonElement,\n  SelectScrollUpButtonProps\n>((props: ScopedProps<SelectScrollUpButtonProps>, forwardedRef) => {\n  const contentContext = useSelectContentContext(SCROLL_UP_BUTTON_NAME, props.__scopeSelect);\n  const viewportContext = useSelectViewportContext(SCROLL_UP_BUTTON_NAME, props.__scopeSelect);\n  const [canScrollUp, setCanScrollUp] = React.useState(false);\n  const composedRefs = useComposedRefs(forwardedRef, viewportContext.onScrollButtonChange);\n\n  useLayoutEffect(() => {\n    if (contentContext.viewport && contentContext.isPositioned) {\n      const viewport = contentContext.viewport;\n      function handleScroll() {\n        const canScrollUp = viewport.scrollTop > 0;\n        setCanScrollUp(canScrollUp);\n      }\n      handleScroll();\n      viewport.addEventListener('scroll', handleScroll);\n      return () => viewport.removeEventListener('scroll', handleScroll);\n    }\n  }, [contentContext.viewport, contentContext.isPositioned]);\n\n  return canScrollUp ? (\n    <SelectScrollButtonImpl\n      {...props}\n      ref={composedRefs}\n      onAutoScroll={() => {\n        const { viewport, selectedItem } = contentContext;\n        if (viewport && selectedItem) {\n          viewport.scrollTop = viewport.scrollTop - selectedItem.offsetHeight;\n        }\n      }}\n    />\n  ) : null;\n});\n\nSelectScrollUpButton.displayName = SCROLL_UP_BUTTON_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectScrollDownButton\n * -----------------------------------------------------------------------------------------------*/\n\nconst SCROLL_DOWN_BUTTON_NAME = 'SelectScrollDownButton';\n\ntype SelectScrollDownButtonElement = SelectScrollButtonImplElement;\ninterface SelectScrollDownButtonProps extends Omit<SelectScrollButtonImplProps, 'onAutoScroll'> {}\n\nconst SelectScrollDownButton = React.forwardRef<\n  SelectScrollDownButtonElement,\n  SelectScrollDownButtonProps\n>((props: ScopedProps<SelectScrollDownButtonProps>, forwardedRef) => {\n  const contentContext = useSelectContentContext(SCROLL_DOWN_BUTTON_NAME, props.__scopeSelect);\n  const viewportContext = useSelectViewportContext(SCROLL_DOWN_BUTTON_NAME, props.__scopeSelect);\n  const [canScrollDown, setCanScrollDown] = React.useState(false);\n  const composedRefs = useComposedRefs(forwardedRef, viewportContext.onScrollButtonChange);\n\n  useLayoutEffect(() => {\n    if (contentContext.viewport && contentContext.isPositioned) {\n      const viewport = contentContext.viewport;\n      function handleScroll() {\n        const maxScroll = viewport.scrollHeight - viewport.clientHeight;\n        // we use Math.ceil here because if the UI is zoomed-in\n        // `scrollTop` is not always reported as an integer\n        const canScrollDown = Math.ceil(viewport.scrollTop) < maxScroll;\n        setCanScrollDown(canScrollDown);\n      }\n      handleScroll();\n      viewport.addEventListener('scroll', handleScroll);\n      return () => viewport.removeEventListener('scroll', handleScroll);\n    }\n  }, [contentContext.viewport, contentContext.isPositioned]);\n\n  return canScrollDown ? (\n    <SelectScrollButtonImpl\n      {...props}\n      ref={composedRefs}\n      onAutoScroll={() => {\n        const { viewport, selectedItem } = contentContext;\n        if (viewport && selectedItem) {\n          viewport.scrollTop = viewport.scrollTop + selectedItem.offsetHeight;\n        }\n      }}\n    />\n  ) : null;\n});\n\nSelectScrollDownButton.displayName = SCROLL_DOWN_BUTTON_NAME;\n\ntype SelectScrollButtonImplElement = React.ComponentRef<typeof Primitive.div>;\ninterface SelectScrollButtonImplProps extends PrimitiveDivProps {\n  onAutoScroll(): void;\n}\n\nconst SelectScrollButtonImpl = React.forwardRef<\n  SelectScrollButtonImplElement,\n  SelectScrollButtonImplProps\n>((props: ScopedProps<SelectScrollButtonImplProps>, forwardedRef) => {\n  const { __scopeSelect, onAutoScroll, ...scrollIndicatorProps } = props;\n  const contentContext = useSelectContentContext('SelectScrollButton', __scopeSelect);\n  const autoScrollTimerRef = React.useRef<number | null>(null);\n  const getItems = useCollection(__scopeSelect);\n\n  const clearAutoScrollTimer = React.useCallback(() => {\n    if (autoScrollTimerRef.current !== null) {\n      window.clearInterval(autoScrollTimerRef.current);\n      autoScrollTimerRef.current = null;\n    }\n  }, []);\n\n  React.useEffect(() => {\n    return () => clearAutoScrollTimer();\n  }, [clearAutoScrollTimer]);\n\n  // When the viewport becomes scrollable on either side, the relevant scroll button will mount.\n  // Because it is part of the normal flow, it will push down (top button) or shrink (bottom button)\n  // the viewport, potentially causing the active item to now be partially out of view.\n  // We re-run the `scrollIntoView` logic to make sure it stays within the viewport.\n  useLayoutEffect(() => {\n    const activeItem = getItems().find((item) => item.ref.current === document.activeElement);\n    activeItem?.ref.current?.scrollIntoView({ block: 'nearest' });\n  }, [getItems]);\n\n  return (\n    <Primitive.div\n      aria-hidden\n      {...scrollIndicatorProps}\n      ref={forwardedRef}\n      style={{ flexShrink: 0, ...scrollIndicatorProps.style }}\n      onPointerDown={composeEventHandlers(scrollIndicatorProps.onPointerDown, () => {\n        if (autoScrollTimerRef.current === null) {\n          autoScrollTimerRef.current = window.setInterval(onAutoScroll, 50);\n        }\n      })}\n      onPointerMove={composeEventHandlers(scrollIndicatorProps.onPointerMove, () => {\n        contentContext.onItemLeave?.();\n        if (autoScrollTimerRef.current === null) {\n          autoScrollTimerRef.current = window.setInterval(onAutoScroll, 50);\n        }\n      })}\n      onPointerLeave={composeEventHandlers(scrollIndicatorProps.onPointerLeave, () => {\n        clearAutoScrollTimer();\n      })}\n    />\n  );\n});\n\n/* -------------------------------------------------------------------------------------------------\n * SelectSeparator\n * -----------------------------------------------------------------------------------------------*/\n\nconst SEPARATOR_NAME = 'SelectSeparator';\n\ntype SelectSeparatorElement = React.ComponentRef<typeof Primitive.div>;\ninterface SelectSeparatorProps extends PrimitiveDivProps {}\n\nconst SelectSeparator = React.forwardRef<SelectSeparatorElement, SelectSeparatorProps>(\n  (props: ScopedProps<SelectSeparatorProps>, forwardedRef) => {\n    const { __scopeSelect, ...separatorProps } = props;\n    return <Primitive.div aria-hidden {...separatorProps} ref={forwardedRef} />;\n  }\n);\n\nSelectSeparator.displayName = SEPARATOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectArrow\n * -----------------------------------------------------------------------------------------------*/\n\nconst ARROW_NAME = 'SelectArrow';\n\ntype SelectArrowElement = React.ComponentRef<typeof PopperPrimitive.Arrow>;\ntype PopperArrowProps = React.ComponentPropsWithoutRef<typeof PopperPrimitive.Arrow>;\ninterface SelectArrowProps extends PopperArrowProps {}\n\nconst SelectArrow = React.forwardRef<SelectArrowElement, SelectArrowProps>(\n  (props: ScopedProps<SelectArrowProps>, forwardedRef) => {\n    const { __scopeSelect, ...arrowProps } = props;\n    const popperScope = usePopperScope(__scopeSelect);\n    const context = useSelectContext(ARROW_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(ARROW_NAME, __scopeSelect);\n    return context.open && contentContext.position === 'popper' ? (\n      <PopperPrimitive.Arrow {...popperScope} {...arrowProps} ref={forwardedRef} />\n    ) : null;\n  }\n);\n\nSelectArrow.displayName = ARROW_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SelectBubbleInput\n * -----------------------------------------------------------------------------------------------*/\n\nconst BUBBLE_INPUT_NAME = 'SelectBubbleInput';\n\ntype InputProps = React.ComponentPropsWithoutRef<typeof Primitive.select>;\ninterface SwitchBubbleInputProps extends InputProps {}\n\nconst SelectBubbleInput = React.forwardRef<HTMLSelectElement, SwitchBubbleInputProps>(\n  ({ __scopeSelect, value, ...props }: ScopedProps<SwitchBubbleInputProps>, forwardedRef) => {\n    const ref = React.useRef<HTMLSelectElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref);\n    const prevValue = usePrevious(value);\n\n    // Bubble value change to parents (e.g form change event)\n    React.useEffect(() => {\n      const select = ref.current;\n      if (!select) return;\n\n      const selectProto = window.HTMLSelectElement.prototype;\n      const descriptor = Object.getOwnPropertyDescriptor(\n        selectProto,\n        'value'\n      ) as PropertyDescriptor;\n      const setValue = descriptor.set;\n      if (prevValue !== value && setValue) {\n        const event = new Event('change', { bubbles: true });\n        setValue.call(select, value);\n        select.dispatchEvent(event);\n      }\n    }, [prevValue, value]);\n\n    /**\n     * We purposefully use a `select` here to support form autofill as much as\n     * possible.\n     *\n     * We purposefully do not add the `value` attribute here to allow the value\n     * to be set programmatically and bubble to any parent form `onChange`\n     * event. Adding the `value` will cause React to consider the programmatic\n     * dispatch a duplicate and it will get swallowed.\n     *\n     * We use visually hidden styles rather than `display: \"none\"` because\n     * Safari autofill won't work otherwise.\n     */\n    return (\n      <Primitive.select\n        {...props}\n        style={{ ...VISUALLY_HIDDEN_STYLES, ...props.style }}\n        ref={composedRefs}\n        defaultValue={value}\n      />\n    );\n  }\n);\n\nSelectBubbleInput.displayName = BUBBLE_INPUT_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction shouldShowPlaceholder(value?: string) {\n  return value === '' || value === undefined;\n}\n\nfunction useTypeaheadSearch(onSearchChange: (search: string) => void) {\n  const handleSearchChange = useCallbackRef(onSearchChange);\n  const searchRef = React.useRef('');\n  const timerRef = React.useRef(0);\n\n  const handleTypeaheadSearch = React.useCallback(\n    (key: string) => {\n      const search = searchRef.current + key;\n      handleSearchChange(search);\n\n      (function updateSearch(value: string) {\n        searchRef.current = value;\n        window.clearTimeout(timerRef.current);\n        // Reset `searchRef` 1 second after it was last updated\n        if (value !== '') timerRef.current = window.setTimeout(() => updateSearch(''), 1000);\n      })(search);\n    },\n    [handleSearchChange]\n  );\n\n  const resetTypeahead = React.useCallback(() => {\n    searchRef.current = '';\n    window.clearTimeout(timerRef.current);\n  }, []);\n\n  React.useEffect(() => {\n    return () => window.clearTimeout(timerRef.current);\n  }, []);\n\n  return [searchRef, handleTypeaheadSearch, resetTypeahead] as const;\n}\n\n/**\n * This is the \"meat\" of the typeahead matching logic. It takes in a list of items,\n * the search and the current item, and returns the next item (or `undefined`).\n *\n * We normalize the search because if a user has repeatedly pressed a character,\n * we want the exact same behavior as if we only had that one character\n * (ie. cycle through items starting with that character)\n *\n * We also reorder the items by wrapping the array around the current item.\n * This is so we always look forward from the current item, and picking the first\n * item will always be the correct one.\n *\n * Finally, if the normalized search is exactly one character, we exclude the\n * current item from the values because otherwise it would be the first to match always\n * and focus would never move. This is as opposed to the regular case, where we\n * don't want focus to move if the current item still matches.\n */\nfunction findNextItem<T extends { textValue: string }>(\n  items: T[],\n  search: string,\n  currentItem?: T\n) {\n  const isRepeated = search.length > 1 && Array.from(search).every((char) => char === search[0]);\n  const normalizedSearch = isRepeated ? search[0]! : search;\n  const currentItemIndex = currentItem ? items.indexOf(currentItem) : -1;\n  let wrappedItems = wrapArray(items, Math.max(currentItemIndex, 0));\n  const excludeCurrentItem = normalizedSearch.length === 1;\n  if (excludeCurrentItem) wrappedItems = wrappedItems.filter((v) => v !== currentItem);\n  const nextItem = wrappedItems.find((item) =>\n    item.textValue.toLowerCase().startsWith(normalizedSearch.toLowerCase())\n  );\n  return nextItem !== currentItem ? nextItem : undefined;\n}\n\n/**\n * Wraps an array around itself at a given start index\n * Example: `wrapArray(['a', 'b', 'c', 'd'], 2) === ['c', 'd', 'a', 'b']`\n */\nfunction wrapArray<T>(array: T[], startIndex: number) {\n  return array.map<T>((_, index) => array[(startIndex + index) % array.length]!);\n}\n\nconst Root = Select;\nconst Trigger = SelectTrigger;\nconst Value = SelectValue;\nconst Icon = SelectIcon;\nconst Portal = SelectPortal;\nconst Content = SelectContent;\nconst Viewport = SelectViewport;\nconst Group = SelectGroup;\nconst Label = SelectLabel;\nconst Item = SelectItem;\nconst ItemText = SelectItemText;\nconst ItemIndicator = SelectItemIndicator;\nconst ScrollUpButton = SelectScrollUpButton;\nconst ScrollDownButton = SelectScrollDownButton;\nconst Separator = SelectSeparator;\nconst Arrow = SelectArrow;\n\nexport {\n  createSelectScope,\n  //\n  Select,\n  SelectTrigger,\n  SelectValue,\n  SelectIcon,\n  SelectPortal,\n  SelectContent,\n  SelectViewport,\n  SelectGroup,\n  SelectLabel,\n  SelectItem,\n  SelectItemText,\n  SelectItemIndicator,\n  SelectScrollUpButton,\n  SelectScrollDownButton,\n  SelectSeparator,\n  SelectArrow,\n  //\n  Root,\n  Trigger,\n  Value,\n  Icon,\n  Portal,\n  Content,\n  Viewport,\n  Group,\n  Label,\n  Item,\n  ItemText,\n  ItemIndicator,\n  ScrollUpButton,\n  ScrollDownButton,\n  Separator,\n  Arrow,\n};\nexport type {\n  SelectProps,\n  SelectTriggerProps,\n  SelectValueProps,\n  SelectIconProps,\n  SelectPortalProps,\n  SelectContentProps,\n  SelectViewportProps,\n  SelectGroupProps,\n  SelectLabelProps,\n  SelectItemProps,\n  SelectItemTextProps,\n  SelectItemIndicatorProps,\n  SelectScrollUpButtonProps,\n  SelectScrollDownButtonProps,\n  SelectSeparatorProps,\n  SelectArrowProps,\n};\n"], "names": ["handleScroll", "canScrollUp", "canScrollDown", "Root", "Content", "Arrow"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,YAAY,WAAW;AACvB,YAAY,cAAc;AAuMhB,SA0LsC,UA1LtC,KAkBA,YAlBA;AApMV,SAAS,wBAAwB;AAEjC,SAAS,0BAA0B;AAOnC,SAAS,yBAAyB;AANlC,SAAS,oBAAoB;AAW7B,SAAS,4BAA4B;AAPrC,SAAS,aAAa;AANtB,SAAS,uBAAuB;AAUhC,SAAS,iBAAiB;AAZ1B,SAAS,4BAA4B;AAgBrC,SAAS,uBAAuB;AALhC,SAAS,UAAU,uBAAuB;AAE1C,SAAS,kBAAkB;AAM3B,SAAS,kBAAkB;AAb3B,SAAS,sBAAsB;AAc/B,SAAS,oBAAoB;AAb7B,SAAS,kBAAkB;AAF3B,SAAS,wBAAwB;AANjC,SAAS,aAAa;AAkBtB,SAAS,mBAAmB;AAC5B,SAAS,8BAA8B;AAJvC,SAAS,sBAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;AAY/B,IAAM,YAAY;IAAC;IAAK;IAAS;IAAW,WAAW;CAAA;AACvD,IAAM,iBAAiB;IAAC;IAAK,OAAO;CAAA;AAMpC,IAAM,cAAc;AAGpB,IAAM,CAAC,YAAY,eAAe,qBAAqB,CAAA,8KAAI,mBAAA,EAGzD,WAAW;AAGb,IAAM,CAAC,qBAAqB,iBAAiB,CAAA,0KAAI,sBAAA,EAAmB,aAAa;IAC/E;uKACA,oBAAA;CACD;AACD,IAAM,wLAAiB,oBAAA,CAAkB;AAoBzC,IAAM,CAAC,gBAAgB,gBAAgB,CAAA,GAAI,oBAAwC,WAAW;AAQ9F,IAAM,CAAC,6BAA6B,6BAA6B,CAAA,GAC/D,oBAAqD,WAAW;AAoDlE,IAAM,SAAgC,CAAC,UAAoC;IACzE,MAAM,EACJ,aAAA,EACA,QAAA,EACA,MAAM,QAAA,EACN,WAAA,EACA,YAAA,EACA,OAAO,SAAA,EACP,YAAA,EACA,aAAA,EACA,GAAA,EACA,IAAA,EACA,YAAA,EACA,QAAA,EACA,QAAA,EACA,IAAA,EACF,GAAI;IACJ,MAAM,cAAc,eAAe,aAAa;IAChD,MAAM,CAAC,SAAS,UAAU,CAAA,GAAU,sMAAA,QAAA,CAAsC,IAAI;IAC9E,MAAM,CAAC,WAAW,YAAY,CAAA,GAAU,sMAAA,QAAA,CAAoC,IAAI;IAChF,MAAM,CAAC,sBAAsB,uBAAuB,CAAA,GAAU,sMAAA,QAAA,CAAS,KAAK;IAC5E,MAAM,sLAAY,eAAA,EAAa,GAAG;IAClC,MAAM,CAAC,MAAM,OAAO,CAAA,GAAI,oNAAA,EAAqB;QAC3C,MAAM;QACN,aAAa,eAAe;QAC5B,UAAU;QACV,QAAQ;IACV,CAAC;IACD,MAAM,CAAC,OAAO,QAAQ,CAAA,gMAAI,uBAAA,EAAqB;QAC7C,MAAM;QACN,aAAa;QACb,UAAU;QACV,QAAQ;IACV,CAAC;IACD,MAAM,2BAAiC,sMAAA,MAAA,CAAwC,IAAI;IAGnF,MAAM,gBAAgB,UAAU,QAAQ,CAAC,CAAC,QAAQ,OAAA,CAAQ,MAAM,IAAI;IACpE,MAAM,CAAC,kBAAkB,mBAAmB,CAAA,GAAU,sMAAA,QAAA,CAAS,aAAA,GAAA,IAAI,IAAkB,CAAC;IAOtF,MAAM,kBAAkB,MAAM,IAAA,CAAK,gBAAgB,EAChD,GAAA,CAAI,CAAC,SAAW,OAAO,KAAA,CAAM,KAAK,EAClC,IAAA,CAAK,GAAG;IAEX,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAiB,mKAAA,IAAA,EAAhB;QAAsB,GAAG,WAAA;QACxB,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,OAAA,EAAC,gBAAA;YACC;YACA,OAAO;YACP;YACA,iBAAiB;YACjB;YACA,mBAAmB;YACnB;YACA,8BAA8B;YAC9B,8KAAW,QAAA,CAAM;YACjB;YACA,eAAe;YACf;YACA,cAAc;YACd,KAAK;YACL;YACA;YAEA,UAAA;gBAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,WAAW,QAAA,EAAX;oBAAoB,OAAO;oBAC1B,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,6BAAA;wBACC,OAAO,MAAM,aAAA;wBACb,mBAAyB,sMAAA,WAAA,CAAY,CAAC,WAAW;4BAC/C,oBAAoB,CAAC,OAAS,IAAI,IAAI,IAAI,EAAE,GAAA,CAAI,MAAM,CAAC;wBACzD,GAAG,CAAC,CAAC;wBACL,sBAA4B,sMAAA,WAAA,CAAY,CAAC,WAAW;4BAClD,oBAAoB,CAAC,SAAS;gCAC5B,MAAM,aAAa,IAAI,IAAI,IAAI;gCAC/B,WAAW,MAAA,CAAO,MAAM;gCACxB,OAAO;4BACT,CAAC;wBACH,GAAG,CAAC,CAAC;wBAEJ;oBAAA;gBACH,CACF;gBAEC,gBACC,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,OAAA,EAAC,mBAAA;oBAEC,eAAW;oBACX;oBACA,UAAU,CAAA;oBACV;oBACA;oBACA;oBAEA,UAAU,CAAC,QAAU,SAAS,MAAM,MAAA,CAAO,KAAK;oBAChD;oBACA;oBAEC,UAAA;wBAAA,UAAU,KAAA,IAAY,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,UAAA;4BAAO,OAAM;wBAAA,CAAG,IAAK;wBAC5C,MAAM,IAAA,CAAK,gBAAgB;qBAAA;gBAAA,GAbvB,mBAeL;aAAA;QAAA;IACN,CACF;AAEJ;AAEA,OAAO,WAAA,GAAc;AAMrB,IAAM,eAAe;AAMrB,IAAM,gBAAsB,sMAAA,UAAA,CAC1B,CAAC,OAAwC,iBAAiB;IACxD,MAAM,EAAE,aAAA,EAAe,WAAW,KAAA,EAAO,GAAG,aAAa,CAAA,GAAI;IAC7D,MAAM,cAAc,eAAe,aAAa;IAChD,MAAM,UAAU,iBAAiB,cAAc,aAAa;IAC5D,MAAM,aAAa,QAAQ,QAAA,IAAY;IACvC,MAAM,+LAAe,kBAAA,EAAgB,cAAc,QAAQ,eAAe;IAC1E,MAAM,WAAW,cAAc,aAAa;IAC5C,MAAM,iBAAuB,sMAAA,MAAA,CAA0C,OAAO;IAE9E,MAAM,CAAC,WAAW,uBAAuB,cAAc,CAAA,GAAI,mBAAmB,CAAC,WAAW;QACxF,MAAM,eAAe,SAAS,EAAE,MAAA,CAAO,CAAC,OAAS,CAAC,KAAK,QAAQ;QAC/D,MAAM,cAAc,aAAa,IAAA,CAAK,CAAC,OAAS,KAAK,KAAA,KAAU,QAAQ,KAAK;QAC5E,MAAM,WAAW,aAAa,cAAc,QAAQ,WAAW;QAC/D,IAAI,aAAa,KAAA,GAAW;YAC1B,QAAQ,aAAA,CAAc,SAAS,KAAK;QACtC;IACF,CAAC;IAED,MAAM,aAAa,CAAC,iBAAyD;QAC3E,IAAI,CAAC,YAAY;YACf,QAAQ,YAAA,CAAa,IAAI;YAEzB,eAAe;QACjB;QAEA,IAAI,cAAc;YAChB,QAAQ,wBAAA,CAAyB,OAAA,GAAU;gBACzC,GAAG,KAAK,KAAA,CAAM,aAAa,KAAK;gBAChC,GAAG,KAAK,KAAA,CAAM,aAAa,KAAK;YAClC;QACF;IACF;IAEA,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAiB,mKAAA,MAAA,EAAhB;QAAuB,SAAO;QAAE,GAAG,WAAA;QAClC,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,wKAAC,YAAA,CAAU,MAAA,EAAV;YACC,MAAK;YACL,MAAK;YACL,iBAAe,QAAQ,SAAA;YACvB,iBAAe,QAAQ,IAAA;YACvB,iBAAe,QAAQ,QAAA;YACvB,qBAAkB;YAClB,KAAK,QAAQ,GAAA;YACb,cAAY,QAAQ,IAAA,GAAO,SAAS;YACpC,UAAU;YACV,iBAAe,aAAa,KAAK,KAAA;YACjC,oBAAkB,sBAAsB,QAAQ,KAAK,IAAI,KAAK,KAAA;YAC7D,GAAG,YAAA;YACJ,KAAK;YAEL,0KAAS,uBAAA,EAAqB,aAAa,OAAA,EAAS,CAAC,UAAU;gBAM7D,MAAM,aAAA,CAAc,KAAA,CAAM;gBAG1B,IAAI,eAAe,OAAA,KAAY,SAAS;oBACtC,WAAW,KAAK;gBAClB;YACF,CAAC;YACD,gLAAe,uBAAA,EAAqB,aAAa,aAAA,EAAe,CAAC,UAAU;gBACzE,eAAe,OAAA,GAAU,MAAM,WAAA;gBAI/B,MAAM,SAAS,MAAM,MAAA;gBACrB,IAAI,OAAO,iBAAA,CAAkB,MAAM,SAAS,GAAG;oBAC7C,OAAO,qBAAA,CAAsB,MAAM,SAAS;gBAC9C;gBAKA,IAAI,MAAM,MAAA,KAAW,KAAK,MAAM,OAAA,KAAY,SAAS,MAAM,WAAA,KAAgB,SAAS;oBAClF,WAAW,KAAK;oBAEhB,MAAM,cAAA,CAAe;gBACvB;YACF,CAAC;YACD,WAAW,wLAAA,EAAqB,aAAa,SAAA,EAAW,CAAC,UAAU;gBACjE,MAAM,gBAAgB,UAAU,OAAA,KAAY;gBAC5C,MAAM,gBAAgB,MAAM,OAAA,IAAW,MAAM,MAAA,IAAU,MAAM,OAAA;gBAC7D,IAAI,CAAC,iBAAiB,MAAM,GAAA,CAAI,MAAA,KAAW,EAAG,CAAA,sBAAsB,MAAM,GAAG;gBAC7E,IAAI,iBAAiB,MAAM,GAAA,KAAQ,IAAK,CAAA;gBACxC,IAAI,UAAU,QAAA,CAAS,MAAM,GAAG,GAAG;oBACjC,WAAW;oBACX,MAAM,cAAA,CAAe;gBACvB;YACF,CAAC;QAAA;IACH,CACF;AAEJ;AAGF,cAAc,WAAA,GAAc;AAM5B,IAAM,aAAa;AAQnB,IAAM,cAAoB,sMAAA,UAAA,CACxB,CAAC,OAAsC,iBAAiB;IAEtD,MAAM,EAAE,aAAA,EAAe,SAAA,EAAW,KAAA,EAAO,QAAA,EAAU,cAAc,EAAA,EAAI,GAAG,WAAW,CAAA,GAAI;IACvF,MAAM,UAAU,iBAAiB,YAAY,aAAa;IAC1D,MAAM,EAAE,4BAAA,CAA6B,CAAA,GAAI;IACzC,MAAM,cAAc,aAAa,KAAA;IACjC,MAAM,+LAAe,kBAAA,EAAgB,cAAc,QAAQ,iBAAiB;IAE5E,CAAA,GAAA,mLAAA,CAAA,kBAAA,EAAgB,MAAM;QACpB,6BAA6B,WAAW;IAC1C,GAAG;QAAC;QAA8B,WAAW;KAAC;IAE9C,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,wKAAC,YAAA,CAAU,IAAA,EAAV;QACE,GAAG,UAAA;QACJ,KAAK;QAGL,OAAO;YAAE,eAAe;QAAO;QAE9B,UAAA,sBAAsB,QAAQ,KAAK,IAAI,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAA,uNAAA,CAAA,WAAA,EAAA;YAAG,UAAA;QAAA,CAAY,IAAM;IAAA;AAGnE;AAGF,YAAY,WAAA,GAAc;AAM1B,IAAM,YAAY;AAKlB,IAAM,aAAmB,sMAAA,UAAA,CACvB,CAAC,OAAqC,iBAAiB;IACrD,MAAM,EAAE,aAAA,EAAe,QAAA,EAAU,GAAG,UAAU,CAAA,GAAI;IAClD,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,wKAAC,YAAA,CAAU,IAAA,EAAV;QAAe,eAAW;QAAE,GAAG,SAAA;QAAW,KAAK;QAC7C,UAAA,YAAY;IAAA,CACf;AAEJ;AAGF,WAAW,WAAA,GAAc;AAMzB,IAAM,cAAc;AAWpB,IAAM,eAA4C,CAAC,UAA0C;IAC3F,OAAO,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,qKAAC,SAAA,EAAA;QAAgB,SAAO;QAAE,GAAG,KAAA;IAAA,CAAO;AAC7C;AAEA,aAAa,WAAA,GAAc;AAM3B,IAAM,eAAe;AAKrB,IAAM,gBAAsB,sMAAA,UAAA,CAC1B,CAAC,OAAwC,iBAAiB;IACxD,MAAM,UAAU,iBAAiB,cAAc,MAAM,aAAa;IAClE,MAAM,CAAC,UAAU,WAAW,CAAA,GAAU,sMAAA,QAAA,CAA2B;IAGjE,CAAA,GAAA,mLAAA,CAAA,kBAAA,EAAgB,MAAM;QACpB,YAAY,IAAI,iBAAiB,CAAC;IACpC,GAAG,CAAC,CAAC;IAEL,IAAI,CAAC,QAAQ,IAAA,EAAM;QACjB,MAAM,OAAO;QACb,OAAO,OACM,6MAAA,YAAA,CACP,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,uBAAA;YAAsB,OAAO,MAAM,aAAA;YAClC,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,WAAW,IAAA,EAAX;gBAAgB,OAAO,MAAM,aAAA;gBAC5B,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,OAAA;oBAAK,UAAA,MAAM,QAAA;gBAAA,CAAS;YAAA,CACvB;QAAA,CACF,GACA,QAEF;IACN;IAEA,OAAO,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,mBAAA;QAAmB,GAAG,KAAA;QAAO,KAAK;IAAA,CAAc;AAC1D;AAGF,cAAc,WAAA,GAAc;AAM5B,IAAM,iBAAiB;AAqBvB,IAAM,CAAC,uBAAuB,uBAAuB,CAAA,GACnD,oBAA+C,YAAY;AAE7D,IAAM,oBAAoB;AA8B1B,IAAM,QAAO,iLAAA,EAAW,4BAA4B;AAEpD,IAAM,oBAA0B,sMAAA,UAAA,CAC9B,CAAC,OAA4C,iBAAiB;IAC5D,MAAM,EACJ,aAAA,EACA,WAAW,cAAA,EACX,gBAAA,EACA,eAAA,EACA,oBAAA,EAAA,EAAA;IAAA,sBAAA;IAGA,IAAA,EACA,UAAA,EACA,KAAA,EACA,WAAA,EACA,YAAA,EACA,iBAAA,EACA,gBAAA,EACA,MAAA,EACA,gBAAA,EACA,eAAA,EAAA,EAAA;IAEA,GAAG,cACL,GAAI;IACJ,MAAM,UAAU,iBAAiB,cAAc,aAAa;IAC5D,MAAM,CAAC,SAAS,UAAU,CAAA,GAAU,sMAAA,QAAA,CAA0C,IAAI;IAClF,MAAM,CAAC,UAAU,WAAW,CAAA,GAAU,sMAAA,QAAA,CAAuC,IAAI;IACjF,MAAM,eAAe,kMAAA,EAAgB,cAAc,CAAC,OAAS,WAAW,IAAI,CAAC;IAC7E,MAAM,CAAC,cAAc,eAAe,CAAA,GAAU,sMAAA,QAAA,CAAmC,IAAI;IACrF,MAAM,CAAC,kBAAkB,mBAAmB,CAAA,GAAU,sMAAA,QAAA,CACpD;IAEF,MAAM,WAAW,cAAc,aAAa;IAC5C,MAAM,CAAC,cAAc,eAAe,CAAA,GAAU,sMAAA,QAAA,CAAS,KAAK;IAC5D,MAAM,yBAA+B,sMAAA,MAAA,CAAO,KAAK;IAG3C,sMAAA,SAAA,CAAU,MAAM;QACpB,IAAI,QAAS,CAAA,qKAAO,aAAA,EAAW,OAAO;IACxC,GAAG;QAAC,OAAO;KAAC;IAIZ,CAAA,GAAA,2KAAA,CAAA,iBAAA,CAAe;IAEf,MAAM,aAAmB,sMAAA,WAAA,CACvB,CAAC,eAA0C;QACzC,MAAM,CAAC,WAAW,GAAG,SAAS,CAAA,GAAI,SAAS,EAAE,GAAA,CAAI,CAAC,OAAS,KAAK,GAAA,CAAI,OAAO;QAC3E,MAAM,CAAC,QAAQ,CAAA,GAAI,UAAU,KAAA,CAAM,CAAA,CAAE;QAErC,MAAM,6BAA6B,SAAS,aAAA;QAC5C,KAAA,MAAW,aAAa,WAAY;YAElC,IAAI,cAAc,2BAA4B,CAAA;YAC9C,WAAW,eAAe;gBAAE,OAAO;YAAU,CAAC;YAE9C,IAAI,cAAc,aAAa,SAAU,CAAA,SAAS,SAAA,GAAY;YAC9D,IAAI,cAAc,YAAY,SAAU,CAAA,SAAS,SAAA,GAAY,SAAS,YAAA;YACtE,WAAW,MAAM;YACjB,IAAI,SAAS,aAAA,KAAkB,2BAA4B,CAAA;QAC7D;IACF,GACA;QAAC;QAAU,QAAQ;KAAA;IAGrB,MAAM,oBAA0B,sMAAA,WAAA,CAC9B,IAAM,WAAW;YAAC;YAAc,OAAO;SAAC,GACxC;QAAC;QAAY;QAAc,OAAO;KAAA;IAK9B,sMAAA,SAAA,CAAU,MAAM;QACpB,IAAI,cAAc;YAChB,kBAAkB;QACpB;IACF,GAAG;QAAC;QAAc,iBAAiB;KAAC;IAIpC,MAAM,EAAE,YAAA,EAAc,wBAAA,CAAyB,CAAA,GAAI;IAC7C,sMAAA,SAAA,CAAU,MAAM;QACpB,IAAI,SAAS;YACX,IAAI,mBAAmB;gBAAE,GAAG;gBAAG,GAAG;YAAE;YAEpC,MAAM,oBAAoB,CAAC,UAAwB;gBACjD,mBAAmB;oBACjB,GAAG,KAAK,GAAA,CAAI,KAAK,KAAA,CAAM,MAAM,KAAK,IAAA,CAAK,yBAAyB,OAAA,EAAS,KAAK,CAAA,CAAE;oBAChF,GAAG,KAAK,GAAA,CAAI,KAAK,KAAA,CAAM,MAAM,KAAK,IAAA,CAAK,yBAAyB,OAAA,EAAS,KAAK,CAAA,CAAE;gBAClF;YACF;YACA,MAAM,kBAAkB,CAAC,UAAwB;gBAE/C,IAAI,iBAAiB,CAAA,IAAK,MAAM,iBAAiB,CAAA,IAAK,IAAI;oBACxD,MAAM,cAAA,CAAe;gBACvB,OAAO;oBAEL,IAAI,CAAC,QAAQ,QAAA,CAAS,MAAM,MAAqB,GAAG;wBAClD,aAAa,KAAK;oBACpB;gBACF;gBACA,SAAS,mBAAA,CAAoB,eAAe,iBAAiB;gBAC7D,yBAAyB,OAAA,GAAU;YACrC;YAEA,IAAI,yBAAyB,OAAA,KAAY,MAAM;gBAC7C,SAAS,gBAAA,CAAiB,eAAe,iBAAiB;gBAC1D,SAAS,gBAAA,CAAiB,aAAa,iBAAiB;oBAAE,SAAS;oBAAM,MAAM;gBAAK,CAAC;YACvF;YAEA,OAAO,MAAM;gBACX,SAAS,mBAAA,CAAoB,eAAe,iBAAiB;gBAC7D,SAAS,mBAAA,CAAoB,aAAa,iBAAiB;oBAAE,SAAS;gBAAK,CAAC;YAC9E;QACF;IACF,GAAG;QAAC;QAAS;QAAc,wBAAwB;KAAC;IAE9C,sMAAA,SAAA,CAAU,MAAM;QACpB,MAAM,QAAQ,IAAM,aAAa,KAAK;QACtC,OAAO,gBAAA,CAAiB,QAAQ,KAAK;QACrC,OAAO,gBAAA,CAAiB,UAAU,KAAK;QACvC,OAAO,MAAM;YACX,OAAO,mBAAA,CAAoB,QAAQ,KAAK;YACxC,OAAO,mBAAA,CAAoB,UAAU,KAAK;QAC5C;IACF,GAAG;QAAC,YAAY;KAAC;IAEjB,MAAM,CAAC,WAAW,qBAAqB,CAAA,GAAI,mBAAmB,CAAC,WAAW;QACxE,MAAM,eAAe,SAAS,EAAE,MAAA,CAAO,CAAC,OAAS,CAAC,KAAK,QAAQ;QAC/D,MAAM,cAAc,aAAa,IAAA,CAAK,CAAC,OAAS,KAAK,GAAA,CAAI,OAAA,KAAY,SAAS,aAAa;QAC3F,MAAM,WAAW,aAAa,cAAc,QAAQ,WAAW;QAC/D,IAAI,UAAU;YAKZ,WAAW,IAAO,SAAS,GAAA,CAAI,OAAA,CAAwB,KAAA,CAAM,CAAC;QAChE;IACF,CAAC;IAED,MAAM,kBAAwB,sMAAA,WAAA,CAC5B,CAAC,MAAgC,OAAe,aAAsB;QACpE,MAAM,mBAAmB,CAAC,uBAAuB,OAAA,IAAW,CAAC;QAC7D,MAAM,iBAAiB,QAAQ,KAAA,KAAU,KAAA,KAAa,QAAQ,KAAA,KAAU;QACxE,IAAI,kBAAkB,kBAAkB;YACtC,gBAAgB,IAAI;YACpB,IAAI,iBAAkB,CAAA,uBAAuB,OAAA,GAAU;QACzD;IACF,GACA;QAAC,QAAQ,KAAK;KAAA;IAEhB,MAAM,kBAAwB,sMAAA,WAAA,CAAY,IAAM,SAAS,MAAM,GAAG;QAAC,OAAO;KAAC;IAC3E,MAAM,sBAA4B,sMAAA,WAAA,CAChC,CAAC,MAAoC,OAAe,aAAsB;QACxE,MAAM,mBAAmB,CAAC,uBAAuB,OAAA,IAAW,CAAC;QAC7D,MAAM,iBAAiB,QAAQ,KAAA,KAAU,KAAA,KAAa,QAAQ,KAAA,KAAU;QACxE,IAAI,kBAAkB,kBAAkB;YACtC,oBAAoB,IAAI;QAC1B;IACF,GACA;QAAC,QAAQ,KAAK;KAAA;IAGhB,MAAM,iBAAiB,aAAa,WAAW,uBAAuB;IAGtE,MAAM,qBACJ,mBAAmB,uBACf;QACE;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF,IACA,CAAC;IAEP,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,uBAAA;QACC,OAAO;QACP;QACA;QACA,kBAAkB;QAClB;QACA;QACA,aAAa;QACb;QACA;QACA;QACA;QACA;QACA;QAEA,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,wNAAC,eAAA,EAAA;YAAa,IAAI;YAAM,gBAAc;YACpC,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,6KAAC,aAAA,EAAA;gBACC,SAAO;gBAGP,SAAS,QAAQ,IAAA;gBACjB,kBAAkB,CAAC,UAAU;oBAE3B,MAAM,cAAA,CAAe;gBACvB;gBACA,qLAAoB,uBAAA,EAAqB,kBAAkB,CAAC,UAAU;oBACpE,QAAQ,OAAA,EAAS,MAAM;wBAAE,eAAe;oBAAK,CAAC;oBAC9C,MAAM,cAAA,CAAe;gBACvB,CAAC;gBAED,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,mLAAC,mBAAA,EAAA;oBACC,SAAO;oBACP,6BAA2B;oBAC3B;oBACA;oBAGA,gBAAgB,CAAC,QAAU,MAAM,cAAA,CAAe;oBAChD,WAAW,IAAM,QAAQ,YAAA,CAAa,KAAK;oBAE3C,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,gBAAA;wBACC,MAAK;wBACL,IAAI,QAAQ,SAAA;wBACZ,cAAY,QAAQ,IAAA,GAAO,SAAS;wBACpC,KAAK,QAAQ,GAAA;wBACb,eAAe,CAAC,QAAU,MAAM,cAAA,CAAe;wBAC9C,GAAG,YAAA;wBACH,GAAG,kBAAA;wBACJ,UAAU,IAAM,gBAAgB,IAAI;wBACpC,KAAK;wBACL,OAAO;4BAAA,0DAAA;4BAEL,SAAS;4BACT,eAAe;4BAAA,8DAAA;4BAEf,SAAS;4BACT,GAAG,aAAa,KAAA;wBAClB;wBACA,2KAAW,wBAAA,EAAqB,aAAa,SAAA,EAAW,CAAC,UAAU;4BACjE,MAAM,gBAAgB,MAAM,OAAA,IAAW,MAAM,MAAA,IAAU,MAAM,OAAA;4BAG7D,IAAI,MAAM,GAAA,KAAQ,MAAO,CAAA,MAAM,cAAA,CAAe;4BAE9C,IAAI,CAAC,iBAAiB,MAAM,GAAA,CAAI,MAAA,KAAW,EAAG,CAAA,sBAAsB,MAAM,GAAG;4BAE7E,IAAI;gCAAC;gCAAW;gCAAa;gCAAQ,KAAK;6BAAA,CAAE,QAAA,CAAS,MAAM,GAAG,GAAG;gCAC/D,MAAM,QAAQ,SAAS,EAAE,MAAA,CAAO,CAAC,OAAS,CAAC,KAAK,QAAQ;gCACxD,IAAI,iBAAiB,MAAM,GAAA,CAAI,CAAC,OAAS,KAAK,GAAA,CAAI,OAAQ;gCAE1D,IAAI;oCAAC;oCAAW,KAAK;iCAAA,CAAE,QAAA,CAAS,MAAM,GAAG,GAAG;oCAC1C,iBAAiB,eAAe,KAAA,CAAM,EAAE,OAAA,CAAQ;gCAClD;gCACA,IAAI;oCAAC;oCAAW,WAAW;iCAAA,CAAE,QAAA,CAAS,MAAM,GAAG,GAAG;oCAChD,MAAM,iBAAiB,MAAM,MAAA;oCAC7B,MAAM,eAAe,eAAe,OAAA,CAAQ,cAAc;oCAC1D,iBAAiB,eAAe,KAAA,CAAM,eAAe,CAAC;gCACxD;gCAMA,WAAW,IAAM,WAAW,cAAc,CAAC;gCAE3C,MAAM,cAAA,CAAe;4BACvB;wBACF,CAAC;oBAAA;gBACH;YACF;QACF,CACF;IAAA;AAGN;AAGF,kBAAkB,WAAA,GAAc;AAMhC,IAAM,6BAA6B;AAKnC,IAAM,4BAAkC,sMAAA,UAAA,CAGtC,CAAC,OAAoD,iBAAiB;IACtE,MAAM,EAAE,aAAA,EAAe,QAAA,EAAU,GAAG,YAAY,CAAA,GAAI;IACpD,MAAM,UAAU,iBAAiB,cAAc,aAAa;IAC5D,MAAM,iBAAiB,wBAAwB,cAAc,aAAa;IAC1E,MAAM,CAAC,gBAAgB,iBAAiB,CAAA,GAAU,sMAAA,QAAA,CAAgC,IAAI;IACtF,MAAM,CAAC,SAAS,UAAU,CAAA,GAAU,sMAAA,QAAA,CAAkD,IAAI;IAC1F,MAAM,+LAAe,kBAAA,EAAgB,cAAc,CAAC,OAAS,WAAW,IAAI,CAAC;IAC7E,MAAM,WAAW,cAAc,aAAa;IAC5C,MAAM,0BAAgC,sMAAA,MAAA,CAAO,KAAK;IAClD,MAAM,sBAA4B,sMAAA,MAAA,CAAO,IAAI;IAE7C,MAAM,EAAE,QAAA,EAAU,YAAA,EAAc,gBAAA,EAAkB,iBAAA,CAAkB,CAAA,GAAI;IACxE,MAAM,WAAiB,sMAAA,WAAA,CAAY,MAAM;QACvC,IACE,QAAQ,OAAA,IACR,QAAQ,SAAA,IACR,kBACA,WACA,YACA,gBACA,kBACA;YACA,MAAM,cAAc,QAAQ,OAAA,CAAQ,qBAAA,CAAsB;YAK1D,MAAM,cAAc,QAAQ,qBAAA,CAAsB;YAClD,MAAM,gBAAgB,QAAQ,SAAA,CAAU,qBAAA,CAAsB;YAC9D,MAAM,eAAe,iBAAiB,qBAAA,CAAsB;YAE5D,IAAI,QAAQ,GAAA,KAAQ,OAAO;gBACzB,MAAM,iBAAiB,aAAa,IAAA,GAAO,YAAY,IAAA;gBACvD,MAAM,OAAO,cAAc,IAAA,GAAO;gBAClC,MAAM,YAAY,YAAY,IAAA,GAAO;gBACrC,MAAM,kBAAkB,YAAY,KAAA,GAAQ;gBAC5C,MAAM,eAAe,KAAK,GAAA,CAAI,iBAAiB,YAAY,KAAK;gBAChE,MAAM,YAAY,OAAO,UAAA,GAAa;gBACtC,MAAM,2KAAc,SAAA,EAAM,MAAM;oBAC9B;oBAAA,+DAAA;oBAAA,iEAAA;oBAAA,qEAAA;oBAAA,gBAAA;oBAAA,qDAAA;oBAMA,KAAK,GAAA,CAAI,gBAAgB,YAAY,YAAY;iBAClD;gBAED,eAAe,KAAA,CAAM,QAAA,GAAW,kBAAkB;gBAClD,eAAe,KAAA,CAAM,IAAA,GAAO,cAAc;YAC5C,OAAO;gBACL,MAAM,iBAAiB,YAAY,KAAA,GAAQ,aAAa,KAAA;gBACxD,MAAM,QAAQ,OAAO,UAAA,GAAa,cAAc,KAAA,GAAQ;gBACxD,MAAM,aAAa,OAAO,UAAA,GAAa,YAAY,KAAA,GAAQ;gBAC3D,MAAM,kBAAkB,YAAY,KAAA,GAAQ;gBAC5C,MAAM,eAAe,KAAK,GAAA,CAAI,iBAAiB,YAAY,KAAK;gBAChE,MAAM,WAAW,OAAO,UAAA,GAAa;gBACrC,MAAM,6KAAe,QAAA,EAAM,OAAO;oBAChC;oBACA,KAAK,GAAA,CAAI,gBAAgB,WAAW,YAAY;iBACjD;gBAED,eAAe,KAAA,CAAM,QAAA,GAAW,kBAAkB;gBAClD,eAAe,KAAA,CAAM,KAAA,GAAQ,eAAe;YAC9C;YAKA,MAAM,QAAQ,SAAS;YACvB,MAAM,kBAAkB,OAAO,WAAA,GAAc,iBAAiB;YAC9D,MAAM,cAAc,SAAS,YAAA;YAE7B,MAAM,gBAAgB,OAAO,gBAAA,CAAiB,OAAO;YACrD,MAAM,wBAAwB,SAAS,cAAc,cAAA,EAAgB,EAAE;YACvE,MAAM,oBAAoB,SAAS,cAAc,UAAA,EAAY,EAAE;YAC/D,MAAM,2BAA2B,SAAS,cAAc,iBAAA,EAAmB,EAAE;YAC7E,MAAM,uBAAuB,SAAS,cAAc,aAAA,EAAe,EAAE;YACrE,MAAM,oBAAoB,wBAAwB,oBAAoB,cAAc,uBAAuB;YAC3G,MAAM,mBAAmB,KAAK,GAAA,CAAI,aAAa,YAAA,GAAe,GAAG,iBAAiB;YAElF,MAAM,iBAAiB,OAAO,gBAAA,CAAiB,QAAQ;YACvD,MAAM,qBAAqB,SAAS,eAAe,UAAA,EAAY,EAAE;YACjE,MAAM,wBAAwB,SAAS,eAAe,aAAA,EAAe,EAAE;YAEvE,MAAM,yBAAyB,YAAY,GAAA,GAAM,YAAY,MAAA,GAAS,IAAI;YAC1E,MAAM,4BAA4B,kBAAkB;YAEpD,MAAM,yBAAyB,aAAa,YAAA,GAAe;YAC3D,MAAM,mBAAmB,aAAa,SAAA,GAAY;YAClD,MAAM,yBAAyB,wBAAwB,oBAAoB;YAC3E,MAAM,4BAA4B,oBAAoB;YAEtD,MAAM,8BAA8B,0BAA0B;YAE9D,IAAI,6BAA6B;gBAC/B,MAAM,aACJ,MAAM,MAAA,GAAS,KAAK,iBAAiB,KAAA,CAAM,MAAM,MAAA,GAAS,CAAC,CAAA,CAAG,GAAA,CAAI,OAAA;gBACpE,eAAe,KAAA,CAAM,MAAA,GAAS;gBAC9B,MAAM,uBACJ,QAAQ,YAAA,GAAe,SAAS,SAAA,GAAY,SAAS,YAAA;gBACvD,MAAM,mCAAmC,KAAK,GAAA,CAC5C,2BACA,yBAAA,gFAAA;gBAAA,CAEG,aAAa,wBAAwB,CAAA,IACtC,uBACA;gBAEJ,MAAM,SAAS,yBAAyB;gBACxC,eAAe,KAAA,CAAM,MAAA,GAAS,SAAS;YACzC,OAAO;gBACL,MAAM,cAAc,MAAM,MAAA,GAAS,KAAK,iBAAiB,KAAA,CAAM,CAAC,CAAA,CAAG,GAAA,CAAI,OAAA;gBACvE,eAAe,KAAA,CAAM,GAAA,GAAM;gBAC3B,MAAM,gCAAgC,KAAK,GAAA,CACzC,wBACA,wBACE,SAAS,SAAA,GAAA,6EAAA;gBAAA,CAER,cAAc,qBAAqB,CAAA,IACpC;gBAEJ,MAAM,SAAS,gCAAgC;gBAC/C,eAAe,KAAA,CAAM,MAAA,GAAS,SAAS;gBACvC,SAAS,SAAA,GAAY,yBAAyB,yBAAyB,SAAS,SAAA;YAClF;YAEA,eAAe,KAAA,CAAM,MAAA,GAAS,GAAG,cAAc,CAAA,IAAA,CAAA;YAC/C,eAAe,KAAA,CAAM,SAAA,GAAY,mBAAmB;YACpD,eAAe,KAAA,CAAM,SAAA,GAAY,kBAAkB;YAGnD,WAAW;YAIX,sBAAsB,IAAO,wBAAwB,OAAA,GAAU,IAAK;QACtE;IACF,GAAG;QACD;QACA,QAAQ,OAAA;QACR,QAAQ,SAAA;QACR;QACA;QACA;QACA;QACA;QACA,QAAQ,GAAA;QACR;KACD;IAED,CAAA,GAAA,mLAAA,CAAA,kBAAA,EAAgB,IAAM,SAAS,GAAG;QAAC,QAAQ;KAAC;IAG5C,MAAM,CAAC,eAAe,gBAAgB,CAAA,GAAU,sMAAA,QAAA,CAAiB;IACjE,CAAA,GAAA,mLAAA,CAAA,kBAAA,EAAgB,MAAM;QACpB,IAAI,QAAS,CAAA,iBAAiB,OAAO,gBAAA,CAAiB,OAAO,EAAE,MAAM;IACvE,GAAG;QAAC,OAAO;KAAC;IAMZ,MAAM,2BAAiC,sMAAA,WAAA,CACrC,CAAC,SAA+C;QAC9C,IAAI,QAAQ,oBAAoB,OAAA,KAAY,MAAM;YAChD,SAAS;YACT,oBAAoB;YACpB,oBAAoB,OAAA,GAAU;QAChC;IACF,GACA;QAAC;QAAU,iBAAiB;KAAA;IAG9B,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,wBAAA;QACC,OAAO;QACP;QACA;QACA,sBAAsB;QAEtB,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,OAAA;YACC,KAAK;YACL,OAAO;gBACL,SAAS;gBACT,eAAe;gBACf,UAAU;gBACV,QAAQ;YACV;YAEA,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,wKAAC,YAAA,CAAU,GAAA,EAAV;gBACE,GAAG,WAAA;gBACJ,KAAK;gBACL,OAAO;oBAAA,gFAAA;oBAAA,2EAAA;oBAGL,WAAW;oBAAA,oEAAA;oBAEX,WAAW;oBACX,GAAG,YAAY,KAAA;gBACjB;YAAA;QACF;IACF;AAGN,CAAC;AAED,0BAA0B,WAAA,GAAc;AAMxC,IAAM,uBAAuB;AAM7B,IAAM,uBAA6B,sMAAA,UAAA,CAGjC,CAAC,OAA+C,iBAAiB;IACjE,MAAM,EACJ,aAAA,EACA,QAAQ,OAAA,EACR,mBAAmB,cAAA,EACnB,GAAG,aACL,GAAI;IACJ,MAAM,cAAc,eAAe,aAAa;IAEhD,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAiB,mKAAA,OAAA,EAAhB;QACE,GAAG,WAAA;QACH,GAAG,WAAA;QACJ,KAAK;QACL;QACA;QACA,OAAO;YAAA,iDAAA;YAEL,WAAW;YACX,GAAG,YAAY,KAAA;YAAA,iDAAA;YAEf,GAAG;gBACD,2CAA2C;gBAC3C,0CAA0C;gBAC1C,2CAA2C;gBAC3C,gCAAgC;gBAChC,iCAAiC;YACnC,CAAA;QACF;IAAA;AAGN,CAAC;AAED,qBAAqB,WAAA,GAAc;AAYnC,IAAM,CAAC,wBAAwB,wBAAwB,CAAA,GACrD,oBAAgD,cAAc,CAAC,CAAC;AAElE,IAAM,gBAAgB;AAQtB,IAAM,iBAAuB,sMAAA,UAAA,CAC3B,CAAC,OAAyC,iBAAiB;IACzD,MAAM,EAAE,aAAA,EAAe,KAAA,EAAO,GAAG,cAAc,CAAA,GAAI;IACnD,MAAM,iBAAiB,wBAAwB,eAAe,aAAa;IAC3E,MAAM,kBAAkB,yBAAyB,eAAe,aAAa;IAC7E,MAAM,+LAAe,kBAAA,EAAgB,cAAc,eAAe,gBAAgB;IAClF,MAAM,mBAAyB,sMAAA,MAAA,CAAO,CAAC;IACvC,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,OAAA,EAAA,uNAAA,CAAA,WAAA,EAAA;QAEE,UAAA;YAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,SAAA;gBACC,yBAAyB;oBACvB,QAAQ,CAAA,yKAAA,CAAA;gBACV;gBACA;YAAA;YAEF,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,WAAW,IAAA,EAAX;gBAAgB,OAAO;gBACtB,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,wKAAC,YAAA,CAAU,GAAA,EAAV;oBACC,8BAA2B;oBAC3B,MAAK;oBACJ,GAAG,aAAA;oBACJ,KAAK;oBACL,OAAO;wBAAA,0EAAA;wBAAA,mFAAA;wBAAA,uCAAA;wBAIL,UAAU;wBACV,MAAM;wBAAA,gEAAA;wBAAA,8DAAA;wBAAA,yCAAA;wBAAA,2DAAA;wBAKN,UAAU;wBACV,GAAG,cAAc,KAAA;oBACnB;oBACA,UAAU,wLAAA,EAAqB,cAAc,QAAA,EAAU,CAAC,UAAU;wBAChE,MAAM,WAAW,MAAM,aAAA;wBACvB,MAAM,EAAE,cAAA,EAAgB,uBAAA,CAAwB,CAAA,GAAI;wBACpD,IAAI,yBAAyB,WAAW,gBAAgB;4BACtD,MAAM,aAAa,KAAK,GAAA,CAAI,iBAAiB,OAAA,GAAU,SAAS,SAAS;4BACzE,IAAI,aAAa,GAAG;gCAClB,MAAM,kBAAkB,OAAO,WAAA,GAAc,iBAAiB;gCAC9D,MAAM,eAAe,WAAW,eAAe,KAAA,CAAM,SAAS;gCAC9D,MAAM,YAAY,WAAW,eAAe,KAAA,CAAM,MAAM;gCACxD,MAAM,aAAa,KAAK,GAAA,CAAI,cAAc,SAAS;gCAEnD,IAAI,aAAa,iBAAiB;oCAChC,MAAM,aAAa,aAAa;oCAChC,MAAM,oBAAoB,KAAK,GAAA,CAAI,iBAAiB,UAAU;oCAC9D,MAAM,aAAa,aAAa;oCAEhC,eAAe,KAAA,CAAM,MAAA,GAAS,oBAAoB;oCAClD,IAAI,eAAe,KAAA,CAAM,MAAA,KAAW,OAAO;wCACzC,SAAS,SAAA,GAAY,aAAa,IAAI,aAAa;wCAEnD,eAAe,KAAA,CAAM,cAAA,GAAiB;oCACxC;gCACF;4BACF;wBACF;wBACA,iBAAiB,OAAA,GAAU,SAAS,SAAA;oBACtC,CAAC;gBAAA;YACH,CACF;SAAA;IAAA,CACF;AAEJ;AAGF,eAAe,WAAA,GAAc;AAM7B,IAAM,aAAa;AAInB,IAAM,CAAC,4BAA4B,qBAAqB,CAAA,GACtD,oBAA6C,UAAU;AAKzD,IAAM,cAAoB,sMAAA,UAAA,CACxB,CAAC,OAAsC,iBAAiB;IACtD,MAAM,EAAE,aAAA,EAAe,GAAG,WAAW,CAAA,GAAI;IACzC,MAAM,6KAAU,QAAA,CAAM;IACtB,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,4BAAA;QAA2B,OAAO;QAAe,IAAI;QACpD,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,wKAAC,YAAA,CAAU,GAAA,EAAV;YAAc,MAAK;YAAQ,mBAAiB;YAAU,GAAG,UAAA;YAAY,KAAK;QAAA,CAAc;IAAA,CAC3F;AAEJ;AAGF,YAAY,WAAA,GAAc;AAM1B,IAAM,aAAa;AAKnB,IAAM,cAAoB,sMAAA,UAAA,CACxB,CAAC,OAAsC,iBAAiB;IACtD,MAAM,EAAE,aAAA,EAAe,GAAG,WAAW,CAAA,GAAI;IACzC,MAAM,eAAe,sBAAsB,YAAY,aAAa;IACpE,OAAO,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,wKAAC,YAAA,CAAU,GAAA,EAAV;QAAc,IAAI,aAAa,EAAA;QAAK,GAAG,UAAA;QAAY,KAAK;IAAA,CAAc;AAChF;AAGF,YAAY,WAAA,GAAc;AAM1B,IAAM,YAAY;AAUlB,IAAM,CAAC,2BAA2B,oBAAoB,CAAA,GACpD,oBAA4C,SAAS;AASvD,IAAM,aAAmB,sMAAA,UAAA,CACvB,CAAC,OAAqC,iBAAiB;IACrD,MAAM,EACJ,aAAA,EACA,KAAA,EACA,WAAW,KAAA,EACX,WAAW,aAAA,EACX,GAAG,WACL,GAAI;IACJ,MAAM,UAAU,iBAAiB,WAAW,aAAa;IACzD,MAAM,iBAAiB,wBAAwB,WAAW,aAAa;IACvE,MAAM,aAAa,QAAQ,KAAA,KAAU;IACrC,MAAM,CAAC,WAAW,YAAY,CAAA,GAAU,sMAAA,QAAA,CAAS,iBAAiB,EAAE;IACpE,MAAM,CAAC,WAAW,YAAY,CAAA,GAAU,sMAAA,QAAA,CAAS,KAAK;IACtD,MAAM,mBAAe,8LAAA,EAAgB,cAAc,CAAC,OAClD,eAAe,eAAA,GAAkB,MAAM,OAAO,QAAQ;IAExD,MAAM,aAAS,uKAAA,CAAM;IACrB,MAAM,iBAAuB,sMAAA,MAAA,CAA0C,OAAO;IAE9E,MAAM,eAAe,MAAM;QACzB,IAAI,CAAC,UAAU;YACb,QAAQ,aAAA,CAAc,KAAK;YAC3B,QAAQ,YAAA,CAAa,KAAK;QAC5B;IACF;IAEA,IAAI,UAAU,IAAI;QAChB,MAAM,IAAI,MACR;IAEJ;IAEA,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,2BAAA;QACC,OAAO;QACP;QACA;QACA;QACA;QACA,kBAAwB,sMAAA,WAAA,CAAY,CAAC,SAAS;YAC5C,aAAa,CAAC,gBAAkB,iBAAA,CAAkB,MAAM,eAAe,EAAA,EAAI,IAAA,CAAK,CAAC;QACnF,GAAG,CAAC,CAAC;QAEL,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,WAAW,QAAA,EAAX;YACC,OAAO;YACP;YACA;YACA;YAEA,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,wKAAC,YAAA,CAAU,GAAA,EAAV;gBACC,MAAK;gBACL,mBAAiB;gBACjB,oBAAkB,YAAY,KAAK,KAAA;gBAEnC,iBAAe,cAAc;gBAC7B,cAAY,aAAa,YAAY;gBACrC,iBAAe,YAAY,KAAA;gBAC3B,iBAAe,WAAW,KAAK,KAAA;gBAC/B,UAAU,WAAW,KAAA,IAAY,CAAA;gBAChC,GAAG,SAAA;gBACJ,KAAK;gBACL,SAAS,wLAAA,EAAqB,UAAU,OAAA,EAAS,IAAM,aAAa,IAAI,CAAC;gBACzE,yKAAQ,uBAAA,EAAqB,UAAU,MAAA,EAAQ,IAAM,aAAa,KAAK,CAAC;gBACxE,UAAS,uLAAA,EAAqB,UAAU,OAAA,EAAS,MAAM;oBAErD,IAAI,eAAe,OAAA,KAAY,QAAS,CAAA,aAAa;gBACvD,CAAC;gBACD,8KAAa,uBAAA,EAAqB,UAAU,WAAA,EAAa,MAAM;oBAG7D,IAAI,eAAe,OAAA,KAAY,QAAS,CAAA,aAAa;gBACvD,CAAC;gBACD,gLAAe,uBAAA,EAAqB,UAAU,aAAA,EAAe,CAAC,UAAU;oBACtE,eAAe,OAAA,GAAU,MAAM,WAAA;gBACjC,CAAC;gBACD,eAAe,wLAAA,EAAqB,UAAU,aAAA,EAAe,CAAC,UAAU;oBAEtE,eAAe,OAAA,GAAU,MAAM,WAAA;oBAC/B,IAAI,UAAU;wBACZ,eAAe,WAAA,GAAc;oBAC/B,OAAA,IAAW,eAAe,OAAA,KAAY,SAAS;wBAG7C,MAAM,aAAA,CAAc,KAAA,CAAM;4BAAE,eAAe;wBAAK,CAAC;oBACnD;gBACF,CAAC;gBACD,gBAAgB,wLAAA,EAAqB,UAAU,cAAA,EAAgB,CAAC,UAAU;oBACxE,IAAI,MAAM,aAAA,KAAkB,SAAS,aAAA,EAAe;wBAClD,eAAe,WAAA,GAAc;oBAC/B;gBACF,CAAC;gBACD,4KAAW,uBAAA,EAAqB,UAAU,SAAA,EAAW,CAAC,UAAU;oBAC9D,MAAM,gBAAgB,eAAe,SAAA,EAAW,YAAY;oBAC5D,IAAI,iBAAiB,MAAM,GAAA,KAAQ,IAAK,CAAA;oBACxC,IAAI,eAAe,QAAA,CAAS,MAAM,GAAG,EAAG,CAAA,aAAa;oBAErD,IAAI,MAAM,GAAA,KAAQ,IAAK,CAAA,MAAM,cAAA,CAAe;gBAC9C,CAAC;YAAA;QACH;IACF;AAGN;AAGF,WAAW,WAAA,GAAc;AAMzB,IAAM,iBAAiB;AAKvB,IAAM,iBAAuB,sMAAA,UAAA,CAC3B,CAAC,OAAyC,iBAAiB;IAEzD,MAAM,EAAE,aAAA,EAAe,SAAA,EAAW,KAAA,EAAO,GAAG,cAAc,CAAA,GAAI;IAC9D,MAAM,UAAU,iBAAiB,gBAAgB,aAAa;IAC9D,MAAM,iBAAiB,wBAAwB,gBAAgB,aAAa;IAC5E,MAAM,cAAc,qBAAqB,gBAAgB,aAAa;IACtE,MAAM,uBAAuB,8BAA8B,gBAAgB,aAAa;IACxF,MAAM,CAAC,cAAc,eAAe,CAAA,GAAU,sMAAA,QAAA,CAAuC,IAAI;IACzF,MAAM,+LAAe,kBAAA,EACnB,cACA,CAAC,OAAS,gBAAgB,IAAI,GAC9B,YAAY,gBAAA,EACZ,CAAC,OAAS,eAAe,mBAAA,GAAsB,MAAM,YAAY,KAAA,EAAO,YAAY,QAAQ;IAG9F,MAAM,cAAc,cAAc;IAClC,MAAM,eAAqB,sMAAA,OAAA,CACzB,IACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,UAAA;YAA+B,OAAO,YAAY,KAAA;YAAO,UAAU,YAAY,QAAA;YAC7E,UAAA;QAAA,GADU,YAAY,KAEzB,GAEF;QAAC,YAAY,QAAA;QAAU,YAAY,KAAA;QAAO,WAAW;KAAA;IAGvD,MAAM,EAAE,iBAAA,EAAmB,oBAAA,CAAqB,CAAA,GAAI;IACpD,CAAA,GAAA,mLAAA,CAAA,kBAAA,EAAgB,MAAM;QACpB,kBAAkB,YAAY;QAC9B,OAAO,IAAM,qBAAqB,YAAY;IAChD,GAAG;QAAC;QAAmB;QAAsB,YAAY;KAAC;IAE1D,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,OAAA,EAAA,uNAAA,CAAA,WAAA,EAAA;QACE,UAAA;YAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,wKAAC,YAAA,CAAU,IAAA,EAAV;gBAAe,IAAI,YAAY,MAAA;gBAAS,GAAG,aAAA;gBAAe,KAAK;YAAA,CAAc;YAG7E,YAAY,UAAA,IAAc,QAAQ,SAAA,IAAa,CAAC,QAAQ,oBAAA,GAC5C,6MAAA,YAAA,CAAa,cAAc,QAAA,EAAU,QAAQ,SAAS,IAC/D;SAAA;IAAA,CACN;AAEJ;AAGF,eAAe,WAAA,GAAc;AAM7B,IAAM,sBAAsB;AAK5B,IAAM,sBAA4B,sMAAA,UAAA,CAChC,CAAC,OAA8C,iBAAiB;IAC9D,MAAM,EAAE,aAAA,EAAe,GAAG,mBAAmB,CAAA,GAAI;IACjD,MAAM,cAAc,qBAAqB,qBAAqB,aAAa;IAC3E,OAAO,YAAY,UAAA,GACjB,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,wKAAC,YAAA,CAAU,IAAA,EAAV;QAAe,eAAW;QAAE,GAAG,kBAAA;QAAoB,KAAK;IAAA,CAAc,IACrE;AACN;AAGF,oBAAoB,WAAA,GAAc;AAMlC,IAAM,wBAAwB;AAK9B,IAAM,uBAA6B,sMAAA,UAAA,CAGjC,CAAC,OAA+C,iBAAiB;IACjE,MAAM,iBAAiB,wBAAwB,uBAAuB,MAAM,aAAa;IACzF,MAAM,kBAAkB,yBAAyB,uBAAuB,MAAM,aAAa;IAC3F,MAAM,CAAC,aAAa,cAAc,CAAA,GAAU,sMAAA,QAAA,CAAS,KAAK;IAC1D,MAAM,+LAAe,kBAAA,EAAgB,cAAc,gBAAgB,oBAAoB;IAEvF,CAAA,GAAA,mLAAA,CAAA,kBAAA,EAAgB,MAAM;QACpB,IAAI,eAAe,QAAA,IAAY,eAAe,YAAA,EAAc;YAE1D,IAASA,gBAAT,WAAwB;gBACtB,MAAMC,eAAc,SAAS,SAAA,GAAY;gBACzC,eAAeA,YAAW;YAC5B;YAHS,IAAA,eAAAD;YADT,MAAM,WAAW,eAAe,QAAA;YAKhCA,cAAa;YACb,SAAS,gBAAA,CAAiB,UAAUA,aAAY;YAChD,OAAO,IAAM,SAAS,mBAAA,CAAoB,UAAUA,aAAY;QAClE;IACF,GAAG;QAAC,eAAe,QAAA;QAAU,eAAe,YAAY;KAAC;IAEzD,OAAO,cACL,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,wBAAA;QACE,GAAG,KAAA;QACJ,KAAK;QACL,cAAc,MAAM;YAClB,MAAM,EAAE,QAAA,EAAU,YAAA,CAAa,CAAA,GAAI;YACnC,IAAI,YAAY,cAAc;gBAC5B,SAAS,SAAA,GAAY,SAAS,SAAA,GAAY,aAAa,YAAA;YACzD;QACF;IAAA,KAEA;AACN,CAAC;AAED,qBAAqB,WAAA,GAAc;AAMnC,IAAM,0BAA0B;AAKhC,IAAM,yBAA+B,sMAAA,UAAA,CAGnC,CAAC,OAAiD,iBAAiB;IACnE,MAAM,iBAAiB,wBAAwB,yBAAyB,MAAM,aAAa;IAC3F,MAAM,kBAAkB,yBAAyB,yBAAyB,MAAM,aAAa;IAC7F,MAAM,CAAC,eAAe,gBAAgB,CAAA,GAAU,sMAAA,QAAA,CAAS,KAAK;IAC9D,MAAM,+LAAe,kBAAA,EAAgB,cAAc,gBAAgB,oBAAoB;IAEvF,CAAA,GAAA,mLAAA,CAAA,kBAAA,EAAgB,MAAM;QACpB,IAAI,eAAe,QAAA,IAAY,eAAe,YAAA,EAAc;YAE1D,IAASA,gBAAT,WAAwB;gBACtB,MAAM,YAAY,SAAS,YAAA,GAAe,SAAS,YAAA;gBAGnD,MAAME,iBAAgB,KAAK,IAAA,CAAK,SAAS,SAAS,IAAI;gBACtD,iBAAiBA,cAAa;YAChC;YANS,IAAA,eAAAF;YADT,MAAM,WAAW,eAAe,QAAA;YAQhCA,cAAa;YACb,SAAS,gBAAA,CAAiB,UAAUA,aAAY;YAChD,OAAO,IAAM,SAAS,mBAAA,CAAoB,UAAUA,aAAY;QAClE;IACF,GAAG;QAAC,eAAe,QAAA;QAAU,eAAe,YAAY;KAAC;IAEzD,OAAO,gBACL,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,wBAAA;QACE,GAAG,KAAA;QACJ,KAAK;QACL,cAAc,MAAM;YAClB,MAAM,EAAE,QAAA,EAAU,YAAA,CAAa,CAAA,GAAI;YACnC,IAAI,YAAY,cAAc;gBAC5B,SAAS,SAAA,GAAY,SAAS,SAAA,GAAY,aAAa,YAAA;YACzD;QACF;IAAA,KAEA;AACN,CAAC;AAED,uBAAuB,WAAA,GAAc;AAOrC,IAAM,yBAA+B,sMAAA,UAAA,CAGnC,CAAC,OAAiD,iBAAiB;IACnE,MAAM,EAAE,aAAA,EAAe,YAAA,EAAc,GAAG,qBAAqB,CAAA,GAAI;IACjE,MAAM,iBAAiB,wBAAwB,sBAAsB,aAAa;IAClF,MAAM,qBAA2B,sMAAA,MAAA,CAAsB,IAAI;IAC3D,MAAM,WAAW,cAAc,aAAa;IAE5C,MAAM,uBAA6B,sMAAA,WAAA,CAAY,MAAM;QACnD,IAAI,mBAAmB,OAAA,KAAY,MAAM;YACvC,OAAO,aAAA,CAAc,mBAAmB,OAAO;YAC/C,mBAAmB,OAAA,GAAU;QAC/B;IACF,GAAG,CAAC,CAAC;IAEC,sMAAA,SAAA,CAAU,MAAM;QACpB,OAAO,IAAM,qBAAqB;IACpC,GAAG;QAAC,oBAAoB;KAAC;IAMzB,CAAA,GAAA,mLAAA,CAAA,kBAAA,EAAgB,MAAM;QACpB,MAAM,aAAa,SAAS,EAAE,IAAA,CAAK,CAAC,OAAS,KAAK,GAAA,CAAI,OAAA,KAAY,SAAS,aAAa;QACxF,YAAY,IAAI,SAAS,eAAe;YAAE,OAAO;QAAU,CAAC;IAC9D,GAAG;QAAC,QAAQ;KAAC;IAEb,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,wKAAC,YAAA,CAAU,GAAA,EAAV;QACC,eAAW;QACV,GAAG,oBAAA;QACJ,KAAK;QACL,OAAO;YAAE,YAAY;YAAG,GAAG,qBAAqB,KAAA;QAAM;QACtD,+KAAe,wBAAA,EAAqB,qBAAqB,aAAA,EAAe,MAAM;YAC5E,IAAI,mBAAmB,OAAA,KAAY,MAAM;gBACvC,mBAAmB,OAAA,GAAU,OAAO,WAAA,CAAY,cAAc,EAAE;YAClE;QACF,CAAC;QACD,gLAAe,uBAAA,EAAqB,qBAAqB,aAAA,EAAe,MAAM;YAC5E,eAAe,WAAA,GAAc;YAC7B,IAAI,mBAAmB,OAAA,KAAY,MAAM;gBACvC,mBAAmB,OAAA,GAAU,OAAO,WAAA,CAAY,cAAc,EAAE;YAClE;QACF,CAAC;QACD,iLAAgB,uBAAA,EAAqB,qBAAqB,cAAA,EAAgB,MAAM;YAC9E,qBAAqB;QACvB,CAAC;IAAA;AAGP,CAAC;AAMD,IAAM,iBAAiB;AAKvB,IAAM,kBAAwB,sMAAA,UAAA,CAC5B,CAAC,OAA0C,iBAAiB;IAC1D,MAAM,EAAE,aAAA,EAAe,GAAG,eAAe,CAAA,GAAI;IAC7C,OAAO,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,wKAAC,YAAA,CAAU,GAAA,EAAV;QAAc,eAAW;QAAE,GAAG,cAAA;QAAgB,KAAK;IAAA,CAAc;AAC3E;AAGF,gBAAgB,WAAA,GAAc;AAM9B,IAAM,aAAa;AAMnB,IAAM,cAAoB,sMAAA,UAAA,CACxB,CAAC,OAAsC,iBAAiB;IACtD,MAAM,EAAE,aAAA,EAAe,GAAG,WAAW,CAAA,GAAI;IACzC,MAAM,cAAc,eAAe,aAAa;IAChD,MAAM,UAAU,iBAAiB,YAAY,aAAa;IAC1D,MAAM,iBAAiB,wBAAwB,YAAY,aAAa;IACxE,OAAO,QAAQ,IAAA,IAAQ,eAAe,QAAA,KAAa,WACjD,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAiB,mKAAA,KAAA,EAAhB;QAAuB,GAAG,WAAA;QAAc,GAAG,UAAA;QAAY,KAAK;IAAA,CAAc,IACzE;AACN;AAGF,YAAY,WAAA,GAAc;AAM1B,IAAM,oBAAoB;AAK1B,IAAM,oBAA0B,sMAAA,UAAA,CAC9B,CAAC,EAAE,aAAA,EAAe,KAAA,EAAO,GAAG,MAAM,CAAA,EAAwC,iBAAiB;IACzF,MAAM,MAAY,sMAAA,MAAA,CAA0B,IAAI;IAChD,MAAM,+LAAe,kBAAA,EAAgB,cAAc,GAAG;IACtD,MAAM,2LAAY,eAAA,EAAY,KAAK;IAG7B,sMAAA,SAAA,CAAU,MAAM;QACpB,MAAM,SAAS,IAAI,OAAA;QACnB,IAAI,CAAC,OAAQ,CAAA;QAEb,MAAM,cAAc,OAAO,iBAAA,CAAkB,SAAA;QAC7C,MAAM,aAAa,OAAO,wBAAA,CACxB,aACA;QAEF,MAAM,WAAW,WAAW,GAAA;QAC5B,IAAI,cAAc,SAAS,UAAU;YACnC,MAAM,QAAQ,IAAI,MAAM,UAAU;gBAAE,SAAS;YAAK,CAAC;YACnD,SAAS,IAAA,CAAK,QAAQ,KAAK;YAC3B,OAAO,aAAA,CAAc,KAAK;QAC5B;IACF,GAAG;QAAC;QAAW,KAAK;KAAC;IAcrB,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,wKAAC,YAAA,CAAU,MAAA,EAAV;QACE,GAAG,KAAA;QACJ,OAAO;YAAE,kLAAG,yBAAA;YAAwB,GAAG,MAAM,KAAA;QAAM;QACnD,KAAK;QACL,cAAc;IAAA;AAGpB;AAGF,kBAAkB,WAAA,GAAc;AAIhC,SAAS,sBAAsB,KAAA,EAAgB;IAC7C,OAAO,UAAU,MAAM,UAAU,KAAA;AACnC;AAEA,SAAS,mBAAmB,cAAA,EAA0C;IACpE,MAAM,4MAAqB,iBAAA,EAAe,cAAc;IACxD,MAAM,YAAkB,sMAAA,MAAA,CAAO,EAAE;IACjC,MAAM,WAAiB,sMAAA,MAAA,CAAO,CAAC;IAE/B,MAAM,wBAA8B,sMAAA,WAAA,CAClC,CAAC,QAAgB;QACf,MAAM,SAAS,UAAU,OAAA,GAAU;QACnC,mBAAmB,MAAM;QAEzB,CAAC,SAAS,aAAa,KAAA,EAAe;YACpC,UAAU,OAAA,GAAU;YACpB,OAAO,YAAA,CAAa,SAAS,OAAO;YAEpC,IAAI,UAAU,GAAI,CAAA,SAAS,OAAA,GAAU,OAAO,UAAA,CAAW,IAAM,aAAa,EAAE,GAAG,GAAI;QACrF,CAAA,EAAG,MAAM;IACX,GACA;QAAC,kBAAkB;KAAA;IAGrB,MAAM,iBAAuB,sMAAA,WAAA,CAAY,MAAM;QAC7C,UAAU,OAAA,GAAU;QACpB,OAAO,YAAA,CAAa,SAAS,OAAO;IACtC,GAAG,CAAC,CAAC;IAEC,sMAAA,SAAA,CAAU,MAAM;QACpB,OAAO,IAAM,OAAO,YAAA,CAAa,SAAS,OAAO;IACnD,GAAG,CAAC,CAAC;IAEL,OAAO;QAAC;QAAW;QAAuB,cAAc;KAAA;AAC1D;AAmBA,SAAS,aACP,KAAA,EACA,MAAA,EACA,WAAA,EACA;IACA,MAAM,aAAa,OAAO,MAAA,GAAS,KAAK,MAAM,IAAA,CAAK,MAAM,EAAE,KAAA,CAAM,CAAC,OAAS,SAAS,MAAA,CAAO,CAAC,CAAC;IAC7F,MAAM,mBAAmB,aAAa,MAAA,CAAO,CAAC,CAAA,GAAK;IACnD,MAAM,mBAAmB,cAAc,MAAM,OAAA,CAAQ,WAAW,IAAI,CAAA;IACpE,IAAI,eAAe,UAAU,OAAO,KAAK,GAAA,CAAI,kBAAkB,CAAC,CAAC;IACjE,MAAM,qBAAqB,iBAAiB,MAAA,KAAW;IACvD,IAAI,mBAAoB,CAAA,eAAe,aAAa,MAAA,CAAO,CAAC,IAAM,MAAM,WAAW;IACnF,MAAM,WAAW,aAAa,IAAA,CAAK,CAAC,OAClC,KAAK,SAAA,CAAU,WAAA,CAAY,EAAE,UAAA,CAAW,iBAAiB,WAAA,CAAY,CAAC;IAExE,OAAO,aAAa,cAAc,WAAW,KAAA;AAC/C;AAMA,SAAS,UAAa,KAAA,EAAY,UAAA,EAAoB;IACpD,OAAO,MAAM,GAAA,CAAO,CAAC,GAAG,QAAU,KAAA,CAAA,CAAO,aAAa,KAAA,IAAS,MAAM,MAAM,CAAE;AAC/E;AAEA,IAAMG,QAAO;AACb,IAAM,UAAU;AAChB,IAAM,QAAQ;AACd,IAAM,OAAO;AACb,IAAM,SAAS;AACf,IAAMC,WAAU;AAChB,IAAM,WAAW;AACjB,IAAM,QAAQ;AACd,IAAM,QAAQ;AACd,IAAM,OAAO;AACb,IAAM,WAAW;AACjB,IAAM,gBAAgB;AACtB,IAAM,iBAAiB;AACvB,IAAM,mBAAmB;AACzB,IAAM,YAAY;AAClB,IAAMC,SAAQ", "ignoreList": [0]}}, {"offset": {"line": 2019, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2025, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/%40radix-ui/react-checkbox/src/checkbox.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { usePrevious } from '@radix-ui/react-use-previous';\nimport { useSize } from '@radix-ui/react-use-size';\nimport { Presence } from '@radix-ui/react-presence';\nimport { Primitive } from '@radix-ui/react-primitive';\n\nimport type { Scope } from '@radix-ui/react-context';\n\nconst CHECKBOX_NAME = 'Checkbox';\n\ntype ScopedProps<P> = P & { __scopeCheckbox?: Scope };\nconst [createCheckboxContext, createCheckboxScope] = createContextScope(CHECKBOX_NAME);\n\ntype CheckedState = boolean | 'indeterminate';\n\ntype CheckboxContextValue<State extends CheckedState | boolean = CheckedState> = {\n  checked: State | boolean;\n  setChecked: React.Dispatch<React.SetStateAction<State | boolean>>;\n  disabled: boolean | undefined;\n  control: HTMLButtonElement | null;\n  setControl: React.Dispatch<React.SetStateAction<HTMLButtonElement | null>>;\n  name: string | undefined;\n  form: string | undefined;\n  value: string | number | readonly string[];\n  hasConsumerStoppedPropagationRef: React.RefObject<boolean>;\n  required: boolean | undefined;\n  defaultChecked: boolean | undefined;\n  isFormControl: boolean;\n  bubbleInput: HTMLInputElement | null;\n  setBubbleInput: React.Dispatch<React.SetStateAction<HTMLInputElement | null>>;\n};\n\nconst [CheckboxProviderImpl, useCheckboxContext] =\n  createCheckboxContext<CheckboxContextValue>(CHECKBOX_NAME);\n\n/* -------------------------------------------------------------------------------------------------\n * CheckboxProvider\n * -----------------------------------------------------------------------------------------------*/\n\ninterface CheckboxProviderProps<State extends CheckedState = CheckedState> {\n  checked?: State | boolean;\n  defaultChecked?: State | boolean;\n  required?: boolean;\n  onCheckedChange?(checked: State | boolean): void;\n  name?: string;\n  form?: string;\n  disabled?: boolean;\n  value?: string | number | readonly string[];\n  children?: React.ReactNode;\n}\n\nfunction CheckboxProvider<State extends CheckedState = CheckedState>(\n  props: ScopedProps<CheckboxProviderProps<State>>\n) {\n  const {\n    __scopeCheckbox,\n    checked: checkedProp,\n    children,\n    defaultChecked,\n    disabled,\n    form,\n    name,\n    onCheckedChange,\n    required,\n    value = 'on',\n    // @ts-expect-error\n    internal_do_not_use_render,\n  } = props;\n\n  const [checked, setChecked] = useControllableState({\n    prop: checkedProp,\n    defaultProp: defaultChecked ?? false,\n    onChange: onCheckedChange,\n    caller: CHECKBOX_NAME,\n  });\n  const [control, setControl] = React.useState<HTMLButtonElement | null>(null);\n  const [bubbleInput, setBubbleInput] = React.useState<HTMLInputElement | null>(null);\n  const hasConsumerStoppedPropagationRef = React.useRef(false);\n  const isFormControl = control\n    ? !!form || !!control.closest('form')\n    : // We set this to true by default so that events bubble to forms without JS (SSR)\n      true;\n\n  const context: CheckboxContextValue<State> = {\n    checked: checked,\n    disabled: disabled,\n    setChecked: setChecked,\n    control: control,\n    setControl: setControl,\n    name: name,\n    form: form,\n    value: value,\n    hasConsumerStoppedPropagationRef: hasConsumerStoppedPropagationRef,\n    required: required,\n    defaultChecked: isIndeterminate(defaultChecked) ? false : defaultChecked,\n    isFormControl: isFormControl,\n    bubbleInput,\n    setBubbleInput,\n  };\n\n  return (\n    <CheckboxProviderImpl\n      scope={__scopeCheckbox}\n      {...(context as unknown as CheckboxContextValue<CheckedState>)}\n    >\n      {isFunction(internal_do_not_use_render) ? internal_do_not_use_render(context) : children}\n    </CheckboxProviderImpl>\n  );\n}\n\n/* -------------------------------------------------------------------------------------------------\n * CheckboxTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst TRIGGER_NAME = 'CheckboxTrigger';\n\ninterface CheckboxTriggerProps\n  extends Omit<\n    React.ComponentPropsWithoutRef<typeof Primitive.button>,\n    keyof CheckboxProviderProps\n  > {\n  children?: React.ReactNode;\n}\n\nconst CheckboxTrigger = React.forwardRef<HTMLButtonElement, CheckboxTriggerProps>(\n  (\n    { __scopeCheckbox, onKeyDown, onClick, ...checkboxProps }: ScopedProps<CheckboxTriggerProps>,\n    forwardedRef\n  ) => {\n    const {\n      control,\n      value,\n      disabled,\n      checked,\n      required,\n      setControl,\n      setChecked,\n      hasConsumerStoppedPropagationRef,\n      isFormControl,\n      bubbleInput,\n    } = useCheckboxContext(TRIGGER_NAME, __scopeCheckbox);\n    const composedRefs = useComposedRefs(forwardedRef, setControl);\n\n    const initialCheckedStateRef = React.useRef(checked);\n    React.useEffect(() => {\n      const form = control?.form;\n      if (form) {\n        const reset = () => setChecked(initialCheckedStateRef.current);\n        form.addEventListener('reset', reset);\n        return () => form.removeEventListener('reset', reset);\n      }\n    }, [control, setChecked]);\n\n    return (\n      <Primitive.button\n        type=\"button\"\n        role=\"checkbox\"\n        aria-checked={isIndeterminate(checked) ? 'mixed' : checked}\n        aria-required={required}\n        data-state={getState(checked)}\n        data-disabled={disabled ? '' : undefined}\n        disabled={disabled}\n        value={value}\n        {...checkboxProps}\n        ref={composedRefs}\n        onKeyDown={composeEventHandlers(onKeyDown, (event) => {\n          // According to WAI ARIA, Checkboxes don't activate on enter keypress\n          if (event.key === 'Enter') event.preventDefault();\n        })}\n        onClick={composeEventHandlers(onClick, (event) => {\n          setChecked((prevChecked) => (isIndeterminate(prevChecked) ? true : !prevChecked));\n          if (bubbleInput && isFormControl) {\n            hasConsumerStoppedPropagationRef.current = event.isPropagationStopped();\n            // if checkbox has a bubble input and is a form control, stop\n            // propagation from the button so that we only propagate one click\n            // event (from the input). We propagate changes from an input so\n            // that native form validation works and form events reflect\n            // checkbox updates.\n            if (!hasConsumerStoppedPropagationRef.current) event.stopPropagation();\n          }\n        })}\n      />\n    );\n  }\n);\n\nCheckboxTrigger.displayName = TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * Checkbox\n * -----------------------------------------------------------------------------------------------*/\n\ntype CheckboxElement = React.ComponentRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = React.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface CheckboxProps extends Omit<PrimitiveButtonProps, 'checked' | 'defaultChecked'> {\n  checked?: CheckedState;\n  defaultChecked?: CheckedState;\n  required?: boolean;\n  onCheckedChange?(checked: CheckedState): void;\n}\n\nconst Checkbox = React.forwardRef<CheckboxElement, CheckboxProps>(\n  (props: ScopedProps<CheckboxProps>, forwardedRef) => {\n    const {\n      __scopeCheckbox,\n      name,\n      checked,\n      defaultChecked,\n      required,\n      disabled,\n      value,\n      onCheckedChange,\n      form,\n      ...checkboxProps\n    } = props;\n\n    return (\n      <CheckboxProvider\n        __scopeCheckbox={__scopeCheckbox}\n        checked={checked}\n        defaultChecked={defaultChecked}\n        disabled={disabled}\n        required={required}\n        onCheckedChange={onCheckedChange}\n        name={name}\n        form={form}\n        value={value}\n        // @ts-expect-error\n        internal_do_not_use_render={({ isFormControl }: CheckboxContextValue) => (\n          <>\n            <CheckboxTrigger\n              {...checkboxProps}\n              ref={forwardedRef}\n              // @ts-expect-error\n              __scopeCheckbox={__scopeCheckbox}\n            />\n            {isFormControl && (\n              <CheckboxBubbleInput\n                // @ts-expect-error\n                __scopeCheckbox={__scopeCheckbox}\n              />\n            )}\n          </>\n        )}\n      />\n    );\n  }\n);\n\nCheckbox.displayName = CHECKBOX_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * CheckboxIndicator\n * -----------------------------------------------------------------------------------------------*/\n\nconst INDICATOR_NAME = 'CheckboxIndicator';\n\ntype CheckboxIndicatorElement = React.ComponentRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = React.ComponentPropsWithoutRef<typeof Primitive.span>;\ninterface CheckboxIndicatorProps extends PrimitiveSpanProps {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst CheckboxIndicator = React.forwardRef<CheckboxIndicatorElement, CheckboxIndicatorProps>(\n  (props: ScopedProps<CheckboxIndicatorProps>, forwardedRef) => {\n    const { __scopeCheckbox, forceMount, ...indicatorProps } = props;\n    const context = useCheckboxContext(INDICATOR_NAME, __scopeCheckbox);\n    return (\n      <Presence\n        present={forceMount || isIndeterminate(context.checked) || context.checked === true}\n      >\n        <Primitive.span\n          data-state={getState(context.checked)}\n          data-disabled={context.disabled ? '' : undefined}\n          {...indicatorProps}\n          ref={forwardedRef}\n          style={{ pointerEvents: 'none', ...props.style }}\n        />\n      </Presence>\n    );\n  }\n);\n\nCheckboxIndicator.displayName = INDICATOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * CheckboxBubbleInput\n * -----------------------------------------------------------------------------------------------*/\n\nconst BUBBLE_INPUT_NAME = 'CheckboxBubbleInput';\n\ntype InputProps = React.ComponentPropsWithoutRef<typeof Primitive.input>;\ninterface CheckboxBubbleInputProps extends Omit<InputProps, 'checked'> {}\n\nconst CheckboxBubbleInput = React.forwardRef<HTMLInputElement, CheckboxBubbleInputProps>(\n  ({ __scopeCheckbox, ...props }: ScopedProps<CheckboxBubbleInputProps>, forwardedRef) => {\n    const {\n      control,\n      hasConsumerStoppedPropagationRef,\n      checked,\n      defaultChecked,\n      required,\n      disabled,\n      name,\n      value,\n      form,\n      bubbleInput,\n      setBubbleInput,\n    } = useCheckboxContext(BUBBLE_INPUT_NAME, __scopeCheckbox);\n\n    const composedRefs = useComposedRefs(forwardedRef, setBubbleInput);\n    const prevChecked = usePrevious(checked);\n    const controlSize = useSize(control);\n\n    // Bubble checked change to parents (e.g form change event)\n    React.useEffect(() => {\n      const input = bubbleInput;\n      if (!input) return;\n\n      const inputProto = window.HTMLInputElement.prototype;\n      const descriptor = Object.getOwnPropertyDescriptor(\n        inputProto,\n        'checked'\n      ) as PropertyDescriptor;\n      const setChecked = descriptor.set;\n\n      const bubbles = !hasConsumerStoppedPropagationRef.current;\n      if (prevChecked !== checked && setChecked) {\n        const event = new Event('click', { bubbles });\n        input.indeterminate = isIndeterminate(checked);\n        setChecked.call(input, isIndeterminate(checked) ? false : checked);\n        input.dispatchEvent(event);\n      }\n    }, [bubbleInput, prevChecked, checked, hasConsumerStoppedPropagationRef]);\n\n    const defaultCheckedRef = React.useRef(isIndeterminate(checked) ? false : checked);\n    return (\n      <Primitive.input\n        type=\"checkbox\"\n        aria-hidden\n        defaultChecked={defaultChecked ?? defaultCheckedRef.current}\n        required={required}\n        disabled={disabled}\n        name={name}\n        value={value}\n        form={form}\n        {...props}\n        tabIndex={-1}\n        ref={composedRefs}\n        style={{\n          ...props.style,\n          ...controlSize,\n          position: 'absolute',\n          pointerEvents: 'none',\n          opacity: 0,\n          margin: 0,\n          // We transform because the input is absolutely positioned but we have\n          // rendered it **after** the button. This pulls it back to sit on top\n          // of the button.\n          transform: 'translateX(-100%)',\n        }}\n      />\n    );\n  }\n);\n\nCheckboxBubbleInput.displayName = BUBBLE_INPUT_NAME;\n\n/* ---------------------------------------------------------------------------------------------- */\n\nfunction isFunction(value: unknown): value is (...args: any[]) => any {\n  return typeof value === 'function';\n}\n\nfunction isIndeterminate(checked?: CheckedState): checked is 'indeterminate' {\n  return checked === 'indeterminate';\n}\n\nfunction getState(checked: CheckedState) {\n  return isIndeterminate(checked) ? 'indeterminate' : checked ? 'checked' : 'unchecked';\n}\n\nexport {\n  createCheckboxScope,\n  //\n  Checkbox,\n  CheckboxProvider,\n  CheckboxTrigger,\n  CheckboxIndicator,\n  CheckboxBubbleInput,\n  //\n  Checkbox as Root,\n  CheckboxProvider as Provider,\n  CheckboxTrigger as Trigger,\n  CheckboxIndicator as Indicator,\n  CheckboxBubbleInput as BubbleInput,\n};\nexport type {\n  CheckboxProps,\n  CheckboxProviderProps,\n  CheckboxTriggerProps,\n  CheckboxIndicatorProps,\n  CheckboxBubbleInputProps,\n  CheckedState,\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA,YAAY,WAAW;AAyGnB,SAgIM,UAhIN,KAgIM,YAhIN;AAvGJ,SAAS,0BAA0B;AAEnC,SAAS,4BAA4B;AAHrC,SAAS,uBAAuB;AAOhC,SAAS,iBAAiB;AAL1B,SAAS,4BAA4B;AAIrC,SAAS,gBAAgB;AAFzB,SAAS,mBAAmB;AAC5B,SAAS,eAAe;;;;;;;;;;;;AAMxB,IAAM,gBAAgB;AAGtB,IAAM,CAAC,uBAAuB,mBAAmB,CAAA,2KAAI,qBAAA,EAAmB,aAAa;AAqBrF,IAAM,CAAC,sBAAsB,kBAAkB,CAAA,GAC7C,sBAA4C,aAAa;AAkB3D,SAAS,iBACP,KAAA,EACA;IACA,MAAM,EACJ,eAAA,EACA,SAAS,WAAA,EACT,QAAA,EACA,cAAA,EACA,QAAA,EACA,IAAA,EACA,IAAA,EACA,eAAA,EACA,QAAA,EACA,QAAQ,IAAA,EAAA,mBAAA;IAER,0BAAA,EACF,GAAI;IAEJ,MAAM,CAAC,SAAS,UAAU,CAAA,gMAAI,uBAAA,EAAqB;QACjD,MAAM;QACN,aAAa,kBAAkB;QAC/B,UAAU;QACV,QAAQ;IACV,CAAC;IACD,MAAM,CAAC,SAAS,UAAU,CAAA,GAAU,sMAAA,QAAA,CAAmC,IAAI;IAC3E,MAAM,CAAC,aAAa,cAAc,CAAA,GAAU,sMAAA,QAAA,CAAkC,IAAI;IAClF,MAAM,mCAAyC,sMAAA,MAAA,CAAO,KAAK;IAC3D,MAAM,gBAAgB,UAClB,CAAC,CAAC,QAAQ,CAAC,CAAC,QAAQ,OAAA,CAAQ,MAAM,IAAA,iFAAA;IAElC;IAEJ,MAAM,UAAuC;QAC3C;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,gBAAgB,gBAAgB,cAAc,IAAI,QAAQ;QAC1D;QACA;QACA;IACF;IAEA,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,sBAAA;QACC,OAAO;QACN,GAAI,OAAA;QAEJ,UAAA,WAAW,0BAA0B,IAAI,2BAA2B,OAAO,IAAI;IAAA;AAGtF;AAMA,IAAM,eAAe;AAUrB,IAAM,kBAAwB,sMAAA,UAAA,CAC5B,CACE,EAAE,eAAA,EAAiB,SAAA,EAAW,OAAA,EAAS,GAAG,cAAc,CAAA,EACxD,iBACG;IACH,MAAM,EACJ,OAAA,EACA,KAAA,EACA,QAAA,EACA,OAAA,EACA,QAAA,EACA,UAAA,EACA,UAAA,EACA,gCAAA,EACA,aAAA,EACA,WAAA,EACF,GAAI,mBAAmB,cAAc,eAAe;IACpD,MAAM,+LAAe,kBAAA,EAAgB,cAAc,UAAU;IAE7D,MAAM,yBAA+B,sMAAA,MAAA,CAAO,OAAO;IAC7C,sMAAA,SAAA,CAAU,MAAM;QACpB,MAAM,OAAO,SAAS;QACtB,IAAI,MAAM;YACR,MAAM,QAAQ,IAAM,WAAW,uBAAuB,OAAO;YAC7D,KAAK,gBAAA,CAAiB,SAAS,KAAK;YACpC,OAAO,IAAM,KAAK,mBAAA,CAAoB,SAAS,KAAK;QACtD;IACF,GAAG;QAAC;QAAS,UAAU;KAAC;IAExB,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,wKAAC,YAAA,CAAU,MAAA,EAAV;QACC,MAAK;QACL,MAAK;QACL,gBAAc,gBAAgB,OAAO,IAAI,UAAU;QACnD,iBAAe;QACf,cAAY,SAAS,OAAO;QAC5B,iBAAe,WAAW,KAAK,KAAA;QAC/B;QACA;QACC,GAAG,aAAA;QACJ,KAAK;QACL,4KAAW,uBAAA,EAAqB,WAAW,CAAC,UAAU;YAEpD,IAAI,MAAM,GAAA,KAAQ,QAAS,CAAA,MAAM,cAAA,CAAe;QAClD,CAAC;QACD,0KAAS,uBAAA,EAAqB,SAAS,CAAC,UAAU;YAChD,WAAW,CAAC,cAAiB,gBAAgB,WAAW,IAAI,OAAO,CAAC,WAAY;YAChF,IAAI,eAAe,eAAe;gBAChC,iCAAiC,OAAA,GAAU,MAAM,oBAAA,CAAqB;gBAMtE,IAAI,CAAC,iCAAiC,OAAA,CAAS,CAAA,MAAM,eAAA,CAAgB;YACvE;QACF,CAAC;IAAA;AAGP;AAGF,gBAAgB,WAAA,GAAc;AAe9B,IAAM,WAAiB,sMAAA,UAAA,CACrB,CAAC,OAAmC,iBAAiB;IACnD,MAAM,EACJ,eAAA,EACA,IAAA,EACA,OAAA,EACA,cAAA,EACA,QAAA,EACA,QAAA,EACA,KAAA,EACA,eAAA,EACA,IAAA,EACA,GAAG,eACL,GAAI;IAEJ,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,kBAAA;QACC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QAEA,4BAA4B,CAAC,EAAE,aAAA,CAAc,CAAA,GAC3C,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,OAAA,EAAA,uNAAA,CAAA,WAAA,EAAA;gBACE,UAAA;oBAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,iBAAA;wBACE,GAAG,aAAA;wBACJ,KAAK;wBAEL;oBAAA;oBAED,iBACC,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,qBAAA;wBAEC;oBAAA;iBACF;YAAA,CAEJ;IAAA;AAIR;AAGF,SAAS,WAAA,GAAc;AAMvB,IAAM,iBAAiB;AAYvB,IAAM,oBAA0B,sMAAA,UAAA,CAC9B,CAAC,OAA4C,iBAAiB;IAC5D,MAAM,EAAE,eAAA,EAAiB,UAAA,EAAY,GAAG,eAAe,CAAA,GAAI;IAC3D,MAAM,UAAU,mBAAmB,gBAAgB,eAAe;IAClE,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,uKAAC,WAAA,EAAA;QACC,SAAS,cAAc,gBAAgB,QAAQ,OAAO,KAAK,QAAQ,OAAA,KAAY;QAE/E,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,wKAAC,YAAA,CAAU,IAAA,EAAV;YACC,cAAY,SAAS,QAAQ,OAAO;YACpC,iBAAe,QAAQ,QAAA,GAAW,KAAK,KAAA;YACtC,GAAG,cAAA;YACJ,KAAK;YACL,OAAO;gBAAE,eAAe;gBAAQ,GAAG,MAAM,KAAA;YAAM;QAAA;IACjD;AAGN;AAGF,kBAAkB,WAAA,GAAc;AAMhC,IAAM,oBAAoB;AAK1B,IAAM,sBAA4B,sMAAA,UAAA,CAChC,CAAC,EAAE,eAAA,EAAiB,GAAG,MAAM,CAAA,EAA0C,iBAAiB;IACtF,MAAM,EACJ,OAAA,EACA,gCAAA,EACA,OAAA,EACA,cAAA,EACA,QAAA,EACA,QAAA,EACA,IAAA,EACA,KAAA,EACA,IAAA,EACA,WAAA,EACA,cAAA,EACF,GAAI,mBAAmB,mBAAmB,eAAe;IAEzD,MAAM,gBAAe,iMAAA,EAAgB,cAAc,cAAc;IACjE,MAAM,8LAAc,cAAA,EAAY,OAAO;IACvC,MAAM,cAAc,sLAAA,EAAQ,OAAO;IAG7B,sMAAA,SAAA,CAAU,MAAM;QACpB,MAAM,QAAQ;QACd,IAAI,CAAC,MAAO,CAAA;QAEZ,MAAM,aAAa,OAAO,gBAAA,CAAiB,SAAA;QAC3C,MAAM,aAAa,OAAO,wBAAA,CACxB,YACA;QAEF,MAAM,aAAa,WAAW,GAAA;QAE9B,MAAM,UAAU,CAAC,iCAAiC,OAAA;QAClD,IAAI,gBAAgB,WAAW,YAAY;YACzC,MAAM,QAAQ,IAAI,MAAM,SAAS;gBAAE;YAAQ,CAAC;YAC5C,MAAM,aAAA,GAAgB,gBAAgB,OAAO;YAC7C,WAAW,IAAA,CAAK,OAAO,gBAAgB,OAAO,IAAI,QAAQ,OAAO;YACjE,MAAM,aAAA,CAAc,KAAK;QAC3B;IACF,GAAG;QAAC;QAAa;QAAa;QAAS,gCAAgC;KAAC;IAExE,MAAM,oBAA0B,sMAAA,MAAA,CAAO,gBAAgB,OAAO,IAAI,QAAQ,OAAO;IACjF,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,wKAAC,YAAA,CAAU,KAAA,EAAV;QACC,MAAK;QACL,eAAW;QACX,gBAAgB,kBAAkB,kBAAkB,OAAA;QACpD;QACA;QACA;QACA;QACA;QACC,GAAG,KAAA;QACJ,UAAU,CAAA;QACV,KAAK;QACL,OAAO;YACL,GAAG,MAAM,KAAA;YACT,GAAG,WAAA;YACH,UAAU;YACV,eAAe;YACf,SAAS;YACT,QAAQ;YAAA,sEAAA;YAAA,qEAAA;YAAA,iBAAA;YAIR,WAAW;QACb;IAAA;AAGN;AAGF,oBAAoB,WAAA,GAAc;AAIlC,SAAS,WAAW,KAAA,EAAkD;IACpE,OAAO,OAAO,UAAU;AAC1B;AAEA,SAAS,gBAAgB,OAAA,EAAoD;IAC3E,OAAO,YAAY;AACrB;AAEA,SAAS,SAAS,OAAA,EAAuB;IACvC,OAAO,gBAAgB,OAAO,IAAI,kBAAkB,UAAU,YAAY;AAC5E", "ignoreList": [0]}}, {"offset": {"line": 2251, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2257, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/%40radix-ui/react-roving-focus/src/roving-focus-group.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { createCollection } from '@radix-ui/react-collection';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useId } from '@radix-ui/react-id';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { useDirection } from '@radix-ui/react-direction';\n\nimport type { Scope } from '@radix-ui/react-context';\n\nconst ENTRY_FOCUS = 'rovingFocusGroup.onEntryFocus';\nconst EVENT_OPTIONS = { bubbles: false, cancelable: true };\n\n/* -------------------------------------------------------------------------------------------------\n * RovingFocusGroup\n * -----------------------------------------------------------------------------------------------*/\n\nconst GROUP_NAME = 'RovingFocusGroup';\n\ntype ItemData = { id: string; focusable: boolean; active: boolean };\nconst [Collection, useCollection, createCollectionScope] = createCollection<\n  HTMLSpanElement,\n  ItemData\n>(GROUP_NAME);\n\ntype ScopedProps<P> = P & { __scopeRovingFocusGroup?: Scope };\nconst [createRovingFocusGroupContext, createRovingFocusGroupScope] = createContextScope(\n  GROUP_NAME,\n  [createCollectionScope]\n);\n\ntype Orientation = React.AriaAttributes['aria-orientation'];\ntype Direction = 'ltr' | 'rtl';\n\ninterface RovingFocusGroupOptions {\n  /**\n   * The orientation of the group.\n   * Mainly so arrow navigation is done accordingly (left & right vs. up & down)\n   */\n  orientation?: Orientation;\n  /**\n   * The direction of navigation between items.\n   */\n  dir?: Direction;\n  /**\n   * Whether keyboard navigation should loop around\n   * @defaultValue false\n   */\n  loop?: boolean;\n}\n\ntype RovingContextValue = RovingFocusGroupOptions & {\n  currentTabStopId: string | null;\n  onItemFocus(tabStopId: string): void;\n  onItemShiftTab(): void;\n  onFocusableItemAdd(): void;\n  onFocusableItemRemove(): void;\n};\n\nconst [RovingFocusProvider, useRovingFocusContext] =\n  createRovingFocusGroupContext<RovingContextValue>(GROUP_NAME);\n\ntype RovingFocusGroupElement = RovingFocusGroupImplElement;\ninterface RovingFocusGroupProps extends RovingFocusGroupImplProps {}\n\nconst RovingFocusGroup = React.forwardRef<RovingFocusGroupElement, RovingFocusGroupProps>(\n  (props: ScopedProps<RovingFocusGroupProps>, forwardedRef) => {\n    return (\n      <Collection.Provider scope={props.__scopeRovingFocusGroup}>\n        <Collection.Slot scope={props.__scopeRovingFocusGroup}>\n          <RovingFocusGroupImpl {...props} ref={forwardedRef} />\n        </Collection.Slot>\n      </Collection.Provider>\n    );\n  }\n);\n\nRovingFocusGroup.displayName = GROUP_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype RovingFocusGroupImplElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface RovingFocusGroupImplProps\n  extends Omit<PrimitiveDivProps, 'dir'>,\n    RovingFocusGroupOptions {\n  currentTabStopId?: string | null;\n  defaultCurrentTabStopId?: string;\n  onCurrentTabStopIdChange?: (tabStopId: string | null) => void;\n  onEntryFocus?: (event: Event) => void;\n  preventScrollOnEntryFocus?: boolean;\n}\n\nconst RovingFocusGroupImpl = React.forwardRef<\n  RovingFocusGroupImplElement,\n  RovingFocusGroupImplProps\n>((props: ScopedProps<RovingFocusGroupImplProps>, forwardedRef) => {\n  const {\n    __scopeRovingFocusGroup,\n    orientation,\n    loop = false,\n    dir,\n    currentTabStopId: currentTabStopIdProp,\n    defaultCurrentTabStopId,\n    onCurrentTabStopIdChange,\n    onEntryFocus,\n    preventScrollOnEntryFocus = false,\n    ...groupProps\n  } = props;\n  const ref = React.useRef<RovingFocusGroupImplElement>(null);\n  const composedRefs = useComposedRefs(forwardedRef, ref);\n  const direction = useDirection(dir);\n  const [currentTabStopId, setCurrentTabStopId] = useControllableState({\n    prop: currentTabStopIdProp,\n    defaultProp: defaultCurrentTabStopId ?? null,\n    onChange: onCurrentTabStopIdChange,\n    caller: GROUP_NAME,\n  });\n  const [isTabbingBackOut, setIsTabbingBackOut] = React.useState(false);\n  const handleEntryFocus = useCallbackRef(onEntryFocus);\n  const getItems = useCollection(__scopeRovingFocusGroup);\n  const isClickFocusRef = React.useRef(false);\n  const [focusableItemsCount, setFocusableItemsCount] = React.useState(0);\n\n  React.useEffect(() => {\n    const node = ref.current;\n    if (node) {\n      node.addEventListener(ENTRY_FOCUS, handleEntryFocus);\n      return () => node.removeEventListener(ENTRY_FOCUS, handleEntryFocus);\n    }\n  }, [handleEntryFocus]);\n\n  return (\n    <RovingFocusProvider\n      scope={__scopeRovingFocusGroup}\n      orientation={orientation}\n      dir={direction}\n      loop={loop}\n      currentTabStopId={currentTabStopId}\n      onItemFocus={React.useCallback(\n        (tabStopId) => setCurrentTabStopId(tabStopId),\n        [setCurrentTabStopId]\n      )}\n      onItemShiftTab={React.useCallback(() => setIsTabbingBackOut(true), [])}\n      onFocusableItemAdd={React.useCallback(\n        () => setFocusableItemsCount((prevCount) => prevCount + 1),\n        []\n      )}\n      onFocusableItemRemove={React.useCallback(\n        () => setFocusableItemsCount((prevCount) => prevCount - 1),\n        []\n      )}\n    >\n      <Primitive.div\n        tabIndex={isTabbingBackOut || focusableItemsCount === 0 ? -1 : 0}\n        data-orientation={orientation}\n        {...groupProps}\n        ref={composedRefs}\n        style={{ outline: 'none', ...props.style }}\n        onMouseDown={composeEventHandlers(props.onMouseDown, () => {\n          isClickFocusRef.current = true;\n        })}\n        onFocus={composeEventHandlers(props.onFocus, (event) => {\n          // We normally wouldn't need this check, because we already check\n          // that the focus is on the current target and not bubbling to it.\n          // We do this because Safari doesn't focus buttons when clicked, and\n          // instead, the wrapper will get focused and not through a bubbling event.\n          const isKeyboardFocus = !isClickFocusRef.current;\n\n          if (event.target === event.currentTarget && isKeyboardFocus && !isTabbingBackOut) {\n            const entryFocusEvent = new CustomEvent(ENTRY_FOCUS, EVENT_OPTIONS);\n            event.currentTarget.dispatchEvent(entryFocusEvent);\n\n            if (!entryFocusEvent.defaultPrevented) {\n              const items = getItems().filter((item) => item.focusable);\n              const activeItem = items.find((item) => item.active);\n              const currentItem = items.find((item) => item.id === currentTabStopId);\n              const candidateItems = [activeItem, currentItem, ...items].filter(\n                Boolean\n              ) as typeof items;\n              const candidateNodes = candidateItems.map((item) => item.ref.current!);\n              focusFirst(candidateNodes, preventScrollOnEntryFocus);\n            }\n          }\n\n          isClickFocusRef.current = false;\n        })}\n        onBlur={composeEventHandlers(props.onBlur, () => setIsTabbingBackOut(false))}\n      />\n    </RovingFocusProvider>\n  );\n});\n\n/* -------------------------------------------------------------------------------------------------\n * RovingFocusGroupItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_NAME = 'RovingFocusGroupItem';\n\ntype RovingFocusItemElement = React.ComponentRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = React.ComponentPropsWithoutRef<typeof Primitive.span>;\ninterface RovingFocusItemProps extends Omit<PrimitiveSpanProps, 'children'> {\n  tabStopId?: string;\n  focusable?: boolean;\n  active?: boolean;\n  children?:\n    | React.ReactNode\n    | ((props: { hasTabStop: boolean; isCurrentTabStop: boolean }) => React.ReactNode);\n}\n\nconst RovingFocusGroupItem = React.forwardRef<RovingFocusItemElement, RovingFocusItemProps>(\n  (props: ScopedProps<RovingFocusItemProps>, forwardedRef) => {\n    const {\n      __scopeRovingFocusGroup,\n      focusable = true,\n      active = false,\n      tabStopId,\n      children,\n      ...itemProps\n    } = props;\n    const autoId = useId();\n    const id = tabStopId || autoId;\n    const context = useRovingFocusContext(ITEM_NAME, __scopeRovingFocusGroup);\n    const isCurrentTabStop = context.currentTabStopId === id;\n    const getItems = useCollection(__scopeRovingFocusGroup);\n\n    const { onFocusableItemAdd, onFocusableItemRemove, currentTabStopId } = context;\n\n    React.useEffect(() => {\n      if (focusable) {\n        onFocusableItemAdd();\n        return () => onFocusableItemRemove();\n      }\n    }, [focusable, onFocusableItemAdd, onFocusableItemRemove]);\n\n    return (\n      <Collection.ItemSlot\n        scope={__scopeRovingFocusGroup}\n        id={id}\n        focusable={focusable}\n        active={active}\n      >\n        <Primitive.span\n          tabIndex={isCurrentTabStop ? 0 : -1}\n          data-orientation={context.orientation}\n          {...itemProps}\n          ref={forwardedRef}\n          onMouseDown={composeEventHandlers(props.onMouseDown, (event) => {\n            // We prevent focusing non-focusable items on `mousedown`.\n            // Even though the item has tabIndex={-1}, that only means take it out of the tab order.\n            if (!focusable) event.preventDefault();\n            // Safari doesn't focus a button when clicked so we run our logic on mousedown also\n            else context.onItemFocus(id);\n          })}\n          onFocus={composeEventHandlers(props.onFocus, () => context.onItemFocus(id))}\n          onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n            if (event.key === 'Tab' && event.shiftKey) {\n              context.onItemShiftTab();\n              return;\n            }\n\n            if (event.target !== event.currentTarget) return;\n\n            const focusIntent = getFocusIntent(event, context.orientation, context.dir);\n\n            if (focusIntent !== undefined) {\n              if (event.metaKey || event.ctrlKey || event.altKey || event.shiftKey) return;\n              event.preventDefault();\n              const items = getItems().filter((item) => item.focusable);\n              let candidateNodes = items.map((item) => item.ref.current!);\n\n              if (focusIntent === 'last') candidateNodes.reverse();\n              else if (focusIntent === 'prev' || focusIntent === 'next') {\n                if (focusIntent === 'prev') candidateNodes.reverse();\n                const currentIndex = candidateNodes.indexOf(event.currentTarget);\n                candidateNodes = context.loop\n                  ? wrapArray(candidateNodes, currentIndex + 1)\n                  : candidateNodes.slice(currentIndex + 1);\n              }\n\n              /**\n               * Imperative focus during keydown is risky so we prevent React's batching updates\n               * to avoid potential bugs. See: https://github.com/facebook/react/issues/20332\n               */\n              setTimeout(() => focusFirst(candidateNodes));\n            }\n          })}\n        >\n          {typeof children === 'function'\n            ? children({ isCurrentTabStop, hasTabStop: currentTabStopId != null })\n            : children}\n        </Primitive.span>\n      </Collection.ItemSlot>\n    );\n  }\n);\n\nRovingFocusGroupItem.displayName = ITEM_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\n// prettier-ignore\nconst MAP_KEY_TO_FOCUS_INTENT: Record<string, FocusIntent> = {\n  ArrowLeft: 'prev', ArrowUp: 'prev',\n  ArrowRight: 'next', ArrowDown: 'next',\n  PageUp: 'first', Home: 'first',\n  PageDown: 'last', End: 'last',\n};\n\nfunction getDirectionAwareKey(key: string, dir?: Direction) {\n  if (dir !== 'rtl') return key;\n  return key === 'ArrowLeft' ? 'ArrowRight' : key === 'ArrowRight' ? 'ArrowLeft' : key;\n}\n\ntype FocusIntent = 'first' | 'last' | 'prev' | 'next';\n\nfunction getFocusIntent(event: React.KeyboardEvent, orientation?: Orientation, dir?: Direction) {\n  const key = getDirectionAwareKey(event.key, dir);\n  if (orientation === 'vertical' && ['ArrowLeft', 'ArrowRight'].includes(key)) return undefined;\n  if (orientation === 'horizontal' && ['ArrowUp', 'ArrowDown'].includes(key)) return undefined;\n  return MAP_KEY_TO_FOCUS_INTENT[key];\n}\n\nfunction focusFirst(candidates: HTMLElement[], preventScroll = false) {\n  const PREVIOUSLY_FOCUSED_ELEMENT = document.activeElement;\n  for (const candidate of candidates) {\n    // if focus is already where we want to go, we don't want to keep going through the candidates\n    if (candidate === PREVIOUSLY_FOCUSED_ELEMENT) return;\n    candidate.focus({ preventScroll });\n    if (document.activeElement !== PREVIOUSLY_FOCUSED_ELEMENT) return;\n  }\n}\n\n/**\n * Wraps an array around itself at a given start index\n * Example: `wrapArray(['a', 'b', 'c', 'd'], 2) === ['c', 'd', 'a', 'b']`\n */\nfunction wrapArray<T>(array: T[], startIndex: number) {\n  return array.map<T>((_, index) => array[(startIndex + index) % array.length]!);\n}\n\nconst Root = RovingFocusGroup;\nconst Item = RovingFocusGroupItem;\n\nexport {\n  createRovingFocusGroupScope,\n  //\n  RovingFocusGroup,\n  RovingFocusGroupItem,\n  //\n  Root,\n  Item,\n};\nexport type { RovingFocusGroupProps, RovingFocusItemProps };\n"], "names": [], "mappings": ";;;;;;;;AAAA,YAAY,WAAW;AAyEb;AAvEV,SAAS,wBAAwB;AAEjC,SAAS,0BAA0B;AADnC,SAAS,uBAAuB;AAMhC,SAAS,oBAAoB;AAD7B,SAAS,4BAA4B;AADrC,SAAS,sBAAsB;AAD/B,SAAS,iBAAiB;AAL1B,SAAS,4BAA4B;AAIrC,SAAS,aAAa;;;;;;;;;;;;;AAQtB,IAAM,cAAc;AACpB,IAAM,gBAAgB;IAAE,SAAS;IAAO,YAAY;AAAK;AAMzD,IAAM,aAAa;AAGnB,IAAM,CAAC,YAAY,eAAe,qBAAqB,CAAA,8KAAI,mBAAA,EAGzD,UAAU;AAGZ,IAAM,CAAC,+BAA+B,2BAA2B,CAAA,2KAAI,qBAAA,EACnE,YACA;IAAC,qBAAqB;CAAA;AA+BxB,IAAM,CAAC,qBAAqB,qBAAqB,CAAA,GAC/C,8BAAkD,UAAU;AAK9D,IAAM,mBAAyB,sMAAA,UAAA,CAC7B,CAAC,OAA2C,iBAAiB;IAC3D,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,WAAW,QAAA,EAAX;QAAoB,OAAO,MAAM,uBAAA;QAChC,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,WAAW,IAAA,EAAX;YAAgB,OAAO,MAAM,uBAAA;YAC5B,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,sBAAA;gBAAsB,GAAG,KAAA;gBAAO,KAAK;YAAA,CAAc;QAAA,CACtD;IAAA,CACF;AAEJ;AAGF,iBAAiB,WAAA,GAAc;AAgB/B,IAAM,uBAA6B,sMAAA,UAAA,CAGjC,CAAC,OAA+C,iBAAiB;IACjE,MAAM,EACJ,uBAAA,EACA,WAAA,EACA,OAAO,KAAA,EACP,GAAA,EACA,kBAAkB,oBAAA,EAClB,uBAAA,EACA,wBAAA,EACA,YAAA,EACA,4BAA4B,KAAA,EAC5B,GAAG,YACL,GAAI;IACJ,MAAM,MAAY,sMAAA,MAAA,CAAoC,IAAI;IAC1D,MAAM,+LAAe,kBAAA,EAAgB,cAAc,GAAG;IACtD,MAAM,YAAY,yLAAA,EAAa,GAAG;IAClC,MAAM,CAAC,kBAAkB,mBAAmB,CAAA,gMAAI,uBAAA,EAAqB;QACnE,MAAM;QACN,aAAa,2BAA2B;QACxC,UAAU;QACV,QAAQ;IACV,CAAC;IACD,MAAM,CAAC,kBAAkB,mBAAmB,CAAA,GAAU,sMAAA,QAAA,CAAS,KAAK;IACpE,MAAM,mBAAmB,wMAAA,EAAe,YAAY;IACpD,MAAM,WAAW,cAAc,uBAAuB;IACtD,MAAM,kBAAwB,sMAAA,MAAA,CAAO,KAAK;IAC1C,MAAM,CAAC,qBAAqB,sBAAsB,CAAA,GAAU,sMAAA,QAAA,CAAS,CAAC;IAEhE,sMAAA,SAAA,CAAU,MAAM;QACpB,MAAM,OAAO,IAAI,OAAA;QACjB,IAAI,MAAM;YACR,KAAK,gBAAA,CAAiB,aAAa,gBAAgB;YACnD,OAAO,IAAM,KAAK,mBAAA,CAAoB,aAAa,gBAAgB;QACrE;IACF,GAAG;QAAC,gBAAgB;KAAC;IAErB,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,qBAAA;QACC,OAAO;QACP;QACA,KAAK;QACL;QACA;QACA,aAAmB,sMAAA,WAAA,CACjB,CAAC,YAAc,oBAAoB,SAAS,GAC5C;YAAC,mBAAmB;SAAA;QAEtB,gBAAsB,sMAAA,WAAA,CAAY,IAAM,oBAAoB,IAAI,GAAG,CAAC,CAAC;QACrE,oBAA0B,sMAAA,WAAA,CACxB,IAAM,uBAAuB,CAAC,YAAc,YAAY,CAAC,GACzD,CAAC,CAAA;QAEH,uBAA6B,sMAAA,WAAA,CAC3B,IAAM,uBAAuB,CAAC,YAAc,YAAY,CAAC,GACzD,CAAC,CAAA;QAGH,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,wKAAC,YAAA,CAAU,GAAA,EAAV;YACC,UAAU,oBAAoB,wBAAwB,IAAI,CAAA,IAAK;YAC/D,oBAAkB;YACjB,GAAG,UAAA;YACJ,KAAK;YACL,OAAO;gBAAE,SAAS;gBAAQ,GAAG,MAAM,KAAA;YAAM;YACzC,8KAAa,uBAAA,EAAqB,MAAM,WAAA,EAAa,MAAM;gBACzD,gBAAgB,OAAA,GAAU;YAC5B,CAAC;YACD,0KAAS,uBAAA,EAAqB,MAAM,OAAA,EAAS,CAAC,UAAU;gBAKtD,MAAM,kBAAkB,CAAC,gBAAgB,OAAA;gBAEzC,IAAI,MAAM,MAAA,KAAW,MAAM,aAAA,IAAiB,mBAAmB,CAAC,kBAAkB;oBAChF,MAAM,kBAAkB,IAAI,YAAY,aAAa,aAAa;oBAClE,MAAM,aAAA,CAAc,aAAA,CAAc,eAAe;oBAEjD,IAAI,CAAC,gBAAgB,gBAAA,EAAkB;wBACrC,MAAM,QAAQ,SAAS,EAAE,MAAA,CAAO,CAAC,OAAS,KAAK,SAAS;wBACxD,MAAM,aAAa,MAAM,IAAA,CAAK,CAAC,OAAS,KAAK,MAAM;wBACnD,MAAM,cAAc,MAAM,IAAA,CAAK,CAAC,OAAS,KAAK,EAAA,KAAO,gBAAgB;wBACrE,MAAM,iBAAiB;4BAAC;4BAAY,aAAa;+BAAG,KAAK;yBAAA,CAAE,MAAA,CACzD;wBAEF,MAAM,iBAAiB,eAAe,GAAA,CAAI,CAAC,OAAS,KAAK,GAAA,CAAI,OAAQ;wBACrE,WAAW,gBAAgB,yBAAyB;oBACtD;gBACF;gBAEA,gBAAgB,OAAA,GAAU;YAC5B,CAAC;YACD,SAAQ,uLAAA,EAAqB,MAAM,MAAA,EAAQ,IAAM,oBAAoB,KAAK,CAAC;QAAA;IAC7E;AAGN,CAAC;AAMD,IAAM,YAAY;AAalB,IAAM,uBAA6B,sMAAA,UAAA,CACjC,CAAC,OAA0C,iBAAiB;IAC1D,MAAM,EACJ,uBAAA,EACA,YAAY,IAAA,EACZ,SAAS,KAAA,EACT,SAAA,EACA,QAAA,EACA,GAAG,WACL,GAAI;IACJ,MAAM,4KAAS,QAAA,CAAM;IACrB,MAAM,KAAK,aAAa;IACxB,MAAM,UAAU,sBAAsB,WAAW,uBAAuB;IACxE,MAAM,mBAAmB,QAAQ,gBAAA,KAAqB;IACtD,MAAM,WAAW,cAAc,uBAAuB;IAEtD,MAAM,EAAE,kBAAA,EAAoB,qBAAA,EAAuB,gBAAA,CAAiB,CAAA,GAAI;IAElE,sMAAA,SAAA,CAAU,MAAM;QACpB,IAAI,WAAW;YACb,mBAAmB;YACnB,OAAO,IAAM,sBAAsB;QACrC;IACF,GAAG;QAAC;QAAW;QAAoB,qBAAqB;KAAC;IAEzD,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,WAAW,QAAA,EAAX;QACC,OAAO;QACP;QACA;QACA;QAEA,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,kLAAA,CAAU,IAAA,EAAV;YACC,UAAU,mBAAmB,IAAI,CAAA;YACjC,oBAAkB,QAAQ,WAAA;YACzB,GAAG,SAAA;YACJ,KAAK;YACL,8KAAa,uBAAA,EAAqB,MAAM,WAAA,EAAa,CAAC,UAAU;gBAG9D,IAAI,CAAC,UAAW,CAAA,MAAM,cAAA,CAAe;qBAEhC,QAAQ,WAAA,CAAY,EAAE;YAC7B,CAAC;YACD,0KAAS,uBAAA,EAAqB,MAAM,OAAA,EAAS,IAAM,QAAQ,WAAA,CAAY,EAAE,CAAC;YAC1E,4KAAW,uBAAA,EAAqB,MAAM,SAAA,EAAW,CAAC,UAAU;gBAC1D,IAAI,MAAM,GAAA,KAAQ,SAAS,MAAM,QAAA,EAAU;oBACzC,QAAQ,cAAA,CAAe;oBACvB;gBACF;gBAEA,IAAI,MAAM,MAAA,KAAW,MAAM,aAAA,CAAe,CAAA;gBAE1C,MAAM,cAAc,eAAe,OAAO,QAAQ,WAAA,EAAa,QAAQ,GAAG;gBAE1E,IAAI,gBAAgB,KAAA,GAAW;oBAC7B,IAAI,MAAM,OAAA,IAAW,MAAM,OAAA,IAAW,MAAM,MAAA,IAAU,MAAM,QAAA,CAAU,CAAA;oBACtE,MAAM,cAAA,CAAe;oBACrB,MAAM,QAAQ,SAAS,EAAE,MAAA,CAAO,CAAC,OAAS,KAAK,SAAS;oBACxD,IAAI,iBAAiB,MAAM,GAAA,CAAI,CAAC,OAAS,KAAK,GAAA,CAAI,OAAQ;oBAE1D,IAAI,gBAAgB,OAAQ,CAAA,eAAe,OAAA,CAAQ;yBAAA,IAC1C,gBAAgB,UAAU,gBAAgB,QAAQ;wBACzD,IAAI,gBAAgB,OAAQ,CAAA,eAAe,OAAA,CAAQ;wBACnD,MAAM,eAAe,eAAe,OAAA,CAAQ,MAAM,aAAa;wBAC/D,iBAAiB,QAAQ,IAAA,GACrB,UAAU,gBAAgB,eAAe,CAAC,IAC1C,eAAe,KAAA,CAAM,eAAe,CAAC;oBAC3C;oBAMA,WAAW,IAAM,WAAW,cAAc,CAAC;gBAC7C;YACF,CAAC;YAEA,UAAA,OAAO,aAAa,aACjB,SAAS;gBAAE;gBAAkB,YAAY,oBAAoB;YAAK,CAAC,IACnE;QAAA;IACN;AAGN;AAGF,qBAAqB,WAAA,GAAc;AAKnC,IAAM,0BAAuD;IAC3D,WAAW;IAAQ,SAAS;IAC5B,YAAY;IAAQ,WAAW;IAC/B,QAAQ;IAAS,MAAM;IACvB,UAAU;IAAQ,KAAK;AACzB;AAEA,SAAS,qBAAqB,GAAA,EAAa,GAAA,EAAiB;IAC1D,IAAI,QAAQ,MAAO,CAAA,OAAO;IAC1B,OAAO,QAAQ,cAAc,eAAe,QAAQ,eAAe,cAAc;AACnF;AAIA,SAAS,eAAe,KAAA,EAA4B,WAAA,EAA2B,GAAA,EAAiB;IAC9F,MAAM,MAAM,qBAAqB,MAAM,GAAA,EAAK,GAAG;IAC/C,IAAI,gBAAgB,cAAc;QAAC;QAAa,YAAY;KAAA,CAAE,QAAA,CAAS,GAAG,EAAG,CAAA,OAAO,KAAA;IACpF,IAAI,gBAAgB,gBAAgB;QAAC;QAAW,WAAW;KAAA,CAAE,QAAA,CAAS,GAAG,EAAG,CAAA,OAAO,KAAA;IACnF,OAAO,uBAAA,CAAwB,GAAG,CAAA;AACpC;AAEA,SAAS,WAAW,UAAA,EAA2B,gBAAgB,KAAA,EAAO;IACpE,MAAM,6BAA6B,SAAS,aAAA;IAC5C,KAAA,MAAW,aAAa,WAAY;QAElC,IAAI,cAAc,2BAA4B,CAAA;QAC9C,UAAU,KAAA,CAAM;YAAE;QAAc,CAAC;QACjC,IAAI,SAAS,aAAA,KAAkB,2BAA4B,CAAA;IAC7D;AACF;AAMA,SAAS,UAAa,KAAA,EAAY,UAAA,EAAoB;IACpD,OAAO,MAAM,GAAA,CAAO,CAAC,GAAG,QAAU,KAAA,CAAA,CAAO,aAAa,KAAA,IAAS,MAAM,MAAM,CAAE;AAC/E;AAEA,IAAM,OAAO;AACb,IAAM,OAAO", "ignoreList": [0]}}, {"offset": {"line": 2491, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2497, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/%40radix-ui/react-radio-group/src/radio-group.tsx", "file:///home/<USER>/Desktop/templgen/node_modules/%40radix-ui/react-radio-group/src/radio.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport * as RovingFocusGroup from '@radix-ui/react-roving-focus';\nimport { createRovingFocusGroupScope } from '@radix-ui/react-roving-focus';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { useDirection } from '@radix-ui/react-direction';\nimport { Radio, RadioIndicator, createRadioScope } from './radio';\n\nimport type { Scope } from '@radix-ui/react-context';\n\nconst ARROW_KEYS = ['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'];\n\n/* -------------------------------------------------------------------------------------------------\n * RadioGroup\n * -----------------------------------------------------------------------------------------------*/\nconst RADIO_GROUP_NAME = 'RadioGroup';\n\ntype ScopedProps<P> = P & { __scopeRadioGroup?: Scope };\nconst [createRadioGroupContext, createRadioGroupScope] = createContextScope(RADIO_GROUP_NAME, [\n  createRovingFocusGroupScope,\n  createRadioScope,\n]);\nconst useRovingFocusGroupScope = createRovingFocusGroupScope();\nconst useRadioScope = createRadioScope();\n\ntype RadioGroupContextValue = {\n  name?: string;\n  required: boolean;\n  disabled: boolean;\n  value: string | null;\n  onValueChange(value: string): void;\n};\n\nconst [RadioGroupProvider, useRadioGroupContext] =\n  createRadioGroupContext<RadioGroupContextValue>(RADIO_GROUP_NAME);\n\ntype RadioGroupElement = React.ComponentRef<typeof Primitive.div>;\ntype RovingFocusGroupProps = React.ComponentPropsWithoutRef<typeof RovingFocusGroup.Root>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface RadioGroupProps extends PrimitiveDivProps {\n  name?: RadioGroupContextValue['name'];\n  required?: React.ComponentPropsWithoutRef<typeof Radio>['required'];\n  disabled?: React.ComponentPropsWithoutRef<typeof Radio>['disabled'];\n  dir?: RovingFocusGroupProps['dir'];\n  orientation?: RovingFocusGroupProps['orientation'];\n  loop?: RovingFocusGroupProps['loop'];\n  defaultValue?: string;\n  value?: string | null;\n  onValueChange?: RadioGroupContextValue['onValueChange'];\n}\n\nconst RadioGroup = React.forwardRef<RadioGroupElement, RadioGroupProps>(\n  (props: ScopedProps<RadioGroupProps>, forwardedRef) => {\n    const {\n      __scopeRadioGroup,\n      name,\n      defaultValue,\n      value: valueProp,\n      required = false,\n      disabled = false,\n      orientation,\n      dir,\n      loop = true,\n      onValueChange,\n      ...groupProps\n    } = props;\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeRadioGroup);\n    const direction = useDirection(dir);\n    const [value, setValue] = useControllableState({\n      prop: valueProp,\n      defaultProp: defaultValue ?? null,\n      onChange: onValueChange as (value: string | null) => void,\n      caller: RADIO_GROUP_NAME,\n    });\n\n    return (\n      <RadioGroupProvider\n        scope={__scopeRadioGroup}\n        name={name}\n        required={required}\n        disabled={disabled}\n        value={value}\n        onValueChange={setValue}\n      >\n        <RovingFocusGroup.Root\n          asChild\n          {...rovingFocusGroupScope}\n          orientation={orientation}\n          dir={direction}\n          loop={loop}\n        >\n          <Primitive.div\n            role=\"radiogroup\"\n            aria-required={required}\n            aria-orientation={orientation}\n            data-disabled={disabled ? '' : undefined}\n            dir={direction}\n            {...groupProps}\n            ref={forwardedRef}\n          />\n        </RovingFocusGroup.Root>\n      </RadioGroupProvider>\n    );\n  }\n);\n\nRadioGroup.displayName = RADIO_GROUP_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * RadioGroupItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_NAME = 'RadioGroupItem';\n\ntype RadioGroupItemElement = React.ComponentRef<typeof Radio>;\ntype RadioProps = React.ComponentPropsWithoutRef<typeof Radio>;\ninterface RadioGroupItemProps extends Omit<RadioProps, 'onCheck' | 'name'> {\n  value: string;\n}\n\nconst RadioGroupItem = React.forwardRef<RadioGroupItemElement, RadioGroupItemProps>(\n  (props: ScopedProps<RadioGroupItemProps>, forwardedRef) => {\n    const { __scopeRadioGroup, disabled, ...itemProps } = props;\n    const context = useRadioGroupContext(ITEM_NAME, __scopeRadioGroup);\n    const isDisabled = context.disabled || disabled;\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeRadioGroup);\n    const radioScope = useRadioScope(__scopeRadioGroup);\n    const ref = React.useRef<React.ComponentRef<typeof Radio>>(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref);\n    const checked = context.value === itemProps.value;\n    const isArrowKeyPressedRef = React.useRef(false);\n\n    React.useEffect(() => {\n      const handleKeyDown = (event: KeyboardEvent) => {\n        if (ARROW_KEYS.includes(event.key)) {\n          isArrowKeyPressedRef.current = true;\n        }\n      };\n      const handleKeyUp = () => (isArrowKeyPressedRef.current = false);\n      document.addEventListener('keydown', handleKeyDown);\n      document.addEventListener('keyup', handleKeyUp);\n      return () => {\n        document.removeEventListener('keydown', handleKeyDown);\n        document.removeEventListener('keyup', handleKeyUp);\n      };\n    }, []);\n\n    return (\n      <RovingFocusGroup.Item\n        asChild\n        {...rovingFocusGroupScope}\n        focusable={!isDisabled}\n        active={checked}\n      >\n        <Radio\n          disabled={isDisabled}\n          required={context.required}\n          checked={checked}\n          {...radioScope}\n          {...itemProps}\n          name={context.name}\n          ref={composedRefs}\n          onCheck={() => context.onValueChange(itemProps.value)}\n          onKeyDown={composeEventHandlers((event) => {\n            // According to WAI ARIA, radio groups don't activate items on enter keypress\n            if (event.key === 'Enter') event.preventDefault();\n          })}\n          onFocus={composeEventHandlers(itemProps.onFocus, () => {\n            /**\n             * Our `RovingFocusGroup` will focus the radio when navigating with arrow keys\n             * and we need to \"check\" it in that case. We click it to \"check\" it (instead\n             * of updating `context.value`) so that the radio change event fires.\n             */\n            if (isArrowKeyPressedRef.current) ref.current?.click();\n          })}\n        />\n      </RovingFocusGroup.Item>\n    );\n  }\n);\n\nRadioGroupItem.displayName = ITEM_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * RadioGroupIndicator\n * -----------------------------------------------------------------------------------------------*/\n\nconst INDICATOR_NAME = 'RadioGroupIndicator';\n\ntype RadioGroupIndicatorElement = React.ComponentRef<typeof RadioIndicator>;\ntype RadioIndicatorProps = React.ComponentPropsWithoutRef<typeof RadioIndicator>;\ninterface RadioGroupIndicatorProps extends RadioIndicatorProps {}\n\nconst RadioGroupIndicator = React.forwardRef<RadioGroupIndicatorElement, RadioGroupIndicatorProps>(\n  (props: ScopedProps<RadioGroupIndicatorProps>, forwardedRef) => {\n    const { __scopeRadioGroup, ...indicatorProps } = props;\n    const radioScope = useRadioScope(__scopeRadioGroup);\n    return <RadioIndicator {...radioScope} {...indicatorProps} ref={forwardedRef} />;\n  }\n);\n\nRadioGroupIndicator.displayName = INDICATOR_NAME;\n\n/* ---------------------------------------------------------------------------------------------- */\n\nconst Root = RadioGroup;\nconst Item = RadioGroupItem;\nconst Indicator = RadioGroupIndicator;\n\nexport {\n  createRadioGroupScope,\n  //\n  RadioGroup,\n  RadioGroupItem,\n  RadioGroupIndicator,\n  //\n  Root,\n  Item,\n  Indicator,\n};\nexport type { RadioGroupProps, RadioGroupItemProps, RadioGroupIndicatorProps };\n", "import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useSize } from '@radix-ui/react-use-size';\nimport { usePrevious } from '@radix-ui/react-use-previous';\nimport { Presence } from '@radix-ui/react-presence';\nimport { Primitive } from '@radix-ui/react-primitive';\n\nimport type { Scope } from '@radix-ui/react-context';\n\n/* -------------------------------------------------------------------------------------------------\n * Radio\n * -----------------------------------------------------------------------------------------------*/\n\nconst RADIO_NAME = 'Radio';\n\ntype ScopedProps<P> = P & { __scopeRadio?: Scope };\nconst [createRadioContext, createRadioScope] = createContextScope(RADIO_NAME);\n\ntype RadioContextValue = { checked: boolean; disabled?: boolean };\nconst [RadioProvider, useRadioContext] = createRadioContext<RadioContextValue>(RADIO_NAME);\n\ntype RadioElement = React.ComponentRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = React.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface RadioProps extends PrimitiveButtonProps {\n  checked?: boolean;\n  required?: boolean;\n  onCheck?(): void;\n}\n\nconst Radio = React.forwardRef<RadioElement, RadioProps>(\n  (props: ScopedProps<RadioProps>, forwardedRef) => {\n    const {\n      __scopeRadio,\n      name,\n      checked = false,\n      required,\n      disabled,\n      value = 'on',\n      onCheck,\n      form,\n      ...radioProps\n    } = props;\n    const [button, setButton] = React.useState<HTMLButtonElement | null>(null);\n    const composedRefs = useComposedRefs(forwardedRef, (node) => setButton(node));\n    const hasConsumerStoppedPropagationRef = React.useRef(false);\n    // We set this to true by default so that events bubble to forms without JS (SSR)\n    const isFormControl = button ? form || !!button.closest('form') : true;\n\n    return (\n      <RadioProvider scope={__scopeRadio} checked={checked} disabled={disabled}>\n        <Primitive.button\n          type=\"button\"\n          role=\"radio\"\n          aria-checked={checked}\n          data-state={getState(checked)}\n          data-disabled={disabled ? '' : undefined}\n          disabled={disabled}\n          value={value}\n          {...radioProps}\n          ref={composedRefs}\n          onClick={composeEventHandlers(props.onClick, (event) => {\n            // radios cannot be unchecked so we only communicate a checked state\n            if (!checked) onCheck?.();\n            if (isFormControl) {\n              hasConsumerStoppedPropagationRef.current = event.isPropagationStopped();\n              // if radio is in a form, stop propagation from the button so that we only propagate\n              // one click event (from the input). We propagate changes from an input so that native\n              // form validation works and form events reflect radio updates.\n              if (!hasConsumerStoppedPropagationRef.current) event.stopPropagation();\n            }\n          })}\n        />\n        {isFormControl && (\n          <RadioBubbleInput\n            control={button}\n            bubbles={!hasConsumerStoppedPropagationRef.current}\n            name={name}\n            value={value}\n            checked={checked}\n            required={required}\n            disabled={disabled}\n            form={form}\n            // We transform because the input is absolutely positioned but we have\n            // rendered it **after** the button. This pulls it back to sit on top\n            // of the button.\n            style={{ transform: 'translateX(-100%)' }}\n          />\n        )}\n      </RadioProvider>\n    );\n  }\n);\n\nRadio.displayName = RADIO_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * RadioIndicator\n * -----------------------------------------------------------------------------------------------*/\n\nconst INDICATOR_NAME = 'RadioIndicator';\n\ntype RadioIndicatorElement = React.ComponentRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = React.ComponentPropsWithoutRef<typeof Primitive.span>;\nexport interface RadioIndicatorProps extends PrimitiveSpanProps {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst RadioIndicator = React.forwardRef<RadioIndicatorElement, RadioIndicatorProps>(\n  (props: ScopedProps<RadioIndicatorProps>, forwardedRef) => {\n    const { __scopeRadio, forceMount, ...indicatorProps } = props;\n    const context = useRadioContext(INDICATOR_NAME, __scopeRadio);\n    return (\n      <Presence present={forceMount || context.checked}>\n        <Primitive.span\n          data-state={getState(context.checked)}\n          data-disabled={context.disabled ? '' : undefined}\n          {...indicatorProps}\n          ref={forwardedRef}\n        />\n      </Presence>\n    );\n  }\n);\n\nRadioIndicator.displayName = INDICATOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * RadioBubbleInput\n * -----------------------------------------------------------------------------------------------*/\n\nconst BUBBLE_INPUT_NAME = 'RadioBubbleInput';\n\ntype InputProps = React.ComponentPropsWithoutRef<typeof Primitive.input>;\ninterface RadioBubbleInputProps extends Omit<InputProps, 'checked'> {\n  checked: boolean;\n  control: HTMLElement | null;\n  bubbles: boolean;\n}\n\nconst RadioBubbleInput = React.forwardRef<HTMLInputElement, RadioBubbleInputProps>(\n  (\n    {\n      __scopeRadio,\n      control,\n      checked,\n      bubbles = true,\n      ...props\n    }: ScopedProps<RadioBubbleInputProps>,\n    forwardedRef\n  ) => {\n    const ref = React.useRef<HTMLInputElement>(null);\n    const composedRefs = useComposedRefs(ref, forwardedRef);\n    const prevChecked = usePrevious(checked);\n    const controlSize = useSize(control);\n\n    // Bubble checked change to parents (e.g form change event)\n    React.useEffect(() => {\n      const input = ref.current;\n      if (!input) return;\n\n      const inputProto = window.HTMLInputElement.prototype;\n      const descriptor = Object.getOwnPropertyDescriptor(\n        inputProto,\n        'checked'\n      ) as PropertyDescriptor;\n      const setChecked = descriptor.set;\n      if (prevChecked !== checked && setChecked) {\n        const event = new Event('click', { bubbles });\n        setChecked.call(input, checked);\n        input.dispatchEvent(event);\n      }\n    }, [prevChecked, checked, bubbles]);\n\n    return (\n      <Primitive.input\n        type=\"radio\"\n        aria-hidden\n        defaultChecked={checked}\n        {...props}\n        tabIndex={-1}\n        ref={composedRefs}\n        style={{\n          ...props.style,\n          ...controlSize,\n          position: 'absolute',\n          pointerEvents: 'none',\n          opacity: 0,\n          margin: 0,\n        }}\n      />\n    );\n  }\n);\n\nRadioBubbleInput.displayName = BUBBLE_INPUT_NAME;\n\n/* ---------------------------------------------------------------------------------------------- */\n\nfunction getState(checked: boolean) {\n  return checked ? 'checked' : 'unchecked';\n}\n\nexport {\n  createRadioScope,\n  //\n  Radio,\n  RadioIndicator,\n};\nexport type { RadioProps };\n"], "names": ["React", "composeEventHandlers", "useComposedRefs", "createContextScope", "Primitive", "jsx", "createContextScope", "Primitive", "useComposedRefs", "composeEventHandlers", "INDICATOR_NAME", "Root", "<PERSON><PERSON>"], "mappings": ";;;;;;;;;;AAAA,YAAYA,YAAW;ACmDjB,SACE,KADF;ADhDN,SAAS,sBAAAG,2BAA0B;AAGnC,SAAS,mCAAmC;AAE5C,SAAS,oBAAoB;AAD7B,SAAS,4BAA4B;AAHrC,SAAS,aAAAC,kBAAiB;AAF1B,SAAS,mBAAAF,wBAAuB;AADhC,SAAS,wBAAAD,6BAA4B;ACKrC,SAAS,gBAAgB;AADzB,SAAS,mBAAmB;AAD5B,SAAS,eAAe;;;;;;;;;;;;;;;;;;;;AAWxB,IAAM,aAAa;AAGnB,IAAM,CAAC,oBAAoB,gBAAgB,CAAA,2KAAI,qBAAA,EAAmB,UAAU;AAG5E,IAAM,CAAC,eAAe,eAAe,CAAA,GAAI,mBAAsC,UAAU;AAUzF,IAAM,QAAc,sMAAA,UAAA,CAClB,CAAC,OAAgC,iBAAiB;IAChD,MAAM,EACJ,YAAA,EACA,IAAA,EACA,UAAU,KAAA,EACV,QAAA,EACA,QAAA,EACA,QAAQ,IAAA,EACR,OAAA,EACA,IAAA,EACA,GAAG,YACL,GAAI;IACJ,MAAM,CAAC,QAAQ,SAAS,CAAA,GAAU,sMAAA,QAAA,CAAmC,IAAI;IACzE,MAAM,+LAAe,kBAAA,EAAgB,cAAc,CAAC,OAAS,UAAU,IAAI,CAAC;IAC5E,MAAM,mCAAyC,sMAAA,MAAA,CAAO,KAAK;IAE3D,MAAM,gBAAgB,SAAS,QAAQ,CAAC,CAAC,OAAO,OAAA,CAAQ,MAAM,IAAI;IAElE,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,OAAA,EAAC,eAAA;QAAc,OAAO;QAAc;QAAkB;QACpD,UAAA;YAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,wKAAC,YAAA,CAAU,MAAA,EAAV;gBACC,MAAK;gBACL,MAAK;gBACL,gBAAc;gBACd,cAAY,SAAS,OAAO;gBAC5B,iBAAe,WAAW,KAAK,KAAA;gBAC/B;gBACA;gBACC,GAAG,UAAA;gBACJ,KAAK;gBACL,yKAAS,wBAAA,EAAqB,MAAM,OAAA,EAAS,CAAC,UAAU;oBAEtD,IAAI,CAAC,QAAS,CAAA,UAAU;oBACxB,IAAI,eAAe;wBACjB,iCAAiC,OAAA,GAAU,MAAM,oBAAA,CAAqB;wBAItE,IAAI,CAAC,iCAAiC,OAAA,CAAS,CAAA,MAAM,eAAA,CAAgB;oBACvE;gBACF,CAAC;YAAA;YAEF,iBACC,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,kBAAA;gBACC,SAAS;gBACT,SAAS,CAAC,iCAAiC,OAAA;gBAC3C;gBACA;gBACA;gBACA;gBACA;gBACA;gBAIA,OAAO;oBAAE,WAAW;gBAAoB;YAAA;SAC1C;IAAA,CAEJ;AAEJ;AAGF,MAAM,WAAA,GAAc;AAMpB,IAAM,iBAAiB;AAYvB,IAAM,iBAAuB,sMAAA,UAAA,CAC3B,CAAC,OAAyC,iBAAiB;IACzD,MAAM,EAAE,YAAA,EAAc,UAAA,EAAY,GAAG,eAAe,CAAA,GAAI;IACxD,MAAM,UAAU,gBAAgB,gBAAgB,YAAY;IAC5D,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,uKAAC,WAAA,EAAA;QAAS,SAAS,cAAc,QAAQ,OAAA;QACvC,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,wKAAC,YAAA,CAAU,IAAA,EAAV;YACC,cAAY,SAAS,QAAQ,OAAO;YACpC,iBAAe,QAAQ,QAAA,GAAW,KAAK,KAAA;YACtC,GAAG,cAAA;YACJ,KAAK;QAAA;IACP,CACF;AAEJ;AAGF,eAAe,WAAA,GAAc;AAM7B,IAAM,oBAAoB;AAS1B,IAAM,mBAAyB,sMAAA,UAAA,CAC7B,CACE,EACE,YAAA,EACA,OAAA,EACA,OAAA,EACA,UAAU,IAAA,EACV,GAAG,OACL,EACA,iBACG;IACH,MAAM,MAAY,sMAAA,MAAA,CAAyB,IAAI;IAC/C,MAAM,+LAAe,kBAAA,EAAgB,KAAK,YAAY;IACtD,MAAM,cAAc,8LAAA,EAAY,OAAO;IACvC,MAAM,0LAAc,UAAA,EAAQ,OAAO;IAG7B,sMAAA,SAAA,CAAU,MAAM;QACpB,MAAM,QAAQ,IAAI,OAAA;QAClB,IAAI,CAAC,MAAO,CAAA;QAEZ,MAAM,aAAa,OAAO,gBAAA,CAAiB,SAAA;QAC3C,MAAM,aAAa,OAAO,wBAAA,CACxB,YACA;QAEF,MAAM,aAAa,WAAW,GAAA;QAC9B,IAAI,gBAAgB,WAAW,YAAY;YACzC,MAAM,QAAQ,IAAI,MAAM,SAAS;gBAAE;YAAQ,CAAC;YAC5C,WAAW,IAAA,CAAK,OAAO,OAAO;YAC9B,MAAM,aAAA,CAAc,KAAK;QAC3B;IACF,GAAG;QAAC;QAAa;QAAS,OAAO;KAAC;IAElC,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,kLAAA,CAAU,KAAA,EAAV;QACC,MAAK;QACL,eAAW;QACX,gBAAgB;QACf,GAAG,KAAA;QACJ,UAAU,CAAA;QACV,KAAK;QACL,OAAO;YACL,GAAG,MAAM,KAAA;YACT,GAAG,WAAA;YACH,UAAU;YACV,eAAe;YACf,SAAS;YACT,QAAQ;QACV;IAAA;AAGN;AAGF,iBAAiB,WAAA,GAAc;AAI/B,SAAS,SAAS,OAAA,EAAkB;IAClC,OAAO,UAAU,YAAY;AAC/B;;ADjMA,IAAM,aAAa;IAAC;IAAW;IAAa;IAAa,YAAY;CAAA;AAKrE,IAAM,mBAAmB;AAGzB,IAAM,CAAC,yBAAyB,qBAAqB,CAAA,2KAAIK,qBAAAA,EAAmB,kBAAkB;gLAC5F,8BAAA;IACA;CACD;AACD,IAAM,2BAA2B,8MAAA,CAA4B;AAC7D,IAAM,gBAAgB,iBAAiB;AAUvC,IAAM,CAAC,oBAAoB,oBAAoB,CAAA,GAC7C,wBAAgD,gBAAgB;AAiBlE,IAAM,aAAmB,sMAAA,UAAA,CACvB,CAAC,OAAqC,iBAAiB;IACrD,MAAM,EACJ,iBAAA,EACA,IAAA,EACA,YAAA,EACA,OAAO,SAAA,EACP,WAAW,KAAA,EACX,WAAW,KAAA,EACX,WAAA,EACA,GAAA,EACA,OAAO,IAAA,EACP,aAAA,EACA,GAAG,YACL,GAAI;IACJ,MAAM,wBAAwB,yBAAyB,iBAAiB;IACxE,MAAM,sLAAY,eAAA,EAAa,GAAG;IAClC,MAAM,CAAC,OAAO,QAAQ,CAAA,GAAI,oNAAA,EAAqB;QAC7C,MAAM;QACN,aAAa,gBAAgB;QAC7B,UAAU;QACV,QAAQ;IACV,CAAC;IAED,OACE,aAAA,+NAAAD,MAAAA,EAAC,oBAAA;QACC,OAAO;QACP;QACA;QACA;QACA;QACA,eAAe;QAEf,UAAA,aAAA,+NAAAA,MAAAA,EAAkB,4KAAA,IAAA,EAAjB;YACC,SAAO;YACN,GAAG,qBAAA;YACJ;YACA,KAAK;YACL;YAEA,UAAA,aAAA,+NAAAA,MAAAA,wKAACE,YAAAA,CAAU,GAAA,EAAV;gBACC,MAAK;gBACL,iBAAe;gBACf,oBAAkB;gBAClB,iBAAe,WAAW,KAAK,KAAA;gBAC/B,KAAK;gBACJ,GAAG,UAAA;gBACJ,KAAK;YAAA;QACP;IACF;AAGN;AAGF,WAAW,WAAA,GAAc;AAMzB,IAAM,YAAY;AAQlB,IAAM,iBAAuB,sMAAA,UAAA,CAC3B,CAAC,OAAyC,iBAAiB;IACzD,MAAM,EAAE,iBAAA,EAAmB,QAAA,EAAU,GAAG,UAAU,CAAA,GAAI;IACtD,MAAM,UAAU,qBAAqB,WAAW,iBAAiB;IACjE,MAAM,aAAa,QAAQ,QAAA,IAAY;IACvC,MAAM,wBAAwB,yBAAyB,iBAAiB;IACxE,MAAM,aAAa,cAAc,iBAAiB;IAClD,MAAM,MAAY,sMAAA,MAAA,CAAyC,IAAI;IAC/D,MAAM,+LAAeC,kBAAAA,EAAgB,cAAc,GAAG;IACtD,MAAM,UAAU,QAAQ,KAAA,KAAU,UAAU,KAAA;IAC5C,MAAM,uBAA6B,sMAAA,MAAA,CAAO,KAAK;IAEzC,sMAAA,SAAA,CAAU,MAAM;QACpB,MAAM,gBAAgB,CAAC,UAAyB;YAC9C,IAAI,WAAW,QAAA,CAAS,MAAM,GAAG,GAAG;gBAClC,qBAAqB,OAAA,GAAU;YACjC;QACF;QACA,MAAM,cAAc,IAAO,qBAAqB,OAAA,GAAU;QAC1D,SAAS,gBAAA,CAAiB,WAAW,aAAa;QAClD,SAAS,gBAAA,CAAiB,SAAS,WAAW;QAC9C,OAAO,MAAM;YACX,SAAS,mBAAA,CAAoB,WAAW,aAAa;YACrD,SAAS,mBAAA,CAAoB,SAAS,WAAW;QACnD;IACF,GAAG,CAAC,CAAC;IAEL,OACE,aAAA,+NAAAH,MAAAA,EAAkB,4KAAA,IAAA,EAAjB;QACC,SAAO;QACN,GAAG,qBAAA;QACJ,WAAW,CAAC;QACZ,QAAQ;QAER,UAAA,aAAA,IAAAA,iOAAAA,EAAC,OAAA;YACC,UAAU;YACV,UAAU,QAAQ,QAAA;YAClB;YACC,GAAG,UAAA;YACH,GAAG,SAAA;YACJ,MAAM,QAAQ,IAAA;YACd,KAAK;YACL,SAAS,IAAM,QAAQ,aAAA,CAAc,UAAU,KAAK;YACpD,4KAAWI,uBAAAA,EAAqB,CAAC,UAAU;gBAEzC,IAAI,MAAM,GAAA,KAAQ,QAAS,CAAA,MAAM,cAAA,CAAe;YAClD,CAAC;YACD,SAASA,wLAAAA,EAAqB,UAAU,OAAA,EAAS,MAAM;gBAMrD,IAAI,qBAAqB,OAAA,CAAS,CAAA,IAAI,OAAA,EAAS,MAAM;YACvD,CAAC;QAAA;IACH;AAGN;AAGF,eAAe,WAAA,GAAc;AAM7B,IAAMC,kBAAiB;AAMvB,IAAM,sBAA4B,sMAAA,UAAA,CAChC,CAAC,OAA8C,iBAAiB;IAC9D,MAAM,EAAE,iBAAA,EAAmB,GAAG,eAAe,CAAA,GAAI;IACjD,MAAM,aAAa,cAAc,iBAAiB;IAClD,OAAO,aAAA,+NAAAL,MAAAA,EAAC,gBAAA;QAAgB,GAAG,UAAA;QAAa,GAAG,cAAA;QAAgB,KAAK;IAAA,CAAc;AAChF;AAGF,oBAAoB,WAAA,GAAcK;AAIlC,IAAMC,QAAO;AACb,IAAMC,QAAO;AACb,IAAM,YAAY", "ignoreList": [0, 1]}}, {"offset": {"line": 2763, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2769, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/%40radix-ui/react-switch/src/switch.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { usePrevious } from '@radix-ui/react-use-previous';\nimport { useSize } from '@radix-ui/react-use-size';\nimport { Primitive } from '@radix-ui/react-primitive';\n\nimport type { Scope } from '@radix-ui/react-context';\n\n/* -------------------------------------------------------------------------------------------------\n * Switch\n * -----------------------------------------------------------------------------------------------*/\n\nconst SWITCH_NAME = 'Switch';\n\ntype ScopedProps<P> = P & { __scopeSwitch?: Scope };\nconst [createSwitchContext, createSwitchScope] = createContextScope(SWITCH_NAME);\n\ntype SwitchContextValue = { checked: boolean; disabled?: boolean };\nconst [SwitchProvider, useSwitchContext] = createSwitchContext<SwitchContextValue>(SWITCH_NAME);\n\ntype SwitchElement = React.ComponentRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = React.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface SwitchProps extends PrimitiveButtonProps {\n  checked?: boolean;\n  defaultChecked?: boolean;\n  required?: boolean;\n  onCheckedChange?(checked: boolean): void;\n}\n\nconst Switch = React.forwardRef<SwitchElement, SwitchProps>(\n  (props: ScopedProps<SwitchProps>, forwardedRef) => {\n    const {\n      __scopeSwitch,\n      name,\n      checked: checkedProp,\n      defaultChecked,\n      required,\n      disabled,\n      value = 'on',\n      onCheckedChange,\n      form,\n      ...switchProps\n    } = props;\n    const [button, setButton] = React.useState<HTMLButtonElement | null>(null);\n    const composedRefs = useComposedRefs(forwardedRef, (node) => setButton(node));\n    const hasConsumerStoppedPropagationRef = React.useRef(false);\n    // We set this to true by default so that events bubble to forms without JS (SSR)\n    const isFormControl = button ? form || !!button.closest('form') : true;\n    const [checked, setChecked] = useControllableState({\n      prop: checkedProp,\n      defaultProp: defaultChecked ?? false,\n      onChange: onCheckedChange,\n      caller: SWITCH_NAME,\n    });\n\n    return (\n      <SwitchProvider scope={__scopeSwitch} checked={checked} disabled={disabled}>\n        <Primitive.button\n          type=\"button\"\n          role=\"switch\"\n          aria-checked={checked}\n          aria-required={required}\n          data-state={getState(checked)}\n          data-disabled={disabled ? '' : undefined}\n          disabled={disabled}\n          value={value}\n          {...switchProps}\n          ref={composedRefs}\n          onClick={composeEventHandlers(props.onClick, (event) => {\n            setChecked((prevChecked) => !prevChecked);\n            if (isFormControl) {\n              hasConsumerStoppedPropagationRef.current = event.isPropagationStopped();\n              // if switch is in a form, stop propagation from the button so that we only propagate\n              // one click event (from the input). We propagate changes from an input so that native\n              // form validation works and form events reflect switch updates.\n              if (!hasConsumerStoppedPropagationRef.current) event.stopPropagation();\n            }\n          })}\n        />\n        {isFormControl && (\n          <SwitchBubbleInput\n            control={button}\n            bubbles={!hasConsumerStoppedPropagationRef.current}\n            name={name}\n            value={value}\n            checked={checked}\n            required={required}\n            disabled={disabled}\n            form={form}\n            // We transform because the input is absolutely positioned but we have\n            // rendered it **after** the button. This pulls it back to sit on top\n            // of the button.\n            style={{ transform: 'translateX(-100%)' }}\n          />\n        )}\n      </SwitchProvider>\n    );\n  }\n);\n\nSwitch.displayName = SWITCH_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SwitchThumb\n * -----------------------------------------------------------------------------------------------*/\n\nconst THUMB_NAME = 'SwitchThumb';\n\ntype SwitchThumbElement = React.ComponentRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = React.ComponentPropsWithoutRef<typeof Primitive.span>;\ninterface SwitchThumbProps extends PrimitiveSpanProps {}\n\nconst SwitchThumb = React.forwardRef<SwitchThumbElement, SwitchThumbProps>(\n  (props: ScopedProps<SwitchThumbProps>, forwardedRef) => {\n    const { __scopeSwitch, ...thumbProps } = props;\n    const context = useSwitchContext(THUMB_NAME, __scopeSwitch);\n    return (\n      <Primitive.span\n        data-state={getState(context.checked)}\n        data-disabled={context.disabled ? '' : undefined}\n        {...thumbProps}\n        ref={forwardedRef}\n      />\n    );\n  }\n);\n\nSwitchThumb.displayName = THUMB_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * SwitchBubbleInput\n * -----------------------------------------------------------------------------------------------*/\n\nconst BUBBLE_INPUT_NAME = 'SwitchBubbleInput';\n\ntype InputProps = React.ComponentPropsWithoutRef<typeof Primitive.input>;\ninterface SwitchBubbleInputProps extends Omit<InputProps, 'checked'> {\n  checked: boolean;\n  control: HTMLElement | null;\n  bubbles: boolean;\n}\n\nconst SwitchBubbleInput = React.forwardRef<HTMLInputElement, SwitchBubbleInputProps>(\n  (\n    {\n      __scopeSwitch,\n      control,\n      checked,\n      bubbles = true,\n      ...props\n    }: ScopedProps<SwitchBubbleInputProps>,\n    forwardedRef\n  ) => {\n    const ref = React.useRef<HTMLInputElement>(null);\n    const composedRefs = useComposedRefs(ref, forwardedRef);\n    const prevChecked = usePrevious(checked);\n    const controlSize = useSize(control);\n\n    // Bubble checked change to parents (e.g form change event)\n    React.useEffect(() => {\n      const input = ref.current;\n      if (!input) return;\n\n      const inputProto = window.HTMLInputElement.prototype;\n      const descriptor = Object.getOwnPropertyDescriptor(\n        inputProto,\n        'checked'\n      ) as PropertyDescriptor;\n      const setChecked = descriptor.set;\n      if (prevChecked !== checked && setChecked) {\n        const event = new Event('click', { bubbles });\n        setChecked.call(input, checked);\n        input.dispatchEvent(event);\n      }\n    }, [prevChecked, checked, bubbles]);\n\n    return (\n      <input\n        type=\"checkbox\"\n        aria-hidden\n        defaultChecked={checked}\n        {...props}\n        tabIndex={-1}\n        ref={composedRefs}\n        style={{\n          ...props.style,\n          ...controlSize,\n          position: 'absolute',\n          pointerEvents: 'none',\n          opacity: 0,\n          margin: 0,\n        }}\n      />\n    );\n  }\n);\n\nSwitchBubbleInput.displayName = BUBBLE_INPUT_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction getState(checked: boolean) {\n  return checked ? 'checked' : 'unchecked';\n}\n\nconst Root = Switch;\nconst Thumb = SwitchThumb;\n\nexport {\n  createSwitchScope,\n  //\n  Switch,\n  SwitchThumb,\n  //\n  Root,\n  Thumb,\n};\nexport type { SwitchProps, SwitchThumbProps };\n"], "names": [], "mappings": ";;;;;;;;AAAA,YAAY,WAAW;AA2DjB,SACE,KADF;AAxDN,SAAS,0BAA0B;AADnC,SAAS,uBAAuB;AAEhC,SAAS,4BAA4B;AAGrC,SAAS,iBAAiB;AAN1B,SAAS,4BAA4B;AAIrC,SAAS,mBAAmB;AAC5B,SAAS,eAAe;;;;;;;;;;;AASxB,IAAM,cAAc;AAGpB,IAAM,CAAC,qBAAqB,iBAAiB,CAAA,GAAI,6LAAA,EAAmB,WAAW;AAG/E,IAAM,CAAC,gBAAgB,gBAAgB,CAAA,GAAI,oBAAwC,WAAW;AAW9F,IAAM,SAAe,sMAAA,UAAA,CACnB,CAAC,OAAiC,iBAAiB;IACjD,MAAM,EACJ,aAAA,EACA,IAAA,EACA,SAAS,WAAA,EACT,cAAA,EACA,QAAA,EACA,QAAA,EACA,QAAQ,IAAA,EACR,eAAA,EACA,IAAA,EACA,GAAG,aACL,GAAI;IACJ,MAAM,CAAC,QAAQ,SAAS,CAAA,GAAU,sMAAA,QAAA,CAAmC,IAAI;IACzE,MAAM,+LAAe,kBAAA,EAAgB,cAAc,CAAC,OAAS,UAAU,IAAI,CAAC;IAC5E,MAAM,mCAAyC,sMAAA,MAAA,CAAO,KAAK;IAE3D,MAAM,gBAAgB,SAAS,QAAQ,CAAC,CAAC,OAAO,OAAA,CAAQ,MAAM,IAAI;IAClE,MAAM,CAAC,SAAS,UAAU,CAAA,gMAAI,uBAAA,EAAqB;QACjD,MAAM;QACN,aAAa,kBAAkB;QAC/B,UAAU;QACV,QAAQ;IACV,CAAC;IAED,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,OAAA,EAAC,gBAAA;QAAe,OAAO;QAAe;QAAkB;QACtD,UAAA;YAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,uKAAC,aAAA,CAAU,MAAA,EAAV;gBACC,MAAK;gBACL,MAAK;gBACL,gBAAc;gBACd,iBAAe;gBACf,cAAY,SAAS,OAAO;gBAC5B,iBAAe,WAAW,KAAK,KAAA;gBAC/B;gBACA;gBACC,GAAG,WAAA;gBACJ,KAAK;gBACL,0KAAS,uBAAA,EAAqB,MAAM,OAAA,EAAS,CAAC,UAAU;oBACtD,WAAW,CAAC,cAAgB,CAAC,WAAW;oBACxC,IAAI,eAAe;wBACjB,iCAAiC,OAAA,GAAU,MAAM,oBAAA,CAAqB;wBAItE,IAAI,CAAC,iCAAiC,OAAA,CAAS,CAAA,MAAM,eAAA,CAAgB;oBACvE;gBACF,CAAC;YAAA;YAEF,iBACC,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,mBAAA;gBACC,SAAS;gBACT,SAAS,CAAC,iCAAiC,OAAA;gBAC3C;gBACA;gBACA;gBACA;gBACA;gBACA;gBAIA,OAAO;oBAAE,WAAW;gBAAoB;YAAA;SAC1C;IAAA,CAEJ;AAEJ;AAGF,OAAO,WAAA,GAAc;AAMrB,IAAM,aAAa;AAMnB,IAAM,cAAoB,sMAAA,UAAA,CACxB,CAAC,OAAsC,iBAAiB;IACtD,MAAM,EAAE,aAAA,EAAe,GAAG,WAAW,CAAA,GAAI;IACzC,MAAM,UAAU,iBAAiB,YAAY,aAAa;IAC1D,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,wKAAC,YAAA,CAAU,IAAA,EAAV;QACC,cAAY,SAAS,QAAQ,OAAO;QACpC,iBAAe,QAAQ,QAAA,GAAW,KAAK,KAAA;QACtC,GAAG,UAAA;QACJ,KAAK;IAAA;AAGX;AAGF,YAAY,WAAA,GAAc;AAM1B,IAAM,oBAAoB;AAS1B,IAAM,oBAA0B,sMAAA,UAAA,CAC9B,CACE,EACE,aAAA,EACA,OAAA,EACA,OAAA,EACA,UAAU,IAAA,EACV,GAAG,OACL,EACA,iBACG;IACH,MAAM,MAAY,sMAAA,MAAA,CAAyB,IAAI;IAC/C,MAAM,eAAe,kMAAA,EAAgB,KAAK,YAAY;IACtD,MAAM,8LAAc,cAAA,EAAY,OAAO;IACvC,MAAM,0LAAc,UAAA,EAAQ,OAAO;IAG7B,sMAAA,SAAA,CAAU,MAAM;QACpB,MAAM,QAAQ,IAAI,OAAA;QAClB,IAAI,CAAC,MAAO,CAAA;QAEZ,MAAM,aAAa,OAAO,gBAAA,CAAiB,SAAA;QAC3C,MAAM,aAAa,OAAO,wBAAA,CACxB,YACA;QAEF,MAAM,aAAa,WAAW,GAAA;QAC9B,IAAI,gBAAgB,WAAW,YAAY;YACzC,MAAM,QAAQ,IAAI,MAAM,SAAS;gBAAE;YAAQ,CAAC;YAC5C,WAAW,IAAA,CAAK,OAAO,OAAO;YAC9B,MAAM,aAAA,CAAc,KAAK;QAC3B;IACF,GAAG;QAAC;QAAa;QAAS,OAAO;KAAC;IAElC,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,SAAA;QACC,MAAK;QACL,eAAW;QACX,gBAAgB;QACf,GAAG,KAAA;QACJ,UAAU,CAAA;QACV,KAAK;QACL,OAAO;YACL,GAAG,MAAM,KAAA;YACT,GAAG,WAAA;YACH,UAAU;YACV,eAAe;YACf,SAAS;YACT,QAAQ;QACV;IAAA;AAGN;AAGF,kBAAkB,WAAA,GAAc;AAIhC,SAAS,SAAS,OAAA,EAAkB;IAClC,OAAO,UAAU,YAAY;AAC/B;AAEA,IAAM,OAAO;AACb,IAAM,QAAQ", "ignoreList": [0]}}, {"offset": {"line": 2913, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2919, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/%40radix-ui/react-separator/src/separator.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { Primitive } from '@radix-ui/react-primitive';\n\n/* -------------------------------------------------------------------------------------------------\n *  Separator\n * -----------------------------------------------------------------------------------------------*/\n\nconst NAME = 'Separator';\nconst DEFAULT_ORIENTATION = 'horizontal';\nconst ORIENTATIONS = ['horizontal', 'vertical'] as const;\n\ntype Orientation = (typeof ORIENTATIONS)[number];\ntype SeparatorElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface SeparatorProps extends PrimitiveDivProps {\n  /**\n   * Either `vertical` or `horizontal`. Defaults to `horizontal`.\n   */\n  orientation?: Orientation;\n  /**\n   * Whether or not the component is purely decorative. When true, accessibility-related attributes\n   * are updated so that that the rendered element is removed from the accessibility tree.\n   */\n  decorative?: boolean;\n}\n\nconst Separator = React.forwardRef<SeparatorElement, SeparatorProps>((props, forwardedRef) => {\n  const { decorative, orientation: orientationProp = DEFAULT_ORIENTATION, ...domProps } = props;\n  const orientation = isValidOrientation(orientationProp) ? orientationProp : DEFAULT_ORIENTATION;\n  // `aria-orientation` defaults to `horizontal` so we only need it if `orientation` is vertical\n  const ariaOrientation = orientation === 'vertical' ? orientation : undefined;\n  const semanticProps = decorative\n    ? { role: 'none' }\n    : { 'aria-orientation': ariaOrientation, role: 'separator' };\n\n  return (\n    <Primitive.div\n      data-orientation={orientation}\n      {...semanticProps}\n      {...domProps}\n      ref={forwardedRef}\n    />\n  );\n});\n\nSeparator.displayName = NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction isValidOrientation(orientation: any): orientation is Orientation {\n  return ORIENTATIONS.includes(orientation);\n}\n\nconst Root = Separator;\n\nexport {\n  Separator,\n  //\n  Root,\n};\nexport type { SeparatorProps };\n"], "names": [], "mappings": ";;;;;AAAA,YAAY,WAAW;AAoCnB;AAnCJ,SAAS,iBAAiB;;;;AAM1B,IAAM,OAAO;AACb,IAAM,sBAAsB;AAC5B,IAAM,eAAe;IAAC;IAAc,UAAU;CAAA;AAiB9C,IAAM,YAAkB,sMAAA,UAAA,CAA6C,CAAC,OAAO,iBAAiB;IAC5F,MAAM,EAAE,UAAA,EAAY,aAAa,kBAAkB,mBAAA,EAAqB,GAAG,SAAS,CAAA,GAAI;IACxF,MAAM,cAAc,mBAAmB,eAAe,IAAI,kBAAkB;IAE5E,MAAM,kBAAkB,gBAAgB,aAAa,cAAc,KAAA;IACnE,MAAM,gBAAgB,aAClB;QAAE,MAAM;IAAO,IACf;QAAE,oBAAoB;QAAiB,MAAM;IAAY;IAE7D,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,wKAAC,YAAA,CAAU,GAAA,EAAV;QACC,oBAAkB;QACjB,GAAG,aAAA;QACH,GAAG,QAAA;QACJ,KAAK;IAAA;AAGX,CAAC;AAED,UAAU,WAAA,GAAc;AAIxB,SAAS,mBAAmB,WAAA,EAA8C;IACxE,OAAO,aAAa,QAAA,CAAS,WAAW;AAC1C;AAEA,IAAM,OAAO", "ignoreList": [0]}}, {"offset": {"line": 2960, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2966, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/%40radix-ui/react-tabs/src/tabs.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { createRovingFocusGroupScope } from '@radix-ui/react-roving-focus';\nimport { Presence } from '@radix-ui/react-presence';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport * as RovingFocusGroup from '@radix-ui/react-roving-focus';\nimport { useDirection } from '@radix-ui/react-direction';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { useId } from '@radix-ui/react-id';\n\nimport type { Scope } from '@radix-ui/react-context';\n\n/* -------------------------------------------------------------------------------------------------\n * Tabs\n * -----------------------------------------------------------------------------------------------*/\n\nconst TABS_NAME = 'Tabs';\n\ntype ScopedProps<P> = P & { __scopeTabs?: Scope };\nconst [createTabsContext, createTabsScope] = createContextScope(TABS_NAME, [\n  createRovingFocusGroupScope,\n]);\nconst useRovingFocusGroupScope = createRovingFocusGroupScope();\n\ntype TabsContextValue = {\n  baseId: string;\n  value: string;\n  onValueChange: (value: string) => void;\n  orientation?: TabsProps['orientation'];\n  dir?: TabsProps['dir'];\n  activationMode?: TabsProps['activationMode'];\n};\n\nconst [TabsProvider, useTabsContext] = createTabsContext<TabsContextValue>(TABS_NAME);\n\ntype TabsElement = React.ComponentRef<typeof Primitive.div>;\ntype RovingFocusGroupProps = React.ComponentPropsWithoutRef<typeof RovingFocusGroup.Root>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface TabsProps extends PrimitiveDivProps {\n  /** The value for the selected tab, if controlled */\n  value?: string;\n  /** The value of the tab to select by default, if uncontrolled */\n  defaultValue?: string;\n  /** A function called when a new tab is selected */\n  onValueChange?: (value: string) => void;\n  /**\n   * The orientation the tabs are layed out.\n   * Mainly so arrow navigation is done accordingly (left & right vs. up & down)\n   * @defaultValue horizontal\n   */\n  orientation?: RovingFocusGroupProps['orientation'];\n  /**\n   * The direction of navigation between toolbar items.\n   */\n  dir?: RovingFocusGroupProps['dir'];\n  /**\n   * Whether a tab is activated automatically or manually.\n   * @defaultValue automatic\n   * */\n  activationMode?: 'automatic' | 'manual';\n}\n\nconst Tabs = React.forwardRef<TabsElement, TabsProps>(\n  (props: ScopedProps<TabsProps>, forwardedRef) => {\n    const {\n      __scopeTabs,\n      value: valueProp,\n      onValueChange,\n      defaultValue,\n      orientation = 'horizontal',\n      dir,\n      activationMode = 'automatic',\n      ...tabsProps\n    } = props;\n    const direction = useDirection(dir);\n    const [value, setValue] = useControllableState({\n      prop: valueProp,\n      onChange: onValueChange,\n      defaultProp: defaultValue ?? '',\n      caller: TABS_NAME,\n    });\n\n    return (\n      <TabsProvider\n        scope={__scopeTabs}\n        baseId={useId()}\n        value={value}\n        onValueChange={setValue}\n        orientation={orientation}\n        dir={direction}\n        activationMode={activationMode}\n      >\n        <Primitive.div\n          dir={direction}\n          data-orientation={orientation}\n          {...tabsProps}\n          ref={forwardedRef}\n        />\n      </TabsProvider>\n    );\n  }\n);\n\nTabs.displayName = TABS_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * TabsList\n * -----------------------------------------------------------------------------------------------*/\n\nconst TAB_LIST_NAME = 'TabsList';\n\ntype TabsListElement = React.ComponentRef<typeof Primitive.div>;\ninterface TabsListProps extends PrimitiveDivProps {\n  loop?: RovingFocusGroupProps['loop'];\n}\n\nconst TabsList = React.forwardRef<TabsListElement, TabsListProps>(\n  (props: ScopedProps<TabsListProps>, forwardedRef) => {\n    const { __scopeTabs, loop = true, ...listProps } = props;\n    const context = useTabsContext(TAB_LIST_NAME, __scopeTabs);\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeTabs);\n    return (\n      <RovingFocusGroup.Root\n        asChild\n        {...rovingFocusGroupScope}\n        orientation={context.orientation}\n        dir={context.dir}\n        loop={loop}\n      >\n        <Primitive.div\n          role=\"tablist\"\n          aria-orientation={context.orientation}\n          {...listProps}\n          ref={forwardedRef}\n        />\n      </RovingFocusGroup.Root>\n    );\n  }\n);\n\nTabsList.displayName = TAB_LIST_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * TabsTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst TRIGGER_NAME = 'TabsTrigger';\n\ntype TabsTriggerElement = React.ComponentRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = React.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface TabsTriggerProps extends PrimitiveButtonProps {\n  value: string;\n}\n\nconst TabsTrigger = React.forwardRef<TabsTriggerElement, TabsTriggerProps>(\n  (props: ScopedProps<TabsTriggerProps>, forwardedRef) => {\n    const { __scopeTabs, value, disabled = false, ...triggerProps } = props;\n    const context = useTabsContext(TRIGGER_NAME, __scopeTabs);\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeTabs);\n    const triggerId = makeTriggerId(context.baseId, value);\n    const contentId = makeContentId(context.baseId, value);\n    const isSelected = value === context.value;\n    return (\n      <RovingFocusGroup.Item\n        asChild\n        {...rovingFocusGroupScope}\n        focusable={!disabled}\n        active={isSelected}\n      >\n        <Primitive.button\n          type=\"button\"\n          role=\"tab\"\n          aria-selected={isSelected}\n          aria-controls={contentId}\n          data-state={isSelected ? 'active' : 'inactive'}\n          data-disabled={disabled ? '' : undefined}\n          disabled={disabled}\n          id={triggerId}\n          {...triggerProps}\n          ref={forwardedRef}\n          onMouseDown={composeEventHandlers(props.onMouseDown, (event) => {\n            // only call handler if it's the left button (mousedown gets triggered by all mouse buttons)\n            // but not when the control key is pressed (avoiding MacOS right click)\n            if (!disabled && event.button === 0 && event.ctrlKey === false) {\n              context.onValueChange(value);\n            } else {\n              // prevent focus to avoid accidental activation\n              event.preventDefault();\n            }\n          })}\n          onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n            if ([' ', 'Enter'].includes(event.key)) context.onValueChange(value);\n          })}\n          onFocus={composeEventHandlers(props.onFocus, () => {\n            // handle \"automatic\" activation if necessary\n            // ie. activate tab following focus\n            const isAutomaticActivation = context.activationMode !== 'manual';\n            if (!isSelected && !disabled && isAutomaticActivation) {\n              context.onValueChange(value);\n            }\n          })}\n        />\n      </RovingFocusGroup.Item>\n    );\n  }\n);\n\nTabsTrigger.displayName = TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * TabsContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'TabsContent';\n\ntype TabsContentElement = React.ComponentRef<typeof Primitive.div>;\ninterface TabsContentProps extends PrimitiveDivProps {\n  value: string;\n\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst TabsContent = React.forwardRef<TabsContentElement, TabsContentProps>(\n  (props: ScopedProps<TabsContentProps>, forwardedRef) => {\n    const { __scopeTabs, value, forceMount, children, ...contentProps } = props;\n    const context = useTabsContext(CONTENT_NAME, __scopeTabs);\n    const triggerId = makeTriggerId(context.baseId, value);\n    const contentId = makeContentId(context.baseId, value);\n    const isSelected = value === context.value;\n    const isMountAnimationPreventedRef = React.useRef(isSelected);\n\n    React.useEffect(() => {\n      const rAF = requestAnimationFrame(() => (isMountAnimationPreventedRef.current = false));\n      return () => cancelAnimationFrame(rAF);\n    }, []);\n\n    return (\n      <Presence present={forceMount || isSelected}>\n        {({ present }) => (\n          <Primitive.div\n            data-state={isSelected ? 'active' : 'inactive'}\n            data-orientation={context.orientation}\n            role=\"tabpanel\"\n            aria-labelledby={triggerId}\n            hidden={!present}\n            id={contentId}\n            tabIndex={0}\n            {...contentProps}\n            ref={forwardedRef}\n            style={{\n              ...props.style,\n              animationDuration: isMountAnimationPreventedRef.current ? '0s' : undefined,\n            }}\n          >\n            {present && children}\n          </Primitive.div>\n        )}\n      </Presence>\n    );\n  }\n);\n\nTabsContent.displayName = CONTENT_NAME;\n\n/* ---------------------------------------------------------------------------------------------- */\n\nfunction makeTriggerId(baseId: string, value: string) {\n  return `${baseId}-trigger-${value}`;\n}\n\nfunction makeContentId(baseId: string, value: string) {\n  return `${baseId}-content-${value}`;\n}\n\nconst Root = Tabs;\nconst List = TabsList;\nconst Trigger = TabsTrigger;\nconst Content = TabsContent;\n\nexport {\n  createTabsScope,\n  //\n  Tabs,\n  TabsList,\n  TabsTrigger,\n  TabsContent,\n  //\n  Root,\n  List,\n  Trigger,\n  Content,\n};\nexport type { TabsProps, TabsListProps, TabsTriggerProps, TabsContentProps };\n"], "names": ["Root"], "mappings": ";;;;;;;;;;;;AAAA,YAAY,WAAW;AA6Ff;AA3FR,SAAS,0BAA0B;AACnC,SAAS,mCAAmC;AAI5C,SAAS,oBAAoB;AAC7B,SAAS,4BAA4B;AACrC,SAAS,aAAa;AAJtB,SAAS,iBAAiB;AAJ1B,SAAS,4BAA4B;AAGrC,SAAS,gBAAgB;;;;;;;;;;;;;AAazB,IAAM,YAAY;AAGlB,IAAM,CAAC,mBAAmB,eAAe,CAAA,2KAAI,qBAAA,EAAmB,WAAW;IACzE,0MAAA;CACD;AACD,IAAM,2MAA2B,8BAAA,CAA4B;AAW7D,IAAM,CAAC,cAAc,cAAc,CAAA,GAAI,kBAAoC,SAAS;AA6BpF,IAAM,OAAa,sMAAA,UAAA,CACjB,CAAC,OAA+B,iBAAiB;IAC/C,MAAM,EACJ,WAAA,EACA,OAAO,SAAA,EACP,aAAA,EACA,YAAA,EACA,cAAc,YAAA,EACd,GAAA,EACA,iBAAiB,WAAA,EACjB,GAAG,WACL,GAAI;IACJ,MAAM,sLAAY,eAAA,EAAa,GAAG;IAClC,MAAM,CAAC,OAAO,QAAQ,CAAA,gMAAI,uBAAA,EAAqB;QAC7C,MAAM;QACN,UAAU;QACV,aAAa,gBAAgB;QAC7B,QAAQ;IACV,CAAC;IAED,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,cAAA;QACC,OAAO;QACP,2KAAQ,QAAA,CAAM;QACd;QACA,eAAe;QACf;QACA,KAAK;QACL;QAEA,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,wKAAC,YAAA,CAAU,GAAA,EAAV;YACC,KAAK;YACL,oBAAkB;YACjB,GAAG,SAAA;YACJ,KAAK;QAAA;IACP;AAGN;AAGF,KAAK,WAAA,GAAc;AAMnB,IAAM,gBAAgB;AAOtB,IAAM,WAAiB,sMAAA,UAAA,CACrB,CAAC,OAAmC,iBAAiB;IACnD,MAAM,EAAE,WAAA,EAAa,OAAO,IAAA,EAAM,GAAG,UAAU,CAAA,GAAI;IACnD,MAAM,UAAU,eAAe,eAAe,WAAW;IACzD,MAAM,wBAAwB,yBAAyB,WAAW;IAClE,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAkB,4KAAA,IAAA,EAAjB;QACC,SAAO;QACN,GAAG,qBAAA;QACJ,aAAa,QAAQ,WAAA;QACrB,KAAK,QAAQ,GAAA;QACb;QAEA,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,wKAAC,YAAA,CAAU,GAAA,EAAV;YACC,MAAK;YACL,oBAAkB,QAAQ,WAAA;YACzB,GAAG,SAAA;YACJ,KAAK;QAAA;IACP;AAGN;AAGF,SAAS,WAAA,GAAc;AAMvB,IAAM,eAAe;AAQrB,IAAM,cAAoB,sMAAA,UAAA,CACxB,CAAC,OAAsC,iBAAiB;IACtD,MAAM,EAAE,WAAA,EAAa,KAAA,EAAO,WAAW,KAAA,EAAO,GAAG,aAAa,CAAA,GAAI;IAClE,MAAM,UAAU,eAAe,cAAc,WAAW;IACxD,MAAM,wBAAwB,yBAAyB,WAAW;IAClE,MAAM,YAAY,cAAc,QAAQ,MAAA,EAAQ,KAAK;IACrD,MAAM,YAAY,cAAc,QAAQ,MAAA,EAAQ,KAAK;IACrD,MAAM,aAAa,UAAU,QAAQ,KAAA;IACrC,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAkB,4KAAA,IAAA,EAAjB;QACC,SAAO;QACN,GAAG,qBAAA;QACJ,WAAW,CAAC;QACZ,QAAQ;QAER,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,kLAAA,CAAU,MAAA,EAAV;YACC,MAAK;YACL,MAAK;YACL,iBAAe;YACf,iBAAe;YACf,cAAY,aAAa,WAAW;YACpC,iBAAe,WAAW,KAAK,KAAA;YAC/B;YACA,IAAI;YACH,GAAG,YAAA;YACJ,KAAK;YACL,cAAa,uLAAA,EAAqB,MAAM,WAAA,EAAa,CAAC,UAAU;gBAG9D,IAAI,CAAC,YAAY,MAAM,MAAA,KAAW,KAAK,MAAM,OAAA,KAAY,OAAO;oBAC9D,QAAQ,aAAA,CAAc,KAAK;gBAC7B,OAAO;oBAEL,MAAM,cAAA,CAAe;gBACvB;YACF,CAAC;YACD,4KAAW,uBAAA,EAAqB,MAAM,SAAA,EAAW,CAAC,UAAU;gBAC1D,IAAI;oBAAC;oBAAK,OAAO;iBAAA,CAAE,QAAA,CAAS,MAAM,GAAG,EAAG,CAAA,QAAQ,aAAA,CAAc,KAAK;YACrE,CAAC;YACD,0KAAS,uBAAA,EAAqB,MAAM,OAAA,EAAS,MAAM;gBAGjD,MAAM,wBAAwB,QAAQ,cAAA,KAAmB;gBACzD,IAAI,CAAC,cAAc,CAAC,YAAY,uBAAuB;oBACrD,QAAQ,aAAA,CAAc,KAAK;gBAC7B;YACF,CAAC;QAAA;IACH;AAGN;AAGF,YAAY,WAAA,GAAc;AAM1B,IAAM,eAAe;AAarB,IAAM,cAAoB,sMAAA,UAAA,CACxB,CAAC,OAAsC,iBAAiB;IACtD,MAAM,EAAE,WAAA,EAAa,KAAA,EAAO,UAAA,EAAY,QAAA,EAAU,GAAG,aAAa,CAAA,GAAI;IACtE,MAAM,UAAU,eAAe,cAAc,WAAW;IACxD,MAAM,YAAY,cAAc,QAAQ,MAAA,EAAQ,KAAK;IACrD,MAAM,YAAY,cAAc,QAAQ,MAAA,EAAQ,KAAK;IACrD,MAAM,aAAa,UAAU,QAAQ,KAAA;IACrC,MAAM,+BAAqC,sMAAA,MAAA,CAAO,UAAU;IAEtD,sMAAA,SAAA,CAAU,MAAM;QACpB,MAAM,MAAM,sBAAsB,IAAO,6BAA6B,OAAA,GAAU,KAAM;QACtF,OAAO,IAAM,qBAAqB,GAAG;IACvC,GAAG,CAAC,CAAC;IAEL,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,gLAAA,EAAA;QAAS,SAAS,cAAc;QAC9B,UAAA,CAAC,EAAE,OAAA,CAAQ,CAAA,GACV,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,uKAAC,aAAA,CAAU,GAAA,EAAV;gBACC,cAAY,aAAa,WAAW;gBACpC,oBAAkB,QAAQ,WAAA;gBAC1B,MAAK;gBACL,mBAAiB;gBACjB,QAAQ,CAAC;gBACT,IAAI;gBACJ,UAAU;gBACT,GAAG,YAAA;gBACJ,KAAK;gBACL,OAAO;oBACL,GAAG,MAAM,KAAA;oBACT,mBAAmB,6BAA6B,OAAA,GAAU,OAAO,KAAA;gBACnE;gBAEC,UAAA,WAAW;YAAA;IACd,CAEJ;AAEJ;AAGF,YAAY,WAAA,GAAc;AAI1B,SAAS,cAAc,MAAA,EAAgB,KAAA,EAAe;IACpD,OAAO,GAAG,MAAM,CAAA,SAAA,EAAY,KAAK,EAAA;AACnC;AAEA,SAAS,cAAc,MAAA,EAAgB,KAAA,EAAe;IACpD,OAAO,GAAG,MAAM,CAAA,SAAA,EAAY,KAAK,EAAA;AACnC;AAEA,IAAMA,QAAO;AACb,IAAM,OAAO;AACb,IAAM,UAAU;AAChB,IAAM,UAAU", "ignoreList": [0]}}, {"offset": {"line": 3144, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3150, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/%40radix-ui/react-collapsible/src/collapsible.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { Presence } from '@radix-ui/react-presence';\nimport { useId } from '@radix-ui/react-id';\n\nimport type { Scope } from '@radix-ui/react-context';\n\n/* -------------------------------------------------------------------------------------------------\n * Collapsible\n * -----------------------------------------------------------------------------------------------*/\n\nconst COLLAPSIBLE_NAME = 'Collapsible';\n\ntype ScopedProps<P> = P & { __scopeCollapsible?: Scope };\nconst [createCollapsibleContext, createCollapsibleScope] = createContextScope(COLLAPSIBLE_NAME);\n\ntype CollapsibleContextValue = {\n  contentId: string;\n  disabled?: boolean;\n  open: boolean;\n  onOpenToggle(): void;\n};\n\nconst [CollapsibleProvider, useCollapsibleContext] =\n  createCollapsibleContext<CollapsibleContextValue>(COLLAPSIBLE_NAME);\n\ntype CollapsibleElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface CollapsibleProps extends PrimitiveDivProps {\n  defaultOpen?: boolean;\n  open?: boolean;\n  disabled?: boolean;\n  onOpenChange?(open: boolean): void;\n}\n\nconst Collapsible = React.forwardRef<CollapsibleElement, CollapsibleProps>(\n  (props: ScopedProps<CollapsibleProps>, forwardedRef) => {\n    const {\n      __scopeCollapsible,\n      open: openProp,\n      defaultOpen,\n      disabled,\n      onOpenChange,\n      ...collapsibleProps\n    } = props;\n\n    const [open, setOpen] = useControllableState({\n      prop: openProp,\n      defaultProp: defaultOpen ?? false,\n      onChange: onOpenChange,\n      caller: COLLAPSIBLE_NAME,\n    });\n\n    return (\n      <CollapsibleProvider\n        scope={__scopeCollapsible}\n        disabled={disabled}\n        contentId={useId()}\n        open={open}\n        onOpenToggle={React.useCallback(() => setOpen((prevOpen) => !prevOpen), [setOpen])}\n      >\n        <Primitive.div\n          data-state={getState(open)}\n          data-disabled={disabled ? '' : undefined}\n          {...collapsibleProps}\n          ref={forwardedRef}\n        />\n      </CollapsibleProvider>\n    );\n  }\n);\n\nCollapsible.displayName = COLLAPSIBLE_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * CollapsibleTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst TRIGGER_NAME = 'CollapsibleTrigger';\n\ntype CollapsibleTriggerElement = React.ComponentRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = React.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface CollapsibleTriggerProps extends PrimitiveButtonProps {}\n\nconst CollapsibleTrigger = React.forwardRef<CollapsibleTriggerElement, CollapsibleTriggerProps>(\n  (props: ScopedProps<CollapsibleTriggerProps>, forwardedRef) => {\n    const { __scopeCollapsible, ...triggerProps } = props;\n    const context = useCollapsibleContext(TRIGGER_NAME, __scopeCollapsible);\n    return (\n      <Primitive.button\n        type=\"button\"\n        aria-controls={context.contentId}\n        aria-expanded={context.open || false}\n        data-state={getState(context.open)}\n        data-disabled={context.disabled ? '' : undefined}\n        disabled={context.disabled}\n        {...triggerProps}\n        ref={forwardedRef}\n        onClick={composeEventHandlers(props.onClick, context.onOpenToggle)}\n      />\n    );\n  }\n);\n\nCollapsibleTrigger.displayName = TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * CollapsibleContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'CollapsibleContent';\n\ntype CollapsibleContentElement = CollapsibleContentImplElement;\ninterface CollapsibleContentProps extends Omit<CollapsibleContentImplProps, 'present'> {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst CollapsibleContent = React.forwardRef<CollapsibleContentElement, CollapsibleContentProps>(\n  (props: ScopedProps<CollapsibleContentProps>, forwardedRef) => {\n    const { forceMount, ...contentProps } = props;\n    const context = useCollapsibleContext(CONTENT_NAME, props.__scopeCollapsible);\n    return (\n      <Presence present={forceMount || context.open}>\n        {({ present }) => (\n          <CollapsibleContentImpl {...contentProps} ref={forwardedRef} present={present} />\n        )}\n      </Presence>\n    );\n  }\n);\n\nCollapsibleContent.displayName = CONTENT_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype CollapsibleContentImplElement = React.ComponentRef<typeof Primitive.div>;\ninterface CollapsibleContentImplProps extends PrimitiveDivProps {\n  present: boolean;\n}\n\nconst CollapsibleContentImpl = React.forwardRef<\n  CollapsibleContentImplElement,\n  CollapsibleContentImplProps\n>((props: ScopedProps<CollapsibleContentImplProps>, forwardedRef) => {\n  const { __scopeCollapsible, present, children, ...contentProps } = props;\n  const context = useCollapsibleContext(CONTENT_NAME, __scopeCollapsible);\n  const [isPresent, setIsPresent] = React.useState(present);\n  const ref = React.useRef<CollapsibleContentImplElement>(null);\n  const composedRefs = useComposedRefs(forwardedRef, ref);\n  const heightRef = React.useRef<number | undefined>(0);\n  const height = heightRef.current;\n  const widthRef = React.useRef<number | undefined>(0);\n  const width = widthRef.current;\n  // when opening we want it to immediately open to retrieve dimensions\n  // when closing we delay `present` to retrieve dimensions before closing\n  const isOpen = context.open || isPresent;\n  const isMountAnimationPreventedRef = React.useRef(isOpen);\n  const originalStylesRef = React.useRef<Record<string, string>>(undefined);\n\n  React.useEffect(() => {\n    const rAF = requestAnimationFrame(() => (isMountAnimationPreventedRef.current = false));\n    return () => cancelAnimationFrame(rAF);\n  }, []);\n\n  useLayoutEffect(() => {\n    const node = ref.current;\n    if (node) {\n      originalStylesRef.current = originalStylesRef.current || {\n        transitionDuration: node.style.transitionDuration,\n        animationName: node.style.animationName,\n      };\n      // block any animations/transitions so the element renders at its full dimensions\n      node.style.transitionDuration = '0s';\n      node.style.animationName = 'none';\n\n      // get width and height from full dimensions\n      const rect = node.getBoundingClientRect();\n      heightRef.current = rect.height;\n      widthRef.current = rect.width;\n\n      // kick off any animations/transitions that were originally set up if it isn't the initial mount\n      if (!isMountAnimationPreventedRef.current) {\n        node.style.transitionDuration = originalStylesRef.current.transitionDuration!;\n        node.style.animationName = originalStylesRef.current.animationName!;\n      }\n\n      setIsPresent(present);\n    }\n    /**\n     * depends on `context.open` because it will change to `false`\n     * when a close is triggered but `present` will be `false` on\n     * animation end (so when close finishes). This allows us to\n     * retrieve the dimensions *before* closing.\n     */\n  }, [context.open, present]);\n\n  return (\n    <Primitive.div\n      data-state={getState(context.open)}\n      data-disabled={context.disabled ? '' : undefined}\n      id={context.contentId}\n      hidden={!isOpen}\n      {...contentProps}\n      ref={composedRefs}\n      style={{\n        [`--radix-collapsible-content-height` as any]: height ? `${height}px` : undefined,\n        [`--radix-collapsible-content-width` as any]: width ? `${width}px` : undefined,\n        ...props.style,\n      }}\n    >\n      {isOpen && children}\n    </Primitive.div>\n  );\n});\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction getState(open?: boolean) {\n  return open ? 'open' : 'closed';\n}\n\nconst Root = Collapsible;\nconst Trigger = CollapsibleTrigger;\nconst Content = CollapsibleContent;\n\nexport {\n  createCollapsibleScope,\n  //\n  Collapsible,\n  CollapsibleTrigger,\n  CollapsibleContent,\n  //\n  Root,\n  Trigger,\n  Content,\n};\nexport type { CollapsibleProps, CollapsibleTriggerProps, CollapsibleContentProps };\n"], "names": [], "mappings": ";;;;;;;;;;AAAA,YAAY,WAAW;AAkEf;AAhER,SAAS,0BAA0B;AACnC,SAAS,4BAA4B;AAKrC,SAAS,aAAa;AAFtB,SAAS,iBAAiB;AAL1B,SAAS,4BAA4B;AAMrC,SAAS,gBAAgB;AAFzB,SAAS,uBAAuB;AADhC,SAAS,uBAAuB;;;;;;;;;;;;AAYhC,IAAM,mBAAmB;AAGzB,IAAM,CAAC,0BAA0B,sBAAsB,CAAA,2KAAI,qBAAA,EAAmB,gBAAgB;AAS9F,IAAM,CAAC,qBAAqB,qBAAqB,CAAA,GAC/C,yBAAkD,gBAAgB;AAWpE,IAAM,cAAoB,sMAAA,UAAA,CACxB,CAAC,OAAsC,iBAAiB;IACtD,MAAM,EACJ,kBAAA,EACA,MAAM,QAAA,EACN,WAAA,EACA,QAAA,EACA,YAAA,EACA,GAAG,kBACL,GAAI;IAEJ,MAAM,CAAC,MAAM,OAAO,CAAA,GAAI,oNAAA,EAAqB;QAC3C,MAAM;QACN,aAAa,eAAe;QAC5B,UAAU;QACV,QAAQ;IACV,CAAC;IAED,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,qBAAA;QACC,OAAO;QACP;QACA,8KAAW,QAAA,CAAM;QACjB;QACA,cAAoB,sMAAA,WAAA,CAAY,IAAM,QAAQ,CAAC,WAAa,CAAC,QAAQ,GAAG;YAAC,OAAO;SAAC;QAEjF,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,wKAAC,YAAA,CAAU,GAAA,EAAV;YACC,cAAY,SAAS,IAAI;YACzB,iBAAe,WAAW,KAAK,KAAA;YAC9B,GAAG,gBAAA;YACJ,KAAK;QAAA;IACP;AAGN;AAGF,YAAY,WAAA,GAAc;AAM1B,IAAM,eAAe;AAMrB,IAAM,qBAA2B,sMAAA,UAAA,CAC/B,CAAC,OAA6C,iBAAiB;IAC7D,MAAM,EAAE,kBAAA,EAAoB,GAAG,aAAa,CAAA,GAAI;IAChD,MAAM,UAAU,sBAAsB,cAAc,kBAAkB;IACtE,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,wKAAC,YAAA,CAAU,MAAA,EAAV;QACC,MAAK;QACL,iBAAe,QAAQ,SAAA;QACvB,iBAAe,QAAQ,IAAA,IAAQ;QAC/B,cAAY,SAAS,QAAQ,IAAI;QACjC,iBAAe,QAAQ,QAAA,GAAW,KAAK,KAAA;QACvC,UAAU,QAAQ,QAAA;QACjB,GAAG,YAAA;QACJ,KAAK;QACL,SAAS,wLAAA,EAAqB,MAAM,OAAA,EAAS,QAAQ,YAAY;IAAA;AAGvE;AAGF,mBAAmB,WAAA,GAAc;AAMjC,IAAM,eAAe;AAWrB,IAAM,qBAA2B,sMAAA,UAAA,CAC/B,CAAC,OAA6C,iBAAiB;IAC7D,MAAM,EAAE,UAAA,EAAY,GAAG,aAAa,CAAA,GAAI;IACxC,MAAM,UAAU,sBAAsB,cAAc,MAAM,kBAAkB;IAC5E,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,uKAAC,WAAA,EAAA;QAAS,SAAS,cAAc,QAAQ,IAAA;QACtC,UAAA,CAAC,EAAE,OAAA,CAAQ,CAAA,GACV,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,wBAAA;gBAAwB,GAAG,YAAA;gBAAc,KAAK;gBAAc;YAAA,CAAkB;IAAA,CAEnF;AAEJ;AAGF,mBAAmB,WAAA,GAAc;AASjC,IAAM,yBAA+B,sMAAA,UAAA,CAGnC,CAAC,OAAiD,iBAAiB;IACnE,MAAM,EAAE,kBAAA,EAAoB,OAAA,EAAS,QAAA,EAAU,GAAG,aAAa,CAAA,GAAI;IACnE,MAAM,UAAU,sBAAsB,cAAc,kBAAkB;IACtE,MAAM,CAAC,WAAW,YAAY,CAAA,GAAU,sMAAA,QAAA,CAAS,OAAO;IACxD,MAAM,MAAY,sMAAA,MAAA,CAAsC,IAAI;IAC5D,MAAM,+LAAe,kBAAA,EAAgB,cAAc,GAAG;IACtD,MAAM,YAAkB,sMAAA,MAAA,CAA2B,CAAC;IACpD,MAAM,SAAS,UAAU,OAAA;IACzB,MAAM,WAAiB,sMAAA,MAAA,CAA2B,CAAC;IACnD,MAAM,QAAQ,SAAS,OAAA;IAGvB,MAAM,SAAS,QAAQ,IAAA,IAAQ;IAC/B,MAAM,+BAAqC,sMAAA,MAAA,CAAO,MAAM;IACxD,MAAM,oBAA0B,sMAAA,MAAA,CAA+B,KAAA,CAAS;IAElE,sMAAA,SAAA,CAAU,MAAM;QACpB,MAAM,MAAM,sBAAsB,IAAO,6BAA6B,OAAA,GAAU,KAAM;QACtF,OAAO,IAAM,qBAAqB,GAAG;IACvC,GAAG,CAAC,CAAC;IAEL,CAAA,GAAA,mLAAA,CAAA,kBAAA,EAAgB,MAAM;QACpB,MAAM,OAAO,IAAI,OAAA;QACjB,IAAI,MAAM;YACR,kBAAkB,OAAA,GAAU,kBAAkB,OAAA,IAAW;gBACvD,oBAAoB,KAAK,KAAA,CAAM,kBAAA;gBAC/B,eAAe,KAAK,KAAA,CAAM,aAAA;YAC5B;YAEA,KAAK,KAAA,CAAM,kBAAA,GAAqB;YAChC,KAAK,KAAA,CAAM,aAAA,GAAgB;YAG3B,MAAM,OAAO,KAAK,qBAAA,CAAsB;YACxC,UAAU,OAAA,GAAU,KAAK,MAAA;YACzB,SAAS,OAAA,GAAU,KAAK,KAAA;YAGxB,IAAI,CAAC,6BAA6B,OAAA,EAAS;gBACzC,KAAK,KAAA,CAAM,kBAAA,GAAqB,kBAAkB,OAAA,CAAQ,kBAAA;gBAC1D,KAAK,KAAA,CAAM,aAAA,GAAgB,kBAAkB,OAAA,CAAQ,aAAA;YACvD;YAEA,aAAa,OAAO;QACtB;IAOF,GAAG;QAAC,QAAQ,IAAA;QAAM,OAAO;KAAC;IAE1B,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,wKAAC,YAAA,CAAU,GAAA,EAAV;QACC,cAAY,SAAS,QAAQ,IAAI;QACjC,iBAAe,QAAQ,QAAA,GAAW,KAAK,KAAA;QACvC,IAAI,QAAQ,SAAA;QACZ,QAAQ,CAAC;QACR,GAAG,YAAA;QACJ,KAAK;QACL,OAAO;YACL,CAAC,CAAA,kCAAA,CAA2C,CAAA,EAAG,SAAS,GAAG,MAAM,CAAA,EAAA,CAAA,GAAO,KAAA;YACxE,CAAC,CAAA,iCAAA,CAA0C,CAAA,EAAG,QAAQ,GAAG,KAAK,CAAA,EAAA,CAAA,GAAO,KAAA;YACrE,GAAG,MAAM,KAAA;QACX;QAEC,UAAA,UAAU;IAAA;AAGjB,CAAC;AAID,SAAS,SAAS,IAAA,EAAgB;IAChC,OAAO,OAAO,SAAS;AACzB;AAEA,IAAM,OAAO;AACb,IAAM,UAAU;AAChB,IAAM,UAAU", "ignoreList": [0]}}, {"offset": {"line": 3302, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3308, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/%40radix-ui/react-accordion/src/accordion.tsx"], "sourcesContent": ["import React from 'react';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { createCollection } from '@radix-ui/react-collection';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport * as CollapsiblePrimitive from '@radix-ui/react-collapsible';\nimport { createCollapsibleScope } from '@radix-ui/react-collapsible';\nimport { useId } from '@radix-ui/react-id';\n\nimport type { Scope } from '@radix-ui/react-context';\nimport { useDirection } from '@radix-ui/react-direction';\n\ntype Direction = 'ltr' | 'rtl';\n\n/* -------------------------------------------------------------------------------------------------\n * Accordion\n * -----------------------------------------------------------------------------------------------*/\n\nconst ACCORDION_NAME = 'Accordion';\nconst ACCORDION_KEYS = ['Home', 'End', 'ArrowDown', 'ArrowUp', 'ArrowLeft', 'ArrowRight'];\n\nconst [Collection, useCollection, createCollectionScope] =\n  createCollection<AccordionTriggerElement>(ACCORDION_NAME);\n\ntype ScopedProps<P> = P & { __scopeAccordion?: Scope };\nconst [createAccordionContext, createAccordionScope] = createContextScope(ACCORDION_NAME, [\n  createCollectionScope,\n  createCollapsibleScope,\n]);\nconst useCollapsibleScope = createCollapsibleScope();\n\ntype AccordionElement = AccordionImplMultipleElement | AccordionImplSingleElement;\ninterface AccordionSingleProps extends AccordionImplSingleProps {\n  type: 'single';\n}\ninterface AccordionMultipleProps extends AccordionImplMultipleProps {\n  type: 'multiple';\n}\n\nconst Accordion = React.forwardRef<AccordionElement, AccordionSingleProps | AccordionMultipleProps>(\n  (props: ScopedProps<AccordionSingleProps | AccordionMultipleProps>, forwardedRef) => {\n    const { type, ...accordionProps } = props;\n    const singleProps = accordionProps as AccordionImplSingleProps;\n    const multipleProps = accordionProps as AccordionImplMultipleProps;\n    return (\n      <Collection.Provider scope={props.__scopeAccordion}>\n        {type === 'multiple' ? (\n          <AccordionImplMultiple {...multipleProps} ref={forwardedRef} />\n        ) : (\n          <AccordionImplSingle {...singleProps} ref={forwardedRef} />\n        )}\n      </Collection.Provider>\n    );\n  }\n);\n\nAccordion.displayName = ACCORDION_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype AccordionValueContextValue = {\n  value: string[];\n  onItemOpen(value: string): void;\n  onItemClose(value: string): void;\n};\n\nconst [AccordionValueProvider, useAccordionValueContext] =\n  createAccordionContext<AccordionValueContextValue>(ACCORDION_NAME);\n\nconst [AccordionCollapsibleProvider, useAccordionCollapsibleContext] = createAccordionContext(\n  ACCORDION_NAME,\n  { collapsible: false }\n);\n\ntype AccordionImplSingleElement = AccordionImplElement;\ninterface AccordionImplSingleProps extends AccordionImplProps {\n  /**\n   * The controlled stateful value of the accordion item whose content is expanded.\n   */\n  value?: string;\n  /**\n   * The value of the item whose content is expanded when the accordion is initially rendered. Use\n   * `defaultValue` if you do not need to control the state of an accordion.\n   */\n  defaultValue?: string;\n  /**\n   * The callback that fires when the state of the accordion changes.\n   */\n  onValueChange?(value: string): void;\n  /**\n   * Whether an accordion item can be collapsed after it has been opened.\n   * @default false\n   */\n  collapsible?: boolean;\n}\n\nconst AccordionImplSingle = React.forwardRef<AccordionImplSingleElement, AccordionImplSingleProps>(\n  (props: ScopedProps<AccordionImplSingleProps>, forwardedRef) => {\n    const {\n      value: valueProp,\n      defaultValue,\n      onValueChange = () => {},\n      collapsible = false,\n      ...accordionSingleProps\n    } = props;\n\n    const [value, setValue] = useControllableState({\n      prop: valueProp,\n      defaultProp: defaultValue ?? '',\n      onChange: onValueChange,\n      caller: ACCORDION_NAME,\n    });\n\n    return (\n      <AccordionValueProvider\n        scope={props.__scopeAccordion}\n        value={React.useMemo(() => (value ? [value] : []), [value])}\n        onItemOpen={setValue}\n        onItemClose={React.useCallback(() => collapsible && setValue(''), [collapsible, setValue])}\n      >\n        <AccordionCollapsibleProvider scope={props.__scopeAccordion} collapsible={collapsible}>\n          <AccordionImpl {...accordionSingleProps} ref={forwardedRef} />\n        </AccordionCollapsibleProvider>\n      </AccordionValueProvider>\n    );\n  }\n);\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype AccordionImplMultipleElement = AccordionImplElement;\ninterface AccordionImplMultipleProps extends AccordionImplProps {\n  /**\n   * The controlled stateful value of the accordion items whose contents are expanded.\n   */\n  value?: string[];\n  /**\n   * The value of the items whose contents are expanded when the accordion is initially rendered. Use\n   * `defaultValue` if you do not need to control the state of an accordion.\n   */\n  defaultValue?: string[];\n  /**\n   * The callback that fires when the state of the accordion changes.\n   */\n  onValueChange?(value: string[]): void;\n}\n\nconst AccordionImplMultiple = React.forwardRef<\n  AccordionImplMultipleElement,\n  AccordionImplMultipleProps\n>((props: ScopedProps<AccordionImplMultipleProps>, forwardedRef) => {\n  const {\n    value: valueProp,\n    defaultValue,\n    onValueChange = () => {},\n    ...accordionMultipleProps\n  } = props;\n\n  const [value, setValue] = useControllableState({\n    prop: valueProp,\n    defaultProp: defaultValue ?? [],\n    onChange: onValueChange,\n    caller: ACCORDION_NAME,\n  });\n\n  const handleItemOpen = React.useCallback(\n    (itemValue: string) => setValue((prevValue = []) => [...prevValue, itemValue]),\n    [setValue]\n  );\n\n  const handleItemClose = React.useCallback(\n    (itemValue: string) =>\n      setValue((prevValue = []) => prevValue.filter((value) => value !== itemValue)),\n    [setValue]\n  );\n\n  return (\n    <AccordionValueProvider\n      scope={props.__scopeAccordion}\n      value={value}\n      onItemOpen={handleItemOpen}\n      onItemClose={handleItemClose}\n    >\n      <AccordionCollapsibleProvider scope={props.__scopeAccordion} collapsible={true}>\n        <AccordionImpl {...accordionMultipleProps} ref={forwardedRef} />\n      </AccordionCollapsibleProvider>\n    </AccordionValueProvider>\n  );\n});\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype AccordionImplContextValue = {\n  disabled?: boolean;\n  direction: AccordionImplProps['dir'];\n  orientation: AccordionImplProps['orientation'];\n};\n\nconst [AccordionImplProvider, useAccordionContext] =\n  createAccordionContext<AccordionImplContextValue>(ACCORDION_NAME);\n\ntype AccordionImplElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface AccordionImplProps extends PrimitiveDivProps {\n  /**\n   * Whether or not an accordion is disabled from user interaction.\n   *\n   * @defaultValue false\n   */\n  disabled?: boolean;\n  /**\n   * The layout in which the Accordion operates.\n   * @default vertical\n   */\n  orientation?: React.AriaAttributes['aria-orientation'];\n  /**\n   * The language read direction.\n   */\n  dir?: Direction;\n}\n\nconst AccordionImpl = React.forwardRef<AccordionImplElement, AccordionImplProps>(\n  (props: ScopedProps<AccordionImplProps>, forwardedRef) => {\n    const { __scopeAccordion, disabled, dir, orientation = 'vertical', ...accordionProps } = props;\n    const accordionRef = React.useRef<AccordionImplElement>(null);\n    const composedRefs = useComposedRefs(accordionRef, forwardedRef);\n    const getItems = useCollection(__scopeAccordion);\n    const direction = useDirection(dir);\n    const isDirectionLTR = direction === 'ltr';\n\n    const handleKeyDown = composeEventHandlers(props.onKeyDown, (event) => {\n      if (!ACCORDION_KEYS.includes(event.key)) return;\n      const target = event.target as HTMLElement;\n      const triggerCollection = getItems().filter((item) => !item.ref.current?.disabled);\n      const triggerIndex = triggerCollection.findIndex((item) => item.ref.current === target);\n      const triggerCount = triggerCollection.length;\n\n      if (triggerIndex === -1) return;\n\n      // Prevents page scroll while user is navigating\n      event.preventDefault();\n\n      let nextIndex = triggerIndex;\n      const homeIndex = 0;\n      const endIndex = triggerCount - 1;\n\n      const moveNext = () => {\n        nextIndex = triggerIndex + 1;\n        if (nextIndex > endIndex) {\n          nextIndex = homeIndex;\n        }\n      };\n\n      const movePrev = () => {\n        nextIndex = triggerIndex - 1;\n        if (nextIndex < homeIndex) {\n          nextIndex = endIndex;\n        }\n      };\n\n      switch (event.key) {\n        case 'Home':\n          nextIndex = homeIndex;\n          break;\n        case 'End':\n          nextIndex = endIndex;\n          break;\n        case 'ArrowRight':\n          if (orientation === 'horizontal') {\n            if (isDirectionLTR) {\n              moveNext();\n            } else {\n              movePrev();\n            }\n          }\n          break;\n        case 'ArrowDown':\n          if (orientation === 'vertical') {\n            moveNext();\n          }\n          break;\n        case 'ArrowLeft':\n          if (orientation === 'horizontal') {\n            if (isDirectionLTR) {\n              movePrev();\n            } else {\n              moveNext();\n            }\n          }\n          break;\n        case 'ArrowUp':\n          if (orientation === 'vertical') {\n            movePrev();\n          }\n          break;\n      }\n\n      const clampedIndex = nextIndex % triggerCount;\n      triggerCollection[clampedIndex]!.ref.current?.focus();\n    });\n\n    return (\n      <AccordionImplProvider\n        scope={__scopeAccordion}\n        disabled={disabled}\n        direction={dir}\n        orientation={orientation}\n      >\n        <Collection.Slot scope={__scopeAccordion}>\n          <Primitive.div\n            {...accordionProps}\n            data-orientation={orientation}\n            ref={composedRefs}\n            onKeyDown={disabled ? undefined : handleKeyDown}\n          />\n        </Collection.Slot>\n      </AccordionImplProvider>\n    );\n  }\n);\n\n/* -------------------------------------------------------------------------------------------------\n * AccordionItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_NAME = 'AccordionItem';\n\ntype AccordionItemContextValue = { open?: boolean; disabled?: boolean; triggerId: string };\nconst [AccordionItemProvider, useAccordionItemContext] =\n  createAccordionContext<AccordionItemContextValue>(ITEM_NAME);\n\ntype AccordionItemElement = React.ComponentRef<typeof CollapsiblePrimitive.Root>;\ntype CollapsibleProps = React.ComponentPropsWithoutRef<typeof CollapsiblePrimitive.Root>;\ninterface AccordionItemProps\n  extends Omit<CollapsibleProps, 'open' | 'defaultOpen' | 'onOpenChange'> {\n  /**\n   * Whether or not an accordion item is disabled from user interaction.\n   *\n   * @defaultValue false\n   */\n  disabled?: boolean;\n  /**\n   * A string value for the accordion item. All items within an accordion should use a unique value.\n   */\n  value: string;\n}\n\n/**\n * `AccordionItem` contains all of the parts of a collapsible section inside of an `Accordion`.\n */\nconst AccordionItem = React.forwardRef<AccordionItemElement, AccordionItemProps>(\n  (props: ScopedProps<AccordionItemProps>, forwardedRef) => {\n    const { __scopeAccordion, value, ...accordionItemProps } = props;\n    const accordionContext = useAccordionContext(ITEM_NAME, __scopeAccordion);\n    const valueContext = useAccordionValueContext(ITEM_NAME, __scopeAccordion);\n    const collapsibleScope = useCollapsibleScope(__scopeAccordion);\n    const triggerId = useId();\n    const open = (value && valueContext.value.includes(value)) || false;\n    const disabled = accordionContext.disabled || props.disabled;\n\n    return (\n      <AccordionItemProvider\n        scope={__scopeAccordion}\n        open={open}\n        disabled={disabled}\n        triggerId={triggerId}\n      >\n        <CollapsiblePrimitive.Root\n          data-orientation={accordionContext.orientation}\n          data-state={getState(open)}\n          {...collapsibleScope}\n          {...accordionItemProps}\n          ref={forwardedRef}\n          disabled={disabled}\n          open={open}\n          onOpenChange={(open) => {\n            if (open) {\n              valueContext.onItemOpen(value);\n            } else {\n              valueContext.onItemClose(value);\n            }\n          }}\n        />\n      </AccordionItemProvider>\n    );\n  }\n);\n\nAccordionItem.displayName = ITEM_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * AccordionHeader\n * -----------------------------------------------------------------------------------------------*/\n\nconst HEADER_NAME = 'AccordionHeader';\n\ntype AccordionHeaderElement = React.ComponentRef<typeof Primitive.h3>;\ntype PrimitiveHeading3Props = React.ComponentPropsWithoutRef<typeof Primitive.h3>;\ninterface AccordionHeaderProps extends PrimitiveHeading3Props {}\n\n/**\n * `AccordionHeader` contains the content for the parts of an `AccordionItem` that will be visible\n * whether or not its content is collapsed.\n */\nconst AccordionHeader = React.forwardRef<AccordionHeaderElement, AccordionHeaderProps>(\n  (props: ScopedProps<AccordionHeaderProps>, forwardedRef) => {\n    const { __scopeAccordion, ...headerProps } = props;\n    const accordionContext = useAccordionContext(ACCORDION_NAME, __scopeAccordion);\n    const itemContext = useAccordionItemContext(HEADER_NAME, __scopeAccordion);\n    return (\n      <Primitive.h3\n        data-orientation={accordionContext.orientation}\n        data-state={getState(itemContext.open)}\n        data-disabled={itemContext.disabled ? '' : undefined}\n        {...headerProps}\n        ref={forwardedRef}\n      />\n    );\n  }\n);\n\nAccordionHeader.displayName = HEADER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * AccordionTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst TRIGGER_NAME = 'AccordionTrigger';\n\ntype AccordionTriggerElement = React.ComponentRef<typeof CollapsiblePrimitive.Trigger>;\ntype CollapsibleTriggerProps = React.ComponentPropsWithoutRef<typeof CollapsiblePrimitive.Trigger>;\ninterface AccordionTriggerProps extends CollapsibleTriggerProps {}\n\n/**\n * `AccordionTrigger` is the trigger that toggles the collapsed state of an `AccordionItem`. It\n * should always be nested inside of an `AccordionHeader`.\n */\nconst AccordionTrigger = React.forwardRef<AccordionTriggerElement, AccordionTriggerProps>(\n  (props: ScopedProps<AccordionTriggerProps>, forwardedRef) => {\n    const { __scopeAccordion, ...triggerProps } = props;\n    const accordionContext = useAccordionContext(ACCORDION_NAME, __scopeAccordion);\n    const itemContext = useAccordionItemContext(TRIGGER_NAME, __scopeAccordion);\n    const collapsibleContext = useAccordionCollapsibleContext(TRIGGER_NAME, __scopeAccordion);\n    const collapsibleScope = useCollapsibleScope(__scopeAccordion);\n    return (\n      <Collection.ItemSlot scope={__scopeAccordion}>\n        <CollapsiblePrimitive.Trigger\n          aria-disabled={(itemContext.open && !collapsibleContext.collapsible) || undefined}\n          data-orientation={accordionContext.orientation}\n          id={itemContext.triggerId}\n          {...collapsibleScope}\n          {...triggerProps}\n          ref={forwardedRef}\n        />\n      </Collection.ItemSlot>\n    );\n  }\n);\n\nAccordionTrigger.displayName = TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * AccordionContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'AccordionContent';\n\ntype AccordionContentElement = React.ComponentRef<typeof CollapsiblePrimitive.Content>;\ntype CollapsibleContentProps = React.ComponentPropsWithoutRef<typeof CollapsiblePrimitive.Content>;\ninterface AccordionContentProps extends CollapsibleContentProps {}\n\n/**\n * `AccordionContent` contains the collapsible content for an `AccordionItem`.\n */\nconst AccordionContent = React.forwardRef<AccordionContentElement, AccordionContentProps>(\n  (props: ScopedProps<AccordionContentProps>, forwardedRef) => {\n    const { __scopeAccordion, ...contentProps } = props;\n    const accordionContext = useAccordionContext(ACCORDION_NAME, __scopeAccordion);\n    const itemContext = useAccordionItemContext(CONTENT_NAME, __scopeAccordion);\n    const collapsibleScope = useCollapsibleScope(__scopeAccordion);\n    return (\n      <CollapsiblePrimitive.Content\n        role=\"region\"\n        aria-labelledby={itemContext.triggerId}\n        data-orientation={accordionContext.orientation}\n        {...collapsibleScope}\n        {...contentProps}\n        ref={forwardedRef}\n        style={{\n          ['--radix-accordion-content-height' as any]: 'var(--radix-collapsible-content-height)',\n          ['--radix-accordion-content-width' as any]: 'var(--radix-collapsible-content-width)',\n          ...props.style,\n        }}\n      />\n    );\n  }\n);\n\nAccordionContent.displayName = CONTENT_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction getState(open?: boolean) {\n  return open ? 'open' : 'closed';\n}\n\nconst Root = Accordion;\nconst Item = AccordionItem;\nconst Header = AccordionHeader;\nconst Trigger = AccordionTrigger;\nconst Content = AccordionContent;\n\nexport {\n  createAccordionScope,\n  //\n  Accordion,\n  AccordionItem,\n  AccordionHeader,\n  AccordionTrigger,\n  AccordionContent,\n  //\n  Root,\n  Item,\n  Header,\n  Trigger,\n  Content,\n};\nexport type {\n  AccordionSingleProps,\n  AccordionMultipleProps,\n  AccordionItemProps,\n  AccordionHeaderProps,\n  AccordionTriggerProps,\n  AccordionContentProps,\n};\n"], "names": ["value", "open", "Root", "<PERSON><PERSON>", "Content"], "mappings": ";;;;;;;;;;;;;;AAAA,OAAO,WAAW;AAiDR;AA/CV,SAAS,wBAAwB;AADjC,SAAS,0BAA0B;AAOnC,SAAS,8BAA8B;AAHvC,SAAS,4BAA4B;AAFrC,SAAS,uBAAuB;AAShC,SAAS,oBAAoB;AAR7B,SAAS,4BAA4B;AAErC,SAAS,iBAAiB;AAG1B,SAAS,aAAa;;;;;;;;;;;;;;AAWtB,IAAM,iBAAiB;AACvB,IAAM,iBAAiB;IAAC;IAAQ;IAAO;IAAa;IAAW;IAAa,YAAY;CAAA;AAExF,IAAM,CAAC,YAAY,eAAe,qBAAqB,CAAA,8KACrD,mBAAA,EAA0C,cAAc;AAG1D,IAAM,CAAC,wBAAwB,oBAAoB,CAAA,GAAI,6LAAA,EAAmB,gBAAgB;IACxF;4KACA,yBAAA;CACD;AACD,IAAM,kMAAsB,yBAAA,CAAuB;AAUnD,IAAM,iNAAY,WAAA,CAAM,UAAA,CACtB,CAAC,OAAmE,iBAAiB;IACnF,MAAM,EAAE,IAAA,EAAM,GAAG,eAAe,CAAA,GAAI;IACpC,MAAM,cAAc;IACpB,MAAM,gBAAgB;IACtB,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,WAAW,QAAA,EAAX;QAAoB,OAAO,MAAM,gBAAA;QAC/B,UAAA,SAAS,aACR,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,uBAAA;YAAuB,GAAG,aAAA;YAAe,KAAK;QAAA,CAAc,IAE7D,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,qBAAA;YAAqB,GAAG,WAAA;YAAa,KAAK;QAAA,CAAc;IAAA,CAE7D;AAEJ;AAGF,UAAU,WAAA,GAAc;AAUxB,IAAM,CAAC,wBAAwB,wBAAwB,CAAA,GACrD,uBAAmD,cAAc;AAEnE,IAAM,CAAC,8BAA8B,8BAA8B,CAAA,GAAI,uBACrE,gBACA;IAAE,aAAa;AAAM;AAyBvB,IAAM,4NAAsB,UAAA,CAAM,UAAA,CAChC,CAAC,OAA8C,iBAAiB;IAC9D,MAAM,EACJ,OAAO,SAAA,EACP,YAAA,EACA,gBAAgB,KAAO,CAAD,AAAC,EACvB,cAAc,KAAA,EACd,GAAG,sBACL,GAAI;IAEJ,MAAM,CAAC,OAAO,QAAQ,CAAA,gMAAI,uBAAA,EAAqB;QAC7C,MAAM;QACN,aAAa,gBAAgB;QAC7B,UAAU;QACV,QAAQ;IACV,CAAC;IAED,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,wBAAA;QACC,OAAO,MAAM,gBAAA;QACb,6MAAO,UAAA,CAAM,OAAA,CAAQ,IAAO,QAAQ;gBAAC,KAAK;aAAA,GAAI,CAAC,CAAA,EAAI;YAAC,KAAK;SAAC;QAC1D,YAAY;QACZ,mNAAa,UAAA,CAAM,WAAA,CAAY,IAAM,eAAe,SAAS,EAAE,GAAG;YAAC;YAAa,QAAQ;SAAC;QAEzF,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,8BAAA;YAA6B,OAAO,MAAM,gBAAA;YAAkB;YAC3D,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,eAAA;gBAAe,GAAG,oBAAA;gBAAsB,KAAK;YAAA,CAAc;QAAA,CAC9D;IAAA;AAGN;AAsBF,IAAM,8NAAwB,UAAA,CAAM,UAAA,CAGlC,CAAC,OAAgD,iBAAiB;IAClE,MAAM,EACJ,OAAO,SAAA,EACP,YAAA,EACA,gBAAgB,KAAO,CAAD,AAAC,EACvB,GAAG,wBACL,GAAI;IAEJ,MAAM,CAAC,OAAO,QAAQ,CAAA,gMAAI,uBAAA,EAAqB;QAC7C,MAAM;QACN,aAAa,gBAAgB,CAAC,CAAA;QAC9B,UAAU;QACV,QAAQ;IACV,CAAC;IAED,MAAM,uNAAiB,UAAA,CAAM,WAAA,CAC3B,CAAC,YAAsB,SAAS,CAAC,YAAY,CAAC,CAAA,GAAM,CAAC;mBAAG;gBAAW,SAAS;aAAC,GAC7E;QAAC,QAAQ;KAAA;IAGX,MAAM,wNAAkB,UAAA,CAAM,WAAA,CAC5B,CAAC,YACC,SAAS,CAAC,YAAY,CAAC,CAAA,GAAM,UAAU,MAAA,CAAO,CAACA,SAAUA,WAAU,SAAS,CAAC,GAC/E;QAAC,QAAQ;KAAA;IAGX,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,wBAAA;QACC,OAAO,MAAM,gBAAA;QACb;QACA,YAAY;QACZ,aAAa;QAEb,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,8BAAA;YAA6B,OAAO,MAAM,gBAAA;YAAkB,aAAa;YACxE,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,eAAA;gBAAe,GAAG,sBAAA;gBAAwB,KAAK;YAAA,CAAc;QAAA,CAChE;IAAA;AAGN,CAAC;AAUD,IAAM,CAAC,uBAAuB,mBAAmB,CAAA,GAC/C,uBAAkD,cAAc;AAsBlE,IAAM,sNAAgB,UAAA,CAAM,UAAA,CAC1B,CAAC,OAAwC,iBAAiB;IACxD,MAAM,EAAE,gBAAA,EAAkB,QAAA,EAAU,GAAA,EAAK,cAAc,UAAA,EAAY,GAAG,eAAe,CAAA,GAAI;IACzF,MAAM,qNAAe,UAAA,CAAM,MAAA,CAA6B,IAAI;IAC5D,MAAM,eAAe,kMAAA,EAAgB,cAAc,YAAY;IAC/D,MAAM,WAAW,cAAc,gBAAgB;IAC/C,MAAM,gBAAY,qLAAA,EAAa,GAAG;IAClC,MAAM,iBAAiB,cAAc;IAErC,MAAM,iLAAgB,uBAAA,EAAqB,MAAM,SAAA,EAAW,CAAC,UAAU;QACrE,IAAI,CAAC,eAAe,QAAA,CAAS,MAAM,GAAG,EAAG,CAAA;QACzC,MAAM,SAAS,MAAM,MAAA;QACrB,MAAM,oBAAoB,SAAS,EAAE,MAAA,CAAO,CAAC,OAAS,CAAC,KAAK,GAAA,CAAI,OAAA,EAAS,QAAQ;QACjF,MAAM,eAAe,kBAAkB,SAAA,CAAU,CAAC,OAAS,KAAK,GAAA,CAAI,OAAA,KAAY,MAAM;QACtF,MAAM,eAAe,kBAAkB,MAAA;QAEvC,IAAI,iBAAiB,CAAA,EAAI,CAAA;QAGzB,MAAM,cAAA,CAAe;QAErB,IAAI,YAAY;QAChB,MAAM,YAAY;QAClB,MAAM,WAAW,eAAe;QAEhC,MAAM,WAAW,MAAM;YACrB,YAAY,eAAe;YAC3B,IAAI,YAAY,UAAU;gBACxB,YAAY;YACd;QACF;QAEA,MAAM,WAAW,MAAM;YACrB,YAAY,eAAe;YAC3B,IAAI,YAAY,WAAW;gBACzB,YAAY;YACd;QACF;QAEA,OAAQ,MAAM,GAAA,EAAK;YACjB,KAAK;gBACH,YAAY;gBACZ;YACF,KAAK;gBACH,YAAY;gBACZ;YACF,KAAK;gBACH,IAAI,gBAAgB,cAAc;oBAChC,IAAI,gBAAgB;wBAClB,SAAS;oBACX,OAAO;wBACL,SAAS;oBACX;gBACF;gBACA;YACF,KAAK;gBACH,IAAI,gBAAgB,YAAY;oBAC9B,SAAS;gBACX;gBACA;YACF,KAAK;gBACH,IAAI,gBAAgB,cAAc;oBAChC,IAAI,gBAAgB;wBAClB,SAAS;oBACX,OAAO;wBACL,SAAS;oBACX;gBACF;gBACA;YACF,KAAK;gBACH,IAAI,gBAAgB,YAAY;oBAC9B,SAAS;gBACX;gBACA;QACJ;QAEA,MAAM,eAAe,YAAY;QACjC,iBAAA,CAAkB,YAAY,CAAA,CAAG,GAAA,CAAI,OAAA,EAAS,MAAM;IACtD,CAAC;IAED,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,uBAAA;QACC,OAAO;QACP;QACA,WAAW;QACX;QAEA,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,WAAW,IAAA,EAAX;YAAgB,OAAO;YACtB,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,wKAAC,YAAA,CAAU,GAAA,EAAV;gBACE,GAAG,cAAA;gBACJ,oBAAkB;gBAClB,KAAK;gBACL,WAAW,WAAW,KAAA,IAAY;YAAA;QACpC,CACF;IAAA;AAGN;AAOF,IAAM,YAAY;AAGlB,IAAM,CAAC,uBAAuB,uBAAuB,CAAA,GACnD,uBAAkD,SAAS;AAqB7D,IAAM,sNAAgB,UAAA,CAAM,UAAA,CAC1B,CAAC,OAAwC,iBAAiB;IACxD,MAAM,EAAE,gBAAA,EAAkB,KAAA,EAAO,GAAG,mBAAmB,CAAA,GAAI;IAC3D,MAAM,mBAAmB,oBAAoB,WAAW,gBAAgB;IACxE,MAAM,eAAe,yBAAyB,WAAW,gBAAgB;IACzE,MAAM,mBAAmB,oBAAoB,gBAAgB;IAC7D,MAAM,+KAAY,QAAA,CAAM;IACxB,MAAM,OAAQ,SAAS,aAAa,KAAA,CAAM,QAAA,CAAS,KAAK,KAAM;IAC9D,MAAM,WAAW,iBAAiB,QAAA,IAAY,MAAM,QAAA;IAEpD,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,uBAAA;QACC,OAAO;QACP;QACA;QACA;QAEA,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAsB,wKAAA,IAAA,EAArB;YACC,oBAAkB,iBAAiB,WAAA;YACnC,cAAY,SAAS,IAAI;YACxB,GAAG,gBAAA;YACH,GAAG,kBAAA;YACJ,KAAK;YACL;YACA;YACA,cAAc,CAACC,UAAS;gBACtB,IAAIA,OAAM;oBACR,aAAa,UAAA,CAAW,KAAK;gBAC/B,OAAO;oBACL,aAAa,WAAA,CAAY,KAAK;gBAChC;YACF;QAAA;IACF;AAGN;AAGF,cAAc,WAAA,GAAc;AAM5B,IAAM,cAAc;AAUpB,IAAM,wNAAkB,UAAA,CAAM,UAAA,CAC5B,CAAC,OAA0C,iBAAiB;IAC1D,MAAM,EAAE,gBAAA,EAAkB,GAAG,YAAY,CAAA,GAAI;IAC7C,MAAM,mBAAmB,oBAAoB,gBAAgB,gBAAgB;IAC7E,MAAM,cAAc,wBAAwB,aAAa,gBAAgB;IACzE,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,wKAAC,YAAA,CAAU,EAAA,EAAV;QACC,oBAAkB,iBAAiB,WAAA;QACnC,cAAY,SAAS,YAAY,IAAI;QACrC,iBAAe,YAAY,QAAA,GAAW,KAAK,KAAA;QAC1C,GAAG,WAAA;QACJ,KAAK;IAAA;AAGX;AAGF,gBAAgB,WAAA,GAAc;AAM9B,IAAM,eAAe;AAUrB,IAAM,yNAAmB,UAAA,CAAM,UAAA,CAC7B,CAAC,OAA2C,iBAAiB;IAC3D,MAAM,EAAE,gBAAA,EAAkB,GAAG,aAAa,CAAA,GAAI;IAC9C,MAAM,mBAAmB,oBAAoB,gBAAgB,gBAAgB;IAC7E,MAAM,cAAc,wBAAwB,cAAc,gBAAgB;IAC1E,MAAM,qBAAqB,+BAA+B,cAAc,gBAAgB;IACxF,MAAM,mBAAmB,oBAAoB,gBAAgB;IAC7D,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,WAAW,QAAA,EAAX;QAAoB,OAAO;QAC1B,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAsB,wKAAA,OAAA,EAArB;YACC,iBAAgB,YAAY,IAAA,IAAQ,CAAC,mBAAmB,WAAA,IAAgB,KAAA;YACxE,oBAAkB,iBAAiB,WAAA;YACnC,IAAI,YAAY,SAAA;YACf,GAAG,gBAAA;YACH,GAAG,YAAA;YACJ,KAAK;QAAA;IACP,CACF;AAEJ;AAGF,iBAAiB,WAAA,GAAc;AAM/B,IAAM,eAAe;AASrB,IAAM,yNAAmB,UAAA,CAAM,UAAA,CAC7B,CAAC,OAA2C,iBAAiB;IAC3D,MAAM,EAAE,gBAAA,EAAkB,GAAG,aAAa,CAAA,GAAI;IAC9C,MAAM,mBAAmB,oBAAoB,gBAAgB,gBAAgB;IAC7E,MAAM,cAAc,wBAAwB,cAAc,gBAAgB;IAC1E,MAAM,mBAAmB,oBAAoB,gBAAgB;IAC7D,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAsB,wKAAA,OAAA,EAArB;QACC,MAAK;QACL,mBAAiB,YAAY,SAAA;QAC7B,oBAAkB,iBAAiB,WAAA;QAClC,GAAG,gBAAA;QACH,GAAG,YAAA;QACJ,KAAK;QACL,OAAO;YACL,CAAC,kCAAyC,CAAA,EAAG;YAC7C,CAAC,iCAAwC,CAAA,EAAG;YAC5C,GAAG,MAAM,KAAA;QACX;IAAA;AAGN;AAGF,iBAAiB,WAAA,GAAc;AAI/B,SAAS,SAAS,IAAA,EAAgB;IAChC,OAAO,OAAO,SAAS;AACzB;AAEA,IAAMC,QAAO;AACb,IAAM,OAAO;AACb,IAAM,SAAS;AACf,IAAMC,WAAU;AAChB,IAAMC,WAAU", "ignoreList": [0]}}, {"offset": {"line": 3628, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3634, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/%40radix-ui/react-progress/src/progress.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { Primitive } from '@radix-ui/react-primitive';\n\nimport type { Scope } from '@radix-ui/react-context';\n\n/* -------------------------------------------------------------------------------------------------\n * Progress\n * -----------------------------------------------------------------------------------------------*/\n\nconst PROGRESS_NAME = 'Progress';\nconst DEFAULT_MAX = 100;\n\ntype ScopedProps<P> = P & { __scopeProgress?: Scope };\nconst [createProgressContext, createProgressScope] = createContextScope(PROGRESS_NAME);\n\ntype ProgressState = 'indeterminate' | 'complete' | 'loading';\ntype ProgressContextValue = { value: number | null; max: number };\nconst [ProgressProvider, useProgressContext] =\n  createProgressContext<ProgressContextValue>(PROGRESS_NAME);\n\ntype ProgressElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface ProgressProps extends PrimitiveDivProps {\n  value?: number | null | undefined;\n  max?: number;\n  getValueLabel?(value: number, max: number): string;\n}\n\nconst Progress = React.forwardRef<ProgressElement, ProgressProps>(\n  (props: ScopedProps<ProgressProps>, forwardedRef) => {\n    const {\n      __scopeProgress,\n      value: valueProp = null,\n      max: maxProp,\n      getValueLabel = defaultGetValueLabel,\n      ...progressProps\n    } = props;\n\n    if ((maxProp || maxProp === 0) && !isValidMaxNumber(maxProp)) {\n      console.error(getInvalidMaxError(`${maxProp}`, 'Progress'));\n    }\n\n    const max = isValidMaxNumber(maxProp) ? maxProp : DEFAULT_MAX;\n\n    if (valueProp !== null && !isValidValueNumber(valueProp, max)) {\n      console.error(getInvalidValueError(`${valueProp}`, 'Progress'));\n    }\n\n    const value = isValidValueNumber(valueProp, max) ? valueProp : null;\n    const valueLabel = isNumber(value) ? getValueLabel(value, max) : undefined;\n\n    return (\n      <ProgressProvider scope={__scopeProgress} value={value} max={max}>\n        <Primitive.div\n          aria-valuemax={max}\n          aria-valuemin={0}\n          aria-valuenow={isNumber(value) ? value : undefined}\n          aria-valuetext={valueLabel}\n          role=\"progressbar\"\n          data-state={getProgressState(value, max)}\n          data-value={value ?? undefined}\n          data-max={max}\n          {...progressProps}\n          ref={forwardedRef}\n        />\n      </ProgressProvider>\n    );\n  }\n);\n\nProgress.displayName = PROGRESS_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * ProgressIndicator\n * -----------------------------------------------------------------------------------------------*/\n\nconst INDICATOR_NAME = 'ProgressIndicator';\n\ntype ProgressIndicatorElement = React.ComponentRef<typeof Primitive.div>;\ninterface ProgressIndicatorProps extends PrimitiveDivProps {}\n\nconst ProgressIndicator = React.forwardRef<ProgressIndicatorElement, ProgressIndicatorProps>(\n  (props: ScopedProps<ProgressIndicatorProps>, forwardedRef) => {\n    const { __scopeProgress, ...indicatorProps } = props;\n    const context = useProgressContext(INDICATOR_NAME, __scopeProgress);\n    return (\n      <Primitive.div\n        data-state={getProgressState(context.value, context.max)}\n        data-value={context.value ?? undefined}\n        data-max={context.max}\n        {...indicatorProps}\n        ref={forwardedRef}\n      />\n    );\n  }\n);\n\nProgressIndicator.displayName = INDICATOR_NAME;\n\n/* ---------------------------------------------------------------------------------------------- */\n\nfunction defaultGetValueLabel(value: number, max: number) {\n  return `${Math.round((value / max) * 100)}%`;\n}\n\nfunction getProgressState(value: number | undefined | null, maxValue: number): ProgressState {\n  return value == null ? 'indeterminate' : value === maxValue ? 'complete' : 'loading';\n}\n\nfunction isNumber(value: any): value is number {\n  return typeof value === 'number';\n}\n\nfunction isValidMaxNumber(max: any): max is number {\n  // prettier-ignore\n  return (\n    isNumber(max) &&\n    !isNaN(max) &&\n    max > 0\n  );\n}\n\nfunction isValidValueNumber(value: any, max: number): value is number {\n  // prettier-ignore\n  return (\n    isNumber(value) &&\n    !isNaN(value) &&\n    value <= max &&\n    value >= 0\n  );\n}\n\n// Split this out for clearer readability of the error message.\nfunction getInvalidMaxError(propValue: string, componentName: string) {\n  return `Invalid prop \\`max\\` of value \\`${propValue}\\` supplied to \\`${componentName}\\`. Only numbers greater than 0 are valid max values. Defaulting to \\`${DEFAULT_MAX}\\`.`;\n}\n\nfunction getInvalidValueError(propValue: string, componentName: string) {\n  return `Invalid prop \\`value\\` of value \\`${propValue}\\` supplied to \\`${componentName}\\`. The \\`value\\` prop must be:\n  - a positive number\n  - less than the value passed to \\`max\\` (or ${DEFAULT_MAX} if no \\`max\\` prop is set)\n  - \\`null\\` or \\`undefined\\` if the progress is indeterminate.\n\nDefaulting to \\`null\\`.`;\n}\n\nconst Root = Progress;\nconst Indicator = ProgressIndicator;\n\nexport {\n  createProgressScope,\n  //\n  Progress,\n  ProgressIndicator,\n  //\n  Root,\n  Indicator,\n};\nexport type { ProgressProps, ProgressIndicatorProps };\n"], "names": [], "mappings": ";;;;;;;;AAAA,YAAY,WAAW;AAsDf;AArDR,SAAS,0BAA0B;AACnC,SAAS,iBAAiB;;;;;;AAQ1B,IAAM,gBAAgB;AACtB,IAAM,cAAc;AAGpB,IAAM,CAAC,uBAAuB,mBAAmB,CAAA,2KAAI,qBAAA,EAAmB,aAAa;AAIrF,IAAM,CAAC,kBAAkB,kBAAkB,CAAA,GACzC,sBAA4C,aAAa;AAU3D,IAAM,WAAiB,sMAAA,UAAA,CACrB,CAAC,OAAmC,iBAAiB;IACnD,MAAM,EACJ,eAAA,EACA,OAAO,YAAY,IAAA,EACnB,KAAK,OAAA,EACL,gBAAgB,oBAAA,EAChB,GAAG,eACL,GAAI;IAEJ,IAAA,CAAK,WAAW,YAAY,CAAA,KAAM,CAAC,iBAAiB,OAAO,GAAG;QAC5D,QAAQ,KAAA,CAAM,mBAAmB,GAAG,OAAO,EAAA,EAAI,UAAU,CAAC;IAC5D;IAEA,MAAM,MAAM,iBAAiB,OAAO,IAAI,UAAU;IAElD,IAAI,cAAc,QAAQ,CAAC,mBAAmB,WAAW,GAAG,GAAG;QAC7D,QAAQ,KAAA,CAAM,qBAAqB,GAAG,SAAS,EAAA,EAAI,UAAU,CAAC;IAChE;IAEA,MAAM,QAAQ,mBAAmB,WAAW,GAAG,IAAI,YAAY;IAC/D,MAAM,aAAa,SAAS,KAAK,IAAI,cAAc,OAAO,GAAG,IAAI,KAAA;IAEjE,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,kBAAA;QAAiB,OAAO;QAAiB;QAAc;QACtD,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,wKAAC,YAAA,CAAU,GAAA,EAAV;YACC,iBAAe;YACf,iBAAe;YACf,iBAAe,SAAS,KAAK,IAAI,QAAQ,KAAA;YACzC,kBAAgB;YAChB,MAAK;YACL,cAAY,iBAAiB,OAAO,GAAG;YACvC,cAAY,SAAS,KAAA;YACrB,YAAU;YACT,GAAG,aAAA;YACJ,KAAK;QAAA;IACP,CACF;AAEJ;AAGF,SAAS,WAAA,GAAc;AAMvB,IAAM,iBAAiB;AAKvB,IAAM,oBAA0B,sMAAA,UAAA,CAC9B,CAAC,OAA4C,iBAAiB;IAC5D,MAAM,EAAE,eAAA,EAAiB,GAAG,eAAe,CAAA,GAAI;IAC/C,MAAM,UAAU,mBAAmB,gBAAgB,eAAe;IAClE,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,wKAAC,YAAA,CAAU,GAAA,EAAV;QACC,cAAY,iBAAiB,QAAQ,KAAA,EAAO,QAAQ,GAAG;QACvD,cAAY,QAAQ,KAAA,IAAS,KAAA;QAC7B,YAAU,QAAQ,GAAA;QACjB,GAAG,cAAA;QACJ,KAAK;IAAA;AAGX;AAGF,kBAAkB,WAAA,GAAc;AAIhC,SAAS,qBAAqB,KAAA,EAAe,GAAA,EAAa;IACxD,OAAO,GAAG,KAAK,KAAA,CAAO,QAAQ,MAAO,GAAG,CAAC,CAAA,CAAA,CAAA;AAC3C;AAEA,SAAS,iBAAiB,KAAA,EAAkC,QAAA,EAAiC;IAC3F,OAAO,SAAS,OAAO,kBAAkB,UAAU,WAAW,aAAa;AAC7E;AAEA,SAAS,SAAS,KAAA,EAA6B;IAC7C,OAAO,OAAO,UAAU;AAC1B;AAEA,SAAS,iBAAiB,GAAA,EAAyB;IAEjD,OACE,SAAS,GAAG,KACZ,CAAC,MAAM,GAAG,KACV,MAAM;AAEV;AAEA,SAAS,mBAAmB,KAAA,EAAY,GAAA,EAA8B;IAEpE,OACE,SAAS,KAAK,KACd,CAAC,MAAM,KAAK,KACZ,SAAS,OACT,SAAS;AAEb;AAGA,SAAS,mBAAmB,SAAA,EAAmB,aAAA,EAAuB;IACpE,OAAO,CAAA,gCAAA,EAAmC,SAAS,CAAA,iBAAA,EAAoB,aAAa,CAAA,sEAAA,EAAyE,WAAW,CAAA,GAAA,CAAA;AAC1K;AAEA,SAAS,qBAAqB,SAAA,EAAmB,aAAA,EAAuB;IACtE,OAAO,CAAA,kCAAA,EAAqC,SAAS,CAAA,iBAAA,EAAoB,aAAa,CAAA;;8CAAA,EAExC,WAAW,CAAA;;;uBAAA,CAAA;AAI3D;AAEA,IAAM,OAAO;AACb,IAAM,YAAY", "ignoreList": [0]}}, {"offset": {"line": 3728, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3734, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/%40radix-ui/react-dialog/src/dialog.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContext, createContextScope } from '@radix-ui/react-context';\nimport { useId } from '@radix-ui/react-id';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { DismissableLayer } from '@radix-ui/react-dismissable-layer';\nimport { FocusScope } from '@radix-ui/react-focus-scope';\nimport { Portal as PortalPrimitive } from '@radix-ui/react-portal';\nimport { Presence } from '@radix-ui/react-presence';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { useFocusGuards } from '@radix-ui/react-focus-guards';\nimport { RemoveScroll } from 'react-remove-scroll';\nimport { hideOthers } from 'aria-hidden';\nimport { createSlot } from '@radix-ui/react-slot';\n\nimport type { Scope } from '@radix-ui/react-context';\n\n/* -------------------------------------------------------------------------------------------------\n * Dialog\n * -----------------------------------------------------------------------------------------------*/\n\nconst DIALOG_NAME = 'Dialog';\n\ntype ScopedProps<P> = P & { __scopeDialog?: Scope };\nconst [createDialogContext, createDialogScope] = createContextScope(DIALOG_NAME);\n\ntype DialogContextValue = {\n  triggerRef: React.RefObject<HTMLButtonElement | null>;\n  contentRef: React.RefObject<DialogContentElement | null>;\n  contentId: string;\n  titleId: string;\n  descriptionId: string;\n  open: boolean;\n  onOpenChange(open: boolean): void;\n  onOpenToggle(): void;\n  modal: boolean;\n};\n\nconst [DialogProvider, useDialogContext] = createDialogContext<DialogContextValue>(DIALOG_NAME);\n\ninterface DialogProps {\n  children?: React.ReactNode;\n  open?: boolean;\n  defaultOpen?: boolean;\n  onOpenChange?(open: boolean): void;\n  modal?: boolean;\n}\n\nconst Dialog: React.FC<DialogProps> = (props: ScopedProps<DialogProps>) => {\n  const {\n    __scopeDialog,\n    children,\n    open: openProp,\n    defaultOpen,\n    onOpenChange,\n    modal = true,\n  } = props;\n  const triggerRef = React.useRef<HTMLButtonElement>(null);\n  const contentRef = React.useRef<DialogContentElement>(null);\n  const [open, setOpen] = useControllableState({\n    prop: openProp,\n    defaultProp: defaultOpen ?? false,\n    onChange: onOpenChange,\n    caller: DIALOG_NAME,\n  });\n\n  return (\n    <DialogProvider\n      scope={__scopeDialog}\n      triggerRef={triggerRef}\n      contentRef={contentRef}\n      contentId={useId()}\n      titleId={useId()}\n      descriptionId={useId()}\n      open={open}\n      onOpenChange={setOpen}\n      onOpenToggle={React.useCallback(() => setOpen((prevOpen) => !prevOpen), [setOpen])}\n      modal={modal}\n    >\n      {children}\n    </DialogProvider>\n  );\n};\n\nDialog.displayName = DIALOG_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DialogTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst TRIGGER_NAME = 'DialogTrigger';\n\ntype DialogTriggerElement = React.ComponentRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = React.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface DialogTriggerProps extends PrimitiveButtonProps {}\n\nconst DialogTrigger = React.forwardRef<DialogTriggerElement, DialogTriggerProps>(\n  (props: ScopedProps<DialogTriggerProps>, forwardedRef) => {\n    const { __scopeDialog, ...triggerProps } = props;\n    const context = useDialogContext(TRIGGER_NAME, __scopeDialog);\n    const composedTriggerRef = useComposedRefs(forwardedRef, context.triggerRef);\n    return (\n      <Primitive.button\n        type=\"button\"\n        aria-haspopup=\"dialog\"\n        aria-expanded={context.open}\n        aria-controls={context.contentId}\n        data-state={getState(context.open)}\n        {...triggerProps}\n        ref={composedTriggerRef}\n        onClick={composeEventHandlers(props.onClick, context.onOpenToggle)}\n      />\n    );\n  }\n);\n\nDialogTrigger.displayName = TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DialogPortal\n * -----------------------------------------------------------------------------------------------*/\n\nconst PORTAL_NAME = 'DialogPortal';\n\ntype PortalContextValue = { forceMount?: true };\nconst [PortalProvider, usePortalContext] = createDialogContext<PortalContextValue>(PORTAL_NAME, {\n  forceMount: undefined,\n});\n\ntype PortalProps = React.ComponentPropsWithoutRef<typeof PortalPrimitive>;\ninterface DialogPortalProps {\n  children?: React.ReactNode;\n  /**\n   * Specify a container element to portal the content into.\n   */\n  container?: PortalProps['container'];\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst DialogPortal: React.FC<DialogPortalProps> = (props: ScopedProps<DialogPortalProps>) => {\n  const { __scopeDialog, forceMount, children, container } = props;\n  const context = useDialogContext(PORTAL_NAME, __scopeDialog);\n  return (\n    <PortalProvider scope={__scopeDialog} forceMount={forceMount}>\n      {React.Children.map(children, (child) => (\n        <Presence present={forceMount || context.open}>\n          <PortalPrimitive asChild container={container}>\n            {child}\n          </PortalPrimitive>\n        </Presence>\n      ))}\n    </PortalProvider>\n  );\n};\n\nDialogPortal.displayName = PORTAL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DialogOverlay\n * -----------------------------------------------------------------------------------------------*/\n\nconst OVERLAY_NAME = 'DialogOverlay';\n\ntype DialogOverlayElement = DialogOverlayImplElement;\ninterface DialogOverlayProps extends DialogOverlayImplProps {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst DialogOverlay = React.forwardRef<DialogOverlayElement, DialogOverlayProps>(\n  (props: ScopedProps<DialogOverlayProps>, forwardedRef) => {\n    const portalContext = usePortalContext(OVERLAY_NAME, props.__scopeDialog);\n    const { forceMount = portalContext.forceMount, ...overlayProps } = props;\n    const context = useDialogContext(OVERLAY_NAME, props.__scopeDialog);\n    return context.modal ? (\n      <Presence present={forceMount || context.open}>\n        <DialogOverlayImpl {...overlayProps} ref={forwardedRef} />\n      </Presence>\n    ) : null;\n  }\n);\n\nDialogOverlay.displayName = OVERLAY_NAME;\n\ntype DialogOverlayImplElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface DialogOverlayImplProps extends PrimitiveDivProps {}\n\nconst Slot = createSlot('DialogOverlay.RemoveScroll');\n\nconst DialogOverlayImpl = React.forwardRef<DialogOverlayImplElement, DialogOverlayImplProps>(\n  (props: ScopedProps<DialogOverlayImplProps>, forwardedRef) => {\n    const { __scopeDialog, ...overlayProps } = props;\n    const context = useDialogContext(OVERLAY_NAME, __scopeDialog);\n    return (\n      // Make sure `Content` is scrollable even when it doesn't live inside `RemoveScroll`\n      // ie. when `Overlay` and `Content` are siblings\n      <RemoveScroll as={Slot} allowPinchZoom shards={[context.contentRef]}>\n        <Primitive.div\n          data-state={getState(context.open)}\n          {...overlayProps}\n          ref={forwardedRef}\n          // We re-enable pointer-events prevented by `Dialog.Content` to allow scrolling the overlay.\n          style={{ pointerEvents: 'auto', ...overlayProps.style }}\n        />\n      </RemoveScroll>\n    );\n  }\n);\n\n/* -------------------------------------------------------------------------------------------------\n * DialogContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'DialogContent';\n\ntype DialogContentElement = DialogContentTypeElement;\ninterface DialogContentProps extends DialogContentTypeProps {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst DialogContent = React.forwardRef<DialogContentElement, DialogContentProps>(\n  (props: ScopedProps<DialogContentProps>, forwardedRef) => {\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopeDialog);\n    const { forceMount = portalContext.forceMount, ...contentProps } = props;\n    const context = useDialogContext(CONTENT_NAME, props.__scopeDialog);\n    return (\n      <Presence present={forceMount || context.open}>\n        {context.modal ? (\n          <DialogContentModal {...contentProps} ref={forwardedRef} />\n        ) : (\n          <DialogContentNonModal {...contentProps} ref={forwardedRef} />\n        )}\n      </Presence>\n    );\n  }\n);\n\nDialogContent.displayName = CONTENT_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype DialogContentTypeElement = DialogContentImplElement;\ninterface DialogContentTypeProps\n  extends Omit<DialogContentImplProps, 'trapFocus' | 'disableOutsidePointerEvents'> {}\n\nconst DialogContentModal = React.forwardRef<DialogContentTypeElement, DialogContentTypeProps>(\n  (props: ScopedProps<DialogContentTypeProps>, forwardedRef) => {\n    const context = useDialogContext(CONTENT_NAME, props.__scopeDialog);\n    const contentRef = React.useRef<HTMLDivElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, context.contentRef, contentRef);\n\n    // aria-hide everything except the content (better supported equivalent to setting aria-modal)\n    React.useEffect(() => {\n      const content = contentRef.current;\n      if (content) return hideOthers(content);\n    }, []);\n\n    return (\n      <DialogContentImpl\n        {...props}\n        ref={composedRefs}\n        // we make sure focus isn't trapped once `DialogContent` has been closed\n        // (closed !== unmounted when animating out)\n        trapFocus={context.open}\n        disableOutsidePointerEvents\n        onCloseAutoFocus={composeEventHandlers(props.onCloseAutoFocus, (event) => {\n          event.preventDefault();\n          context.triggerRef.current?.focus();\n        })}\n        onPointerDownOutside={composeEventHandlers(props.onPointerDownOutside, (event) => {\n          const originalEvent = event.detail.originalEvent;\n          const ctrlLeftClick = originalEvent.button === 0 && originalEvent.ctrlKey === true;\n          const isRightClick = originalEvent.button === 2 || ctrlLeftClick;\n\n          // If the event is a right-click, we shouldn't close because\n          // it is effectively as if we right-clicked the `Overlay`.\n          if (isRightClick) event.preventDefault();\n        })}\n        // When focus is trapped, a `focusout` event may still happen.\n        // We make sure we don't trigger our `onDismiss` in such case.\n        onFocusOutside={composeEventHandlers(props.onFocusOutside, (event) =>\n          event.preventDefault()\n        )}\n      />\n    );\n  }\n);\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst DialogContentNonModal = React.forwardRef<DialogContentTypeElement, DialogContentTypeProps>(\n  (props: ScopedProps<DialogContentTypeProps>, forwardedRef) => {\n    const context = useDialogContext(CONTENT_NAME, props.__scopeDialog);\n    const hasInteractedOutsideRef = React.useRef(false);\n    const hasPointerDownOutsideRef = React.useRef(false);\n\n    return (\n      <DialogContentImpl\n        {...props}\n        ref={forwardedRef}\n        trapFocus={false}\n        disableOutsidePointerEvents={false}\n        onCloseAutoFocus={(event) => {\n          props.onCloseAutoFocus?.(event);\n\n          if (!event.defaultPrevented) {\n            if (!hasInteractedOutsideRef.current) context.triggerRef.current?.focus();\n            // Always prevent auto focus because we either focus manually or want user agent focus\n            event.preventDefault();\n          }\n\n          hasInteractedOutsideRef.current = false;\n          hasPointerDownOutsideRef.current = false;\n        }}\n        onInteractOutside={(event) => {\n          props.onInteractOutside?.(event);\n\n          if (!event.defaultPrevented) {\n            hasInteractedOutsideRef.current = true;\n            if (event.detail.originalEvent.type === 'pointerdown') {\n              hasPointerDownOutsideRef.current = true;\n            }\n          }\n\n          // Prevent dismissing when clicking the trigger.\n          // As the trigger is already setup to close, without doing so would\n          // cause it to close and immediately open.\n          const target = event.target as HTMLElement;\n          const targetIsTrigger = context.triggerRef.current?.contains(target);\n          if (targetIsTrigger) event.preventDefault();\n\n          // On Safari if the trigger is inside a container with tabIndex={0}, when clicked\n          // we will get the pointer down outside event on the trigger, but then a subsequent\n          // focus outside event on the container, we ignore any focus outside event when we've\n          // already had a pointer down outside event.\n          if (event.detail.originalEvent.type === 'focusin' && hasPointerDownOutsideRef.current) {\n            event.preventDefault();\n          }\n        }}\n      />\n    );\n  }\n);\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype DialogContentImplElement = React.ComponentRef<typeof DismissableLayer>;\ntype DismissableLayerProps = React.ComponentPropsWithoutRef<typeof DismissableLayer>;\ntype FocusScopeProps = React.ComponentPropsWithoutRef<typeof FocusScope>;\ninterface DialogContentImplProps extends Omit<DismissableLayerProps, 'onDismiss'> {\n  /**\n   * When `true`, focus cannot escape the `Content` via keyboard,\n   * pointer, or a programmatic focus.\n   * @defaultValue false\n   */\n  trapFocus?: FocusScopeProps['trapped'];\n\n  /**\n   * Event handler called when auto-focusing on open.\n   * Can be prevented.\n   */\n  onOpenAutoFocus?: FocusScopeProps['onMountAutoFocus'];\n\n  /**\n   * Event handler called when auto-focusing on close.\n   * Can be prevented.\n   */\n  onCloseAutoFocus?: FocusScopeProps['onUnmountAutoFocus'];\n}\n\nconst DialogContentImpl = React.forwardRef<DialogContentImplElement, DialogContentImplProps>(\n  (props: ScopedProps<DialogContentImplProps>, forwardedRef) => {\n    const { __scopeDialog, trapFocus, onOpenAutoFocus, onCloseAutoFocus, ...contentProps } = props;\n    const context = useDialogContext(CONTENT_NAME, __scopeDialog);\n    const contentRef = React.useRef<HTMLDivElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, contentRef);\n\n    // Make sure the whole tree has focus guards as our `Dialog` will be\n    // the last element in the DOM (because of the `Portal`)\n    useFocusGuards();\n\n    return (\n      <>\n        <FocusScope\n          asChild\n          loop\n          trapped={trapFocus}\n          onMountAutoFocus={onOpenAutoFocus}\n          onUnmountAutoFocus={onCloseAutoFocus}\n        >\n          <DismissableLayer\n            role=\"dialog\"\n            id={context.contentId}\n            aria-describedby={context.descriptionId}\n            aria-labelledby={context.titleId}\n            data-state={getState(context.open)}\n            {...contentProps}\n            ref={composedRefs}\n            onDismiss={() => context.onOpenChange(false)}\n          />\n        </FocusScope>\n        {process.env.NODE_ENV !== 'production' && (\n          <>\n            <TitleWarning titleId={context.titleId} />\n            <DescriptionWarning contentRef={contentRef} descriptionId={context.descriptionId} />\n          </>\n        )}\n      </>\n    );\n  }\n);\n\n/* -------------------------------------------------------------------------------------------------\n * DialogTitle\n * -----------------------------------------------------------------------------------------------*/\n\nconst TITLE_NAME = 'DialogTitle';\n\ntype DialogTitleElement = React.ComponentRef<typeof Primitive.h2>;\ntype PrimitiveHeading2Props = React.ComponentPropsWithoutRef<typeof Primitive.h2>;\ninterface DialogTitleProps extends PrimitiveHeading2Props {}\n\nconst DialogTitle = React.forwardRef<DialogTitleElement, DialogTitleProps>(\n  (props: ScopedProps<DialogTitleProps>, forwardedRef) => {\n    const { __scopeDialog, ...titleProps } = props;\n    const context = useDialogContext(TITLE_NAME, __scopeDialog);\n    return <Primitive.h2 id={context.titleId} {...titleProps} ref={forwardedRef} />;\n  }\n);\n\nDialogTitle.displayName = TITLE_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DialogDescription\n * -----------------------------------------------------------------------------------------------*/\n\nconst DESCRIPTION_NAME = 'DialogDescription';\n\ntype DialogDescriptionElement = React.ComponentRef<typeof Primitive.p>;\ntype PrimitiveParagraphProps = React.ComponentPropsWithoutRef<typeof Primitive.p>;\ninterface DialogDescriptionProps extends PrimitiveParagraphProps {}\n\nconst DialogDescription = React.forwardRef<DialogDescriptionElement, DialogDescriptionProps>(\n  (props: ScopedProps<DialogDescriptionProps>, forwardedRef) => {\n    const { __scopeDialog, ...descriptionProps } = props;\n    const context = useDialogContext(DESCRIPTION_NAME, __scopeDialog);\n    return <Primitive.p id={context.descriptionId} {...descriptionProps} ref={forwardedRef} />;\n  }\n);\n\nDialogDescription.displayName = DESCRIPTION_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DialogClose\n * -----------------------------------------------------------------------------------------------*/\n\nconst CLOSE_NAME = 'DialogClose';\n\ntype DialogCloseElement = React.ComponentRef<typeof Primitive.button>;\ninterface DialogCloseProps extends PrimitiveButtonProps {}\n\nconst DialogClose = React.forwardRef<DialogCloseElement, DialogCloseProps>(\n  (props: ScopedProps<DialogCloseProps>, forwardedRef) => {\n    const { __scopeDialog, ...closeProps } = props;\n    const context = useDialogContext(CLOSE_NAME, __scopeDialog);\n    return (\n      <Primitive.button\n        type=\"button\"\n        {...closeProps}\n        ref={forwardedRef}\n        onClick={composeEventHandlers(props.onClick, () => context.onOpenChange(false))}\n      />\n    );\n  }\n);\n\nDialogClose.displayName = CLOSE_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction getState(open: boolean) {\n  return open ? 'open' : 'closed';\n}\n\nconst TITLE_WARNING_NAME = 'DialogTitleWarning';\n\nconst [WarningProvider, useWarningContext] = createContext(TITLE_WARNING_NAME, {\n  contentName: CONTENT_NAME,\n  titleName: TITLE_NAME,\n  docsSlug: 'dialog',\n});\n\ntype TitleWarningProps = { titleId?: string };\n\nconst TitleWarning: React.FC<TitleWarningProps> = ({ titleId }) => {\n  const titleWarningContext = useWarningContext(TITLE_WARNING_NAME);\n\n  const MESSAGE = `\\`${titleWarningContext.contentName}\\` requires a \\`${titleWarningContext.titleName}\\` for the component to be accessible for screen reader users.\n\nIf you want to hide the \\`${titleWarningContext.titleName}\\`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/${titleWarningContext.docsSlug}`;\n\n  React.useEffect(() => {\n    if (titleId) {\n      const hasTitle = document.getElementById(titleId);\n      if (!hasTitle) console.error(MESSAGE);\n    }\n  }, [MESSAGE, titleId]);\n\n  return null;\n};\n\nconst DESCRIPTION_WARNING_NAME = 'DialogDescriptionWarning';\n\ntype DescriptionWarningProps = {\n  contentRef: React.RefObject<DialogContentElement | null>;\n  descriptionId?: string;\n};\n\nconst DescriptionWarning: React.FC<DescriptionWarningProps> = ({ contentRef, descriptionId }) => {\n  const descriptionWarningContext = useWarningContext(DESCRIPTION_WARNING_NAME);\n  const MESSAGE = `Warning: Missing \\`Description\\` or \\`aria-describedby={undefined}\\` for {${descriptionWarningContext.contentName}}.`;\n\n  React.useEffect(() => {\n    const describedById = contentRef.current?.getAttribute('aria-describedby');\n    // if we have an id and the user hasn't set aria-describedby={undefined}\n    if (descriptionId && describedById) {\n      const hasDescription = document.getElementById(descriptionId);\n      if (!hasDescription) console.warn(MESSAGE);\n    }\n  }, [MESSAGE, contentRef, descriptionId]);\n\n  return null;\n};\n\nconst Root = Dialog;\nconst Trigger = DialogTrigger;\nconst Portal = DialogPortal;\nconst Overlay = DialogOverlay;\nconst Content = DialogContent;\nconst Title = DialogTitle;\nconst Description = DialogDescription;\nconst Close = DialogClose;\n\nexport {\n  createDialogScope,\n  //\n  Dialog,\n  DialogTrigger,\n  DialogPortal,\n  DialogOverlay,\n  DialogContent,\n  DialogTitle,\n  DialogDescription,\n  DialogClose,\n  //\n  Root,\n  Trigger,\n  Portal,\n  Overlay,\n  Content,\n  Title,\n  Description,\n  Close,\n  //\n  WarningProvider,\n};\nexport type {\n  DialogProps,\n  DialogTriggerProps,\n  DialogPortalProps,\n  DialogOverlayProps,\n  DialogContentProps,\n  DialogTitleProps,\n  DialogDescriptionProps,\n  DialogCloseProps,\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA,YAAY,WAAW;AAoEnB,SA2VM,UA3VN,KA2VM,YA3VN;AAjEJ,SAAS,eAAe,0BAA0B;AAElD,SAAS,4BAA4B;AADrC,SAAS,aAAa;AAFtB,SAAS,uBAAuB;AAQhC,SAAS,iBAAiB;AAT1B,SAAS,4BAA4B;AAQrC,SAAS,gBAAgB;AADzB,SAAS,UAAU,uBAAuB;AAM1C,SAAS,kBAAkB;AAF3B,SAAS,oBAAoB;AAC7B,SAAS,kBAAkB;AAF3B,SAAS,sBAAsB;AAJ/B,SAAS,kBAAkB;AAD3B,SAAS,wBAAwB;;;;;;;;;;;;;;;;;;AAgBjC,IAAM,cAAc;AAGpB,IAAM,CAAC,qBAAqB,iBAAiB,CAAA,2KAAI,qBAAA,EAAmB,WAAW;AAc/E,IAAM,CAAC,gBAAgB,gBAAgB,CAAA,GAAI,oBAAwC,WAAW;AAU9F,IAAM,SAAgC,CAAC,UAAoC;IACzE,MAAM,EACJ,aAAA,EACA,QAAA,EACA,MAAM,QAAA,EACN,WAAA,EACA,YAAA,EACA,QAAQ,IAAA,EACV,GAAI;IACJ,MAAM,aAAmB,sMAAA,MAAA,CAA0B,IAAI;IACvD,MAAM,aAAmB,sMAAA,MAAA,CAA6B,IAAI;IAC1D,MAAM,CAAC,MAAM,OAAO,CAAA,gMAAI,uBAAA,EAAqB;QAC3C,MAAM;QACN,aAAa,eAAe;QAC5B,UAAU;QACV,QAAQ;IACV,CAAC;IAED,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,gBAAA;QACC,OAAO;QACP;QACA;QACA,8KAAW,QAAA,CAAM;QACjB,UAAS,0KAAA,CAAM;QACf,kLAAe,QAAA,CAAM;QACrB;QACA,cAAc;QACd,cAAoB,sMAAA,WAAA,CAAY,IAAM,QAAQ,CAAC,WAAa,CAAC,QAAQ,GAAG;YAAC,OAAO;SAAC;QACjF;QAEC;IAAA;AAGP;AAEA,OAAO,WAAA,GAAc;AAMrB,IAAM,eAAe;AAMrB,IAAM,gBAAsB,sMAAA,UAAA,CAC1B,CAAC,OAAwC,iBAAiB;IACxD,MAAM,EAAE,aAAA,EAAe,GAAG,aAAa,CAAA,GAAI;IAC3C,MAAM,UAAU,iBAAiB,cAAc,aAAa;IAC5D,MAAM,qMAAqB,kBAAA,EAAgB,cAAc,QAAQ,UAAU;IAC3E,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,wKAAC,YAAA,CAAU,MAAA,EAAV;QACC,MAAK;QACL,iBAAc;QACd,iBAAe,QAAQ,IAAA;QACvB,iBAAe,QAAQ,SAAA;QACvB,cAAY,SAAS,QAAQ,IAAI;QAChC,GAAG,YAAA;QACJ,KAAK;QACL,SAAS,wLAAA,EAAqB,MAAM,OAAA,EAAS,QAAQ,YAAY;IAAA;AAGvE;AAGF,cAAc,WAAA,GAAc;AAM5B,IAAM,cAAc;AAGpB,IAAM,CAAC,gBAAgB,gBAAgB,CAAA,GAAI,oBAAwC,aAAa;IAC9F,YAAY,KAAA;AACd,CAAC;AAgBD,IAAM,eAA4C,CAAC,UAA0C;IAC3F,MAAM,EAAE,aAAA,EAAe,UAAA,EAAY,QAAA,EAAU,SAAA,CAAU,CAAA,GAAI;IAC3D,MAAM,UAAU,iBAAiB,aAAa,aAAa;IAC3D,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,gBAAA;QAAe,OAAO;QAAe;QACnC,UAAM,sMAAA,QAAA,CAAS,GAAA,CAAI,UAAU,CAAC,QAC7B,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,uKAAC,WAAA,EAAA;gBAAS,SAAS,cAAc,QAAQ,IAAA;gBACvC,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,qKAAC,SAAA,EAAA;oBAAgB,SAAO;oBAAC;oBACtB,UAAA;gBAAA,CACH;YAAA,CACF,CACD;IAAA,CACH;AAEJ;AAEA,aAAa,WAAA,GAAc;AAM3B,IAAM,eAAe;AAWrB,IAAM,gBAAsB,sMAAA,UAAA,CAC1B,CAAC,OAAwC,iBAAiB;IACxD,MAAM,gBAAgB,iBAAiB,cAAc,MAAM,aAAa;IACxE,MAAM,EAAE,aAAa,cAAc,UAAA,EAAY,GAAG,aAAa,CAAA,GAAI;IACnE,MAAM,UAAU,iBAAiB,cAAc,MAAM,aAAa;IAClE,OAAO,QAAQ,KAAA,GACb,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,uKAAC,WAAA,EAAA;QAAS,SAAS,cAAc,QAAQ,IAAA;QACvC,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,mBAAA;YAAmB,GAAG,YAAA;YAAc,KAAK;QAAA,CAAc;IAAA,CAC1D,IACE;AACN;AAGF,cAAc,WAAA,GAAc;AAM5B,IAAM,4KAAO,aAAA,EAAW,4BAA4B;AAEpD,IAAM,oBAA0B,sMAAA,UAAA,CAC9B,CAAC,OAA4C,iBAAiB;IAC5D,MAAM,EAAE,aAAA,EAAe,GAAG,aAAa,CAAA,GAAI;IAC3C,MAAM,UAAU,iBAAiB,cAAc,aAAa;IAC5D,OAAA,oFAAA;IAAA,gDAAA;IAGE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,wNAAC,eAAA,EAAA;QAAa,IAAI;QAAM,gBAAc;QAAC,QAAQ;YAAC,QAAQ,UAAU;SAAA;QAChE,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,wKAAC,YAAA,CAAU,GAAA,EAAV;YACC,cAAY,SAAS,QAAQ,IAAI;YAChC,GAAG,YAAA;YACJ,KAAK;YAEL,OAAO;gBAAE,eAAe;gBAAQ,GAAG,aAAa,KAAA;YAAM;QAAA;IACxD,CACF;AAEJ;AAOF,IAAM,eAAe;AAWrB,IAAM,gBAAsB,sMAAA,UAAA,CAC1B,CAAC,OAAwC,iBAAiB;IACxD,MAAM,gBAAgB,iBAAiB,cAAc,MAAM,aAAa;IACxE,MAAM,EAAE,aAAa,cAAc,UAAA,EAAY,GAAG,aAAa,CAAA,GAAI;IACnE,MAAM,UAAU,iBAAiB,cAAc,MAAM,aAAa;IAClE,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,uKAAC,WAAA,EAAA;QAAS,SAAS,cAAc,QAAQ,IAAA;QACtC,UAAA,QAAQ,KAAA,GACP,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,oBAAA;YAAoB,GAAG,YAAA;YAAc,KAAK;QAAA,CAAc,IAEzD,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,uBAAA;YAAuB,GAAG,YAAA;YAAc,KAAK;QAAA,CAAc;IAAA,CAEhE;AAEJ;AAGF,cAAc,WAAA,GAAc;AAQ5B,IAAM,qBAA2B,sMAAA,UAAA,CAC/B,CAAC,OAA4C,iBAAiB;IAC5D,MAAM,UAAU,iBAAiB,cAAc,MAAM,aAAa;IAClE,MAAM,aAAmB,sMAAA,MAAA,CAAuB,IAAI;IACpD,MAAM,+LAAe,kBAAA,EAAgB,cAAc,QAAQ,UAAA,EAAY,UAAU;IAG3E,sMAAA,SAAA,CAAU,MAAM;QACpB,MAAM,UAAU,WAAW,OAAA;QAC3B,IAAI,QAAS,CAAA,QAAO,0KAAA,EAAW,OAAO;IACxC,GAAG,CAAC,CAAC;IAEL,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,mBAAA;QACE,GAAG,KAAA;QACJ,KAAK;QAGL,WAAW,QAAQ,IAAA;QACnB,6BAA2B;QAC3B,mLAAkB,uBAAA,EAAqB,MAAM,gBAAA,EAAkB,CAAC,UAAU;YACxE,MAAM,cAAA,CAAe;YACrB,QAAQ,UAAA,CAAW,OAAA,EAAS,MAAM;QACpC,CAAC;QACD,uLAAsB,uBAAA,EAAqB,MAAM,oBAAA,EAAsB,CAAC,UAAU;YAChF,MAAM,gBAAgB,MAAM,MAAA,CAAO,aAAA;YACnC,MAAM,gBAAgB,cAAc,MAAA,KAAW,KAAK,cAAc,OAAA,KAAY;YAC9E,MAAM,eAAe,cAAc,MAAA,KAAW,KAAK;YAInD,IAAI,aAAc,CAAA,MAAM,cAAA,CAAe;QACzC,CAAC;QAGD,iLAAgB,uBAAA,EAAqB,MAAM,cAAA,EAAgB,CAAC,QAC1D,MAAM,cAAA,CAAe;IACvB;AAGN;AAKF,IAAM,wBAA8B,sMAAA,UAAA,CAClC,CAAC,OAA4C,iBAAiB;IAC5D,MAAM,UAAU,iBAAiB,cAAc,MAAM,aAAa;IAClE,MAAM,0BAAgC,sMAAA,MAAA,CAAO,KAAK;IAClD,MAAM,2BAAiC,sMAAA,MAAA,CAAO,KAAK;IAEnD,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,mBAAA;QACE,GAAG,KAAA;QACJ,KAAK;QACL,WAAW;QACX,6BAA6B;QAC7B,kBAAkB,CAAC,UAAU;YAC3B,MAAM,gBAAA,GAAmB,KAAK;YAE9B,IAAI,CAAC,MAAM,gBAAA,EAAkB;gBAC3B,IAAI,CAAC,wBAAwB,OAAA,CAAS,CAAA,QAAQ,UAAA,CAAW,OAAA,EAAS,MAAM;gBAExE,MAAM,cAAA,CAAe;YACvB;YAEA,wBAAwB,OAAA,GAAU;YAClC,yBAAyB,OAAA,GAAU;QACrC;QACA,mBAAmB,CAAC,UAAU;YAC5B,MAAM,iBAAA,GAAoB,KAAK;YAE/B,IAAI,CAAC,MAAM,gBAAA,EAAkB;gBAC3B,wBAAwB,OAAA,GAAU;gBAClC,IAAI,MAAM,MAAA,CAAO,aAAA,CAAc,IAAA,KAAS,eAAe;oBACrD,yBAAyB,OAAA,GAAU;gBACrC;YACF;YAKA,MAAM,SAAS,MAAM,MAAA;YACrB,MAAM,kBAAkB,QAAQ,UAAA,CAAW,OAAA,EAAS,SAAS,MAAM;YACnE,IAAI,gBAAiB,CAAA,MAAM,cAAA,CAAe;YAM1C,IAAI,MAAM,MAAA,CAAO,aAAA,CAAc,IAAA,KAAS,aAAa,yBAAyB,OAAA,EAAS;gBACrF,MAAM,cAAA,CAAe;YACvB;QACF;IAAA;AAGN;AA6BF,IAAM,oBAA0B,sMAAA,UAAA,CAC9B,CAAC,OAA4C,iBAAiB;IAC5D,MAAM,EAAE,aAAA,EAAe,SAAA,EAAW,eAAA,EAAiB,gBAAA,EAAkB,GAAG,aAAa,CAAA,GAAI;IACzF,MAAM,UAAU,iBAAiB,cAAc,aAAa;IAC5D,MAAM,aAAmB,sMAAA,MAAA,CAAuB,IAAI;IACpD,MAAM,+LAAe,kBAAA,EAAgB,cAAc,UAAU;IAI7D,CAAA,GAAA,2KAAA,CAAA,iBAAA,CAAe;IAEf,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,OAAA,EAAA,uNAAA,CAAA,WAAA,EAAA;QACE,UAAA;YAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,6KAAC,aAAA,EAAA;gBACC,SAAO;gBACP,MAAI;gBACJ,SAAS;gBACT,kBAAkB;gBAClB,oBAAoB;gBAEpB,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,mLAAC,mBAAA,EAAA;oBACC,MAAK;oBACL,IAAI,QAAQ,SAAA;oBACZ,oBAAkB,QAAQ,aAAA;oBAC1B,mBAAiB,QAAQ,OAAA;oBACzB,cAAY,SAAS,QAAQ,IAAI;oBAChC,GAAG,YAAA;oBACJ,KAAK;oBACL,WAAW,IAAM,QAAQ,YAAA,CAAa,KAAK;gBAAA;YAC7C;YAGA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,OAAA,EAAA,uNAAA,CAAA,WAAA,EAAA;gBACE,UAAA;oBAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,cAAA;wBAAa,SAAS,QAAQ,OAAA;oBAAA,CAAS;oBACxC,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,oBAAA;wBAAmB;wBAAwB,eAAe,QAAQ,aAAA;oBAAA,CAAe;iBAAA;YAAA,CACpF;SAAA;IAAA,CAEJ;AAEJ;AAOF,IAAM,aAAa;AAMnB,IAAM,cAAoB,sMAAA,UAAA,CACxB,CAAC,OAAsC,iBAAiB;IACtD,MAAM,EAAE,aAAA,EAAe,GAAG,WAAW,CAAA,GAAI;IACzC,MAAM,UAAU,iBAAiB,YAAY,aAAa;IAC1D,OAAO,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,wKAAC,YAAA,CAAU,EAAA,EAAV;QAAa,IAAI,QAAQ,OAAA;QAAU,GAAG,UAAA;QAAY,KAAK;IAAA,CAAc;AAC/E;AAGF,YAAY,WAAA,GAAc;AAM1B,IAAM,mBAAmB;AAMzB,IAAM,oBAA0B,sMAAA,UAAA,CAC9B,CAAC,OAA4C,iBAAiB;IAC5D,MAAM,EAAE,aAAA,EAAe,GAAG,iBAAiB,CAAA,GAAI;IAC/C,MAAM,UAAU,iBAAiB,kBAAkB,aAAa;IAChE,OAAO,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,wKAAC,YAAA,CAAU,CAAA,EAAV;QAAY,IAAI,QAAQ,aAAA;QAAgB,GAAG,gBAAA;QAAkB,KAAK;IAAA,CAAc;AAC1F;AAGF,kBAAkB,WAAA,GAAc;AAMhC,IAAM,aAAa;AAKnB,IAAM,cAAoB,sMAAA,UAAA,CACxB,CAAC,OAAsC,iBAAiB;IACtD,MAAM,EAAE,aAAA,EAAe,GAAG,WAAW,CAAA,GAAI;IACzC,MAAM,UAAU,iBAAiB,YAAY,aAAa;IAC1D,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,wKAAC,YAAA,CAAU,MAAA,EAAV;QACC,MAAK;QACJ,GAAG,UAAA;QACJ,KAAK;QACL,0KAAS,uBAAA,EAAqB,MAAM,OAAA,EAAS,IAAM,QAAQ,YAAA,CAAa,KAAK,CAAC;IAAA;AAGpF;AAGF,YAAY,WAAA,GAAc;AAI1B,SAAS,SAAS,IAAA,EAAe;IAC/B,OAAO,OAAO,SAAS;AACzB;AAEA,IAAM,qBAAqB;AAE3B,IAAM,CAAC,iBAAiB,iBAAiB,CAAA,2KAAI,gBAAA,EAAc,oBAAoB;IAC7E,aAAa;IACb,WAAW;IACX,UAAU;AACZ,CAAC;AAID,IAAM,eAA4C,CAAC,EAAE,OAAA,CAAQ,CAAA,KAAM;IACjE,MAAM,sBAAsB,kBAAkB,kBAAkB;IAEhE,MAAM,UAAU,CAAA,EAAA,EAAK,oBAAoB,WAAW,CAAA,gBAAA,EAAmB,oBAAoB,SAAS,CAAA;;0BAAA,EAE1E,oBAAoB,SAAS,CAAA;;0EAAA,EAEmB,oBAAoB,QAAQ,EAAA;IAEhG,sMAAA,SAAA,CAAU,MAAM;QACpB,IAAI,SAAS;YACX,MAAM,WAAW,SAAS,cAAA,CAAe,OAAO;YAChD,IAAI,CAAC,SAAU,CAAA,QAAQ,KAAA,CAAM,OAAO;QACtC;IACF,GAAG;QAAC;QAAS,OAAO;KAAC;IAErB,OAAO;AACT;AAEA,IAAM,2BAA2B;AAOjC,IAAM,qBAAwD,CAAC,EAAE,UAAA,EAAY,aAAA,CAAc,CAAA,KAAM;IAC/F,MAAM,4BAA4B,kBAAkB,wBAAwB;IAC5E,MAAM,UAAU,CAAA,0EAAA,EAA6E,0BAA0B,WAAW,CAAA,EAAA,CAAA;IAE5H,sMAAA,SAAA,CAAU,MAAM;QACpB,MAAM,gBAAgB,WAAW,OAAA,EAAS,aAAa,kBAAkB;QAEzE,IAAI,iBAAiB,eAAe;YAClC,MAAM,iBAAiB,SAAS,cAAA,CAAe,aAAa;YAC5D,IAAI,CAAC,eAAgB,CAAA,QAAQ,IAAA,CAAK,OAAO;QAC3C;IACF,GAAG;QAAC;QAAS;QAAY,aAAa;KAAC;IAEvC,OAAO;AACT;AAEA,IAAM,OAAO;AACb,IAAM,UAAU;AAChB,IAAM,SAAS;AACf,IAAM,UAAU;AAChB,IAAM,UAAU;AAChB,IAAM,QAAQ;AACd,IAAM,cAAc;AACpB,IAAM,QAAQ", "ignoreList": [0]}}, {"offset": {"line": 4098, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4104, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/%40radix-ui/react-tooltip/src/tooltip.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { DismissableLayer } from '@radix-ui/react-dismissable-layer';\nimport { useId } from '@radix-ui/react-id';\nimport * as PopperPrimitive from '@radix-ui/react-popper';\nimport { createPopperScope } from '@radix-ui/react-popper';\nimport { Portal as PortalPrimitive } from '@radix-ui/react-portal';\nimport { Presence } from '@radix-ui/react-presence';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { createSlottable } from '@radix-ui/react-slot';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport * as VisuallyHiddenPrimitive from '@radix-ui/react-visually-hidden';\n\nimport type { Scope } from '@radix-ui/react-context';\n\ntype ScopedProps<P = {}> = P & { __scopeTooltip?: Scope };\nconst [createTooltipContext, createTooltipScope] = createContextScope('Tooltip', [\n  createPopperScope,\n]);\nconst usePopperScope = createPopperScope();\n\n/* -------------------------------------------------------------------------------------------------\n * TooltipProvider\n * -----------------------------------------------------------------------------------------------*/\n\nconst PROVIDER_NAME = 'TooltipProvider';\nconst DEFAULT_DELAY_DURATION = 700;\nconst TOOLTIP_OPEN = 'tooltip.open';\n\ntype TooltipProviderContextValue = {\n  isOpenDelayedRef: React.MutableRefObject<boolean>;\n  delayDuration: number;\n  onOpen(): void;\n  onClose(): void;\n  onPointerInTransitChange(inTransit: boolean): void;\n  isPointerInTransitRef: React.MutableRefObject<boolean>;\n  disableHoverableContent: boolean;\n};\n\nconst [TooltipProviderContextProvider, useTooltipProviderContext] =\n  createTooltipContext<TooltipProviderContextValue>(PROVIDER_NAME);\n\ninterface TooltipProviderProps {\n  children: React.ReactNode;\n  /**\n   * The duration from when the pointer enters the trigger until the tooltip gets opened.\n   * @defaultValue 700\n   */\n  delayDuration?: number;\n  /**\n   * How much time a user has to enter another trigger without incurring a delay again.\n   * @defaultValue 300\n   */\n  skipDelayDuration?: number;\n  /**\n   * When `true`, trying to hover the content will result in the tooltip closing as the pointer leaves the trigger.\n   * @defaultValue false\n   */\n  disableHoverableContent?: boolean;\n}\n\nconst TooltipProvider: React.FC<TooltipProviderProps> = (\n  props: ScopedProps<TooltipProviderProps>\n) => {\n  const {\n    __scopeTooltip,\n    delayDuration = DEFAULT_DELAY_DURATION,\n    skipDelayDuration = 300,\n    disableHoverableContent = false,\n    children,\n  } = props;\n  const isOpenDelayedRef = React.useRef(true);\n  const isPointerInTransitRef = React.useRef(false);\n  const skipDelayTimerRef = React.useRef(0);\n\n  React.useEffect(() => {\n    const skipDelayTimer = skipDelayTimerRef.current;\n    return () => window.clearTimeout(skipDelayTimer);\n  }, []);\n\n  return (\n    <TooltipProviderContextProvider\n      scope={__scopeTooltip}\n      isOpenDelayedRef={isOpenDelayedRef}\n      delayDuration={delayDuration}\n      onOpen={React.useCallback(() => {\n        window.clearTimeout(skipDelayTimerRef.current);\n        isOpenDelayedRef.current = false;\n      }, [])}\n      onClose={React.useCallback(() => {\n        window.clearTimeout(skipDelayTimerRef.current);\n        skipDelayTimerRef.current = window.setTimeout(\n          () => (isOpenDelayedRef.current = true),\n          skipDelayDuration\n        );\n      }, [skipDelayDuration])}\n      isPointerInTransitRef={isPointerInTransitRef}\n      onPointerInTransitChange={React.useCallback((inTransit: boolean) => {\n        isPointerInTransitRef.current = inTransit;\n      }, [])}\n      disableHoverableContent={disableHoverableContent}\n    >\n      {children}\n    </TooltipProviderContextProvider>\n  );\n};\n\nTooltipProvider.displayName = PROVIDER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * Tooltip\n * -----------------------------------------------------------------------------------------------*/\n\nconst TOOLTIP_NAME = 'Tooltip';\n\ntype TooltipContextValue = {\n  contentId: string;\n  open: boolean;\n  stateAttribute: 'closed' | 'delayed-open' | 'instant-open';\n  trigger: TooltipTriggerElement | null;\n  onTriggerChange(trigger: TooltipTriggerElement | null): void;\n  onTriggerEnter(): void;\n  onTriggerLeave(): void;\n  onOpen(): void;\n  onClose(): void;\n  disableHoverableContent: boolean;\n};\n\nconst [TooltipContextProvider, useTooltipContext] =\n  createTooltipContext<TooltipContextValue>(TOOLTIP_NAME);\n\ninterface TooltipProps {\n  children?: React.ReactNode;\n  open?: boolean;\n  defaultOpen?: boolean;\n  onOpenChange?: (open: boolean) => void;\n  /**\n   * The duration from when the pointer enters the trigger until the tooltip gets opened. This will\n   * override the prop with the same name passed to Provider.\n   * @defaultValue 700\n   */\n  delayDuration?: number;\n  /**\n   * When `true`, trying to hover the content will result in the tooltip closing as the pointer leaves the trigger.\n   * @defaultValue false\n   */\n  disableHoverableContent?: boolean;\n}\n\nconst Tooltip: React.FC<TooltipProps> = (props: ScopedProps<TooltipProps>) => {\n  const {\n    __scopeTooltip,\n    children,\n    open: openProp,\n    defaultOpen,\n    onOpenChange,\n    disableHoverableContent: disableHoverableContentProp,\n    delayDuration: delayDurationProp,\n  } = props;\n  const providerContext = useTooltipProviderContext(TOOLTIP_NAME, props.__scopeTooltip);\n  const popperScope = usePopperScope(__scopeTooltip);\n  const [trigger, setTrigger] = React.useState<HTMLButtonElement | null>(null);\n  const contentId = useId();\n  const openTimerRef = React.useRef(0);\n  const disableHoverableContent =\n    disableHoverableContentProp ?? providerContext.disableHoverableContent;\n  const delayDuration = delayDurationProp ?? providerContext.delayDuration;\n  const wasOpenDelayedRef = React.useRef(false);\n  const [open, setOpen] = useControllableState({\n    prop: openProp,\n    defaultProp: defaultOpen ?? false,\n    onChange: (open) => {\n      if (open) {\n        providerContext.onOpen();\n\n        // as `onChange` is called within a lifecycle method we\n        // avoid dispatching via `dispatchDiscreteCustomEvent`.\n        document.dispatchEvent(new CustomEvent(TOOLTIP_OPEN));\n      } else {\n        providerContext.onClose();\n      }\n      onOpenChange?.(open);\n    },\n    caller: TOOLTIP_NAME,\n  });\n  const stateAttribute = React.useMemo(() => {\n    return open ? (wasOpenDelayedRef.current ? 'delayed-open' : 'instant-open') : 'closed';\n  }, [open]);\n\n  const handleOpen = React.useCallback(() => {\n    window.clearTimeout(openTimerRef.current);\n    openTimerRef.current = 0;\n    wasOpenDelayedRef.current = false;\n    setOpen(true);\n  }, [setOpen]);\n\n  const handleClose = React.useCallback(() => {\n    window.clearTimeout(openTimerRef.current);\n    openTimerRef.current = 0;\n    setOpen(false);\n  }, [setOpen]);\n\n  const handleDelayedOpen = React.useCallback(() => {\n    window.clearTimeout(openTimerRef.current);\n    openTimerRef.current = window.setTimeout(() => {\n      wasOpenDelayedRef.current = true;\n      setOpen(true);\n      openTimerRef.current = 0;\n    }, delayDuration);\n  }, [delayDuration, setOpen]);\n\n  React.useEffect(() => {\n    return () => {\n      if (openTimerRef.current) {\n        window.clearTimeout(openTimerRef.current);\n        openTimerRef.current = 0;\n      }\n    };\n  }, []);\n\n  return (\n    <PopperPrimitive.Root {...popperScope}>\n      <TooltipContextProvider\n        scope={__scopeTooltip}\n        contentId={contentId}\n        open={open}\n        stateAttribute={stateAttribute}\n        trigger={trigger}\n        onTriggerChange={setTrigger}\n        onTriggerEnter={React.useCallback(() => {\n          if (providerContext.isOpenDelayedRef.current) handleDelayedOpen();\n          else handleOpen();\n        }, [providerContext.isOpenDelayedRef, handleDelayedOpen, handleOpen])}\n        onTriggerLeave={React.useCallback(() => {\n          if (disableHoverableContent) {\n            handleClose();\n          } else {\n            // Clear the timer in case the pointer leaves the trigger before the tooltip is opened.\n            window.clearTimeout(openTimerRef.current);\n            openTimerRef.current = 0;\n          }\n        }, [handleClose, disableHoverableContent])}\n        onOpen={handleOpen}\n        onClose={handleClose}\n        disableHoverableContent={disableHoverableContent}\n      >\n        {children}\n      </TooltipContextProvider>\n    </PopperPrimitive.Root>\n  );\n};\n\nTooltip.displayName = TOOLTIP_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * TooltipTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst TRIGGER_NAME = 'TooltipTrigger';\n\ntype TooltipTriggerElement = React.ComponentRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = React.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface TooltipTriggerProps extends PrimitiveButtonProps {}\n\nconst TooltipTrigger = React.forwardRef<TooltipTriggerElement, TooltipTriggerProps>(\n  (props: ScopedProps<TooltipTriggerProps>, forwardedRef) => {\n    const { __scopeTooltip, ...triggerProps } = props;\n    const context = useTooltipContext(TRIGGER_NAME, __scopeTooltip);\n    const providerContext = useTooltipProviderContext(TRIGGER_NAME, __scopeTooltip);\n    const popperScope = usePopperScope(__scopeTooltip);\n    const ref = React.useRef<TooltipTriggerElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref, context.onTriggerChange);\n    const isPointerDownRef = React.useRef(false);\n    const hasPointerMoveOpenedRef = React.useRef(false);\n    const handlePointerUp = React.useCallback(() => (isPointerDownRef.current = false), []);\n\n    React.useEffect(() => {\n      return () => document.removeEventListener('pointerup', handlePointerUp);\n    }, [handlePointerUp]);\n\n    return (\n      <PopperPrimitive.Anchor asChild {...popperScope}>\n        <Primitive.button\n          // We purposefully avoid adding `type=button` here because tooltip triggers are also\n          // commonly anchors and the anchor `type` attribute signifies MIME type.\n          aria-describedby={context.open ? context.contentId : undefined}\n          data-state={context.stateAttribute}\n          {...triggerProps}\n          ref={composedRefs}\n          onPointerMove={composeEventHandlers(props.onPointerMove, (event) => {\n            if (event.pointerType === 'touch') return;\n            if (\n              !hasPointerMoveOpenedRef.current &&\n              !providerContext.isPointerInTransitRef.current\n            ) {\n              context.onTriggerEnter();\n              hasPointerMoveOpenedRef.current = true;\n            }\n          })}\n          onPointerLeave={composeEventHandlers(props.onPointerLeave, () => {\n            context.onTriggerLeave();\n            hasPointerMoveOpenedRef.current = false;\n          })}\n          onPointerDown={composeEventHandlers(props.onPointerDown, () => {\n            if (context.open) {\n              context.onClose();\n            }\n            isPointerDownRef.current = true;\n            document.addEventListener('pointerup', handlePointerUp, { once: true });\n          })}\n          onFocus={composeEventHandlers(props.onFocus, () => {\n            if (!isPointerDownRef.current) context.onOpen();\n          })}\n          onBlur={composeEventHandlers(props.onBlur, context.onClose)}\n          onClick={composeEventHandlers(props.onClick, context.onClose)}\n        />\n      </PopperPrimitive.Anchor>\n    );\n  }\n);\n\nTooltipTrigger.displayName = TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * TooltipPortal\n * -----------------------------------------------------------------------------------------------*/\n\nconst PORTAL_NAME = 'TooltipPortal';\n\ntype PortalContextValue = { forceMount?: true };\nconst [PortalProvider, usePortalContext] = createTooltipContext<PortalContextValue>(PORTAL_NAME, {\n  forceMount: undefined,\n});\n\ntype PortalProps = React.ComponentPropsWithoutRef<typeof PortalPrimitive>;\ninterface TooltipPortalProps {\n  children?: React.ReactNode;\n  /**\n   * Specify a container element to portal the content into.\n   */\n  container?: PortalProps['container'];\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst TooltipPortal: React.FC<TooltipPortalProps> = (props: ScopedProps<TooltipPortalProps>) => {\n  const { __scopeTooltip, forceMount, children, container } = props;\n  const context = useTooltipContext(PORTAL_NAME, __scopeTooltip);\n  return (\n    <PortalProvider scope={__scopeTooltip} forceMount={forceMount}>\n      <Presence present={forceMount || context.open}>\n        <PortalPrimitive asChild container={container}>\n          {children}\n        </PortalPrimitive>\n      </Presence>\n    </PortalProvider>\n  );\n};\n\nTooltipPortal.displayName = PORTAL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * TooltipContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'TooltipContent';\n\ntype TooltipContentElement = TooltipContentImplElement;\ninterface TooltipContentProps extends TooltipContentImplProps {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst TooltipContent = React.forwardRef<TooltipContentElement, TooltipContentProps>(\n  (props: ScopedProps<TooltipContentProps>, forwardedRef) => {\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopeTooltip);\n    const { forceMount = portalContext.forceMount, side = 'top', ...contentProps } = props;\n    const context = useTooltipContext(CONTENT_NAME, props.__scopeTooltip);\n\n    return (\n      <Presence present={forceMount || context.open}>\n        {context.disableHoverableContent ? (\n          <TooltipContentImpl side={side} {...contentProps} ref={forwardedRef} />\n        ) : (\n          <TooltipContentHoverable side={side} {...contentProps} ref={forwardedRef} />\n        )}\n      </Presence>\n    );\n  }\n);\n\ntype Point = { x: number; y: number };\ntype Polygon = Point[];\n\ntype TooltipContentHoverableElement = TooltipContentImplElement;\ninterface TooltipContentHoverableProps extends TooltipContentImplProps {}\n\nconst TooltipContentHoverable = React.forwardRef<\n  TooltipContentHoverableElement,\n  TooltipContentHoverableProps\n>((props: ScopedProps<TooltipContentHoverableProps>, forwardedRef) => {\n  const context = useTooltipContext(CONTENT_NAME, props.__scopeTooltip);\n  const providerContext = useTooltipProviderContext(CONTENT_NAME, props.__scopeTooltip);\n  const ref = React.useRef<TooltipContentHoverableElement>(null);\n  const composedRefs = useComposedRefs(forwardedRef, ref);\n  const [pointerGraceArea, setPointerGraceArea] = React.useState<Polygon | null>(null);\n\n  const { trigger, onClose } = context;\n  const content = ref.current;\n\n  const { onPointerInTransitChange } = providerContext;\n\n  const handleRemoveGraceArea = React.useCallback(() => {\n    setPointerGraceArea(null);\n    onPointerInTransitChange(false);\n  }, [onPointerInTransitChange]);\n\n  const handleCreateGraceArea = React.useCallback(\n    (event: PointerEvent, hoverTarget: HTMLElement) => {\n      const currentTarget = event.currentTarget as HTMLElement;\n      const exitPoint = { x: event.clientX, y: event.clientY };\n      const exitSide = getExitSideFromRect(exitPoint, currentTarget.getBoundingClientRect());\n      const paddedExitPoints = getPaddedExitPoints(exitPoint, exitSide);\n      const hoverTargetPoints = getPointsFromRect(hoverTarget.getBoundingClientRect());\n      const graceArea = getHull([...paddedExitPoints, ...hoverTargetPoints]);\n      setPointerGraceArea(graceArea);\n      onPointerInTransitChange(true);\n    },\n    [onPointerInTransitChange]\n  );\n\n  React.useEffect(() => {\n    return () => handleRemoveGraceArea();\n  }, [handleRemoveGraceArea]);\n\n  React.useEffect(() => {\n    if (trigger && content) {\n      const handleTriggerLeave = (event: PointerEvent) => handleCreateGraceArea(event, content);\n      const handleContentLeave = (event: PointerEvent) => handleCreateGraceArea(event, trigger);\n\n      trigger.addEventListener('pointerleave', handleTriggerLeave);\n      content.addEventListener('pointerleave', handleContentLeave);\n      return () => {\n        trigger.removeEventListener('pointerleave', handleTriggerLeave);\n        content.removeEventListener('pointerleave', handleContentLeave);\n      };\n    }\n  }, [trigger, content, handleCreateGraceArea, handleRemoveGraceArea]);\n\n  React.useEffect(() => {\n    if (pointerGraceArea) {\n      const handleTrackPointerGrace = (event: PointerEvent) => {\n        const target = event.target as HTMLElement;\n        const pointerPosition = { x: event.clientX, y: event.clientY };\n        const hasEnteredTarget = trigger?.contains(target) || content?.contains(target);\n        const isPointerOutsideGraceArea = !isPointInPolygon(pointerPosition, pointerGraceArea);\n\n        if (hasEnteredTarget) {\n          handleRemoveGraceArea();\n        } else if (isPointerOutsideGraceArea) {\n          handleRemoveGraceArea();\n          onClose();\n        }\n      };\n      document.addEventListener('pointermove', handleTrackPointerGrace);\n      return () => document.removeEventListener('pointermove', handleTrackPointerGrace);\n    }\n  }, [trigger, content, pointerGraceArea, onClose, handleRemoveGraceArea]);\n\n  return <TooltipContentImpl {...props} ref={composedRefs} />;\n});\n\nconst [VisuallyHiddenContentContextProvider, useVisuallyHiddenContentContext] =\n  createTooltipContext(TOOLTIP_NAME, { isInside: false });\n\ntype TooltipContentImplElement = React.ComponentRef<typeof PopperPrimitive.Content>;\ntype DismissableLayerProps = React.ComponentPropsWithoutRef<typeof DismissableLayer>;\ntype PopperContentProps = React.ComponentPropsWithoutRef<typeof PopperPrimitive.Content>;\ninterface TooltipContentImplProps extends Omit<PopperContentProps, 'onPlaced'> {\n  /**\n   * A more descriptive label for accessibility purpose\n   */\n  'aria-label'?: string;\n\n  /**\n   * Event handler called when the escape key is down.\n   * Can be prevented.\n   */\n  onEscapeKeyDown?: DismissableLayerProps['onEscapeKeyDown'];\n  /**\n   * Event handler called when the a `pointerdown` event happens outside of the `Tooltip`.\n   * Can be prevented.\n   */\n  onPointerDownOutside?: DismissableLayerProps['onPointerDownOutside'];\n}\n\nconst Slottable = createSlottable('TooltipContent');\n\nconst TooltipContentImpl = React.forwardRef<TooltipContentImplElement, TooltipContentImplProps>(\n  (props: ScopedProps<TooltipContentImplProps>, forwardedRef) => {\n    const {\n      __scopeTooltip,\n      children,\n      'aria-label': ariaLabel,\n      onEscapeKeyDown,\n      onPointerDownOutside,\n      ...contentProps\n    } = props;\n    const context = useTooltipContext(CONTENT_NAME, __scopeTooltip);\n    const popperScope = usePopperScope(__scopeTooltip);\n    const { onClose } = context;\n\n    // Close this tooltip if another one opens\n    React.useEffect(() => {\n      document.addEventListener(TOOLTIP_OPEN, onClose);\n      return () => document.removeEventListener(TOOLTIP_OPEN, onClose);\n    }, [onClose]);\n\n    // Close the tooltip if the trigger is scrolled\n    React.useEffect(() => {\n      if (context.trigger) {\n        const handleScroll = (event: Event) => {\n          const target = event.target as HTMLElement;\n          if (target?.contains(context.trigger)) onClose();\n        };\n        window.addEventListener('scroll', handleScroll, { capture: true });\n        return () => window.removeEventListener('scroll', handleScroll, { capture: true });\n      }\n    }, [context.trigger, onClose]);\n\n    return (\n      <DismissableLayer\n        asChild\n        disableOutsidePointerEvents={false}\n        onEscapeKeyDown={onEscapeKeyDown}\n        onPointerDownOutside={onPointerDownOutside}\n        onFocusOutside={(event) => event.preventDefault()}\n        onDismiss={onClose}\n      >\n        <PopperPrimitive.Content\n          data-state={context.stateAttribute}\n          {...popperScope}\n          {...contentProps}\n          ref={forwardedRef}\n          style={{\n            ...contentProps.style,\n            // re-namespace exposed content custom properties\n            ...{\n              '--radix-tooltip-content-transform-origin': 'var(--radix-popper-transform-origin)',\n              '--radix-tooltip-content-available-width': 'var(--radix-popper-available-width)',\n              '--radix-tooltip-content-available-height': 'var(--radix-popper-available-height)',\n              '--radix-tooltip-trigger-width': 'var(--radix-popper-anchor-width)',\n              '--radix-tooltip-trigger-height': 'var(--radix-popper-anchor-height)',\n            },\n          }}\n        >\n          <Slottable>{children}</Slottable>\n          <VisuallyHiddenContentContextProvider scope={__scopeTooltip} isInside={true}>\n            <VisuallyHiddenPrimitive.Root id={context.contentId} role=\"tooltip\">\n              {ariaLabel || children}\n            </VisuallyHiddenPrimitive.Root>\n          </VisuallyHiddenContentContextProvider>\n        </PopperPrimitive.Content>\n      </DismissableLayer>\n    );\n  }\n);\n\nTooltipContent.displayName = CONTENT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * TooltipArrow\n * -----------------------------------------------------------------------------------------------*/\n\nconst ARROW_NAME = 'TooltipArrow';\n\ntype TooltipArrowElement = React.ComponentRef<typeof PopperPrimitive.Arrow>;\ntype PopperArrowProps = React.ComponentPropsWithoutRef<typeof PopperPrimitive.Arrow>;\ninterface TooltipArrowProps extends PopperArrowProps {}\n\nconst TooltipArrow = React.forwardRef<TooltipArrowElement, TooltipArrowProps>(\n  (props: ScopedProps<TooltipArrowProps>, forwardedRef) => {\n    const { __scopeTooltip, ...arrowProps } = props;\n    const popperScope = usePopperScope(__scopeTooltip);\n    const visuallyHiddenContentContext = useVisuallyHiddenContentContext(\n      ARROW_NAME,\n      __scopeTooltip\n    );\n    // if the arrow is inside the `VisuallyHidden`, we don't want to render it all to\n    // prevent issues in positioning the arrow due to the duplicate\n    return visuallyHiddenContentContext.isInside ? null : (\n      <PopperPrimitive.Arrow {...popperScope} {...arrowProps} ref={forwardedRef} />\n    );\n  }\n);\n\nTooltipArrow.displayName = ARROW_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype Side = NonNullable<TooltipContentProps['side']>;\n\nfunction getExitSideFromRect(point: Point, rect: DOMRect): Side {\n  const top = Math.abs(rect.top - point.y);\n  const bottom = Math.abs(rect.bottom - point.y);\n  const right = Math.abs(rect.right - point.x);\n  const left = Math.abs(rect.left - point.x);\n\n  switch (Math.min(top, bottom, right, left)) {\n    case left:\n      return 'left';\n    case right:\n      return 'right';\n    case top:\n      return 'top';\n    case bottom:\n      return 'bottom';\n    default:\n      throw new Error('unreachable');\n  }\n}\n\nfunction getPaddedExitPoints(exitPoint: Point, exitSide: Side, padding = 5) {\n  const paddedExitPoints: Point[] = [];\n  switch (exitSide) {\n    case 'top':\n      paddedExitPoints.push(\n        { x: exitPoint.x - padding, y: exitPoint.y + padding },\n        { x: exitPoint.x + padding, y: exitPoint.y + padding }\n      );\n      break;\n    case 'bottom':\n      paddedExitPoints.push(\n        { x: exitPoint.x - padding, y: exitPoint.y - padding },\n        { x: exitPoint.x + padding, y: exitPoint.y - padding }\n      );\n      break;\n    case 'left':\n      paddedExitPoints.push(\n        { x: exitPoint.x + padding, y: exitPoint.y - padding },\n        { x: exitPoint.x + padding, y: exitPoint.y + padding }\n      );\n      break;\n    case 'right':\n      paddedExitPoints.push(\n        { x: exitPoint.x - padding, y: exitPoint.y - padding },\n        { x: exitPoint.x - padding, y: exitPoint.y + padding }\n      );\n      break;\n  }\n  return paddedExitPoints;\n}\n\nfunction getPointsFromRect(rect: DOMRect) {\n  const { top, right, bottom, left } = rect;\n  return [\n    { x: left, y: top },\n    { x: right, y: top },\n    { x: right, y: bottom },\n    { x: left, y: bottom },\n  ];\n}\n\n// Determine if a point is inside of a polygon.\n// Based on https://github.com/substack/point-in-polygon\nfunction isPointInPolygon(point: Point, polygon: Polygon) {\n  const { x, y } = point;\n  let inside = false;\n  for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {\n    const ii = polygon[i]!;\n    const jj = polygon[j]!;\n    const xi = ii.x;\n    const yi = ii.y;\n    const xj = jj.x;\n    const yj = jj.y;\n\n    // prettier-ignore\n    const intersect = ((yi > y) !== (yj > y)) && (x < (xj - xi) * (y - yi) / (yj - yi) + xi);\n    if (intersect) inside = !inside;\n  }\n\n  return inside;\n}\n\n// Returns a new array of points representing the convex hull of the given set of points.\n// https://www.nayuki.io/page/convex-hull-algorithm\nfunction getHull<P extends Point>(points: Readonly<Array<P>>): Array<P> {\n  const newPoints: Array<P> = points.slice();\n  newPoints.sort((a: Point, b: Point) => {\n    if (a.x < b.x) return -1;\n    else if (a.x > b.x) return +1;\n    else if (a.y < b.y) return -1;\n    else if (a.y > b.y) return +1;\n    else return 0;\n  });\n  return getHullPresorted(newPoints);\n}\n\n// Returns the convex hull, assuming that each points[i] <= points[i + 1]. Runs in O(n) time.\nfunction getHullPresorted<P extends Point>(points: Readonly<Array<P>>): Array<P> {\n  if (points.length <= 1) return points.slice();\n\n  const upperHull: Array<P> = [];\n  for (let i = 0; i < points.length; i++) {\n    const p = points[i]!;\n    while (upperHull.length >= 2) {\n      const q = upperHull[upperHull.length - 1]!;\n      const r = upperHull[upperHull.length - 2]!;\n      if ((q.x - r.x) * (p.y - r.y) >= (q.y - r.y) * (p.x - r.x)) upperHull.pop();\n      else break;\n    }\n    upperHull.push(p);\n  }\n  upperHull.pop();\n\n  const lowerHull: Array<P> = [];\n  for (let i = points.length - 1; i >= 0; i--) {\n    const p = points[i]!;\n    while (lowerHull.length >= 2) {\n      const q = lowerHull[lowerHull.length - 1]!;\n      const r = lowerHull[lowerHull.length - 2]!;\n      if ((q.x - r.x) * (p.y - r.y) >= (q.y - r.y) * (p.x - r.x)) lowerHull.pop();\n      else break;\n    }\n    lowerHull.push(p);\n  }\n  lowerHull.pop();\n\n  if (\n    upperHull.length === 1 &&\n    lowerHull.length === 1 &&\n    upperHull[0]!.x === lowerHull[0]!.x &&\n    upperHull[0]!.y === lowerHull[0]!.y\n  ) {\n    return upperHull;\n  } else {\n    return upperHull.concat(lowerHull);\n  }\n}\n\nconst Provider = TooltipProvider;\nconst Root = Tooltip;\nconst Trigger = TooltipTrigger;\nconst Portal = TooltipPortal;\nconst Content = TooltipContent;\nconst Arrow = TooltipArrow;\n\nexport {\n  createTooltipScope,\n  //\n  TooltipProvider,\n  Tooltip,\n  TooltipTrigger,\n  TooltipPortal,\n  TooltipContent,\n  TooltipArrow,\n  //\n  Provider,\n  Root,\n  Trigger,\n  Portal,\n  Content,\n  Arrow,\n};\nexport type {\n  TooltipProviderProps,\n  TooltipProps,\n  TooltipTriggerProps,\n  TooltipPortalProps,\n  TooltipContentProps,\n  TooltipArrowProps,\n};\n"], "names": ["open", "Root", "Content", "Arrow"], "mappings": ";;;;;;;;;;;;;;;;AAAA,YAAY,WAAW;AAmFnB,cAgdI,YAhdJ;AAhFJ,SAAS,0BAA0B;AAInC,SAAS,yBAAyB;AAFlC,SAAS,aAAa;AAOtB,SAAS,4BAA4B;AAVrC,SAAS,uBAAuB;AAQhC,SAAS,iBAAiB;AAT1B,SAAS,4BAA4B;AAQrC,SAAS,gBAAgB;AADzB,SAAS,UAAU,uBAAuB;AAG1C,SAAS,uBAAuB;AAPhC,SAAS,wBAAwB;AASjC,YAAY,6BAA6B;;;;;;;;;;;;;;;;;AAKzC,IAAM,CAAC,sBAAsB,kBAAkB,CAAA,2KAAI,qBAAA,EAAmB,WAAW;uKAC/E,oBAAA;CACD;AACD,IAAM,wLAAiB,oBAAA,CAAkB;AAMzC,IAAM,gBAAgB;AACtB,IAAM,yBAAyB;AAC/B,IAAM,eAAe;AAYrB,IAAM,CAAC,gCAAgC,yBAAyB,CAAA,GAC9D,qBAAkD,aAAa;AAqBjE,IAAM,kBAAkD,CACtD,UACG;IACH,MAAM,EACJ,cAAA,EACA,gBAAgB,sBAAA,EAChB,oBAAoB,GAAA,EACpB,0BAA0B,KAAA,EAC1B,QAAA,EACF,GAAI;IACJ,MAAM,mBAAyB,sMAAA,MAAA,CAAO,IAAI;IAC1C,MAAM,wBAA8B,sMAAA,MAAA,CAAO,KAAK;IAChD,MAAM,oBAA0B,sMAAA,MAAA,CAAO,CAAC;IAElC,sMAAA,SAAA,CAAU,MAAM;QACpB,MAAM,iBAAiB,kBAAkB,OAAA;QACzC,OAAO,IAAM,OAAO,YAAA,CAAa,cAAc;IACjD,GAAG,CAAC,CAAC;IAEL,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,gCAAA;QACC,OAAO;QACP;QACA;QACA,QAAc,sMAAA,WAAA,CAAY,MAAM;YAC9B,OAAO,YAAA,CAAa,kBAAkB,OAAO;YAC7C,iBAAiB,OAAA,GAAU;QAC7B,GAAG,CAAC,CAAC;QACL,SAAe,sMAAA,WAAA,CAAY,MAAM;YAC/B,OAAO,YAAA,CAAa,kBAAkB,OAAO;YAC7C,kBAAkB,OAAA,GAAU,OAAO,UAAA,CACjC,IAAO,iBAAiB,OAAA,GAAU,MAClC;QAEJ,GAAG;YAAC,iBAAiB;SAAC;QACtB;QACA,0BAAgC,sMAAA,WAAA,CAAY,CAAC,cAAuB;YAClE,sBAAsB,OAAA,GAAU;QAClC,GAAG,CAAC,CAAC;QACL;QAEC;IAAA;AAGP;AAEA,gBAAgB,WAAA,GAAc;AAM9B,IAAM,eAAe;AAerB,IAAM,CAAC,wBAAwB,iBAAiB,CAAA,GAC9C,qBAA0C,YAAY;AAoBxD,IAAM,UAAkC,CAAC,UAAqC;IAC5E,MAAM,EACJ,cAAA,EACA,QAAA,EACA,MAAM,QAAA,EACN,WAAA,EACA,YAAA,EACA,yBAAyB,2BAAA,EACzB,eAAe,iBAAA,EACjB,GAAI;IACJ,MAAM,kBAAkB,0BAA0B,cAAc,MAAM,cAAc;IACpF,MAAM,cAAc,eAAe,cAAc;IACjD,MAAM,CAAC,SAAS,UAAU,CAAA,GAAU,sMAAA,QAAA,CAAmC,IAAI;IAC3E,MAAM,+KAAY,QAAA,CAAM;IACxB,MAAM,eAAqB,sMAAA,MAAA,CAAO,CAAC;IACnC,MAAM,0BACJ,+BAA+B,gBAAgB,uBAAA;IACjD,MAAM,gBAAgB,qBAAqB,gBAAgB,aAAA;IAC3D,MAAM,oBAA0B,sMAAA,MAAA,CAAO,KAAK;IAC5C,MAAM,CAAC,MAAM,OAAO,CAAA,gMAAI,uBAAA,EAAqB;QAC3C,MAAM;QACN,aAAa,eAAe;QAC5B,UAAU,CAACA,UAAS;YAClB,IAAIA,OAAM;gBACR,gBAAgB,MAAA,CAAO;gBAIvB,SAAS,aAAA,CAAc,IAAI,YAAY,YAAY,CAAC;YACtD,OAAO;gBACL,gBAAgB,OAAA,CAAQ;YAC1B;YACA,eAAeA,KAAI;QACrB;QACA,QAAQ;IACV,CAAC;IACD,MAAM,iBAAuB,sMAAA,OAAA,CAAQ,MAAM;QACzC,OAAO,OAAQ,kBAAkB,OAAA,GAAU,iBAAiB,iBAAkB;IAChF,GAAG;QAAC,IAAI;KAAC;IAET,MAAM,aAAmB,sMAAA,WAAA,CAAY,MAAM;QACzC,OAAO,YAAA,CAAa,aAAa,OAAO;QACxC,aAAa,OAAA,GAAU;QACvB,kBAAkB,OAAA,GAAU;QAC5B,QAAQ,IAAI;IACd,GAAG;QAAC,OAAO;KAAC;IAEZ,MAAM,cAAoB,sMAAA,WAAA,CAAY,MAAM;QAC1C,OAAO,YAAA,CAAa,aAAa,OAAO;QACxC,aAAa,OAAA,GAAU;QACvB,QAAQ,KAAK;IACf,GAAG;QAAC,OAAO;KAAC;IAEZ,MAAM,oBAA0B,sMAAA,WAAA,CAAY,MAAM;QAChD,OAAO,YAAA,CAAa,aAAa,OAAO;QACxC,aAAa,OAAA,GAAU,OAAO,UAAA,CAAW,MAAM;YAC7C,kBAAkB,OAAA,GAAU;YAC5B,QAAQ,IAAI;YACZ,aAAa,OAAA,GAAU;QACzB,GAAG,aAAa;IAClB,GAAG;QAAC;QAAe,OAAO;KAAC;IAErB,sMAAA,SAAA,CAAU,MAAM;QACpB,OAAO,MAAM;YACX,IAAI,aAAa,OAAA,EAAS;gBACxB,OAAO,YAAA,CAAa,aAAa,OAAO;gBACxC,aAAa,OAAA,GAAU;YACzB;QACF;IACF,GAAG,CAAC,CAAC;IAEL,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAiB,mKAAA,IAAA,EAAhB;QAAsB,GAAG,WAAA;QACxB,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,wBAAA;YACC,OAAO;YACP;YACA;YACA;YACA;YACA,iBAAiB;YACjB,gBAAsB,sMAAA,WAAA,CAAY,MAAM;gBACtC,IAAI,gBAAgB,gBAAA,CAAiB,OAAA,CAAS,CAAA,kBAAkB;qBAC3D,WAAW;YAClB,GAAG;gBAAC,gBAAgB,gBAAA;gBAAkB;gBAAmB,UAAU;aAAC;YACpE,gBAAsB,sMAAA,WAAA,CAAY,MAAM;gBACtC,IAAI,yBAAyB;oBAC3B,YAAY;gBACd,OAAO;oBAEL,OAAO,YAAA,CAAa,aAAa,OAAO;oBACxC,aAAa,OAAA,GAAU;gBACzB;YACF,GAAG;gBAAC;gBAAa,uBAAuB;aAAC;YACzC,QAAQ;YACR,SAAS;YACT;YAEC;QAAA;IACH,CACF;AAEJ;AAEA,QAAQ,WAAA,GAAc;AAMtB,IAAM,eAAe;AAMrB,IAAM,iBAAuB,sMAAA,UAAA,CAC3B,CAAC,OAAyC,iBAAiB;IACzD,MAAM,EAAE,cAAA,EAAgB,GAAG,aAAa,CAAA,GAAI;IAC5C,MAAM,UAAU,kBAAkB,cAAc,cAAc;IAC9D,MAAM,kBAAkB,0BAA0B,cAAc,cAAc;IAC9E,MAAM,cAAc,eAAe,cAAc;IACjD,MAAM,MAAY,sMAAA,MAAA,CAA8B,IAAI;IACpD,MAAM,eAAe,kMAAA,EAAgB,cAAc,KAAK,QAAQ,eAAe;IAC/E,MAAM,mBAAyB,sMAAA,MAAA,CAAO,KAAK;IAC3C,MAAM,0BAAgC,sMAAA,MAAA,CAAO,KAAK;IAClD,MAAM,kBAAwB,sMAAA,WAAA,CAAY,IAAO,iBAAiB,OAAA,GAAU,OAAQ,CAAC,CAAC;IAEhF,sMAAA,SAAA,CAAU,MAAM;QACpB,OAAO,IAAM,SAAS,mBAAA,CAAoB,aAAa,eAAe;IACxE,GAAG;QAAC,eAAe;KAAC;IAEpB,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAiB,mKAAA,MAAA,EAAhB;QAAuB,SAAO;QAAE,GAAG,WAAA;QAClC,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,wKAAC,YAAA,CAAU,MAAA,EAAV;YAGC,oBAAkB,QAAQ,IAAA,GAAO,QAAQ,SAAA,GAAY,KAAA;YACrD,cAAY,QAAQ,cAAA;YACnB,GAAG,YAAA;YACJ,KAAK;YACL,gLAAe,uBAAA,EAAqB,MAAM,aAAA,EAAe,CAAC,UAAU;gBAClE,IAAI,MAAM,WAAA,KAAgB,QAAS,CAAA;gBACnC,IACE,CAAC,wBAAwB,OAAA,IACzB,CAAC,gBAAgB,qBAAA,CAAsB,OAAA,EACvC;oBACA,QAAQ,cAAA,CAAe;oBACvB,wBAAwB,OAAA,GAAU;gBACpC;YACF,CAAC;YACD,iLAAgB,uBAAA,EAAqB,MAAM,cAAA,EAAgB,MAAM;gBAC/D,QAAQ,cAAA,CAAe;gBACvB,wBAAwB,OAAA,GAAU;YACpC,CAAC;YACD,gLAAe,uBAAA,EAAqB,MAAM,aAAA,EAAe,MAAM;gBAC7D,IAAI,QAAQ,IAAA,EAAM;oBAChB,QAAQ,OAAA,CAAQ;gBAClB;gBACA,iBAAiB,OAAA,GAAU;gBAC3B,SAAS,gBAAA,CAAiB,aAAa,iBAAiB;oBAAE,MAAM;gBAAK,CAAC;YACxE,CAAC;YACD,0KAAS,uBAAA,EAAqB,MAAM,OAAA,EAAS,MAAM;gBACjD,IAAI,CAAC,iBAAiB,OAAA,CAAS,CAAA,QAAQ,MAAA,CAAO;YAChD,CAAC;YACD,yKAAQ,uBAAA,EAAqB,MAAM,MAAA,EAAQ,QAAQ,OAAO;YAC1D,0KAAS,uBAAA,EAAqB,MAAM,OAAA,EAAS,QAAQ,OAAO;QAAA;IAC9D,CACF;AAEJ;AAGF,eAAe,WAAA,GAAc;AAM7B,IAAM,cAAc;AAGpB,IAAM,CAAC,gBAAgB,gBAAgB,CAAA,GAAI,qBAAyC,aAAa;IAC/F,YAAY,KAAA;AACd,CAAC;AAgBD,IAAM,gBAA8C,CAAC,UAA2C;IAC9F,MAAM,EAAE,cAAA,EAAgB,UAAA,EAAY,QAAA,EAAU,SAAA,CAAU,CAAA,GAAI;IAC5D,MAAM,UAAU,kBAAkB,aAAa,cAAc;IAC7D,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,gBAAA;QAAe,OAAO;QAAgB;QACrC,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,uKAAC,WAAA,EAAA;YAAS,SAAS,cAAc,QAAQ,IAAA;YACvC,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,4KAAA,EAAA;gBAAgB,SAAO;gBAAC;gBACtB;YAAA,CACH;QAAA,CACF;IAAA,CACF;AAEJ;AAEA,cAAc,WAAA,GAAc;AAM5B,IAAM,eAAe;AAWrB,IAAM,iBAAuB,sMAAA,UAAA,CAC3B,CAAC,OAAyC,iBAAiB;IACzD,MAAM,gBAAgB,iBAAiB,cAAc,MAAM,cAAc;IACzE,MAAM,EAAE,aAAa,cAAc,UAAA,EAAY,OAAO,KAAA,EAAO,GAAG,aAAa,CAAA,GAAI;IACjF,MAAM,UAAU,kBAAkB,cAAc,MAAM,cAAc;IAEpE,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,uKAAC,WAAA,EAAA;QAAS,SAAS,cAAc,QAAQ,IAAA;QACtC,UAAA,QAAQ,uBAAA,GACP,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,oBAAA;YAAmB;YAAa,GAAG,YAAA;YAAc,KAAK;QAAA,CAAc,IAErE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,yBAAA;YAAwB;YAAa,GAAG,YAAA;YAAc,KAAK;QAAA,CAAc;IAAA,CAE9E;AAEJ;AASF,IAAM,0BAAgC,sMAAA,UAAA,CAGpC,CAAC,OAAkD,iBAAiB;IACpE,MAAM,UAAU,kBAAkB,cAAc,MAAM,cAAc;IACpE,MAAM,kBAAkB,0BAA0B,cAAc,MAAM,cAAc;IACpF,MAAM,MAAY,sMAAA,MAAA,CAAuC,IAAI;IAC7D,MAAM,gBAAe,iMAAA,EAAgB,cAAc,GAAG;IACtD,MAAM,CAAC,kBAAkB,mBAAmB,CAAA,GAAU,sMAAA,QAAA,CAAyB,IAAI;IAEnF,MAAM,EAAE,OAAA,EAAS,OAAA,CAAQ,CAAA,GAAI;IAC7B,MAAM,UAAU,IAAI,OAAA;IAEpB,MAAM,EAAE,wBAAA,CAAyB,CAAA,GAAI;IAErC,MAAM,wBAA8B,sMAAA,WAAA,CAAY,MAAM;QACpD,oBAAoB,IAAI;QACxB,yBAAyB,KAAK;IAChC,GAAG;QAAC,wBAAwB;KAAC;IAE7B,MAAM,wBAA8B,sMAAA,WAAA,CAClC,CAAC,OAAqB,gBAA6B;QACjD,MAAM,gBAAgB,MAAM,aAAA;QAC5B,MAAM,YAAY;YAAE,GAAG,MAAM,OAAA;YAAS,GAAG,MAAM,OAAA;QAAQ;QACvD,MAAM,WAAW,oBAAoB,WAAW,cAAc,qBAAA,CAAsB,CAAC;QACrF,MAAM,mBAAmB,oBAAoB,WAAW,QAAQ;QAChE,MAAM,oBAAoB,kBAAkB,YAAY,qBAAA,CAAsB,CAAC;QAC/E,MAAM,YAAY,QAAQ,CAAC;eAAG,kBAAkB;eAAG,iBAAiB;SAAC;QACrE,oBAAoB,SAAS;QAC7B,yBAAyB,IAAI;IAC/B,GACA;QAAC,wBAAwB;KAAA;IAGrB,sMAAA,SAAA,CAAU,MAAM;QACpB,OAAO,IAAM,sBAAsB;IACrC,GAAG;QAAC,qBAAqB;KAAC;IAEpB,sMAAA,SAAA,CAAU,MAAM;QACpB,IAAI,WAAW,SAAS;YACtB,MAAM,qBAAqB,CAAC,QAAwB,sBAAsB,OAAO,OAAO;YACxF,MAAM,qBAAqB,CAAC,QAAwB,sBAAsB,OAAO,OAAO;YAExF,QAAQ,gBAAA,CAAiB,gBAAgB,kBAAkB;YAC3D,QAAQ,gBAAA,CAAiB,gBAAgB,kBAAkB;YAC3D,OAAO,MAAM;gBACX,QAAQ,mBAAA,CAAoB,gBAAgB,kBAAkB;gBAC9D,QAAQ,mBAAA,CAAoB,gBAAgB,kBAAkB;YAChE;QACF;IACF,GAAG;QAAC;QAAS;QAAS;QAAuB,qBAAqB;KAAC;IAE7D,sMAAA,SAAA,CAAU,MAAM;QACpB,IAAI,kBAAkB;YACpB,MAAM,0BAA0B,CAAC,UAAwB;gBACvD,MAAM,SAAS,MAAM,MAAA;gBACrB,MAAM,kBAAkB;oBAAE,GAAG,MAAM,OAAA;oBAAS,GAAG,MAAM,OAAA;gBAAQ;gBAC7D,MAAM,mBAAmB,SAAS,SAAS,MAAM,KAAK,SAAS,SAAS,MAAM;gBAC9E,MAAM,4BAA4B,CAAC,iBAAiB,iBAAiB,gBAAgB;gBAErF,IAAI,kBAAkB;oBACpB,sBAAsB;gBACxB,OAAA,IAAW,2BAA2B;oBACpC,sBAAsB;oBACtB,QAAQ;gBACV;YACF;YACA,SAAS,gBAAA,CAAiB,eAAe,uBAAuB;YAChE,OAAO,IAAM,SAAS,mBAAA,CAAoB,eAAe,uBAAuB;QAClF;IACF,GAAG;QAAC;QAAS;QAAS;QAAkB;QAAS,qBAAqB;KAAC;IAEvE,OAAO,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,oBAAA;QAAoB,GAAG,KAAA;QAAO,KAAK;IAAA,CAAc;AAC3D,CAAC;AAED,IAAM,CAAC,sCAAsC,+BAA+B,CAAA,GAC1E,qBAAqB,cAAc;IAAE,UAAU;AAAM,CAAC;AAuBxD,IAAM,YAAY,uLAAA,EAAgB,gBAAgB;AAElD,IAAM,qBAA2B,sMAAA,UAAA,CAC/B,CAAC,OAA6C,iBAAiB;IAC7D,MAAM,EACJ,cAAA,EACA,QAAA,EACA,cAAc,SAAA,EACd,eAAA,EACA,oBAAA,EACA,GAAG,cACL,GAAI;IACJ,MAAM,UAAU,kBAAkB,cAAc,cAAc;IAC9D,MAAM,cAAc,eAAe,cAAc;IACjD,MAAM,EAAE,OAAA,CAAQ,CAAA,GAAI;IAGd,sMAAA,SAAA,CAAU,MAAM;QACpB,SAAS,gBAAA,CAAiB,cAAc,OAAO;QAC/C,OAAO,IAAM,SAAS,mBAAA,CAAoB,cAAc,OAAO;IACjE,GAAG;QAAC,OAAO;KAAC;IAGN,sMAAA,SAAA,CAAU,MAAM;QACpB,IAAI,QAAQ,OAAA,EAAS;YACnB,MAAM,eAAe,CAAC,UAAiB;gBACrC,MAAM,SAAS,MAAM,MAAA;gBACrB,IAAI,QAAQ,SAAS,QAAQ,OAAO,EAAG,CAAA,QAAQ;YACjD;YACA,OAAO,gBAAA,CAAiB,UAAU,cAAc;gBAAE,SAAS;YAAK,CAAC;YACjE,OAAO,IAAM,OAAO,mBAAA,CAAoB,UAAU,cAAc;oBAAE,SAAS;gBAAK,CAAC;QACnF;IACF,GAAG;QAAC,QAAQ,OAAA;QAAS,OAAO;KAAC;IAE7B,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,mLAAC,mBAAA,EAAA;QACC,SAAO;QACP,6BAA6B;QAC7B;QACA;QACA,gBAAgB,CAAC,QAAU,MAAM,cAAA,CAAe;QAChD,WAAW;QAEX,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,OAAA,EAAiB,mKAAA,OAAA,EAAhB;YACC,cAAY,QAAQ,cAAA;YACnB,GAAG,WAAA;YACH,GAAG,YAAA;YACJ,KAAK;YACL,OAAO;gBACL,GAAG,aAAa,KAAA;gBAAA,iDAAA;gBAEhB,GAAG;oBACD,4CAA4C;oBAC5C,2CAA2C;oBAC3C,4CAA4C;oBAC5C,iCAAiC;oBACjC,kCAAkC;gBACpC,CAAA;YACF;YAEA,UAAA;gBAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,WAAA;oBAAW;gBAAA,CAAS;gBACrB,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,sCAAA;oBAAqC,OAAO;oBAAgB,UAAU;oBACrE,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAyB,+KAAA,IAAA,EAAxB;wBAA6B,IAAI,QAAQ,SAAA;wBAAW,MAAK;wBACvD,UAAA,aAAa;oBAAA,CAChB;gBAAA,CACF;aAAA;QAAA;IACF;AAGN;AAGF,eAAe,WAAA,GAAc;AAM7B,IAAM,aAAa;AAMnB,IAAM,eAAqB,sMAAA,UAAA,CACzB,CAAC,OAAuC,iBAAiB;IACvD,MAAM,EAAE,cAAA,EAAgB,GAAG,WAAW,CAAA,GAAI;IAC1C,MAAM,cAAc,eAAe,cAAc;IACjD,MAAM,+BAA+B,gCACnC,YACA;IAIF,OAAO,6BAA6B,QAAA,GAAW,OAC7C,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAiB,mKAAA,KAAA,EAAhB;QAAuB,GAAG,WAAA;QAAc,GAAG,UAAA;QAAY,KAAK;IAAA,CAAc;AAE/E;AAGF,aAAa,WAAA,GAAc;AAM3B,SAAS,oBAAoB,KAAA,EAAc,IAAA,EAAqB;IAC9D,MAAM,MAAM,KAAK,GAAA,CAAI,KAAK,GAAA,GAAM,MAAM,CAAC;IACvC,MAAM,SAAS,KAAK,GAAA,CAAI,KAAK,MAAA,GAAS,MAAM,CAAC;IAC7C,MAAM,QAAQ,KAAK,GAAA,CAAI,KAAK,KAAA,GAAQ,MAAM,CAAC;IAC3C,MAAM,OAAO,KAAK,GAAA,CAAI,KAAK,IAAA,GAAO,MAAM,CAAC;IAEzC,OAAQ,KAAK,GAAA,CAAI,KAAK,QAAQ,OAAO,IAAI,GAAG;QAC1C,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,MAAM,IAAI,MAAM,aAAa;IACjC;AACF;AAEA,SAAS,oBAAoB,SAAA,EAAkB,QAAA,EAAgB,UAAU,CAAA,EAAG;IAC1E,MAAM,mBAA4B,CAAC,CAAA;IACnC,OAAQ,UAAU;QAChB,KAAK;YACH,iBAAiB,IAAA,CACf;gBAAE,GAAG,UAAU,CAAA,GAAI;gBAAS,GAAG,UAAU,CAAA,GAAI;YAAQ,GACrD;gBAAE,GAAG,UAAU,CAAA,GAAI;gBAAS,GAAG,UAAU,CAAA,GAAI;YAAQ;YAEvD;QACF,KAAK;YACH,iBAAiB,IAAA,CACf;gBAAE,GAAG,UAAU,CAAA,GAAI;gBAAS,GAAG,UAAU,CAAA,GAAI;YAAQ,GACrD;gBAAE,GAAG,UAAU,CAAA,GAAI;gBAAS,GAAG,UAAU,CAAA,GAAI;YAAQ;YAEvD;QACF,KAAK;YACH,iBAAiB,IAAA,CACf;gBAAE,GAAG,UAAU,CAAA,GAAI;gBAAS,GAAG,UAAU,CAAA,GAAI;YAAQ,GACrD;gBAAE,GAAG,UAAU,CAAA,GAAI;gBAAS,GAAG,UAAU,CAAA,GAAI;YAAQ;YAEvD;QACF,KAAK;YACH,iBAAiB,IAAA,CACf;gBAAE,GAAG,UAAU,CAAA,GAAI;gBAAS,GAAG,UAAU,CAAA,GAAI;YAAQ,GACrD;gBAAE,GAAG,UAAU,CAAA,GAAI;gBAAS,GAAG,UAAU,CAAA,GAAI;YAAQ;YAEvD;IACJ;IACA,OAAO;AACT;AAEA,SAAS,kBAAkB,IAAA,EAAe;IACxC,MAAM,EAAE,GAAA,EAAK,KAAA,EAAO,MAAA,EAAQ,IAAA,CAAK,CAAA,GAAI;IACrC,OAAO;QACL;YAAE,GAAG;YAAM,GAAG;QAAI;QAClB;YAAE,GAAG;YAAO,GAAG;QAAI;QACnB;YAAE,GAAG;YAAO,GAAG;QAAO;QACtB;YAAE,GAAG;YAAM,GAAG;QAAO;KACvB;AACF;AAIA,SAAS,iBAAiB,KAAA,EAAc,OAAA,EAAkB;IACxD,MAAM,EAAE,CAAA,EAAG,CAAA,CAAE,CAAA,GAAI;IACjB,IAAI,SAAS;IACb,IAAA,IAAS,IAAI,GAAG,IAAI,QAAQ,MAAA,GAAS,GAAG,IAAI,QAAQ,MAAA,EAAQ,IAAI,IAAK;QACnE,MAAM,KAAK,OAAA,CAAQ,CAAC,CAAA;QACpB,MAAM,KAAK,OAAA,CAAQ,CAAC,CAAA;QACpB,MAAM,KAAK,GAAG,CAAA;QACd,MAAM,KAAK,GAAG,CAAA;QACd,MAAM,KAAK,GAAG,CAAA;QACd,MAAM,KAAK,GAAG,CAAA;QAGd,MAAM,YAAc,KAAK,MAAQ,KAAK,KAAQ,IAAA,CAAK,KAAK,EAAA,IAAA,CAAO,IAAI,EAAA,IAAA,CAAO,KAAK,EAAA,IAAM;QACrF,IAAI,UAAW,CAAA,SAAS,CAAC;IAC3B;IAEA,OAAO;AACT;AAIA,SAAS,QAAyB,MAAA,EAAsC;IACtE,MAAM,YAAsB,OAAO,KAAA,CAAM;IACzC,UAAU,IAAA,CAAK,CAAC,GAAU,MAAa;QACrC,IAAI,EAAE,CAAA,GAAI,EAAE,CAAA,CAAG,CAAA,OAAO,CAAA;aAAA,IACb,EAAE,CAAA,GAAI,EAAE,CAAA,CAAG,CAAA,OAAO;aAAA,IAClB,EAAE,CAAA,GAAI,EAAE,CAAA,CAAG,CAAA,OAAO,CAAA;aAAA,IAClB,EAAE,CAAA,GAAI,EAAE,CAAA,CAAG,CAAA,OAAO;aACtB,OAAO;IACd,CAAC;IACD,OAAO,iBAAiB,SAAS;AACnC;AAGA,SAAS,iBAAkC,MAAA,EAAsC;IAC/E,IAAI,OAAO,MAAA,IAAU,EAAG,CAAA,OAAO,OAAO,KAAA,CAAM;IAE5C,MAAM,YAAsB,CAAC,CAAA;IAC7B,IAAA,IAAS,IAAI,GAAG,IAAI,OAAO,MAAA,EAAQ,IAAK;QACtC,MAAM,IAAI,MAAA,CAAO,CAAC,CAAA;QAClB,MAAO,UAAU,MAAA,IAAU,EAAG;YAC5B,MAAM,IAAI,SAAA,CAAU,UAAU,MAAA,GAAS,CAAC,CAAA;YACxC,MAAM,IAAI,SAAA,CAAU,UAAU,MAAA,GAAS,CAAC,CAAA;YACxC,IAAA,CAAK,EAAE,CAAA,GAAI,EAAE,CAAA,IAAA,CAAM,EAAE,CAAA,GAAI,EAAE,CAAA,KAAA,CAAO,EAAE,CAAA,GAAI,EAAE,CAAA,IAAA,CAAM,EAAE,CAAA,GAAI,EAAE,CAAA,EAAI,CAAA,UAAU,GAAA,CAAI;iBACrE;QACP;QACA,UAAU,IAAA,CAAK,CAAC;IAClB;IACA,UAAU,GAAA,CAAI;IAEd,MAAM,YAAsB,CAAC,CAAA;IAC7B,IAAA,IAAS,IAAI,OAAO,MAAA,GAAS,GAAG,KAAK,GAAG,IAAK;QAC3C,MAAM,IAAI,MAAA,CAAO,CAAC,CAAA;QAClB,MAAO,UAAU,MAAA,IAAU,EAAG;YAC5B,MAAM,IAAI,SAAA,CAAU,UAAU,MAAA,GAAS,CAAC,CAAA;YACxC,MAAM,IAAI,SAAA,CAAU,UAAU,MAAA,GAAS,CAAC,CAAA;YACxC,IAAA,CAAK,EAAE,CAAA,GAAI,EAAE,CAAA,IAAA,CAAM,EAAE,CAAA,GAAI,EAAE,CAAA,KAAA,CAAO,EAAE,CAAA,GAAI,EAAE,CAAA,IAAA,CAAM,EAAE,CAAA,GAAI,EAAE,CAAA,EAAI,CAAA,UAAU,GAAA,CAAI;iBACrE;QACP;QACA,UAAU,IAAA,CAAK,CAAC;IAClB;IACA,UAAU,GAAA,CAAI;IAEd,IACE,UAAU,MAAA,KAAW,KACrB,UAAU,MAAA,KAAW,KACrB,SAAA,CAAU,CAAC,CAAA,CAAG,CAAA,KAAM,SAAA,CAAU,CAAC,CAAA,CAAG,CAAA,IAClC,SAAA,CAAU,CAAC,CAAA,CAAG,CAAA,KAAM,SAAA,CAAU,CAAC,CAAA,CAAG,CAAA,EAClC;QACA,OAAO;IACT,OAAO;QACL,OAAO,UAAU,MAAA,CAAO,SAAS;IACnC;AACF;AAEA,IAAM,WAAW;AACjB,IAAMC,QAAO;AACb,IAAM,UAAU;AAChB,IAAM,SAAS;AACf,IAAMC,WAAU;AAChB,IAAMC,SAAQ", "ignoreList": [0]}}, {"offset": {"line": 4702, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4708, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/%40radix-ui/react-menu/src/menu.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { createCollection } from '@radix-ui/react-collection';\nimport { useComposedRefs, composeRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useDirection } from '@radix-ui/react-direction';\nimport { DismissableLayer } from '@radix-ui/react-dismissable-layer';\nimport { useFocusGuards } from '@radix-ui/react-focus-guards';\nimport { FocusScope } from '@radix-ui/react-focus-scope';\nimport { useId } from '@radix-ui/react-id';\nimport * as PopperPrimitive from '@radix-ui/react-popper';\nimport { createPopperScope } from '@radix-ui/react-popper';\nimport { Portal as PortalPrimitive } from '@radix-ui/react-portal';\nimport { Presence } from '@radix-ui/react-presence';\nimport { Primitive, dispatchDiscreteCustomEvent } from '@radix-ui/react-primitive';\nimport * as RovingFocusGroup from '@radix-ui/react-roving-focus';\nimport { createRovingFocusGroupScope } from '@radix-ui/react-roving-focus';\nimport { createSlot } from '@radix-ui/react-slot';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\nimport { hideOthers } from 'aria-hidden';\nimport { RemoveScroll } from 'react-remove-scroll';\n\nimport type { Scope } from '@radix-ui/react-context';\n\ntype Direction = 'ltr' | 'rtl';\n\nconst SELECTION_KEYS = ['Enter', ' '];\nconst FIRST_KEYS = ['ArrowDown', 'PageUp', 'Home'];\nconst LAST_KEYS = ['ArrowUp', 'PageDown', 'End'];\nconst FIRST_LAST_KEYS = [...FIRST_KEYS, ...LAST_KEYS];\nconst SUB_OPEN_KEYS: Record<Direction, string[]> = {\n  ltr: [...SELECTION_KEYS, 'ArrowRight'],\n  rtl: [...SELECTION_KEYS, 'ArrowLeft'],\n};\nconst SUB_CLOSE_KEYS: Record<Direction, string[]> = {\n  ltr: ['ArrowLeft'],\n  rtl: ['ArrowRight'],\n};\n\n/* -------------------------------------------------------------------------------------------------\n * Menu\n * -----------------------------------------------------------------------------------------------*/\n\nconst MENU_NAME = 'Menu';\n\ntype ItemData = { disabled: boolean; textValue: string };\nconst [Collection, useCollection, createCollectionScope] = createCollection<\n  MenuItemElement,\n  ItemData\n>(MENU_NAME);\n\ntype ScopedProps<P> = P & { __scopeMenu?: Scope };\nconst [createMenuContext, createMenuScope] = createContextScope(MENU_NAME, [\n  createCollectionScope,\n  createPopperScope,\n  createRovingFocusGroupScope,\n]);\nconst usePopperScope = createPopperScope();\nconst useRovingFocusGroupScope = createRovingFocusGroupScope();\n\ntype MenuContextValue = {\n  open: boolean;\n  onOpenChange(open: boolean): void;\n  content: MenuContentElement | null;\n  onContentChange(content: MenuContentElement | null): void;\n};\n\nconst [MenuProvider, useMenuContext] = createMenuContext<MenuContextValue>(MENU_NAME);\n\ntype MenuRootContextValue = {\n  onClose(): void;\n  isUsingKeyboardRef: React.RefObject<boolean>;\n  dir: Direction;\n  modal: boolean;\n};\n\nconst [MenuRootProvider, useMenuRootContext] = createMenuContext<MenuRootContextValue>(MENU_NAME);\n\ninterface MenuProps {\n  children?: React.ReactNode;\n  open?: boolean;\n  onOpenChange?(open: boolean): void;\n  dir?: Direction;\n  modal?: boolean;\n}\n\nconst Menu: React.FC<MenuProps> = (props: ScopedProps<MenuProps>) => {\n  const { __scopeMenu, open = false, children, dir, onOpenChange, modal = true } = props;\n  const popperScope = usePopperScope(__scopeMenu);\n  const [content, setContent] = React.useState<MenuContentElement | null>(null);\n  const isUsingKeyboardRef = React.useRef(false);\n  const handleOpenChange = useCallbackRef(onOpenChange);\n  const direction = useDirection(dir);\n\n  React.useEffect(() => {\n    // Capture phase ensures we set the boolean before any side effects execute\n    // in response to the key or pointer event as they might depend on this value.\n    const handleKeyDown = () => {\n      isUsingKeyboardRef.current = true;\n      document.addEventListener('pointerdown', handlePointer, { capture: true, once: true });\n      document.addEventListener('pointermove', handlePointer, { capture: true, once: true });\n    };\n    const handlePointer = () => (isUsingKeyboardRef.current = false);\n    document.addEventListener('keydown', handleKeyDown, { capture: true });\n    return () => {\n      document.removeEventListener('keydown', handleKeyDown, { capture: true });\n      document.removeEventListener('pointerdown', handlePointer, { capture: true });\n      document.removeEventListener('pointermove', handlePointer, { capture: true });\n    };\n  }, []);\n\n  return (\n    <PopperPrimitive.Root {...popperScope}>\n      <MenuProvider\n        scope={__scopeMenu}\n        open={open}\n        onOpenChange={handleOpenChange}\n        content={content}\n        onContentChange={setContent}\n      >\n        <MenuRootProvider\n          scope={__scopeMenu}\n          onClose={React.useCallback(() => handleOpenChange(false), [handleOpenChange])}\n          isUsingKeyboardRef={isUsingKeyboardRef}\n          dir={direction}\n          modal={modal}\n        >\n          {children}\n        </MenuRootProvider>\n      </MenuProvider>\n    </PopperPrimitive.Root>\n  );\n};\n\nMenu.displayName = MENU_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuAnchor\n * -----------------------------------------------------------------------------------------------*/\n\nconst ANCHOR_NAME = 'MenuAnchor';\n\ntype MenuAnchorElement = React.ComponentRef<typeof PopperPrimitive.Anchor>;\ntype PopperAnchorProps = React.ComponentPropsWithoutRef<typeof PopperPrimitive.Anchor>;\ninterface MenuAnchorProps extends PopperAnchorProps {}\n\nconst MenuAnchor = React.forwardRef<MenuAnchorElement, MenuAnchorProps>(\n  (props: ScopedProps<MenuAnchorProps>, forwardedRef) => {\n    const { __scopeMenu, ...anchorProps } = props;\n    const popperScope = usePopperScope(__scopeMenu);\n    return <PopperPrimitive.Anchor {...popperScope} {...anchorProps} ref={forwardedRef} />;\n  }\n);\n\nMenuAnchor.displayName = ANCHOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuPortal\n * -----------------------------------------------------------------------------------------------*/\n\nconst PORTAL_NAME = 'MenuPortal';\n\ntype PortalContextValue = { forceMount?: true };\nconst [PortalProvider, usePortalContext] = createMenuContext<PortalContextValue>(PORTAL_NAME, {\n  forceMount: undefined,\n});\n\ntype PortalProps = React.ComponentPropsWithoutRef<typeof PortalPrimitive>;\ninterface MenuPortalProps {\n  children?: React.ReactNode;\n  /**\n   * Specify a container element to portal the content into.\n   */\n  container?: PortalProps['container'];\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst MenuPortal: React.FC<MenuPortalProps> = (props: ScopedProps<MenuPortalProps>) => {\n  const { __scopeMenu, forceMount, children, container } = props;\n  const context = useMenuContext(PORTAL_NAME, __scopeMenu);\n  return (\n    <PortalProvider scope={__scopeMenu} forceMount={forceMount}>\n      <Presence present={forceMount || context.open}>\n        <PortalPrimitive asChild container={container}>\n          {children}\n        </PortalPrimitive>\n      </Presence>\n    </PortalProvider>\n  );\n};\n\nMenuPortal.displayName = PORTAL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'MenuContent';\n\ntype MenuContentContextValue = {\n  onItemEnter(event: React.PointerEvent): void;\n  onItemLeave(event: React.PointerEvent): void;\n  onTriggerLeave(event: React.PointerEvent): void;\n  searchRef: React.RefObject<string>;\n  pointerGraceTimerRef: React.MutableRefObject<number>;\n  onPointerGraceIntentChange(intent: GraceIntent | null): void;\n};\nconst [MenuContentProvider, useMenuContentContext] =\n  createMenuContext<MenuContentContextValue>(CONTENT_NAME);\n\ntype MenuContentElement = MenuRootContentTypeElement;\n/**\n * We purposefully don't union MenuRootContent and MenuSubContent props here because\n * they have conflicting prop types. We agreed that we would allow MenuSubContent to\n * accept props that it would just ignore.\n */\ninterface MenuContentProps extends MenuRootContentTypeProps {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst MenuContent = React.forwardRef<MenuContentElement, MenuContentProps>(\n  (props: ScopedProps<MenuContentProps>, forwardedRef) => {\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopeMenu);\n    const { forceMount = portalContext.forceMount, ...contentProps } = props;\n    const context = useMenuContext(CONTENT_NAME, props.__scopeMenu);\n    const rootContext = useMenuRootContext(CONTENT_NAME, props.__scopeMenu);\n\n    return (\n      <Collection.Provider scope={props.__scopeMenu}>\n        <Presence present={forceMount || context.open}>\n          <Collection.Slot scope={props.__scopeMenu}>\n            {rootContext.modal ? (\n              <MenuRootContentModal {...contentProps} ref={forwardedRef} />\n            ) : (\n              <MenuRootContentNonModal {...contentProps} ref={forwardedRef} />\n            )}\n          </Collection.Slot>\n        </Presence>\n      </Collection.Provider>\n    );\n  }\n);\n\n/* ---------------------------------------------------------------------------------------------- */\n\ntype MenuRootContentTypeElement = MenuContentImplElement;\ninterface MenuRootContentTypeProps\n  extends Omit<MenuContentImplProps, keyof MenuContentImplPrivateProps> {}\n\nconst MenuRootContentModal = React.forwardRef<MenuRootContentTypeElement, MenuRootContentTypeProps>(\n  (props: ScopedProps<MenuRootContentTypeProps>, forwardedRef) => {\n    const context = useMenuContext(CONTENT_NAME, props.__scopeMenu);\n    const ref = React.useRef<MenuRootContentTypeElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref);\n\n    // Hide everything from ARIA except the `MenuContent`\n    React.useEffect(() => {\n      const content = ref.current;\n      if (content) return hideOthers(content);\n    }, []);\n\n    return (\n      <MenuContentImpl\n        {...props}\n        ref={composedRefs}\n        // we make sure we're not trapping once it's been closed\n        // (closed !== unmounted when animating out)\n        trapFocus={context.open}\n        // make sure to only disable pointer events when open\n        // this avoids blocking interactions while animating out\n        disableOutsidePointerEvents={context.open}\n        disableOutsideScroll\n        // When focus is trapped, a `focusout` event may still happen.\n        // We make sure we don't trigger our `onDismiss` in such case.\n        onFocusOutside={composeEventHandlers(\n          props.onFocusOutside,\n          (event) => event.preventDefault(),\n          { checkForDefaultPrevented: false }\n        )}\n        onDismiss={() => context.onOpenChange(false)}\n      />\n    );\n  }\n);\n\nconst MenuRootContentNonModal = React.forwardRef<\n  MenuRootContentTypeElement,\n  MenuRootContentTypeProps\n>((props: ScopedProps<MenuRootContentTypeProps>, forwardedRef) => {\n  const context = useMenuContext(CONTENT_NAME, props.__scopeMenu);\n  return (\n    <MenuContentImpl\n      {...props}\n      ref={forwardedRef}\n      trapFocus={false}\n      disableOutsidePointerEvents={false}\n      disableOutsideScroll={false}\n      onDismiss={() => context.onOpenChange(false)}\n    />\n  );\n});\n\n/* ---------------------------------------------------------------------------------------------- */\n\ntype MenuContentImplElement = React.ComponentRef<typeof PopperPrimitive.Content>;\ntype FocusScopeProps = React.ComponentPropsWithoutRef<typeof FocusScope>;\ntype DismissableLayerProps = React.ComponentPropsWithoutRef<typeof DismissableLayer>;\ntype RovingFocusGroupProps = React.ComponentPropsWithoutRef<typeof RovingFocusGroup.Root>;\ntype PopperContentProps = React.ComponentPropsWithoutRef<typeof PopperPrimitive.Content>;\ntype MenuContentImplPrivateProps = {\n  onOpenAutoFocus?: FocusScopeProps['onMountAutoFocus'];\n  onDismiss?: DismissableLayerProps['onDismiss'];\n  disableOutsidePointerEvents?: DismissableLayerProps['disableOutsidePointerEvents'];\n\n  /**\n   * Whether scrolling outside the `MenuContent` should be prevented\n   * (default: `false`)\n   */\n  disableOutsideScroll?: boolean;\n\n  /**\n   * Whether focus should be trapped within the `MenuContent`\n   * (default: false)\n   */\n  trapFocus?: FocusScopeProps['trapped'];\n};\ninterface MenuContentImplProps\n  extends MenuContentImplPrivateProps,\n    Omit<PopperContentProps, 'dir' | 'onPlaced'> {\n  /**\n   * Event handler called when auto-focusing on close.\n   * Can be prevented.\n   */\n  onCloseAutoFocus?: FocusScopeProps['onUnmountAutoFocus'];\n\n  /**\n   * Whether keyboard navigation should loop around\n   * @defaultValue false\n   */\n  loop?: RovingFocusGroupProps['loop'];\n\n  onEntryFocus?: RovingFocusGroupProps['onEntryFocus'];\n  onEscapeKeyDown?: DismissableLayerProps['onEscapeKeyDown'];\n  onPointerDownOutside?: DismissableLayerProps['onPointerDownOutside'];\n  onFocusOutside?: DismissableLayerProps['onFocusOutside'];\n  onInteractOutside?: DismissableLayerProps['onInteractOutside'];\n}\n\nconst Slot = createSlot('MenuContent.ScrollLock');\n\nconst MenuContentImpl = React.forwardRef<MenuContentImplElement, MenuContentImplProps>(\n  (props: ScopedProps<MenuContentImplProps>, forwardedRef) => {\n    const {\n      __scopeMenu,\n      loop = false,\n      trapFocus,\n      onOpenAutoFocus,\n      onCloseAutoFocus,\n      disableOutsidePointerEvents,\n      onEntryFocus,\n      onEscapeKeyDown,\n      onPointerDownOutside,\n      onFocusOutside,\n      onInteractOutside,\n      onDismiss,\n      disableOutsideScroll,\n      ...contentProps\n    } = props;\n    const context = useMenuContext(CONTENT_NAME, __scopeMenu);\n    const rootContext = useMenuRootContext(CONTENT_NAME, __scopeMenu);\n    const popperScope = usePopperScope(__scopeMenu);\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeMenu);\n    const getItems = useCollection(__scopeMenu);\n    const [currentItemId, setCurrentItemId] = React.useState<string | null>(null);\n    const contentRef = React.useRef<HTMLDivElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, contentRef, context.onContentChange);\n    const timerRef = React.useRef(0);\n    const searchRef = React.useRef('');\n    const pointerGraceTimerRef = React.useRef(0);\n    const pointerGraceIntentRef = React.useRef<GraceIntent | null>(null);\n    const pointerDirRef = React.useRef<Side>('right');\n    const lastPointerXRef = React.useRef(0);\n\n    const ScrollLockWrapper = disableOutsideScroll ? RemoveScroll : React.Fragment;\n    const scrollLockWrapperProps = disableOutsideScroll\n      ? { as: Slot, allowPinchZoom: true }\n      : undefined;\n\n    const handleTypeaheadSearch = (key: string) => {\n      const search = searchRef.current + key;\n      const items = getItems().filter((item) => !item.disabled);\n      const currentItem = document.activeElement;\n      const currentMatch = items.find((item) => item.ref.current === currentItem)?.textValue;\n      const values = items.map((item) => item.textValue);\n      const nextMatch = getNextMatch(values, search, currentMatch);\n      const newItem = items.find((item) => item.textValue === nextMatch)?.ref.current;\n\n      // Reset `searchRef` 1 second after it was last updated\n      (function updateSearch(value: string) {\n        searchRef.current = value;\n        window.clearTimeout(timerRef.current);\n        if (value !== '') timerRef.current = window.setTimeout(() => updateSearch(''), 1000);\n      })(search);\n\n      if (newItem) {\n        /**\n         * Imperative focus during keydown is risky so we prevent React's batching updates\n         * to avoid potential bugs. See: https://github.com/facebook/react/issues/20332\n         */\n        setTimeout(() => (newItem as HTMLElement).focus());\n      }\n    };\n\n    React.useEffect(() => {\n      return () => window.clearTimeout(timerRef.current);\n    }, []);\n\n    // Make sure the whole tree has focus guards as our `MenuContent` may be\n    // the last element in the DOM (because of the `Portal`)\n    useFocusGuards();\n\n    const isPointerMovingToSubmenu = React.useCallback((event: React.PointerEvent) => {\n      const isMovingTowards = pointerDirRef.current === pointerGraceIntentRef.current?.side;\n      return isMovingTowards && isPointerInGraceArea(event, pointerGraceIntentRef.current?.area);\n    }, []);\n\n    return (\n      <MenuContentProvider\n        scope={__scopeMenu}\n        searchRef={searchRef}\n        onItemEnter={React.useCallback(\n          (event) => {\n            if (isPointerMovingToSubmenu(event)) event.preventDefault();\n          },\n          [isPointerMovingToSubmenu]\n        )}\n        onItemLeave={React.useCallback(\n          (event) => {\n            if (isPointerMovingToSubmenu(event)) return;\n            contentRef.current?.focus();\n            setCurrentItemId(null);\n          },\n          [isPointerMovingToSubmenu]\n        )}\n        onTriggerLeave={React.useCallback(\n          (event) => {\n            if (isPointerMovingToSubmenu(event)) event.preventDefault();\n          },\n          [isPointerMovingToSubmenu]\n        )}\n        pointerGraceTimerRef={pointerGraceTimerRef}\n        onPointerGraceIntentChange={React.useCallback((intent) => {\n          pointerGraceIntentRef.current = intent;\n        }, [])}\n      >\n        <ScrollLockWrapper {...scrollLockWrapperProps}>\n          <FocusScope\n            asChild\n            trapped={trapFocus}\n            onMountAutoFocus={composeEventHandlers(onOpenAutoFocus, (event) => {\n              // when opening, explicitly focus the content area only and leave\n              // `onEntryFocus` in  control of focusing first item\n              event.preventDefault();\n              contentRef.current?.focus({ preventScroll: true });\n            })}\n            onUnmountAutoFocus={onCloseAutoFocus}\n          >\n            <DismissableLayer\n              asChild\n              disableOutsidePointerEvents={disableOutsidePointerEvents}\n              onEscapeKeyDown={onEscapeKeyDown}\n              onPointerDownOutside={onPointerDownOutside}\n              onFocusOutside={onFocusOutside}\n              onInteractOutside={onInteractOutside}\n              onDismiss={onDismiss}\n            >\n              <RovingFocusGroup.Root\n                asChild\n                {...rovingFocusGroupScope}\n                dir={rootContext.dir}\n                orientation=\"vertical\"\n                loop={loop}\n                currentTabStopId={currentItemId}\n                onCurrentTabStopIdChange={setCurrentItemId}\n                onEntryFocus={composeEventHandlers(onEntryFocus, (event) => {\n                  // only focus first item when using keyboard\n                  if (!rootContext.isUsingKeyboardRef.current) event.preventDefault();\n                })}\n                preventScrollOnEntryFocus\n              >\n                <PopperPrimitive.Content\n                  role=\"menu\"\n                  aria-orientation=\"vertical\"\n                  data-state={getOpenState(context.open)}\n                  data-radix-menu-content=\"\"\n                  dir={rootContext.dir}\n                  {...popperScope}\n                  {...contentProps}\n                  ref={composedRefs}\n                  style={{ outline: 'none', ...contentProps.style }}\n                  onKeyDown={composeEventHandlers(contentProps.onKeyDown, (event) => {\n                    // submenu key events bubble through portals. We only care about keys in this menu.\n                    const target = event.target as HTMLElement;\n                    const isKeyDownInside =\n                      target.closest('[data-radix-menu-content]') === event.currentTarget;\n                    const isModifierKey = event.ctrlKey || event.altKey || event.metaKey;\n                    const isCharacterKey = event.key.length === 1;\n                    if (isKeyDownInside) {\n                      // menus should not be navigated using tab key so we prevent it\n                      if (event.key === 'Tab') event.preventDefault();\n                      if (!isModifierKey && isCharacterKey) handleTypeaheadSearch(event.key);\n                    }\n                    // focus first/last item based on key pressed\n                    const content = contentRef.current;\n                    if (event.target !== content) return;\n                    if (!FIRST_LAST_KEYS.includes(event.key)) return;\n                    event.preventDefault();\n                    const items = getItems().filter((item) => !item.disabled);\n                    const candidateNodes = items.map((item) => item.ref.current!);\n                    if (LAST_KEYS.includes(event.key)) candidateNodes.reverse();\n                    focusFirst(candidateNodes);\n                  })}\n                  onBlur={composeEventHandlers(props.onBlur, (event) => {\n                    // clear search buffer when leaving the menu\n                    if (!event.currentTarget.contains(event.target)) {\n                      window.clearTimeout(timerRef.current);\n                      searchRef.current = '';\n                    }\n                  })}\n                  onPointerMove={composeEventHandlers(\n                    props.onPointerMove,\n                    whenMouse((event) => {\n                      const target = event.target as HTMLElement;\n                      const pointerXHasChanged = lastPointerXRef.current !== event.clientX;\n\n                      // We don't use `event.movementX` for this check because Safari will\n                      // always return `0` on a pointer event.\n                      if (event.currentTarget.contains(target) && pointerXHasChanged) {\n                        const newDir = event.clientX > lastPointerXRef.current ? 'right' : 'left';\n                        pointerDirRef.current = newDir;\n                        lastPointerXRef.current = event.clientX;\n                      }\n                    })\n                  )}\n                />\n              </RovingFocusGroup.Root>\n            </DismissableLayer>\n          </FocusScope>\n        </ScrollLockWrapper>\n      </MenuContentProvider>\n    );\n  }\n);\n\nMenuContent.displayName = CONTENT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuGroup\n * -----------------------------------------------------------------------------------------------*/\n\nconst GROUP_NAME = 'MenuGroup';\n\ntype MenuGroupElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface MenuGroupProps extends PrimitiveDivProps {}\n\nconst MenuGroup = React.forwardRef<MenuGroupElement, MenuGroupProps>(\n  (props: ScopedProps<MenuGroupProps>, forwardedRef) => {\n    const { __scopeMenu, ...groupProps } = props;\n    return <Primitive.div role=\"group\" {...groupProps} ref={forwardedRef} />;\n  }\n);\n\nMenuGroup.displayName = GROUP_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuLabel\n * -----------------------------------------------------------------------------------------------*/\n\nconst LABEL_NAME = 'MenuLabel';\n\ntype MenuLabelElement = React.ComponentRef<typeof Primitive.div>;\ninterface MenuLabelProps extends PrimitiveDivProps {}\n\nconst MenuLabel = React.forwardRef<MenuLabelElement, MenuLabelProps>(\n  (props: ScopedProps<MenuLabelProps>, forwardedRef) => {\n    const { __scopeMenu, ...labelProps } = props;\n    return <Primitive.div {...labelProps} ref={forwardedRef} />;\n  }\n);\n\nMenuLabel.displayName = LABEL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_NAME = 'MenuItem';\nconst ITEM_SELECT = 'menu.itemSelect';\n\ntype MenuItemElement = MenuItemImplElement;\ninterface MenuItemProps extends Omit<MenuItemImplProps, 'onSelect'> {\n  onSelect?: (event: Event) => void;\n}\n\nconst MenuItem = React.forwardRef<MenuItemElement, MenuItemProps>(\n  (props: ScopedProps<MenuItemProps>, forwardedRef) => {\n    const { disabled = false, onSelect, ...itemProps } = props;\n    const ref = React.useRef<HTMLDivElement>(null);\n    const rootContext = useMenuRootContext(ITEM_NAME, props.__scopeMenu);\n    const contentContext = useMenuContentContext(ITEM_NAME, props.__scopeMenu);\n    const composedRefs = useComposedRefs(forwardedRef, ref);\n    const isPointerDownRef = React.useRef(false);\n\n    const handleSelect = () => {\n      const menuItem = ref.current;\n      if (!disabled && menuItem) {\n        const itemSelectEvent = new CustomEvent(ITEM_SELECT, { bubbles: true, cancelable: true });\n        menuItem.addEventListener(ITEM_SELECT, (event) => onSelect?.(event), { once: true });\n        dispatchDiscreteCustomEvent(menuItem, itemSelectEvent);\n        if (itemSelectEvent.defaultPrevented) {\n          isPointerDownRef.current = false;\n        } else {\n          rootContext.onClose();\n        }\n      }\n    };\n\n    return (\n      <MenuItemImpl\n        {...itemProps}\n        ref={composedRefs}\n        disabled={disabled}\n        onClick={composeEventHandlers(props.onClick, handleSelect)}\n        onPointerDown={(event) => {\n          props.onPointerDown?.(event);\n          isPointerDownRef.current = true;\n        }}\n        onPointerUp={composeEventHandlers(props.onPointerUp, (event) => {\n          // Pointer down can move to a different menu item which should activate it on pointer up.\n          // We dispatch a click for selection to allow composition with click based triggers and to\n          // prevent Firefox from getting stuck in text selection mode when the menu closes.\n          if (!isPointerDownRef.current) event.currentTarget?.click();\n        })}\n        onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n          const isTypingAhead = contentContext.searchRef.current !== '';\n          if (disabled || (isTypingAhead && event.key === ' ')) return;\n          if (SELECTION_KEYS.includes(event.key)) {\n            event.currentTarget.click();\n            /**\n             * We prevent default browser behaviour for selection keys as they should trigger\n             * a selection only:\n             * - prevents space from scrolling the page.\n             * - if keydown causes focus to move, prevents keydown from firing on the new target.\n             */\n            event.preventDefault();\n          }\n        })}\n      />\n    );\n  }\n);\n\nMenuItem.displayName = ITEM_NAME;\n\n/* ---------------------------------------------------------------------------------------------- */\n\ntype MenuItemImplElement = React.ComponentRef<typeof Primitive.div>;\ninterface MenuItemImplProps extends PrimitiveDivProps {\n  disabled?: boolean;\n  textValue?: string;\n}\n\nconst MenuItemImpl = React.forwardRef<MenuItemImplElement, MenuItemImplProps>(\n  (props: ScopedProps<MenuItemImplProps>, forwardedRef) => {\n    const { __scopeMenu, disabled = false, textValue, ...itemProps } = props;\n    const contentContext = useMenuContentContext(ITEM_NAME, __scopeMenu);\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeMenu);\n    const ref = React.useRef<HTMLDivElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref);\n    const [isFocused, setIsFocused] = React.useState(false);\n\n    // get the item's `.textContent` as default strategy for typeahead `textValue`\n    const [textContent, setTextContent] = React.useState('');\n    React.useEffect(() => {\n      const menuItem = ref.current;\n      if (menuItem) {\n        setTextContent((menuItem.textContent ?? '').trim());\n      }\n    }, [itemProps.children]);\n\n    return (\n      <Collection.ItemSlot\n        scope={__scopeMenu}\n        disabled={disabled}\n        textValue={textValue ?? textContent}\n      >\n        <RovingFocusGroup.Item asChild {...rovingFocusGroupScope} focusable={!disabled}>\n          <Primitive.div\n            role=\"menuitem\"\n            data-highlighted={isFocused ? '' : undefined}\n            aria-disabled={disabled || undefined}\n            data-disabled={disabled ? '' : undefined}\n            {...itemProps}\n            ref={composedRefs}\n            /**\n             * We focus items on `pointerMove` to achieve the following:\n             *\n             * - Mouse over an item (it focuses)\n             * - Leave mouse where it is and use keyboard to focus a different item\n             * - Wiggle mouse without it leaving previously focused item\n             * - Previously focused item should re-focus\n             *\n             * If we used `mouseOver`/`mouseEnter` it would not re-focus when the mouse\n             * wiggles. This is to match native menu implementation.\n             */\n            onPointerMove={composeEventHandlers(\n              props.onPointerMove,\n              whenMouse((event) => {\n                if (disabled) {\n                  contentContext.onItemLeave(event);\n                } else {\n                  contentContext.onItemEnter(event);\n                  if (!event.defaultPrevented) {\n                    const item = event.currentTarget;\n                    item.focus({ preventScroll: true });\n                  }\n                }\n              })\n            )}\n            onPointerLeave={composeEventHandlers(\n              props.onPointerLeave,\n              whenMouse((event) => contentContext.onItemLeave(event))\n            )}\n            onFocus={composeEventHandlers(props.onFocus, () => setIsFocused(true))}\n            onBlur={composeEventHandlers(props.onBlur, () => setIsFocused(false))}\n          />\n        </RovingFocusGroup.Item>\n      </Collection.ItemSlot>\n    );\n  }\n);\n\n/* -------------------------------------------------------------------------------------------------\n * MenuCheckboxItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst CHECKBOX_ITEM_NAME = 'MenuCheckboxItem';\n\ntype MenuCheckboxItemElement = MenuItemElement;\n\ntype CheckedState = boolean | 'indeterminate';\n\ninterface MenuCheckboxItemProps extends MenuItemProps {\n  checked?: CheckedState;\n  // `onCheckedChange` can never be called with `\"indeterminate\"` from the inside\n  onCheckedChange?: (checked: boolean) => void;\n}\n\nconst MenuCheckboxItem = React.forwardRef<MenuCheckboxItemElement, MenuCheckboxItemProps>(\n  (props: ScopedProps<MenuCheckboxItemProps>, forwardedRef) => {\n    const { checked = false, onCheckedChange, ...checkboxItemProps } = props;\n    return (\n      <ItemIndicatorProvider scope={props.__scopeMenu} checked={checked}>\n        <MenuItem\n          role=\"menuitemcheckbox\"\n          aria-checked={isIndeterminate(checked) ? 'mixed' : checked}\n          {...checkboxItemProps}\n          ref={forwardedRef}\n          data-state={getCheckedState(checked)}\n          onSelect={composeEventHandlers(\n            checkboxItemProps.onSelect,\n            () => onCheckedChange?.(isIndeterminate(checked) ? true : !checked),\n            { checkForDefaultPrevented: false }\n          )}\n        />\n      </ItemIndicatorProvider>\n    );\n  }\n);\n\nMenuCheckboxItem.displayName = CHECKBOX_ITEM_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuRadioGroup\n * -----------------------------------------------------------------------------------------------*/\n\nconst RADIO_GROUP_NAME = 'MenuRadioGroup';\n\nconst [RadioGroupProvider, useRadioGroupContext] = createMenuContext<MenuRadioGroupProps>(\n  RADIO_GROUP_NAME,\n  { value: undefined, onValueChange: () => {} }\n);\n\ntype MenuRadioGroupElement = React.ComponentRef<typeof MenuGroup>;\ninterface MenuRadioGroupProps extends MenuGroupProps {\n  value?: string;\n  onValueChange?: (value: string) => void;\n}\n\nconst MenuRadioGroup = React.forwardRef<MenuRadioGroupElement, MenuRadioGroupProps>(\n  (props: ScopedProps<MenuRadioGroupProps>, forwardedRef) => {\n    const { value, onValueChange, ...groupProps } = props;\n    const handleValueChange = useCallbackRef(onValueChange);\n    return (\n      <RadioGroupProvider scope={props.__scopeMenu} value={value} onValueChange={handleValueChange}>\n        <MenuGroup {...groupProps} ref={forwardedRef} />\n      </RadioGroupProvider>\n    );\n  }\n);\n\nMenuRadioGroup.displayName = RADIO_GROUP_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuRadioItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst RADIO_ITEM_NAME = 'MenuRadioItem';\n\ntype MenuRadioItemElement = React.ComponentRef<typeof MenuItem>;\ninterface MenuRadioItemProps extends MenuItemProps {\n  value: string;\n}\n\nconst MenuRadioItem = React.forwardRef<MenuRadioItemElement, MenuRadioItemProps>(\n  (props: ScopedProps<MenuRadioItemProps>, forwardedRef) => {\n    const { value, ...radioItemProps } = props;\n    const context = useRadioGroupContext(RADIO_ITEM_NAME, props.__scopeMenu);\n    const checked = value === context.value;\n    return (\n      <ItemIndicatorProvider scope={props.__scopeMenu} checked={checked}>\n        <MenuItem\n          role=\"menuitemradio\"\n          aria-checked={checked}\n          {...radioItemProps}\n          ref={forwardedRef}\n          data-state={getCheckedState(checked)}\n          onSelect={composeEventHandlers(\n            radioItemProps.onSelect,\n            () => context.onValueChange?.(value),\n            { checkForDefaultPrevented: false }\n          )}\n        />\n      </ItemIndicatorProvider>\n    );\n  }\n);\n\nMenuRadioItem.displayName = RADIO_ITEM_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuItemIndicator\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_INDICATOR_NAME = 'MenuItemIndicator';\n\ntype CheckboxContextValue = { checked: CheckedState };\n\nconst [ItemIndicatorProvider, useItemIndicatorContext] = createMenuContext<CheckboxContextValue>(\n  ITEM_INDICATOR_NAME,\n  { checked: false }\n);\n\ntype MenuItemIndicatorElement = React.ComponentRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = React.ComponentPropsWithoutRef<typeof Primitive.span>;\ninterface MenuItemIndicatorProps extends PrimitiveSpanProps {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst MenuItemIndicator = React.forwardRef<MenuItemIndicatorElement, MenuItemIndicatorProps>(\n  (props: ScopedProps<MenuItemIndicatorProps>, forwardedRef) => {\n    const { __scopeMenu, forceMount, ...itemIndicatorProps } = props;\n    const indicatorContext = useItemIndicatorContext(ITEM_INDICATOR_NAME, __scopeMenu);\n    return (\n      <Presence\n        present={\n          forceMount ||\n          isIndeterminate(indicatorContext.checked) ||\n          indicatorContext.checked === true\n        }\n      >\n        <Primitive.span\n          {...itemIndicatorProps}\n          ref={forwardedRef}\n          data-state={getCheckedState(indicatorContext.checked)}\n        />\n      </Presence>\n    );\n  }\n);\n\nMenuItemIndicator.displayName = ITEM_INDICATOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuSeparator\n * -----------------------------------------------------------------------------------------------*/\n\nconst SEPARATOR_NAME = 'MenuSeparator';\n\ntype MenuSeparatorElement = React.ComponentRef<typeof Primitive.div>;\ninterface MenuSeparatorProps extends PrimitiveDivProps {}\n\nconst MenuSeparator = React.forwardRef<MenuSeparatorElement, MenuSeparatorProps>(\n  (props: ScopedProps<MenuSeparatorProps>, forwardedRef) => {\n    const { __scopeMenu, ...separatorProps } = props;\n    return (\n      <Primitive.div\n        role=\"separator\"\n        aria-orientation=\"horizontal\"\n        {...separatorProps}\n        ref={forwardedRef}\n      />\n    );\n  }\n);\n\nMenuSeparator.displayName = SEPARATOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuArrow\n * -----------------------------------------------------------------------------------------------*/\n\nconst ARROW_NAME = 'MenuArrow';\n\ntype MenuArrowElement = React.ComponentRef<typeof PopperPrimitive.Arrow>;\ntype PopperArrowProps = React.ComponentPropsWithoutRef<typeof PopperPrimitive.Arrow>;\ninterface MenuArrowProps extends PopperArrowProps {}\n\nconst MenuArrow = React.forwardRef<MenuArrowElement, MenuArrowProps>(\n  (props: ScopedProps<MenuArrowProps>, forwardedRef) => {\n    const { __scopeMenu, ...arrowProps } = props;\n    const popperScope = usePopperScope(__scopeMenu);\n    return <PopperPrimitive.Arrow {...popperScope} {...arrowProps} ref={forwardedRef} />;\n  }\n);\n\nMenuArrow.displayName = ARROW_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuSub\n * -----------------------------------------------------------------------------------------------*/\n\nconst SUB_NAME = 'MenuSub';\n\ntype MenuSubContextValue = {\n  contentId: string;\n  triggerId: string;\n  trigger: MenuSubTriggerElement | null;\n  onTriggerChange(trigger: MenuSubTriggerElement | null): void;\n};\n\nconst [MenuSubProvider, useMenuSubContext] = createMenuContext<MenuSubContextValue>(SUB_NAME);\n\ninterface MenuSubProps {\n  children?: React.ReactNode;\n  open?: boolean;\n  onOpenChange?(open: boolean): void;\n}\n\nconst MenuSub: React.FC<MenuSubProps> = (props: ScopedProps<MenuSubProps>) => {\n  const { __scopeMenu, children, open = false, onOpenChange } = props;\n  const parentMenuContext = useMenuContext(SUB_NAME, __scopeMenu);\n  const popperScope = usePopperScope(__scopeMenu);\n  const [trigger, setTrigger] = React.useState<MenuSubTriggerElement | null>(null);\n  const [content, setContent] = React.useState<MenuContentElement | null>(null);\n  const handleOpenChange = useCallbackRef(onOpenChange);\n\n  // Prevent the parent menu from reopening with open submenus.\n  React.useEffect(() => {\n    if (parentMenuContext.open === false) handleOpenChange(false);\n    return () => handleOpenChange(false);\n  }, [parentMenuContext.open, handleOpenChange]);\n\n  return (\n    <PopperPrimitive.Root {...popperScope}>\n      <MenuProvider\n        scope={__scopeMenu}\n        open={open}\n        onOpenChange={handleOpenChange}\n        content={content}\n        onContentChange={setContent}\n      >\n        <MenuSubProvider\n          scope={__scopeMenu}\n          contentId={useId()}\n          triggerId={useId()}\n          trigger={trigger}\n          onTriggerChange={setTrigger}\n        >\n          {children}\n        </MenuSubProvider>\n      </MenuProvider>\n    </PopperPrimitive.Root>\n  );\n};\n\nMenuSub.displayName = SUB_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuSubTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst SUB_TRIGGER_NAME = 'MenuSubTrigger';\n\ntype MenuSubTriggerElement = MenuItemImplElement;\ninterface MenuSubTriggerProps extends MenuItemImplProps {}\n\nconst MenuSubTrigger = React.forwardRef<MenuSubTriggerElement, MenuSubTriggerProps>(\n  (props: ScopedProps<MenuSubTriggerProps>, forwardedRef) => {\n    const context = useMenuContext(SUB_TRIGGER_NAME, props.__scopeMenu);\n    const rootContext = useMenuRootContext(SUB_TRIGGER_NAME, props.__scopeMenu);\n    const subContext = useMenuSubContext(SUB_TRIGGER_NAME, props.__scopeMenu);\n    const contentContext = useMenuContentContext(SUB_TRIGGER_NAME, props.__scopeMenu);\n    const openTimerRef = React.useRef<number | null>(null);\n    const { pointerGraceTimerRef, onPointerGraceIntentChange } = contentContext;\n    const scope = { __scopeMenu: props.__scopeMenu };\n\n    const clearOpenTimer = React.useCallback(() => {\n      if (openTimerRef.current) window.clearTimeout(openTimerRef.current);\n      openTimerRef.current = null;\n    }, []);\n\n    React.useEffect(() => clearOpenTimer, [clearOpenTimer]);\n\n    React.useEffect(() => {\n      const pointerGraceTimer = pointerGraceTimerRef.current;\n      return () => {\n        window.clearTimeout(pointerGraceTimer);\n        onPointerGraceIntentChange(null);\n      };\n    }, [pointerGraceTimerRef, onPointerGraceIntentChange]);\n\n    return (\n      <MenuAnchor asChild {...scope}>\n        <MenuItemImpl\n          id={subContext.triggerId}\n          aria-haspopup=\"menu\"\n          aria-expanded={context.open}\n          aria-controls={subContext.contentId}\n          data-state={getOpenState(context.open)}\n          {...props}\n          ref={composeRefs(forwardedRef, subContext.onTriggerChange)}\n          // This is redundant for mouse users but we cannot determine pointer type from\n          // click event and we cannot use pointerup event (see git history for reasons why)\n          onClick={(event) => {\n            props.onClick?.(event);\n            if (props.disabled || event.defaultPrevented) return;\n            /**\n             * We manually focus because iOS Safari doesn't always focus on click (e.g. buttons)\n             * and we rely heavily on `onFocusOutside` for submenus to close when switching\n             * between separate submenus.\n             */\n            event.currentTarget.focus();\n            if (!context.open) context.onOpenChange(true);\n          }}\n          onPointerMove={composeEventHandlers(\n            props.onPointerMove,\n            whenMouse((event) => {\n              contentContext.onItemEnter(event);\n              if (event.defaultPrevented) return;\n              if (!props.disabled && !context.open && !openTimerRef.current) {\n                contentContext.onPointerGraceIntentChange(null);\n                openTimerRef.current = window.setTimeout(() => {\n                  context.onOpenChange(true);\n                  clearOpenTimer();\n                }, 100);\n              }\n            })\n          )}\n          onPointerLeave={composeEventHandlers(\n            props.onPointerLeave,\n            whenMouse((event) => {\n              clearOpenTimer();\n\n              const contentRect = context.content?.getBoundingClientRect();\n              if (contentRect) {\n                // TODO: make sure to update this when we change positioning logic\n                const side = context.content?.dataset.side as Side;\n                const rightSide = side === 'right';\n                const bleed = rightSide ? -5 : +5;\n                const contentNearEdge = contentRect[rightSide ? 'left' : 'right'];\n                const contentFarEdge = contentRect[rightSide ? 'right' : 'left'];\n\n                contentContext.onPointerGraceIntentChange({\n                  area: [\n                    // Apply a bleed on clientX to ensure that our exit point is\n                    // consistently within polygon bounds\n                    { x: event.clientX + bleed, y: event.clientY },\n                    { x: contentNearEdge, y: contentRect.top },\n                    { x: contentFarEdge, y: contentRect.top },\n                    { x: contentFarEdge, y: contentRect.bottom },\n                    { x: contentNearEdge, y: contentRect.bottom },\n                  ],\n                  side,\n                });\n\n                window.clearTimeout(pointerGraceTimerRef.current);\n                pointerGraceTimerRef.current = window.setTimeout(\n                  () => contentContext.onPointerGraceIntentChange(null),\n                  300\n                );\n              } else {\n                contentContext.onTriggerLeave(event);\n                if (event.defaultPrevented) return;\n\n                // There's 100ms where the user may leave an item before the submenu was opened.\n                contentContext.onPointerGraceIntentChange(null);\n              }\n            })\n          )}\n          onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n            const isTypingAhead = contentContext.searchRef.current !== '';\n            if (props.disabled || (isTypingAhead && event.key === ' ')) return;\n            if (SUB_OPEN_KEYS[rootContext.dir].includes(event.key)) {\n              context.onOpenChange(true);\n              // The trigger may hold focus if opened via pointer interaction\n              // so we ensure content is given focus again when switching to keyboard.\n              context.content?.focus();\n              // prevent window from scrolling\n              event.preventDefault();\n            }\n          })}\n        />\n      </MenuAnchor>\n    );\n  }\n);\n\nMenuSubTrigger.displayName = SUB_TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * MenuSubContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst SUB_CONTENT_NAME = 'MenuSubContent';\n\ntype MenuSubContentElement = MenuContentImplElement;\ninterface MenuSubContentProps\n  extends Omit<\n    MenuContentImplProps,\n    keyof MenuContentImplPrivateProps | 'onCloseAutoFocus' | 'onEntryFocus' | 'side' | 'align'\n  > {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst MenuSubContent = React.forwardRef<MenuSubContentElement, MenuSubContentProps>(\n  (props: ScopedProps<MenuSubContentProps>, forwardedRef) => {\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopeMenu);\n    const { forceMount = portalContext.forceMount, ...subContentProps } = props;\n    const context = useMenuContext(CONTENT_NAME, props.__scopeMenu);\n    const rootContext = useMenuRootContext(CONTENT_NAME, props.__scopeMenu);\n    const subContext = useMenuSubContext(SUB_CONTENT_NAME, props.__scopeMenu);\n    const ref = React.useRef<MenuSubContentElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref);\n    return (\n      <Collection.Provider scope={props.__scopeMenu}>\n        <Presence present={forceMount || context.open}>\n          <Collection.Slot scope={props.__scopeMenu}>\n            <MenuContentImpl\n              id={subContext.contentId}\n              aria-labelledby={subContext.triggerId}\n              {...subContentProps}\n              ref={composedRefs}\n              align=\"start\"\n              side={rootContext.dir === 'rtl' ? 'left' : 'right'}\n              disableOutsidePointerEvents={false}\n              disableOutsideScroll={false}\n              trapFocus={false}\n              onOpenAutoFocus={(event) => {\n                // when opening a submenu, focus content for keyboard users only\n                if (rootContext.isUsingKeyboardRef.current) ref.current?.focus();\n                event.preventDefault();\n              }}\n              // The menu might close because of focusing another menu item in the parent menu. We\n              // don't want it to refocus the trigger in that case so we handle trigger focus ourselves.\n              onCloseAutoFocus={(event) => event.preventDefault()}\n              onFocusOutside={composeEventHandlers(props.onFocusOutside, (event) => {\n                // We prevent closing when the trigger is focused to avoid triggering a re-open animation\n                // on pointer interaction.\n                if (event.target !== subContext.trigger) context.onOpenChange(false);\n              })}\n              onEscapeKeyDown={composeEventHandlers(props.onEscapeKeyDown, (event) => {\n                rootContext.onClose();\n                // ensure pressing escape in submenu doesn't escape full screen mode\n                event.preventDefault();\n              })}\n              onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n                // Submenu key events bubble through portals. We only care about keys in this menu.\n                const isKeyDownInside = event.currentTarget.contains(event.target as HTMLElement);\n                const isCloseKey = SUB_CLOSE_KEYS[rootContext.dir].includes(event.key);\n                if (isKeyDownInside && isCloseKey) {\n                  context.onOpenChange(false);\n                  // We focus manually because we prevented it in `onCloseAutoFocus`\n                  subContext.trigger?.focus();\n                  // prevent window from scrolling\n                  event.preventDefault();\n                }\n              })}\n            />\n          </Collection.Slot>\n        </Presence>\n      </Collection.Provider>\n    );\n  }\n);\n\nMenuSubContent.displayName = SUB_CONTENT_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction getOpenState(open: boolean) {\n  return open ? 'open' : 'closed';\n}\n\nfunction isIndeterminate(checked?: CheckedState): checked is 'indeterminate' {\n  return checked === 'indeterminate';\n}\n\nfunction getCheckedState(checked: CheckedState) {\n  return isIndeterminate(checked) ? 'indeterminate' : checked ? 'checked' : 'unchecked';\n}\n\nfunction focusFirst(candidates: HTMLElement[]) {\n  const PREVIOUSLY_FOCUSED_ELEMENT = document.activeElement;\n  for (const candidate of candidates) {\n    // if focus is already where we want to go, we don't want to keep going through the candidates\n    if (candidate === PREVIOUSLY_FOCUSED_ELEMENT) return;\n    candidate.focus();\n    if (document.activeElement !== PREVIOUSLY_FOCUSED_ELEMENT) return;\n  }\n}\n\n/**\n * Wraps an array around itself at a given start index\n * Example: `wrapArray(['a', 'b', 'c', 'd'], 2) === ['c', 'd', 'a', 'b']`\n */\nfunction wrapArray<T>(array: T[], startIndex: number) {\n  return array.map<T>((_, index) => array[(startIndex + index) % array.length]!);\n}\n\n/**\n * This is the \"meat\" of the typeahead matching logic. It takes in all the values,\n * the search and the current match, and returns the next match (or `undefined`).\n *\n * We normalize the search because if a user has repeatedly pressed a character,\n * we want the exact same behavior as if we only had that one character\n * (ie. cycle through options starting with that character)\n *\n * We also reorder the values by wrapping the array around the current match.\n * This is so we always look forward from the current match, and picking the first\n * match will always be the correct one.\n *\n * Finally, if the normalized search is exactly one character, we exclude the\n * current match from the values because otherwise it would be the first to match always\n * and focus would never move. This is as opposed to the regular case, where we\n * don't want focus to move if the current match still matches.\n */\nfunction getNextMatch(values: string[], search: string, currentMatch?: string) {\n  const isRepeated = search.length > 1 && Array.from(search).every((char) => char === search[0]);\n  const normalizedSearch = isRepeated ? search[0]! : search;\n  const currentMatchIndex = currentMatch ? values.indexOf(currentMatch) : -1;\n  let wrappedValues = wrapArray(values, Math.max(currentMatchIndex, 0));\n  const excludeCurrentMatch = normalizedSearch.length === 1;\n  if (excludeCurrentMatch) wrappedValues = wrappedValues.filter((v) => v !== currentMatch);\n  const nextMatch = wrappedValues.find((value) =>\n    value.toLowerCase().startsWith(normalizedSearch.toLowerCase())\n  );\n  return nextMatch !== currentMatch ? nextMatch : undefined;\n}\n\ntype Point = { x: number; y: number };\ntype Polygon = Point[];\ntype Side = 'left' | 'right';\ntype GraceIntent = { area: Polygon; side: Side };\n\n// Determine if a point is inside of a polygon.\n// Based on https://github.com/substack/point-in-polygon\nfunction isPointInPolygon(point: Point, polygon: Polygon) {\n  const { x, y } = point;\n  let inside = false;\n  for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {\n    const ii = polygon[i]!;\n    const jj = polygon[j]!;\n    const xi = ii.x;\n    const yi = ii.y;\n    const xj = jj.x;\n    const yj = jj.y;\n\n    // prettier-ignore\n    const intersect = ((yi > y) !== (yj > y)) && (x < (xj - xi) * (y - yi) / (yj - yi) + xi);\n    if (intersect) inside = !inside;\n  }\n\n  return inside;\n}\n\nfunction isPointerInGraceArea(event: React.PointerEvent, area?: Polygon) {\n  if (!area) return false;\n  const cursorPos = { x: event.clientX, y: event.clientY };\n  return isPointInPolygon(cursorPos, area);\n}\n\nfunction whenMouse<E>(handler: React.PointerEventHandler<E>): React.PointerEventHandler<E> {\n  return (event) => (event.pointerType === 'mouse' ? handler(event) : undefined);\n}\n\nconst Root = Menu;\nconst Anchor = MenuAnchor;\nconst Portal = MenuPortal;\nconst Content = MenuContent;\nconst Group = MenuGroup;\nconst Label = MenuLabel;\nconst Item = MenuItem;\nconst CheckboxItem = MenuCheckboxItem;\nconst RadioGroup = MenuRadioGroup;\nconst RadioItem = MenuRadioItem;\nconst ItemIndicator = MenuItemIndicator;\nconst Separator = MenuSeparator;\nconst Arrow = MenuArrow;\nconst Sub = MenuSub;\nconst SubTrigger = MenuSubTrigger;\nconst SubContent = MenuSubContent;\n\nexport {\n  createMenuScope,\n  //\n  Menu,\n  MenuAnchor,\n  MenuPortal,\n  MenuContent,\n  MenuGroup,\n  MenuLabel,\n  MenuItem,\n  MenuCheckboxItem,\n  MenuRadioGroup,\n  MenuRadioItem,\n  MenuItemIndicator,\n  MenuSeparator,\n  MenuArrow,\n  MenuSub,\n  MenuSubTrigger,\n  MenuSubContent,\n  //\n  Root,\n  Anchor,\n  Portal,\n  Content,\n  Group,\n  Label,\n  Item,\n  CheckboxItem,\n  RadioGroup,\n  RadioItem,\n  ItemIndicator,\n  Separator,\n  Arrow,\n  Sub,\n  SubTrigger,\n  SubContent,\n};\nexport type {\n  MenuProps,\n  MenuAnchorProps,\n  MenuPortalProps,\n  MenuContentProps,\n  MenuGroupProps,\n  MenuLabelProps,\n  MenuItemProps,\n  MenuCheckboxItemProps,\n  MenuRadioGroupProps,\n  MenuRadioItemProps,\n  MenuItemIndicatorProps,\n  MenuSeparatorProps,\n  MenuArrowProps,\n  MenuSubProps,\n  MenuSubTriggerProps,\n  MenuSubContentProps,\n};\n"], "names": ["Root", "<PERSON><PERSON>", "Content", "<PERSON><PERSON>", "Arrow"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,YAAY,WAAW;AAwHf;AAtHR,SAAS,wBAAwB;AAEjC,SAAS,0BAA0B;AAOnC,SAAS,yBAAyB;AAKlC,SAAS,mCAAmC;AAE5C,SAAS,sBAAsB;AAb/B,SAAS,oBAAoB;AAQ7B,SAAS,gBAAgB;AADzB,SAAS,UAAU,uBAAuB;AAT1C,SAAS,iBAAiB,mBAAmB;AAgB7C,SAAS,kBAAkB;AAlB3B,SAAS,4BAA4B;AAgBrC,SAAS,kBAAkB;AAG3B,SAAS,oBAAoB;AAb7B,SAAS,sBAAsB;AAC/B,SAAS,kBAAkB;AAF3B,SAAS,wBAAwB;AAQjC,SAAS,WAAW,mCAAmC;AALvD,SAAS,aAAa;;;;;;;;;;;;;;;;;;;;;;;;AAiBtB,IAAM,iBAAiB;IAAC;IAAS,GAAG;CAAA;AACpC,IAAM,aAAa;IAAC;IAAa;IAAU,MAAM;CAAA;AACjD,IAAM,YAAY;IAAC;IAAW;IAAY,KAAK;CAAA;AAC/C,IAAM,kBAAkB,CAAC;OAAG,YAAY;OAAG,SAAS;CAAA;AACpD,IAAM,gBAA6C;IACjD,KAAK,CAAC;WAAG;QAAgB,YAAY;KAAA;IACrC,KAAK,CAAC;WAAG;QAAgB,WAAW;KAAA;AACtC;AACA,IAAM,iBAA8C;IAClD,KAAK;QAAC,WAAW;KAAA;IACjB,KAAK;QAAC,YAAY;KAAA;AACpB;AAMA,IAAM,YAAY;AAGlB,IAAM,CAAC,YAAY,eAAe,qBAAqB,CAAA,GAAI,8LAAA,EAGzD,SAAS;AAGX,IAAM,CAAC,mBAAmB,eAAe,CAAA,2KAAI,qBAAA,EAAmB,WAAW;IACzE;IACA,uLAAA;gLACA,8BAAA;CACD;AACD,IAAM,wLAAiB,oBAAA,CAAkB;AACzC,IAAM,2MAA2B,8BAAA,CAA4B;AAS7D,IAAM,CAAC,cAAc,cAAc,CAAA,GAAI,kBAAoC,SAAS;AASpF,IAAM,CAAC,kBAAkB,kBAAkB,CAAA,GAAI,kBAAwC,SAAS;AAUhG,IAAM,OAA4B,CAAC,UAAkC;IACnE,MAAM,EAAE,WAAA,EAAa,OAAO,KAAA,EAAO,QAAA,EAAU,GAAA,EAAK,YAAA,EAAc,QAAQ,IAAA,CAAK,CAAA,GAAI;IACjF,MAAM,cAAc,eAAe,WAAW;IAC9C,MAAM,CAAC,SAAS,UAAU,CAAA,GAAU,sMAAA,QAAA,CAAoC,IAAI;IAC5E,MAAM,qBAA2B,sMAAA,MAAA,CAAO,KAAK;IAC7C,MAAM,0MAAmB,iBAAA,EAAe,YAAY;IACpD,MAAM,sLAAY,eAAA,EAAa,GAAG;IAE5B,sMAAA,SAAA,CAAU,MAAM;QAGpB,MAAM,gBAAgB,MAAM;YAC1B,mBAAmB,OAAA,GAAU;YAC7B,SAAS,gBAAA,CAAiB,eAAe,eAAe;gBAAE,SAAS;gBAAM,MAAM;YAAK,CAAC;YACrF,SAAS,gBAAA,CAAiB,eAAe,eAAe;gBAAE,SAAS;gBAAM,MAAM;YAAK,CAAC;QACvF;QACA,MAAM,gBAAgB,IAAO,mBAAmB,OAAA,GAAU;QAC1D,SAAS,gBAAA,CAAiB,WAAW,eAAe;YAAE,SAAS;QAAK,CAAC;QACrE,OAAO,MAAM;YACX,SAAS,mBAAA,CAAoB,WAAW,eAAe;gBAAE,SAAS;YAAK,CAAC;YACxE,SAAS,mBAAA,CAAoB,eAAe,eAAe;gBAAE,SAAS;YAAK,CAAC;YAC5E,SAAS,mBAAA,CAAoB,eAAe,eAAe;gBAAE,SAAS;YAAK,CAAC;QAC9E;IACF,GAAG,CAAC,CAAC;IAEL,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAiB,mKAAA,IAAA,EAAhB;QAAsB,GAAG,WAAA;QACxB,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,cAAA;YACC,OAAO;YACP;YACA,cAAc;YACd;YACA,iBAAiB;YAEjB,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,kBAAA;gBACC,OAAO;gBACP,SAAe,sMAAA,WAAA,CAAY,IAAM,iBAAiB,KAAK,GAAG;oBAAC,gBAAgB;iBAAC;gBAC5E;gBACA,KAAK;gBACL;gBAEC;YAAA;QACH;IACF,CACF;AAEJ;AAEA,KAAK,WAAA,GAAc;AAMnB,IAAM,cAAc;AAMpB,IAAM,aAAmB,sMAAA,UAAA,CACvB,CAAC,OAAqC,iBAAiB;IACrD,MAAM,EAAE,WAAA,EAAa,GAAG,YAAY,CAAA,GAAI;IACxC,MAAM,cAAc,eAAe,WAAW;IAC9C,OAAO,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAiB,mKAAA,MAAA,EAAhB;QAAwB,GAAG,WAAA;QAAc,GAAG,WAAA;QAAa,KAAK;IAAA,CAAc;AACtF;AAGF,WAAW,WAAA,GAAc;AAMzB,IAAM,cAAc;AAGpB,IAAM,CAAC,gBAAgB,gBAAgB,CAAA,GAAI,kBAAsC,aAAa;IAC5F,YAAY,KAAA;AACd,CAAC;AAgBD,IAAM,aAAwC,CAAC,UAAwC;IACrF,MAAM,EAAE,WAAA,EAAa,UAAA,EAAY,QAAA,EAAU,SAAA,CAAU,CAAA,GAAI;IACzD,MAAM,UAAU,eAAe,aAAa,WAAW;IACvD,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,gBAAA;QAAe,OAAO;QAAa;QAClC,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,uKAAC,WAAA,EAAA;YAAS,SAAS,cAAc,QAAQ,IAAA;YACvC,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,qKAAC,SAAA,EAAA;gBAAgB,SAAO;gBAAC;gBACtB;YAAA,CACH;QAAA,CACF;IAAA,CACF;AAEJ;AAEA,WAAW,WAAA,GAAc;AAMzB,IAAM,eAAe;AAUrB,IAAM,CAAC,qBAAqB,qBAAqB,CAAA,GAC/C,kBAA2C,YAAY;AAgBzD,IAAM,cAAoB,sMAAA,UAAA,CACxB,CAAC,OAAsC,iBAAiB;IACtD,MAAM,gBAAgB,iBAAiB,cAAc,MAAM,WAAW;IACtE,MAAM,EAAE,aAAa,cAAc,UAAA,EAAY,GAAG,aAAa,CAAA,GAAI;IACnE,MAAM,UAAU,eAAe,cAAc,MAAM,WAAW;IAC9D,MAAM,cAAc,mBAAmB,cAAc,MAAM,WAAW;IAEtE,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,WAAW,QAAA,EAAX;QAAoB,OAAO,MAAM,WAAA;QAChC,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,uKAAC,WAAA,EAAA;YAAS,SAAS,cAAc,QAAQ,IAAA;YACvC,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,WAAW,IAAA,EAAX;gBAAgB,OAAO,MAAM,WAAA;gBAC3B,UAAA,YAAY,KAAA,GACX,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,sBAAA;oBAAsB,GAAG,YAAA;oBAAc,KAAK;gBAAA,CAAc,IAE3D,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,yBAAA;oBAAyB,GAAG,YAAA;oBAAc,KAAK;gBAAA,CAAc;YAAA,CAElE;QAAA,CACF;IAAA,CACF;AAEJ;AASF,IAAM,uBAA6B,sMAAA,UAAA,CACjC,CAAC,OAA8C,iBAAiB;IAC9D,MAAM,UAAU,eAAe,cAAc,MAAM,WAAW;IAC9D,MAAM,MAAY,sMAAA,MAAA,CAAmC,IAAI;IACzD,MAAM,+LAAe,kBAAA,EAAgB,cAAc,GAAG;IAGhD,sMAAA,SAAA,CAAU,MAAM;QACpB,MAAM,UAAU,IAAI,OAAA;QACpB,IAAI,QAAS,CAAA,oKAAO,cAAA,EAAW,OAAO;IACxC,GAAG,CAAC,CAAC;IAEL,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,iBAAA;QACE,GAAG,KAAA;QACJ,KAAK;QAGL,WAAW,QAAQ,IAAA;QAGnB,6BAA6B,QAAQ,IAAA;QACrC,sBAAoB;QAGpB,iLAAgB,uBAAA,EACd,MAAM,cAAA,EACN,CAAC,QAAU,MAAM,cAAA,CAAe,GAChC;YAAE,0BAA0B;QAAM;QAEpC,WAAW,IAAM,QAAQ,YAAA,CAAa,KAAK;IAAA;AAGjD;AAGF,IAAM,0BAAgC,sMAAA,UAAA,CAGpC,CAAC,OAA8C,iBAAiB;IAChE,MAAM,UAAU,eAAe,cAAc,MAAM,WAAW;IAC9D,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,iBAAA;QACE,GAAG,KAAA;QACJ,KAAK;QACL,WAAW;QACX,6BAA6B;QAC7B,sBAAsB;QACtB,WAAW,IAAM,QAAQ,YAAA,CAAa,KAAK;IAAA;AAGjD,CAAC;AAgDD,IAAM,WAAO,8KAAA,EAAW,wBAAwB;AAEhD,IAAM,kBAAwB,sMAAA,UAAA,CAC5B,CAAC,OAA0C,iBAAiB;IAC1D,MAAM,EACJ,WAAA,EACA,OAAO,KAAA,EACP,SAAA,EACA,eAAA,EACA,gBAAA,EACA,2BAAA,EACA,YAAA,EACA,eAAA,EACA,oBAAA,EACA,cAAA,EACA,iBAAA,EACA,SAAA,EACA,oBAAA,EACA,GAAG,cACL,GAAI;IACJ,MAAM,UAAU,eAAe,cAAc,WAAW;IACxD,MAAM,cAAc,mBAAmB,cAAc,WAAW;IAChE,MAAM,cAAc,eAAe,WAAW;IAC9C,MAAM,wBAAwB,yBAAyB,WAAW;IAClE,MAAM,WAAW,cAAc,WAAW;IAC1C,MAAM,CAAC,eAAe,gBAAgB,CAAA,GAAU,sMAAA,QAAA,CAAwB,IAAI;IAC5E,MAAM,aAAmB,sMAAA,MAAA,CAAuB,IAAI;IACpD,MAAM,+LAAe,kBAAA,EAAgB,cAAc,YAAY,QAAQ,eAAe;IACtF,MAAM,WAAiB,sMAAA,MAAA,CAAO,CAAC;IAC/B,MAAM,YAAkB,sMAAA,MAAA,CAAO,EAAE;IACjC,MAAM,uBAA6B,sMAAA,MAAA,CAAO,CAAC;IAC3C,MAAM,wBAA8B,sMAAA,MAAA,CAA2B,IAAI;IACnE,MAAM,gBAAsB,sMAAA,MAAA,CAAa,OAAO;IAChD,MAAM,kBAAwB,sMAAA,MAAA,CAAO,CAAC;IAEtC,MAAM,oBAAoB,6OAAuB,eAAA,GAAqB,sMAAA,QAAA;IACtE,MAAM,yBAAyB,uBAC3B;QAAE,IAAI;QAAM,gBAAgB;IAAK,IACjC,KAAA;IAEJ,MAAM,wBAAwB,CAAC,QAAgB;QAC7C,MAAM,SAAS,UAAU,OAAA,GAAU;QACnC,MAAM,QAAQ,SAAS,EAAE,MAAA,CAAO,CAAC,OAAS,CAAC,KAAK,QAAQ;QACxD,MAAM,cAAc,SAAS,aAAA;QAC7B,MAAM,eAAe,MAAM,IAAA,CAAK,CAAC,OAAS,KAAK,GAAA,CAAI,OAAA,KAAY,WAAW,GAAG;QAC7E,MAAM,SAAS,MAAM,GAAA,CAAI,CAAC,OAAS,KAAK,SAAS;QACjD,MAAM,YAAY,aAAa,QAAQ,QAAQ,YAAY;QAC3D,MAAM,UAAU,MAAM,IAAA,CAAK,CAAC,OAAS,KAAK,SAAA,KAAc,SAAS,GAAG,IAAI;QAGxE,CAAC,SAAS,aAAa,KAAA,EAAe;YACpC,UAAU,OAAA,GAAU;YACpB,OAAO,YAAA,CAAa,SAAS,OAAO;YACpC,IAAI,UAAU,GAAI,CAAA,SAAS,OAAA,GAAU,OAAO,UAAA,CAAW,IAAM,aAAa,EAAE,GAAG,GAAI;QACrF,CAAA,EAAG,MAAM;QAET,IAAI,SAAS;YAKX,WAAW,IAAO,QAAwB,KAAA,CAAM,CAAC;QACnD;IACF;IAEM,sMAAA,SAAA,CAAU,MAAM;QACpB,OAAO,IAAM,OAAO,YAAA,CAAa,SAAS,OAAO;IACnD,GAAG,CAAC,CAAC;IAIL,CAAA,GAAA,2KAAA,CAAA,iBAAA,CAAe;IAEf,MAAM,2BAAiC,sMAAA,WAAA,CAAY,CAAC,UAA8B;QAChF,MAAM,kBAAkB,cAAc,OAAA,KAAY,sBAAsB,OAAA,EAAS;QACjF,OAAO,mBAAmB,qBAAqB,OAAO,sBAAsB,OAAA,EAAS,IAAI;IAC3F,GAAG,CAAC,CAAC;IAEL,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,qBAAA;QACC,OAAO;QACP;QACA,aAAmB,sMAAA,WAAA,CACjB,CAAC,UAAU;YACT,IAAI,yBAAyB,KAAK,EAAG,CAAA,MAAM,cAAA,CAAe;QAC5D,GACA;YAAC,wBAAwB;SAAA;QAE3B,aAAmB,sMAAA,WAAA,CACjB,CAAC,UAAU;YACT,IAAI,yBAAyB,KAAK,EAAG,CAAA;YACrC,WAAW,OAAA,EAAS,MAAM;YAC1B,iBAAiB,IAAI;QACvB,GACA;YAAC,wBAAwB;SAAA;QAE3B,gBAAsB,sMAAA,WAAA,CACpB,CAAC,UAAU;YACT,IAAI,yBAAyB,KAAK,EAAG,CAAA,MAAM,cAAA,CAAe;QAC5D,GACA;YAAC,wBAAwB;SAAA;QAE3B;QACA,4BAAkC,sMAAA,WAAA,CAAY,CAAC,WAAW;YACxD,sBAAsB,OAAA,GAAU;QAClC,GAAG,CAAC,CAAC;QAEL,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,mBAAA;YAAmB,GAAG,sBAAA;YACrB,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,6KAAC,aAAA,EAAA;gBACC,SAAO;gBACP,SAAS;gBACT,mLAAkB,uBAAA,EAAqB,iBAAiB,CAAC,UAAU;oBAGjE,MAAM,cAAA,CAAe;oBACrB,WAAW,OAAA,EAAS,MAAM;wBAAE,eAAe;oBAAK,CAAC;gBACnD,CAAC;gBACD,oBAAoB;gBAEpB,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,mLAAC,mBAAA,EAAA;oBACC,SAAO;oBACP;oBACA;oBACA;oBACA;oBACA;oBACA;oBAEA,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAkB,4KAAA,IAAA,EAAjB;wBACC,SAAO;wBACN,GAAG,qBAAA;wBACJ,KAAK,YAAY,GAAA;wBACjB,aAAY;wBACZ;wBACA,kBAAkB;wBAClB,0BAA0B;wBAC1B,+KAAc,uBAAA,EAAqB,cAAc,CAAC,UAAU;4BAE1D,IAAI,CAAC,YAAY,kBAAA,CAAmB,OAAA,CAAS,CAAA,MAAM,cAAA,CAAe;wBACpE,CAAC;wBACD,2BAAyB;wBAEzB,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAiB,mKAAA,OAAA,EAAhB;4BACC,MAAK;4BACL,oBAAiB;4BACjB,cAAY,aAAa,QAAQ,IAAI;4BACrC,2BAAwB;4BACxB,KAAK,YAAY,GAAA;4BAChB,GAAG,WAAA;4BACH,GAAG,YAAA;4BACJ,KAAK;4BACL,OAAO;gCAAE,SAAS;gCAAQ,GAAG,aAAa,KAAA;4BAAM;4BAChD,WAAW,wLAAA,EAAqB,aAAa,SAAA,EAAW,CAAC,UAAU;gCAEjE,MAAM,SAAS,MAAM,MAAA;gCACrB,MAAM,kBACJ,OAAO,OAAA,CAAQ,2BAA2B,MAAM,MAAM,aAAA;gCACxD,MAAM,gBAAgB,MAAM,OAAA,IAAW,MAAM,MAAA,IAAU,MAAM,OAAA;gCAC7D,MAAM,iBAAiB,MAAM,GAAA,CAAI,MAAA,KAAW;gCAC5C,IAAI,iBAAiB;oCAEnB,IAAI,MAAM,GAAA,KAAQ,MAAO,CAAA,MAAM,cAAA,CAAe;oCAC9C,IAAI,CAAC,iBAAiB,eAAgB,CAAA,sBAAsB,MAAM,GAAG;gCACvE;gCAEA,MAAM,UAAU,WAAW,OAAA;gCAC3B,IAAI,MAAM,MAAA,KAAW,QAAS,CAAA;gCAC9B,IAAI,CAAC,gBAAgB,QAAA,CAAS,MAAM,GAAG,EAAG,CAAA;gCAC1C,MAAM,cAAA,CAAe;gCACrB,MAAM,QAAQ,SAAS,EAAE,MAAA,CAAO,CAAC,OAAS,CAAC,KAAK,QAAQ;gCACxD,MAAM,iBAAiB,MAAM,GAAA,CAAI,CAAC,OAAS,KAAK,GAAA,CAAI,OAAQ;gCAC5D,IAAI,UAAU,QAAA,CAAS,MAAM,GAAG,EAAG,CAAA,eAAe,OAAA,CAAQ;gCAC1D,WAAW,cAAc;4BAC3B,CAAC;4BACD,yKAAQ,uBAAA,EAAqB,MAAM,MAAA,EAAQ,CAAC,UAAU;gCAEpD,IAAI,CAAC,MAAM,aAAA,CAAc,QAAA,CAAS,MAAM,MAAM,GAAG;oCAC/C,OAAO,YAAA,CAAa,SAAS,OAAO;oCACpC,UAAU,OAAA,GAAU;gCACtB;4BACF,CAAC;4BACD,gLAAe,uBAAA,EACb,MAAM,aAAA,EACN,UAAU,CAAC,UAAU;gCACnB,MAAM,SAAS,MAAM,MAAA;gCACrB,MAAM,qBAAqB,gBAAgB,OAAA,KAAY,MAAM,OAAA;gCAI7D,IAAI,MAAM,aAAA,CAAc,QAAA,CAAS,MAAM,KAAK,oBAAoB;oCAC9D,MAAM,SAAS,MAAM,OAAA,GAAU,gBAAgB,OAAA,GAAU,UAAU;oCACnE,cAAc,OAAA,GAAU;oCACxB,gBAAgB,OAAA,GAAU,MAAM,OAAA;gCAClC;4BACF,CAAC;wBACH;oBACF;gBACF;YACF;QACF,CACF;IAAA;AAGN;AAGF,YAAY,WAAA,GAAc;AAM1B,IAAM,aAAa;AAMnB,IAAM,YAAkB,sMAAA,UAAA,CACtB,CAAC,OAAoC,iBAAiB;IACpD,MAAM,EAAE,WAAA,EAAa,GAAG,WAAW,CAAA,GAAI;IACvC,OAAO,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,wKAAC,YAAA,CAAU,GAAA,EAAV;QAAc,MAAK;QAAS,GAAG,UAAA;QAAY,KAAK;IAAA,CAAc;AACxE;AAGF,UAAU,WAAA,GAAc;AAMxB,IAAM,aAAa;AAKnB,IAAM,YAAkB,sMAAA,UAAA,CACtB,CAAC,OAAoC,iBAAiB;IACpD,MAAM,EAAE,WAAA,EAAa,GAAG,WAAW,CAAA,GAAI;IACvC,OAAO,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,wKAAC,YAAA,CAAU,GAAA,EAAV;QAAe,GAAG,UAAA;QAAY,KAAK;IAAA,CAAc;AAC3D;AAGF,UAAU,WAAA,GAAc;AAMxB,IAAM,YAAY;AAClB,IAAM,cAAc;AAOpB,IAAM,WAAiB,sMAAA,UAAA,CACrB,CAAC,OAAmC,iBAAiB;IACnD,MAAM,EAAE,WAAW,KAAA,EAAO,QAAA,EAAU,GAAG,UAAU,CAAA,GAAI;IACrD,MAAM,MAAY,sMAAA,MAAA,CAAuB,IAAI;IAC7C,MAAM,cAAc,mBAAmB,WAAW,MAAM,WAAW;IACnE,MAAM,iBAAiB,sBAAsB,WAAW,MAAM,WAAW;IACzE,MAAM,eAAe,kMAAA,EAAgB,cAAc,GAAG;IACtD,MAAM,mBAAyB,sMAAA,MAAA,CAAO,KAAK;IAE3C,MAAM,eAAe,MAAM;QACzB,MAAM,WAAW,IAAI,OAAA;QACrB,IAAI,CAAC,YAAY,UAAU;YACzB,MAAM,kBAAkB,IAAI,YAAY,aAAa;gBAAE,SAAS;gBAAM,YAAY;YAAK,CAAC;YACxF,SAAS,gBAAA,CAAiB,aAAa,CAAC,QAAU,WAAW,KAAK,GAAG;gBAAE,MAAM;YAAK,CAAC;YACnF,CAAA,GAAA,qKAAA,CAAA,8BAAA,EAA4B,UAAU,eAAe;YACrD,IAAI,gBAAgB,gBAAA,EAAkB;gBACpC,iBAAiB,OAAA,GAAU;YAC7B,OAAO;gBACL,YAAY,OAAA,CAAQ;YACtB;QACF;IACF;IAEA,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,cAAA;QACE,GAAG,SAAA;QACJ,KAAK;QACL;QACA,aAAS,oLAAA,EAAqB,MAAM,OAAA,EAAS,YAAY;QACzD,eAAe,CAAC,UAAU;YACxB,MAAM,aAAA,GAAgB,KAAK;YAC3B,iBAAiB,OAAA,GAAU;QAC7B;QACA,8KAAa,uBAAA,EAAqB,MAAM,WAAA,EAAa,CAAC,UAAU;YAI9D,IAAI,CAAC,iBAAiB,OAAA,CAAS,CAAA,MAAM,aAAA,EAAe,MAAM;QAC5D,CAAC;QACD,eAAW,oLAAA,EAAqB,MAAM,SAAA,EAAW,CAAC,UAAU;YAC1D,MAAM,gBAAgB,eAAe,SAAA,CAAU,OAAA,KAAY;YAC3D,IAAI,YAAa,iBAAiB,MAAM,GAAA,KAAQ,IAAM,CAAA;YACtD,IAAI,eAAe,QAAA,CAAS,MAAM,GAAG,GAAG;gBACtC,MAAM,aAAA,CAAc,KAAA,CAAM;gBAO1B,MAAM,cAAA,CAAe;YACvB;QACF,CAAC;IAAA;AAGP;AAGF,SAAS,WAAA,GAAc;AAUvB,IAAM,eAAqB,sMAAA,UAAA,CACzB,CAAC,OAAuC,iBAAiB;IACvD,MAAM,EAAE,WAAA,EAAa,WAAW,KAAA,EAAO,SAAA,EAAW,GAAG,UAAU,CAAA,GAAI;IACnE,MAAM,iBAAiB,sBAAsB,WAAW,WAAW;IACnE,MAAM,wBAAwB,yBAAyB,WAAW;IAClE,MAAM,MAAY,sMAAA,MAAA,CAAuB,IAAI;IAC7C,MAAM,+LAAe,kBAAA,EAAgB,cAAc,GAAG;IACtD,MAAM,CAAC,WAAW,YAAY,CAAA,GAAU,sMAAA,QAAA,CAAS,KAAK;IAGtD,MAAM,CAAC,aAAa,cAAc,CAAA,GAAU,sMAAA,QAAA,CAAS,EAAE;IACjD,sMAAA,SAAA,CAAU,MAAM;QACpB,MAAM,WAAW,IAAI,OAAA;QACrB,IAAI,UAAU;YACZ,eAAA,CAAgB,SAAS,WAAA,IAAe,EAAA,EAAI,IAAA,CAAK,CAAC;QACpD;IACF,GAAG;QAAC,UAAU,QAAQ;KAAC;IAEvB,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,WAAW,QAAA,EAAX;QACC,OAAO;QACP;QACA,WAAW,aAAa;QAExB,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAkB,4KAAA,IAAA,EAAjB;YAAsB,SAAO;YAAE,GAAG,qBAAA;YAAuB,WAAW,CAAC;YACpE,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,wKAAC,YAAA,CAAU,GAAA,EAAV;gBACC,MAAK;gBACL,oBAAkB,YAAY,KAAK,KAAA;gBACnC,iBAAe,YAAY,KAAA;gBAC3B,iBAAe,WAAW,KAAK,KAAA;gBAC9B,GAAG,SAAA;gBACJ,KAAK;gBAYL,eAAe,wLAAA,EACb,MAAM,aAAA,EACN,UAAU,CAAC,UAAU;oBACnB,IAAI,UAAU;wBACZ,eAAe,WAAA,CAAY,KAAK;oBAClC,OAAO;wBACL,eAAe,WAAA,CAAY,KAAK;wBAChC,IAAI,CAAC,MAAM,gBAAA,EAAkB;4BAC3B,MAAM,OAAO,MAAM,aAAA;4BACnB,KAAK,KAAA,CAAM;gCAAE,eAAe;4BAAK,CAAC;wBACpC;oBACF;gBACF,CAAC;gBAEH,gBAAgB,wLAAA,EACd,MAAM,cAAA,EACN,UAAU,CAAC,QAAU,eAAe,WAAA,CAAY,KAAK,CAAC;gBAExD,0KAAS,uBAAA,EAAqB,MAAM,OAAA,EAAS,IAAM,aAAa,IAAI,CAAC;gBACrE,yKAAQ,uBAAA,EAAqB,MAAM,MAAA,EAAQ,IAAM,aAAa,KAAK,CAAC;YAAA;QACtE,CACF;IAAA;AAGN;AAOF,IAAM,qBAAqB;AAY3B,IAAM,mBAAyB,sMAAA,UAAA,CAC7B,CAAC,OAA2C,iBAAiB;IAC3D,MAAM,EAAE,UAAU,KAAA,EAAO,eAAA,EAAiB,GAAG,kBAAkB,CAAA,GAAI;IACnE,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,uBAAA;QAAsB,OAAO,MAAM,WAAA;QAAa;QAC/C,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,UAAA;YACC,MAAK;YACL,gBAAc,gBAAgB,OAAO,IAAI,UAAU;YAClD,GAAG,iBAAA;YACJ,KAAK;YACL,cAAY,gBAAgB,OAAO;YACnC,2KAAU,uBAAA,EACR,kBAAkB,QAAA,EAClB,IAAM,kBAAkB,gBAAgB,OAAO,IAAI,OAAO,CAAC,OAAO,GAClE;gBAAE,0BAA0B;YAAM;QACpC;IACF,CACF;AAEJ;AAGF,iBAAiB,WAAA,GAAc;AAM/B,IAAM,mBAAmB;AAEzB,IAAM,CAAC,oBAAoB,oBAAoB,CAAA,GAAI,kBACjD,kBACA;IAAE,OAAO,KAAA;IAAW,eAAe,KAAO,CAAD;AAAG;AAS9C,IAAM,iBAAuB,sMAAA,UAAA,CAC3B,CAAC,OAAyC,iBAAiB;IACzD,MAAM,EAAE,KAAA,EAAO,aAAA,EAAe,GAAG,WAAW,CAAA,GAAI;IAChD,MAAM,2MAAoB,iBAAA,EAAe,aAAa;IACtD,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,oBAAA;QAAmB,OAAO,MAAM,WAAA;QAAa;QAAc,eAAe;QACzE,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,WAAA;YAAW,GAAG,UAAA;YAAY,KAAK;QAAA,CAAc;IAAA,CAChD;AAEJ;AAGF,eAAe,WAAA,GAAc;AAM7B,IAAM,kBAAkB;AAOxB,IAAM,gBAAsB,sMAAA,UAAA,CAC1B,CAAC,OAAwC,iBAAiB;IACxD,MAAM,EAAE,KAAA,EAAO,GAAG,eAAe,CAAA,GAAI;IACrC,MAAM,UAAU,qBAAqB,iBAAiB,MAAM,WAAW;IACvE,MAAM,UAAU,UAAU,QAAQ,KAAA;IAClC,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,uBAAA;QAAsB,OAAO,MAAM,WAAA;QAAa;QAC/C,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,UAAA;YACC,MAAK;YACL,gBAAc;YACb,GAAG,cAAA;YACJ,KAAK;YACL,cAAY,gBAAgB,OAAO;YACnC,2KAAU,uBAAA,EACR,eAAe,QAAA,EACf,IAAM,QAAQ,aAAA,GAAgB,KAAK,GACnC;gBAAE,0BAA0B;YAAM;QACpC;IACF,CACF;AAEJ;AAGF,cAAc,WAAA,GAAc;AAM5B,IAAM,sBAAsB;AAI5B,IAAM,CAAC,uBAAuB,uBAAuB,CAAA,GAAI,kBACvD,qBACA;IAAE,SAAS;AAAM;AAanB,IAAM,oBAA0B,sMAAA,UAAA,CAC9B,CAAC,OAA4C,iBAAiB;IAC5D,MAAM,EAAE,WAAA,EAAa,UAAA,EAAY,GAAG,mBAAmB,CAAA,GAAI;IAC3D,MAAM,mBAAmB,wBAAwB,qBAAqB,WAAW;IACjF,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,sKAAC,YAAA,EAAA;QACC,SACE,cACA,gBAAgB,iBAAiB,OAAO,KACxC,iBAAiB,OAAA,KAAY;QAG/B,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,wKAAC,YAAA,CAAU,IAAA,EAAV;YACE,GAAG,kBAAA;YACJ,KAAK;YACL,cAAY,gBAAgB,iBAAiB,OAAO;QAAA;IACtD;AAGN;AAGF,kBAAkB,WAAA,GAAc;AAMhC,IAAM,iBAAiB;AAKvB,IAAM,gBAAsB,sMAAA,UAAA,CAC1B,CAAC,OAAwC,iBAAiB;IACxD,MAAM,EAAE,WAAA,EAAa,GAAG,eAAe,CAAA,GAAI;IAC3C,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,wKAAC,YAAA,CAAU,GAAA,EAAV;QACC,MAAK;QACL,oBAAiB;QAChB,GAAG,cAAA;QACJ,KAAK;IAAA;AAGX;AAGF,cAAc,WAAA,GAAc;AAM5B,IAAM,aAAa;AAMnB,IAAM,YAAkB,sMAAA,UAAA,CACtB,CAAC,OAAoC,iBAAiB;IACpD,MAAM,EAAE,WAAA,EAAa,GAAG,WAAW,CAAA,GAAI;IACvC,MAAM,cAAc,eAAe,WAAW;IAC9C,OAAO,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAiB,mKAAA,KAAA,EAAhB;QAAuB,GAAG,WAAA;QAAc,GAAG,UAAA;QAAY,KAAK;IAAA,CAAc;AACpF;AAGF,UAAU,WAAA,GAAc;AAMxB,IAAM,WAAW;AASjB,IAAM,CAAC,iBAAiB,iBAAiB,CAAA,GAAI,kBAAuC,QAAQ;AAQ5F,IAAM,UAAkC,CAAC,UAAqC;IAC5E,MAAM,EAAE,WAAA,EAAa,QAAA,EAAU,OAAO,KAAA,EAAO,YAAA,CAAa,CAAA,GAAI;IAC9D,MAAM,oBAAoB,eAAe,UAAU,WAAW;IAC9D,MAAM,cAAc,eAAe,WAAW;IAC9C,MAAM,CAAC,SAAS,UAAU,CAAA,GAAU,sMAAA,QAAA,CAAuC,IAAI;IAC/E,MAAM,CAAC,SAAS,UAAU,CAAA,GAAU,sMAAA,QAAA,CAAoC,IAAI;IAC5E,MAAM,0MAAmB,iBAAA,EAAe,YAAY;IAG9C,sMAAA,SAAA,CAAU,MAAM;QACpB,IAAI,kBAAkB,IAAA,KAAS,MAAO,CAAA,iBAAiB,KAAK;QAC5D,OAAO,IAAM,iBAAiB,KAAK;IACrC,GAAG;QAAC,kBAAkB,IAAA;QAAM,gBAAgB;KAAC;IAE7C,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAiB,mKAAA,IAAA,EAAhB;QAAsB,GAAG,WAAA;QACxB,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,cAAA;YACC,OAAO;YACP;YACA,cAAc;YACd;YACA,iBAAiB;YAEjB,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,iBAAA;gBACC,OAAO;gBACP,8KAAW,QAAA,CAAM;gBACjB,eAAW,uKAAA,CAAM;gBACjB;gBACA,iBAAiB;gBAEhB;YAAA;QACH;IACF,CACF;AAEJ;AAEA,QAAQ,WAAA,GAAc;AAMtB,IAAM,mBAAmB;AAKzB,IAAM,iBAAuB,sMAAA,UAAA,CAC3B,CAAC,OAAyC,iBAAiB;IACzD,MAAM,UAAU,eAAe,kBAAkB,MAAM,WAAW;IAClE,MAAM,cAAc,mBAAmB,kBAAkB,MAAM,WAAW;IAC1E,MAAM,aAAa,kBAAkB,kBAAkB,MAAM,WAAW;IACxE,MAAM,iBAAiB,sBAAsB,kBAAkB,MAAM,WAAW;IAChF,MAAM,eAAqB,sMAAA,MAAA,CAAsB,IAAI;IACrD,MAAM,EAAE,oBAAA,EAAsB,0BAAA,CAA2B,CAAA,GAAI;IAC7D,MAAM,QAAQ;QAAE,aAAa,MAAM,WAAA;IAAY;IAE/C,MAAM,iBAAuB,sMAAA,WAAA,CAAY,MAAM;QAC7C,IAAI,aAAa,OAAA,CAAS,CAAA,OAAO,YAAA,CAAa,aAAa,OAAO;QAClE,aAAa,OAAA,GAAU;IACzB,GAAG,CAAC,CAAC;IAEC,sMAAA,SAAA,CAAU,IAAM,gBAAgB;QAAC,cAAc;KAAC;IAEhD,sMAAA,SAAA,CAAU,MAAM;QACpB,MAAM,oBAAoB,qBAAqB,OAAA;QAC/C,OAAO,MAAM;YACX,OAAO,YAAA,CAAa,iBAAiB;YACrC,2BAA2B,IAAI;QACjC;IACF,GAAG;QAAC;QAAsB,0BAA0B;KAAC;IAErD,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,YAAA;QAAW,SAAO;QAAE,GAAG,KAAA;QACtB,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,cAAA;YACC,IAAI,WAAW,SAAA;YACf,iBAAc;YACd,iBAAe,QAAQ,IAAA;YACvB,iBAAe,WAAW,SAAA;YAC1B,cAAY,aAAa,QAAQ,IAAI;YACpC,GAAG,KAAA;YACJ,MAAK,6LAAA,EAAY,cAAc,WAAW,eAAe;YAGzD,SAAS,CAAC,UAAU;gBAClB,MAAM,OAAA,GAAU,KAAK;gBACrB,IAAI,MAAM,QAAA,IAAY,MAAM,gBAAA,CAAkB,CAAA;gBAM9C,MAAM,aAAA,CAAc,KAAA,CAAM;gBAC1B,IAAI,CAAC,QAAQ,IAAA,CAAM,CAAA,QAAQ,YAAA,CAAa,IAAI;YAC9C;YACA,gLAAe,uBAAA,EACb,MAAM,aAAA,EACN,UAAU,CAAC,UAAU;gBACnB,eAAe,WAAA,CAAY,KAAK;gBAChC,IAAI,MAAM,gBAAA,CAAkB,CAAA;gBAC5B,IAAI,CAAC,MAAM,QAAA,IAAY,CAAC,QAAQ,IAAA,IAAQ,CAAC,aAAa,OAAA,EAAS;oBAC7D,eAAe,0BAAA,CAA2B,IAAI;oBAC9C,aAAa,OAAA,GAAU,OAAO,UAAA,CAAW,MAAM;wBAC7C,QAAQ,YAAA,CAAa,IAAI;wBACzB,eAAe;oBACjB,GAAG,GAAG;gBACR;YACF,CAAC;YAEH,oBAAgB,oLAAA,EACd,MAAM,cAAA,EACN,UAAU,CAAC,UAAU;gBACnB,eAAe;gBAEf,MAAM,cAAc,QAAQ,OAAA,EAAS,sBAAsB;gBAC3D,IAAI,aAAa;oBAEf,MAAM,OAAO,QAAQ,OAAA,EAAS,QAAQ;oBACtC,MAAM,YAAY,SAAS;oBAC3B,MAAM,QAAQ,YAAY,CAAA,IAAK;oBAC/B,MAAM,kBAAkB,WAAA,CAAY,YAAY,SAAS,OAAO,CAAA;oBAChE,MAAM,iBAAiB,WAAA,CAAY,YAAY,UAAU,MAAM,CAAA;oBAE/D,eAAe,0BAAA,CAA2B;wBACxC,MAAM;4BAAA,4DAAA;4BAAA,qCAAA;4BAGJ;gCAAE,GAAG,MAAM,OAAA,GAAU;gCAAO,GAAG,MAAM,OAAA;4BAAQ;4BAC7C;gCAAE,GAAG;gCAAiB,GAAG,YAAY,GAAA;4BAAI;4BACzC;gCAAE,GAAG;gCAAgB,GAAG,YAAY,GAAA;4BAAI;4BACxC;gCAAE,GAAG;gCAAgB,GAAG,YAAY,MAAA;4BAAO;4BAC3C;gCAAE,GAAG;gCAAiB,GAAG,YAAY,MAAA;4BAAO;yBAC9C;wBACA;oBACF,CAAC;oBAED,OAAO,YAAA,CAAa,qBAAqB,OAAO;oBAChD,qBAAqB,OAAA,GAAU,OAAO,UAAA,CACpC,IAAM,eAAe,0BAAA,CAA2B,IAAI,GACpD;gBAEJ,OAAO;oBACL,eAAe,cAAA,CAAe,KAAK;oBACnC,IAAI,MAAM,gBAAA,CAAkB,CAAA;oBAG5B,eAAe,0BAAA,CAA2B,IAAI;gBAChD;YACF,CAAC;YAEH,4KAAW,uBAAA,EAAqB,MAAM,SAAA,EAAW,CAAC,UAAU;gBAC1D,MAAM,gBAAgB,eAAe,SAAA,CAAU,OAAA,KAAY;gBAC3D,IAAI,MAAM,QAAA,IAAa,iBAAiB,MAAM,GAAA,KAAQ,IAAM,CAAA;gBAC5D,IAAI,aAAA,CAAc,YAAY,GAAG,CAAA,CAAE,QAAA,CAAS,MAAM,GAAG,GAAG;oBACtD,QAAQ,YAAA,CAAa,IAAI;oBAGzB,QAAQ,OAAA,EAAS,MAAM;oBAEvB,MAAM,cAAA,CAAe;gBACvB;YACF,CAAC;QAAA;IACH,CACF;AAEJ;AAGF,eAAe,WAAA,GAAc;AAM7B,IAAM,mBAAmB;AAezB,IAAM,iBAAuB,sMAAA,UAAA,CAC3B,CAAC,OAAyC,iBAAiB;IACzD,MAAM,gBAAgB,iBAAiB,cAAc,MAAM,WAAW;IACtE,MAAM,EAAE,aAAa,cAAc,UAAA,EAAY,GAAG,gBAAgB,CAAA,GAAI;IACtE,MAAM,UAAU,eAAe,cAAc,MAAM,WAAW;IAC9D,MAAM,cAAc,mBAAmB,cAAc,MAAM,WAAW;IACtE,MAAM,aAAa,kBAAkB,kBAAkB,MAAM,WAAW;IACxE,MAAM,MAAY,sMAAA,MAAA,CAA8B,IAAI;IACpD,MAAM,+LAAe,kBAAA,EAAgB,cAAc,GAAG;IACtD,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,WAAW,QAAA,EAAX;QAAoB,OAAO,MAAM,WAAA;QAChC,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,uKAAC,WAAA,EAAA;YAAS,SAAS,cAAc,QAAQ,IAAA;YACvC,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,WAAW,IAAA,EAAX;gBAAgB,OAAO,MAAM,WAAA;gBAC5B,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,iBAAA;oBACC,IAAI,WAAW,SAAA;oBACf,mBAAiB,WAAW,SAAA;oBAC3B,GAAG,eAAA;oBACJ,KAAK;oBACL,OAAM;oBACN,MAAM,YAAY,GAAA,KAAQ,QAAQ,SAAS;oBAC3C,6BAA6B;oBAC7B,sBAAsB;oBACtB,WAAW;oBACX,iBAAiB,CAAC,UAAU;wBAE1B,IAAI,YAAY,kBAAA,CAAmB,OAAA,CAAS,CAAA,IAAI,OAAA,EAAS,MAAM;wBAC/D,MAAM,cAAA,CAAe;oBACvB;oBAGA,kBAAkB,CAAC,QAAU,MAAM,cAAA,CAAe;oBAClD,iLAAgB,uBAAA,EAAqB,MAAM,cAAA,EAAgB,CAAC,UAAU;wBAGpE,IAAI,MAAM,MAAA,KAAW,WAAW,OAAA,CAAS,CAAA,QAAQ,YAAA,CAAa,KAAK;oBACrE,CAAC;oBACD,qBAAiB,oLAAA,EAAqB,MAAM,eAAA,EAAiB,CAAC,UAAU;wBACtE,YAAY,OAAA,CAAQ;wBAEpB,MAAM,cAAA,CAAe;oBACvB,CAAC;oBACD,4KAAW,uBAAA,EAAqB,MAAM,SAAA,EAAW,CAAC,UAAU;wBAE1D,MAAM,kBAAkB,MAAM,aAAA,CAAc,QAAA,CAAS,MAAM,MAAqB;wBAChF,MAAM,aAAa,cAAA,CAAe,YAAY,GAAG,CAAA,CAAE,QAAA,CAAS,MAAM,GAAG;wBACrE,IAAI,mBAAmB,YAAY;4BACjC,QAAQ,YAAA,CAAa,KAAK;4BAE1B,WAAW,OAAA,EAAS,MAAM;4BAE1B,MAAM,cAAA,CAAe;wBACvB;oBACF,CAAC;gBAAA;YACH,CACF;QAAA,CACF;IAAA,CACF;AAEJ;AAGF,eAAe,WAAA,GAAc;AAI7B,SAAS,aAAa,IAAA,EAAe;IACnC,OAAO,OAAO,SAAS;AACzB;AAEA,SAAS,gBAAgB,OAAA,EAAoD;IAC3E,OAAO,YAAY;AACrB;AAEA,SAAS,gBAAgB,OAAA,EAAuB;IAC9C,OAAO,gBAAgB,OAAO,IAAI,kBAAkB,UAAU,YAAY;AAC5E;AAEA,SAAS,WAAW,UAAA,EAA2B;IAC7C,MAAM,6BAA6B,SAAS,aAAA;IAC5C,KAAA,MAAW,aAAa,WAAY;QAElC,IAAI,cAAc,2BAA4B,CAAA;QAC9C,UAAU,KAAA,CAAM;QAChB,IAAI,SAAS,aAAA,KAAkB,2BAA4B,CAAA;IAC7D;AACF;AAMA,SAAS,UAAa,KAAA,EAAY,UAAA,EAAoB;IACpD,OAAO,MAAM,GAAA,CAAO,CAAC,GAAG,QAAU,KAAA,CAAA,CAAO,aAAa,KAAA,IAAS,MAAM,MAAM,CAAE;AAC/E;AAmBA,SAAS,aAAa,MAAA,EAAkB,MAAA,EAAgB,YAAA,EAAuB;IAC7E,MAAM,aAAa,OAAO,MAAA,GAAS,KAAK,MAAM,IAAA,CAAK,MAAM,EAAE,KAAA,CAAM,CAAC,OAAS,SAAS,MAAA,CAAO,CAAC,CAAC;IAC7F,MAAM,mBAAmB,aAAa,MAAA,CAAO,CAAC,CAAA,GAAK;IACnD,MAAM,oBAAoB,eAAe,OAAO,OAAA,CAAQ,YAAY,IAAI,CAAA;IACxE,IAAI,gBAAgB,UAAU,QAAQ,KAAK,GAAA,CAAI,mBAAmB,CAAC,CAAC;IACpE,MAAM,sBAAsB,iBAAiB,MAAA,KAAW;IACxD,IAAI,oBAAqB,CAAA,gBAAgB,cAAc,MAAA,CAAO,CAAC,IAAM,MAAM,YAAY;IACvF,MAAM,YAAY,cAAc,IAAA,CAAK,CAAC,QACpC,MAAM,WAAA,CAAY,EAAE,UAAA,CAAW,iBAAiB,WAAA,CAAY,CAAC;IAE/D,OAAO,cAAc,eAAe,YAAY,KAAA;AAClD;AASA,SAAS,iBAAiB,KAAA,EAAc,OAAA,EAAkB;IACxD,MAAM,EAAE,CAAA,EAAG,CAAA,CAAE,CAAA,GAAI;IACjB,IAAI,SAAS;IACb,IAAA,IAAS,IAAI,GAAG,IAAI,QAAQ,MAAA,GAAS,GAAG,IAAI,QAAQ,MAAA,EAAQ,IAAI,IAAK;QACnE,MAAM,KAAK,OAAA,CAAQ,CAAC,CAAA;QACpB,MAAM,KAAK,OAAA,CAAQ,CAAC,CAAA;QACpB,MAAM,KAAK,GAAG,CAAA;QACd,MAAM,KAAK,GAAG,CAAA;QACd,MAAM,KAAK,GAAG,CAAA;QACd,MAAM,KAAK,GAAG,CAAA;QAGd,MAAM,YAAc,KAAK,MAAQ,KAAK,KAAQ,IAAA,CAAK,KAAK,EAAA,IAAA,CAAO,IAAI,EAAA,IAAA,CAAO,KAAK,EAAA,IAAM;QACrF,IAAI,UAAW,CAAA,SAAS,CAAC;IAC3B;IAEA,OAAO;AACT;AAEA,SAAS,qBAAqB,KAAA,EAA2B,IAAA,EAAgB;IACvE,IAAI,CAAC,KAAM,CAAA,OAAO;IAClB,MAAM,YAAY;QAAE,GAAG,MAAM,OAAA;QAAS,GAAG,MAAM,OAAA;IAAQ;IACvD,OAAO,iBAAiB,WAAW,IAAI;AACzC;AAEA,SAAS,UAAa,OAAA,EAAqE;IACzF,OAAO,CAAC,QAAW,MAAM,WAAA,KAAgB,UAAU,QAAQ,KAAK,IAAI,KAAA;AACtE;AAEA,IAAMA,QAAO;AACb,IAAMC,UAAS;AACf,IAAM,SAAS;AACf,IAAMC,WAAU;AAChB,IAAM,QAAQ;AACd,IAAM,QAAQ;AACd,IAAMC,QAAO;AACb,IAAM,eAAe;AACrB,IAAM,aAAa;AACnB,IAAM,YAAY;AAClB,IAAM,gBAAgB;AACtB,IAAM,YAAY;AAClB,IAAMC,SAAQ;AACd,IAAM,MAAM;AACZ,IAAM,aAAa;AACnB,IAAM,aAAa", "ignoreList": [0]}}, {"offset": {"line": 5628, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5634, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/%40radix-ui/react-dropdown-menu/src/dropdown-menu.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { composeRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport * as MenuPrimitive from '@radix-ui/react-menu';\nimport { createMenuScope } from '@radix-ui/react-menu';\nimport { useId } from '@radix-ui/react-id';\n\nimport type { Scope } from '@radix-ui/react-context';\n\ntype Direction = 'ltr' | 'rtl';\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenu\n * -----------------------------------------------------------------------------------------------*/\n\nconst DROPDOWN_MENU_NAME = 'DropdownMenu';\n\ntype ScopedProps<P> = P & { __scopeDropdownMenu?: Scope };\nconst [createDropdownMenuContext, createDropdownMenuScope] = createContextScope(\n  DROPDOWN_MENU_NAME,\n  [createMenuScope]\n);\nconst useMenuScope = createMenuScope();\n\ntype DropdownMenuContextValue = {\n  triggerId: string;\n  triggerRef: React.RefObject<HTMLButtonElement | null>;\n  contentId: string;\n  open: boolean;\n  onOpenChange(open: boolean): void;\n  onOpenToggle(): void;\n  modal: boolean;\n};\n\nconst [DropdownMenuProvider, useDropdownMenuContext] =\n  createDropdownMenuContext<DropdownMenuContextValue>(DROPDOWN_MENU_NAME);\n\ninterface DropdownMenuProps {\n  children?: React.ReactNode;\n  dir?: Direction;\n  open?: boolean;\n  defaultOpen?: boolean;\n  onOpenChange?(open: boolean): void;\n  modal?: boolean;\n}\n\nconst DropdownMenu: React.FC<DropdownMenuProps> = (props: ScopedProps<DropdownMenuProps>) => {\n  const {\n    __scopeDropdownMenu,\n    children,\n    dir,\n    open: openProp,\n    defaultOpen,\n    onOpenChange,\n    modal = true,\n  } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  const triggerRef = React.useRef<HTMLButtonElement>(null);\n  const [open, setOpen] = useControllableState({\n    prop: openProp,\n    defaultProp: defaultOpen ?? false,\n    onChange: onOpenChange,\n    caller: DROPDOWN_MENU_NAME,\n  });\n\n  return (\n    <DropdownMenuProvider\n      scope={__scopeDropdownMenu}\n      triggerId={useId()}\n      triggerRef={triggerRef}\n      contentId={useId()}\n      open={open}\n      onOpenChange={setOpen}\n      onOpenToggle={React.useCallback(() => setOpen((prevOpen) => !prevOpen), [setOpen])}\n      modal={modal}\n    >\n      <MenuPrimitive.Root {...menuScope} open={open} onOpenChange={setOpen} dir={dir} modal={modal}>\n        {children}\n      </MenuPrimitive.Root>\n    </DropdownMenuProvider>\n  );\n};\n\nDropdownMenu.displayName = DROPDOWN_MENU_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst TRIGGER_NAME = 'DropdownMenuTrigger';\n\ntype DropdownMenuTriggerElement = React.ComponentRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = React.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface DropdownMenuTriggerProps extends PrimitiveButtonProps {}\n\nconst DropdownMenuTrigger = React.forwardRef<DropdownMenuTriggerElement, DropdownMenuTriggerProps>(\n  (props: ScopedProps<DropdownMenuTriggerProps>, forwardedRef) => {\n    const { __scopeDropdownMenu, disabled = false, ...triggerProps } = props;\n    const context = useDropdownMenuContext(TRIGGER_NAME, __scopeDropdownMenu);\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return (\n      <MenuPrimitive.Anchor asChild {...menuScope}>\n        <Primitive.button\n          type=\"button\"\n          id={context.triggerId}\n          aria-haspopup=\"menu\"\n          aria-expanded={context.open}\n          aria-controls={context.open ? context.contentId : undefined}\n          data-state={context.open ? 'open' : 'closed'}\n          data-disabled={disabled ? '' : undefined}\n          disabled={disabled}\n          {...triggerProps}\n          ref={composeRefs(forwardedRef, context.triggerRef)}\n          onPointerDown={composeEventHandlers(props.onPointerDown, (event) => {\n            // only call handler if it's the left button (mousedown gets triggered by all mouse buttons)\n            // but not when the control key is pressed (avoiding MacOS right click)\n            if (!disabled && event.button === 0 && event.ctrlKey === false) {\n              context.onOpenToggle();\n              // prevent trigger focusing when opening\n              // this allows the content to be given focus without competition\n              if (!context.open) event.preventDefault();\n            }\n          })}\n          onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n            if (disabled) return;\n            if (['Enter', ' '].includes(event.key)) context.onOpenToggle();\n            if (event.key === 'ArrowDown') context.onOpenChange(true);\n            // prevent keydown from scrolling window / first focused item to execute\n            // that keydown (inadvertently closing the menu)\n            if (['Enter', ' ', 'ArrowDown'].includes(event.key)) event.preventDefault();\n          })}\n        />\n      </MenuPrimitive.Anchor>\n    );\n  }\n);\n\nDropdownMenuTrigger.displayName = TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuPortal\n * -----------------------------------------------------------------------------------------------*/\n\nconst PORTAL_NAME = 'DropdownMenuPortal';\n\ntype MenuPortalProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.Portal>;\ninterface DropdownMenuPortalProps extends MenuPortalProps {}\n\nconst DropdownMenuPortal: React.FC<DropdownMenuPortalProps> = (\n  props: ScopedProps<DropdownMenuPortalProps>\n) => {\n  const { __scopeDropdownMenu, ...portalProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  return <MenuPrimitive.Portal {...menuScope} {...portalProps} />;\n};\n\nDropdownMenuPortal.displayName = PORTAL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'DropdownMenuContent';\n\ntype DropdownMenuContentElement = React.ComponentRef<typeof MenuPrimitive.Content>;\ntype MenuContentProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.Content>;\ninterface DropdownMenuContentProps extends Omit<MenuContentProps, 'onEntryFocus'> {}\n\nconst DropdownMenuContent = React.forwardRef<DropdownMenuContentElement, DropdownMenuContentProps>(\n  (props: ScopedProps<DropdownMenuContentProps>, forwardedRef) => {\n    const { __scopeDropdownMenu, ...contentProps } = props;\n    const context = useDropdownMenuContext(CONTENT_NAME, __scopeDropdownMenu);\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    const hasInteractedOutsideRef = React.useRef(false);\n\n    return (\n      <MenuPrimitive.Content\n        id={context.contentId}\n        aria-labelledby={context.triggerId}\n        {...menuScope}\n        {...contentProps}\n        ref={forwardedRef}\n        onCloseAutoFocus={composeEventHandlers(props.onCloseAutoFocus, (event) => {\n          if (!hasInteractedOutsideRef.current) context.triggerRef.current?.focus();\n          hasInteractedOutsideRef.current = false;\n          // Always prevent auto focus because we either focus manually or want user agent focus\n          event.preventDefault();\n        })}\n        onInteractOutside={composeEventHandlers(props.onInteractOutside, (event) => {\n          const originalEvent = event.detail.originalEvent as PointerEvent;\n          const ctrlLeftClick = originalEvent.button === 0 && originalEvent.ctrlKey === true;\n          const isRightClick = originalEvent.button === 2 || ctrlLeftClick;\n          if (!context.modal || isRightClick) hasInteractedOutsideRef.current = true;\n        })}\n        style={{\n          ...props.style,\n          // re-namespace exposed content custom properties\n          ...{\n            '--radix-dropdown-menu-content-transform-origin':\n              'var(--radix-popper-transform-origin)',\n            '--radix-dropdown-menu-content-available-width': 'var(--radix-popper-available-width)',\n            '--radix-dropdown-menu-content-available-height':\n              'var(--radix-popper-available-height)',\n            '--radix-dropdown-menu-trigger-width': 'var(--radix-popper-anchor-width)',\n            '--radix-dropdown-menu-trigger-height': 'var(--radix-popper-anchor-height)',\n          },\n        }}\n      />\n    );\n  }\n);\n\nDropdownMenuContent.displayName = CONTENT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuGroup\n * -----------------------------------------------------------------------------------------------*/\n\nconst GROUP_NAME = 'DropdownMenuGroup';\n\ntype DropdownMenuGroupElement = React.ComponentRef<typeof MenuPrimitive.Group>;\ntype MenuGroupProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.Group>;\ninterface DropdownMenuGroupProps extends MenuGroupProps {}\n\nconst DropdownMenuGroup = React.forwardRef<DropdownMenuGroupElement, DropdownMenuGroupProps>(\n  (props: ScopedProps<DropdownMenuGroupProps>, forwardedRef) => {\n    const { __scopeDropdownMenu, ...groupProps } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return <MenuPrimitive.Group {...menuScope} {...groupProps} ref={forwardedRef} />;\n  }\n);\n\nDropdownMenuGroup.displayName = GROUP_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuLabel\n * -----------------------------------------------------------------------------------------------*/\n\nconst LABEL_NAME = 'DropdownMenuLabel';\n\ntype DropdownMenuLabelElement = React.ComponentRef<typeof MenuPrimitive.Label>;\ntype MenuLabelProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.Label>;\ninterface DropdownMenuLabelProps extends MenuLabelProps {}\n\nconst DropdownMenuLabel = React.forwardRef<DropdownMenuLabelElement, DropdownMenuLabelProps>(\n  (props: ScopedProps<DropdownMenuLabelProps>, forwardedRef) => {\n    const { __scopeDropdownMenu, ...labelProps } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return <MenuPrimitive.Label {...menuScope} {...labelProps} ref={forwardedRef} />;\n  }\n);\n\nDropdownMenuLabel.displayName = LABEL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_NAME = 'DropdownMenuItem';\n\ntype DropdownMenuItemElement = React.ComponentRef<typeof MenuPrimitive.Item>;\ntype MenuItemProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.Item>;\ninterface DropdownMenuItemProps extends MenuItemProps {}\n\nconst DropdownMenuItem = React.forwardRef<DropdownMenuItemElement, DropdownMenuItemProps>(\n  (props: ScopedProps<DropdownMenuItemProps>, forwardedRef) => {\n    const { __scopeDropdownMenu, ...itemProps } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return <MenuPrimitive.Item {...menuScope} {...itemProps} ref={forwardedRef} />;\n  }\n);\n\nDropdownMenuItem.displayName = ITEM_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuCheckboxItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst CHECKBOX_ITEM_NAME = 'DropdownMenuCheckboxItem';\n\ntype DropdownMenuCheckboxItemElement = React.ComponentRef<typeof MenuPrimitive.CheckboxItem>;\ntype MenuCheckboxItemProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.CheckboxItem>;\ninterface DropdownMenuCheckboxItemProps extends MenuCheckboxItemProps {}\n\nconst DropdownMenuCheckboxItem = React.forwardRef<\n  DropdownMenuCheckboxItemElement,\n  DropdownMenuCheckboxItemProps\n>((props: ScopedProps<DropdownMenuCheckboxItemProps>, forwardedRef) => {\n  const { __scopeDropdownMenu, ...checkboxItemProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  return <MenuPrimitive.CheckboxItem {...menuScope} {...checkboxItemProps} ref={forwardedRef} />;\n});\n\nDropdownMenuCheckboxItem.displayName = CHECKBOX_ITEM_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuRadioGroup\n * -----------------------------------------------------------------------------------------------*/\n\nconst RADIO_GROUP_NAME = 'DropdownMenuRadioGroup';\n\ntype DropdownMenuRadioGroupElement = React.ComponentRef<typeof MenuPrimitive.RadioGroup>;\ntype MenuRadioGroupProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.RadioGroup>;\ninterface DropdownMenuRadioGroupProps extends MenuRadioGroupProps {}\n\nconst DropdownMenuRadioGroup = React.forwardRef<\n  DropdownMenuRadioGroupElement,\n  DropdownMenuRadioGroupProps\n>((props: ScopedProps<DropdownMenuRadioGroupProps>, forwardedRef) => {\n  const { __scopeDropdownMenu, ...radioGroupProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  return <MenuPrimitive.RadioGroup {...menuScope} {...radioGroupProps} ref={forwardedRef} />;\n});\n\nDropdownMenuRadioGroup.displayName = RADIO_GROUP_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuRadioItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst RADIO_ITEM_NAME = 'DropdownMenuRadioItem';\n\ntype DropdownMenuRadioItemElement = React.ComponentRef<typeof MenuPrimitive.RadioItem>;\ntype MenuRadioItemProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.RadioItem>;\ninterface DropdownMenuRadioItemProps extends MenuRadioItemProps {}\n\nconst DropdownMenuRadioItem = React.forwardRef<\n  DropdownMenuRadioItemElement,\n  DropdownMenuRadioItemProps\n>((props: ScopedProps<DropdownMenuRadioItemProps>, forwardedRef) => {\n  const { __scopeDropdownMenu, ...radioItemProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  return <MenuPrimitive.RadioItem {...menuScope} {...radioItemProps} ref={forwardedRef} />;\n});\n\nDropdownMenuRadioItem.displayName = RADIO_ITEM_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuItemIndicator\n * -----------------------------------------------------------------------------------------------*/\n\nconst INDICATOR_NAME = 'DropdownMenuItemIndicator';\n\ntype DropdownMenuItemIndicatorElement = React.ComponentRef<typeof MenuPrimitive.ItemIndicator>;\ntype MenuItemIndicatorProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.ItemIndicator>;\ninterface DropdownMenuItemIndicatorProps extends MenuItemIndicatorProps {}\n\nconst DropdownMenuItemIndicator = React.forwardRef<\n  DropdownMenuItemIndicatorElement,\n  DropdownMenuItemIndicatorProps\n>((props: ScopedProps<DropdownMenuItemIndicatorProps>, forwardedRef) => {\n  const { __scopeDropdownMenu, ...itemIndicatorProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  return <MenuPrimitive.ItemIndicator {...menuScope} {...itemIndicatorProps} ref={forwardedRef} />;\n});\n\nDropdownMenuItemIndicator.displayName = INDICATOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuSeparator\n * -----------------------------------------------------------------------------------------------*/\n\nconst SEPARATOR_NAME = 'DropdownMenuSeparator';\n\ntype DropdownMenuSeparatorElement = React.ComponentRef<typeof MenuPrimitive.Separator>;\ntype MenuSeparatorProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.Separator>;\ninterface DropdownMenuSeparatorProps extends MenuSeparatorProps {}\n\nconst DropdownMenuSeparator = React.forwardRef<\n  DropdownMenuSeparatorElement,\n  DropdownMenuSeparatorProps\n>((props: ScopedProps<DropdownMenuSeparatorProps>, forwardedRef) => {\n  const { __scopeDropdownMenu, ...separatorProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  return <MenuPrimitive.Separator {...menuScope} {...separatorProps} ref={forwardedRef} />;\n});\n\nDropdownMenuSeparator.displayName = SEPARATOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuArrow\n * -----------------------------------------------------------------------------------------------*/\n\nconst ARROW_NAME = 'DropdownMenuArrow';\n\ntype DropdownMenuArrowElement = React.ComponentRef<typeof MenuPrimitive.Arrow>;\ntype MenuArrowProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.Arrow>;\ninterface DropdownMenuArrowProps extends MenuArrowProps {}\n\nconst DropdownMenuArrow = React.forwardRef<DropdownMenuArrowElement, DropdownMenuArrowProps>(\n  (props: ScopedProps<DropdownMenuArrowProps>, forwardedRef) => {\n    const { __scopeDropdownMenu, ...arrowProps } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return <MenuPrimitive.Arrow {...menuScope} {...arrowProps} ref={forwardedRef} />;\n  }\n);\n\nDropdownMenuArrow.displayName = ARROW_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuSub\n * -----------------------------------------------------------------------------------------------*/\n\ninterface DropdownMenuSubProps {\n  children?: React.ReactNode;\n  open?: boolean;\n  defaultOpen?: boolean;\n  onOpenChange?(open: boolean): void;\n}\n\nconst DropdownMenuSub: React.FC<DropdownMenuSubProps> = (\n  props: ScopedProps<DropdownMenuSubProps>\n) => {\n  const { __scopeDropdownMenu, children, open: openProp, onOpenChange, defaultOpen } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  const [open, setOpen] = useControllableState({\n    prop: openProp,\n    defaultProp: defaultOpen ?? false,\n    onChange: onOpenChange,\n    caller: 'DropdownMenuSub',\n  });\n\n  return (\n    <MenuPrimitive.Sub {...menuScope} open={open} onOpenChange={setOpen}>\n      {children}\n    </MenuPrimitive.Sub>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuSubTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst SUB_TRIGGER_NAME = 'DropdownMenuSubTrigger';\n\ntype DropdownMenuSubTriggerElement = React.ComponentRef<typeof MenuPrimitive.SubTrigger>;\ntype MenuSubTriggerProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.SubTrigger>;\ninterface DropdownMenuSubTriggerProps extends MenuSubTriggerProps {}\n\nconst DropdownMenuSubTrigger = React.forwardRef<\n  DropdownMenuSubTriggerElement,\n  DropdownMenuSubTriggerProps\n>((props: ScopedProps<DropdownMenuSubTriggerProps>, forwardedRef) => {\n  const { __scopeDropdownMenu, ...subTriggerProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n  return <MenuPrimitive.SubTrigger {...menuScope} {...subTriggerProps} ref={forwardedRef} />;\n});\n\nDropdownMenuSubTrigger.displayName = SUB_TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DropdownMenuSubContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst SUB_CONTENT_NAME = 'DropdownMenuSubContent';\n\ntype DropdownMenuSubContentElement = React.ComponentRef<typeof MenuPrimitive.Content>;\ntype MenuSubContentProps = React.ComponentPropsWithoutRef<typeof MenuPrimitive.SubContent>;\ninterface DropdownMenuSubContentProps extends MenuSubContentProps {}\n\nconst DropdownMenuSubContent = React.forwardRef<\n  DropdownMenuSubContentElement,\n  DropdownMenuSubContentProps\n>((props: ScopedProps<DropdownMenuSubContentProps>, forwardedRef) => {\n  const { __scopeDropdownMenu, ...subContentProps } = props;\n  const menuScope = useMenuScope(__scopeDropdownMenu);\n\n  return (\n    <MenuPrimitive.SubContent\n      {...menuScope}\n      {...subContentProps}\n      ref={forwardedRef}\n      style={{\n        ...props.style,\n        // re-namespace exposed content custom properties\n        ...{\n          '--radix-dropdown-menu-content-transform-origin': 'var(--radix-popper-transform-origin)',\n          '--radix-dropdown-menu-content-available-width': 'var(--radix-popper-available-width)',\n          '--radix-dropdown-menu-content-available-height': 'var(--radix-popper-available-height)',\n          '--radix-dropdown-menu-trigger-width': 'var(--radix-popper-anchor-width)',\n          '--radix-dropdown-menu-trigger-height': 'var(--radix-popper-anchor-height)',\n        },\n      }}\n    />\n  );\n});\n\nDropdownMenuSubContent.displayName = SUB_CONTENT_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = DropdownMenu;\nconst Trigger = DropdownMenuTrigger;\nconst Portal = DropdownMenuPortal;\nconst Content = DropdownMenuContent;\nconst Group = DropdownMenuGroup;\nconst Label = DropdownMenuLabel;\nconst Item = DropdownMenuItem;\nconst CheckboxItem = DropdownMenuCheckboxItem;\nconst RadioGroup = DropdownMenuRadioGroup;\nconst RadioItem = DropdownMenuRadioItem;\nconst ItemIndicator = DropdownMenuItemIndicator;\nconst Separator = DropdownMenuSeparator;\nconst Arrow = DropdownMenuArrow;\nconst Sub = DropdownMenuSub;\nconst SubTrigger = DropdownMenuSubTrigger;\nconst SubContent = DropdownMenuSubContent;\n\nexport {\n  createDropdownMenuScope,\n  //\n  DropdownMenu,\n  DropdownMenuTrigger,\n  DropdownMenuPortal,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuItemIndicator,\n  DropdownMenuSeparator,\n  DropdownMenuArrow,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n  //\n  Root,\n  Trigger,\n  Portal,\n  Content,\n  Group,\n  Label,\n  Item,\n  CheckboxItem,\n  RadioGroup,\n  RadioItem,\n  ItemIndicator,\n  Separator,\n  Arrow,\n  Sub,\n  SubTrigger,\n  SubContent,\n};\nexport type {\n  DropdownMenuProps,\n  DropdownMenuTriggerProps,\n  DropdownMenuPortalProps,\n  DropdownMenuContentProps,\n  DropdownMenuGroupProps,\n  DropdownMenuLabelProps,\n  DropdownMenuItemProps,\n  DropdownMenuCheckboxItemProps,\n  DropdownMenuRadioGroupProps,\n  DropdownMenuRadioItemProps,\n  DropdownMenuItemIndicatorProps,\n  DropdownMenuSeparatorProps,\n  DropdownMenuArrowProps,\n  DropdownMenuSubProps,\n  DropdownMenuSubTriggerProps,\n  DropdownMenuSubContentProps,\n};\n"], "names": ["Root", "Portal", "Content", "Group", "Label", "<PERSON><PERSON>", "CheckboxItem", "RadioGroup", "RadioItem", "ItemIndicator", "Separator", "Arrow", "Sub", "SubTrigger", "SubContent"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,YAAY,WAAW;AA+EjB;AA5EN,SAAS,0BAA0B;AAInC,SAAS,uBAAuB;AAHhC,SAAS,4BAA4B;AAIrC,SAAS,aAAa;AAHtB,SAAS,iBAAiB;AAH1B,SAAS,mBAAmB;AAD5B,SAAS,4BAA4B;;;;;;;;;;;;AAiBrC,IAAM,qBAAqB;AAG3B,IAAM,CAAC,2BAA2B,uBAAuB,CAAA,2KAAI,qBAAA,EAC3D,oBACA;qKAAC,kBAAe;CAAA;AAElB,IAAM,oLAAe,kBAAA,CAAgB;AAYrC,IAAM,CAAC,sBAAsB,sBAAsB,CAAA,GACjD,0BAAoD,kBAAkB;AAWxE,IAAM,eAA4C,CAAC,UAA0C;IAC3F,MAAM,EACJ,mBAAA,EACA,QAAA,EACA,GAAA,EACA,MAAM,QAAA,EACN,WAAA,EACA,YAAA,EACA,QAAQ,IAAA,EACV,GAAI;IACJ,MAAM,YAAY,aAAa,mBAAmB;IAClD,MAAM,aAAmB,sMAAA,MAAA,CAA0B,IAAI;IACvD,MAAM,CAAC,MAAM,OAAO,CAAA,gMAAI,uBAAA,EAAqB;QAC3C,MAAM;QACN,aAAa,eAAe;QAC5B,UAAU;QACV,QAAQ;IACV,CAAC;IAED,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,sBAAA;QACC,OAAO;QACP,8KAAW,QAAA,CAAM;QACjB;QACA,6KAAW,SAAA,CAAM;QACjB;QACA,cAAc;QACd,cAAoB,sMAAA,WAAA,CAAY,IAAM,QAAQ,CAAC,WAAa,CAAC,QAAQ,GAAG;YAAC,OAAO;SAAC;QACjF;QAEA,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAe,iKAAA,IAAA,EAAd;YAAoB,GAAG,SAAA;YAAW;YAAY,cAAc;YAAS;YAAU;YAC7E;QAAA,CACH;IAAA;AAGN;AAEA,aAAa,WAAA,GAAc;AAM3B,IAAM,eAAe;AAMrB,IAAM,sBAA4B,sMAAA,UAAA,CAChC,CAAC,OAA8C,iBAAiB;IAC9D,MAAM,EAAE,mBAAA,EAAqB,WAAW,KAAA,EAAO,GAAG,aAAa,CAAA,GAAI;IACnE,MAAM,UAAU,uBAAuB,cAAc,mBAAmB;IACxE,MAAM,YAAY,aAAa,mBAAmB;IAClD,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAe,iKAAA,MAAA,EAAd;QAAqB,SAAO;QAAE,GAAG,SAAA;QAChC,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,wKAAC,YAAA,CAAU,MAAA,EAAV;YACC,MAAK;YACL,IAAI,QAAQ,SAAA;YACZ,iBAAc;YACd,iBAAe,QAAQ,IAAA;YACvB,iBAAe,QAAQ,IAAA,GAAO,QAAQ,SAAA,GAAY,KAAA;YAClD,cAAY,QAAQ,IAAA,GAAO,SAAS;YACpC,iBAAe,WAAW,KAAK,KAAA;YAC/B;YACC,GAAG,YAAA;YACJ,oLAAK,eAAA,EAAY,cAAc,QAAQ,UAAU;YACjD,gLAAe,uBAAA,EAAqB,MAAM,aAAA,EAAe,CAAC,UAAU;gBAGlE,IAAI,CAAC,YAAY,MAAM,MAAA,KAAW,KAAK,MAAM,OAAA,KAAY,OAAO;oBAC9D,QAAQ,YAAA,CAAa;oBAGrB,IAAI,CAAC,QAAQ,IAAA,CAAM,CAAA,MAAM,cAAA,CAAe;gBAC1C;YACF,CAAC;YACD,WAAW,wLAAA,EAAqB,MAAM,SAAA,EAAW,CAAC,UAAU;gBAC1D,IAAI,SAAU,CAAA;gBACd,IAAI;oBAAC;oBAAS,GAAG;iBAAA,CAAE,QAAA,CAAS,MAAM,GAAG,EAAG,CAAA,QAAQ,YAAA,CAAa;gBAC7D,IAAI,MAAM,GAAA,KAAQ,YAAa,CAAA,QAAQ,YAAA,CAAa,IAAI;gBAGxD,IAAI;oBAAC;oBAAS;oBAAK,WAAW;iBAAA,CAAE,QAAA,CAAS,MAAM,GAAG,EAAG,CAAA,MAAM,cAAA,CAAe;YAC5E,CAAC;QAAA;IACH,CACF;AAEJ;AAGF,oBAAoB,WAAA,GAAc;AAMlC,IAAM,cAAc;AAKpB,IAAM,qBAAwD,CAC5D,UACG;IACH,MAAM,EAAE,mBAAA,EAAqB,GAAG,YAAY,CAAA,GAAI;IAChD,MAAM,YAAY,aAAa,mBAAmB;IAClD,OAAO,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAe,iKAAA,MAAA,EAAd;QAAsB,GAAG,SAAA;QAAY,GAAG,WAAA;IAAA,CAAa;AAC/D;AAEA,mBAAmB,WAAA,GAAc;AAMjC,IAAM,eAAe;AAMrB,IAAM,sBAA4B,sMAAA,UAAA,CAChC,CAAC,OAA8C,iBAAiB;IAC9D,MAAM,EAAE,mBAAA,EAAqB,GAAG,aAAa,CAAA,GAAI;IACjD,MAAM,UAAU,uBAAuB,cAAc,mBAAmB;IACxE,MAAM,YAAY,aAAa,mBAAmB;IAClD,MAAM,0BAAgC,sMAAA,MAAA,CAAO,KAAK;IAElD,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAe,iKAAA,OAAA,EAAd;QACC,IAAI,QAAQ,SAAA;QACZ,mBAAiB,QAAQ,SAAA;QACxB,GAAG,SAAA;QACH,GAAG,YAAA;QACJ,KAAK;QACL,mLAAkB,uBAAA,EAAqB,MAAM,gBAAA,EAAkB,CAAC,UAAU;YACxE,IAAI,CAAC,wBAAwB,OAAA,CAAS,CAAA,QAAQ,UAAA,CAAW,OAAA,EAAS,MAAM;YACxE,wBAAwB,OAAA,GAAU;YAElC,MAAM,cAAA,CAAe;QACvB,CAAC;QACD,mBAAmB,wLAAA,EAAqB,MAAM,iBAAA,EAAmB,CAAC,UAAU;YAC1E,MAAM,gBAAgB,MAAM,MAAA,CAAO,aAAA;YACnC,MAAM,gBAAgB,cAAc,MAAA,KAAW,KAAK,cAAc,OAAA,KAAY;YAC9E,MAAM,eAAe,cAAc,MAAA,KAAW,KAAK;YACnD,IAAI,CAAC,QAAQ,KAAA,IAAS,aAAc,CAAA,wBAAwB,OAAA,GAAU;QACxE,CAAC;QACD,OAAO;YACL,GAAG,MAAM,KAAA;YAAA,iDAAA;YAET,GAAG;gBACD,kDACE;gBACF,iDAAiD;gBACjD,kDACE;gBACF,uCAAuC;gBACvC,wCAAwC;YAC1C,CAAA;QACF;IAAA;AAGN;AAGF,oBAAoB,WAAA,GAAc;AAMlC,IAAM,aAAa;AAMnB,IAAM,oBAA0B,sMAAA,UAAA,CAC9B,CAAC,OAA4C,iBAAiB;IAC5D,MAAM,EAAE,mBAAA,EAAqB,GAAG,WAAW,CAAA,GAAI;IAC/C,MAAM,YAAY,aAAa,mBAAmB;IAClD,OAAO,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAe,iKAAA,KAAA,EAAd;QAAqB,GAAG,SAAA;QAAY,GAAG,UAAA;QAAY,KAAK;IAAA,CAAc;AAChF;AAGF,kBAAkB,WAAA,GAAc;AAMhC,IAAM,aAAa;AAMnB,IAAM,oBAA0B,sMAAA,UAAA,CAC9B,CAAC,OAA4C,iBAAiB;IAC5D,MAAM,EAAE,mBAAA,EAAqB,GAAG,WAAW,CAAA,GAAI;IAC/C,MAAM,YAAY,aAAa,mBAAmB;IAClD,OAAO,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAe,iKAAA,KAAA,EAAd;QAAqB,GAAG,SAAA;QAAY,GAAG,UAAA;QAAY,KAAK;IAAA,CAAc;AAChF;AAGF,kBAAkB,WAAA,GAAc;AAMhC,IAAM,YAAY;AAMlB,IAAM,mBAAyB,sMAAA,UAAA,CAC7B,CAAC,OAA2C,iBAAiB;IAC3D,MAAM,EAAE,mBAAA,EAAqB,GAAG,UAAU,CAAA,GAAI;IAC9C,MAAM,YAAY,aAAa,mBAAmB;IAClD,OAAO,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAe,iKAAA,IAAA,EAAd;QAAoB,GAAG,SAAA;QAAY,GAAG,SAAA;QAAW,KAAK;IAAA,CAAc;AAC9E;AAGF,iBAAiB,WAAA,GAAc;AAM/B,IAAM,qBAAqB;AAM3B,IAAM,2BAAiC,sMAAA,UAAA,CAGrC,CAAC,OAAmD,iBAAiB;IACrE,MAAM,EAAE,mBAAA,EAAqB,GAAG,kBAAkB,CAAA,GAAI;IACtD,MAAM,YAAY,aAAa,mBAAmB;IAClD,OAAO,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAe,iKAAA,YAAA,EAAd;QAA4B,GAAG,SAAA;QAAY,GAAG,iBAAA;QAAmB,KAAK;IAAA,CAAc;AAC9F,CAAC;AAED,yBAAyB,WAAA,GAAc;AAMvC,IAAM,mBAAmB;AAMzB,IAAM,yBAA+B,sMAAA,UAAA,CAGnC,CAAC,OAAiD,iBAAiB;IACnE,MAAM,EAAE,mBAAA,EAAqB,GAAG,gBAAgB,CAAA,GAAI;IACpD,MAAM,YAAY,aAAa,mBAAmB;IAClD,OAAO,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAe,iKAAA,UAAA,EAAd;QAA0B,GAAG,SAAA;QAAY,GAAG,eAAA;QAAiB,KAAK;IAAA,CAAc;AAC1F,CAAC;AAED,uBAAuB,WAAA,GAAc;AAMrC,IAAM,kBAAkB;AAMxB,IAAM,wBAA8B,sMAAA,UAAA,CAGlC,CAAC,OAAgD,iBAAiB;IAClE,MAAM,EAAE,mBAAA,EAAqB,GAAG,eAAe,CAAA,GAAI;IACnD,MAAM,YAAY,aAAa,mBAAmB;IAClD,OAAO,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAe,iKAAA,SAAA,EAAd;QAAyB,GAAG,SAAA;QAAY,GAAG,cAAA;QAAgB,KAAK;IAAA,CAAc;AACxF,CAAC;AAED,sBAAsB,WAAA,GAAc;AAMpC,IAAM,iBAAiB;AAMvB,IAAM,4BAAkC,sMAAA,UAAA,CAGtC,CAAC,OAAoD,iBAAiB;IACtE,MAAM,EAAE,mBAAA,EAAqB,GAAG,mBAAmB,CAAA,GAAI;IACvD,MAAM,YAAY,aAAa,mBAAmB;IAClD,OAAO,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAe,iKAAA,aAAA,EAAd;QAA6B,GAAG,SAAA;QAAY,GAAG,kBAAA;QAAoB,KAAK;IAAA,CAAc;AAChG,CAAC;AAED,0BAA0B,WAAA,GAAc;AAMxC,IAAM,iBAAiB;AAMvB,IAAM,wBAA8B,sMAAA,UAAA,CAGlC,CAAC,OAAgD,iBAAiB;IAClE,MAAM,EAAE,mBAAA,EAAqB,GAAG,eAAe,CAAA,GAAI;IACnD,MAAM,YAAY,aAAa,mBAAmB;IAClD,OAAO,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAe,iKAAA,SAAA,EAAd;QAAyB,GAAG,SAAA;QAAY,GAAG,cAAA;QAAgB,KAAK;IAAA,CAAc;AACxF,CAAC;AAED,sBAAsB,WAAA,GAAc;AAMpC,IAAM,aAAa;AAMnB,IAAM,oBAA0B,sMAAA,UAAA,CAC9B,CAAC,OAA4C,iBAAiB;IAC5D,MAAM,EAAE,mBAAA,EAAqB,GAAG,WAAW,CAAA,GAAI;IAC/C,MAAM,YAAY,aAAa,mBAAmB;IAClD,OAAO,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAe,iKAAA,KAAA,EAAd;QAAqB,GAAG,SAAA;QAAY,GAAG,UAAA;QAAY,KAAK;IAAA,CAAc;AAChF;AAGF,kBAAkB,WAAA,GAAc;AAahC,IAAM,kBAAkD,CACtD,UACG;IACH,MAAM,EAAE,mBAAA,EAAqB,QAAA,EAAU,MAAM,QAAA,EAAU,YAAA,EAAc,WAAA,CAAY,CAAA,GAAI;IACrF,MAAM,YAAY,aAAa,mBAAmB;IAClD,MAAM,CAAC,MAAM,OAAO,CAAA,gMAAI,uBAAA,EAAqB;QAC3C,MAAM;QACN,aAAa,eAAe;QAC5B,UAAU;QACV,QAAQ;IACV,CAAC;IAED,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAe,iKAAA,GAAA,EAAd;QAAmB,GAAG,SAAA;QAAW;QAAY,cAAc;QACzD;IAAA,CACH;AAEJ;AAMA,IAAM,mBAAmB;AAMzB,IAAM,yBAA+B,sMAAA,UAAA,CAGnC,CAAC,OAAiD,iBAAiB;IACnE,MAAM,EAAE,mBAAA,EAAqB,GAAG,gBAAgB,CAAA,GAAI;IACpD,MAAM,YAAY,aAAa,mBAAmB;IAClD,OAAO,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAe,iKAAA,UAAA,EAAd;QAA0B,GAAG,SAAA;QAAY,GAAG,eAAA;QAAiB,KAAK;IAAA,CAAc;AAC1F,CAAC;AAED,uBAAuB,WAAA,GAAc;AAMrC,IAAM,mBAAmB;AAMzB,IAAM,yBAA+B,sMAAA,UAAA,CAGnC,CAAC,OAAiD,iBAAiB;IACnE,MAAM,EAAE,mBAAA,EAAqB,GAAG,gBAAgB,CAAA,GAAI;IACpD,MAAM,YAAY,aAAa,mBAAmB;IAElD,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAe,iKAAA,UAAA,EAAd;QACE,GAAG,SAAA;QACH,GAAG,eAAA;QACJ,KAAK;QACL,OAAO;YACL,GAAG,MAAM,KAAA;YAAA,iDAAA;YAET,GAAG;gBACD,kDAAkD;gBAClD,iDAAiD;gBACjD,kDAAkD;gBAClD,uCAAuC;gBACvC,wCAAwC;YAC1C,CAAA;QACF;IAAA;AAGN,CAAC;AAED,uBAAuB,WAAA,GAAc;AAIrC,IAAMA,QAAO;AACb,IAAM,UAAU;AAChB,IAAMC,UAAS;AACf,IAAMC,WAAU;AAChB,IAAMC,SAAQ;AACd,IAAMC,SAAQ;AACd,IAAMC,QAAO;AACb,IAAMC,gBAAe;AACrB,IAAMC,cAAa;AACnB,IAAMC,aAAY;AAClB,IAAMC,iBAAgB;AACtB,IAAMC,aAAY;AAClB,IAAMC,SAAQ;AACd,IAAMC,OAAM;AACZ,IAAMC,cAAa;AACnB,IAAMC,cAAa", "ignoreList": [0]}}, {"offset": {"line": 5983, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5998, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/use-sync-external-store/cjs/use-sync-external-store-shim.development.js"], "sourcesContent": ["/**\n * @license React\n * use-sync-external-store-shim.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function is(x, y) {\n      return (x === y && (0 !== x || 1 / x === 1 / y)) || (x !== x && y !== y);\n    }\n    function useSyncExternalStore$2(subscribe, getSnapshot) {\n      didWarnOld18Alpha ||\n        void 0 === React.startTransition ||\n        ((didWarnOld18Alpha = !0),\n        console.error(\n          \"You are using an outdated, pre-release alpha of React 18 that does not support useSyncExternalStore. The use-sync-external-store shim will not work correctly. Upgrade to a newer pre-release.\"\n        ));\n      var value = getSnapshot();\n      if (!didWarnUncachedGetSnapshot) {\n        var cachedValue = getSnapshot();\n        objectIs(value, cachedValue) ||\n          (console.error(\n            \"The result of getSnapshot should be cached to avoid an infinite loop\"\n          ),\n          (didWarnUncachedGetSnapshot = !0));\n      }\n      cachedValue = useState({\n        inst: { value: value, getSnapshot: getSnapshot }\n      });\n      var inst = cachedValue[0].inst,\n        forceUpdate = cachedValue[1];\n      useLayoutEffect(\n        function () {\n          inst.value = value;\n          inst.getSnapshot = getSnapshot;\n          checkIfSnapshotChanged(inst) && forceUpdate({ inst: inst });\n        },\n        [subscribe, value, getSnapshot]\n      );\n      useEffect(\n        function () {\n          checkIfSnapshotChanged(inst) && forceUpdate({ inst: inst });\n          return subscribe(function () {\n            checkIfSnapshotChanged(inst) && forceUpdate({ inst: inst });\n          });\n        },\n        [subscribe]\n      );\n      useDebugValue(value);\n      return value;\n    }\n    function checkIfSnapshotChanged(inst) {\n      var latestGetSnapshot = inst.getSnapshot;\n      inst = inst.value;\n      try {\n        var nextValue = latestGetSnapshot();\n        return !objectIs(inst, nextValue);\n      } catch (error) {\n        return !0;\n      }\n    }\n    function useSyncExternalStore$1(subscribe, getSnapshot) {\n      return getSnapshot();\n    }\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(Error());\n    var React = require(\"react\"),\n      objectIs = \"function\" === typeof Object.is ? Object.is : is,\n      useState = React.useState,\n      useEffect = React.useEffect,\n      useLayoutEffect = React.useLayoutEffect,\n      useDebugValue = React.useDebugValue,\n      didWarnOld18Alpha = !1,\n      didWarnUncachedGetSnapshot = !1,\n      shim =\n        \"undefined\" === typeof window ||\n        \"undefined\" === typeof window.document ||\n        \"undefined\" === typeof window.document.createElement\n          ? useSyncExternalStore$1\n          : useSyncExternalStore$2;\n    exports.useSyncExternalStore =\n      void 0 !== React.useSyncExternalStore ? React.useSyncExternalStore : shim;\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(Error());\n  })();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAED;AACA,oEACE,AAAC;IACC,SAAS,GAAG,CAAC,EAAE,CAAC;QACd,OAAO,AAAC,MAAM,KAAK,CAAC,MAAM,KAAK,IAAI,MAAM,IAAI,CAAC,KAAO,MAAM,KAAK,MAAM;IACxE;IACA,SAAS,uBAAuB,SAAS,EAAE,WAAW;QACpD,qBACE,KAAK,MAAM,MAAM,eAAe,IAChC,CAAC,AAAC,oBAAoB,CAAC,GACvB,QAAQ,KAAK,CACX,iMACD;QACH,IAAI,QAAQ;QACZ,IAAI,CAAC,4BAA4B;YAC/B,IAAI,cAAc;YAClB,SAAS,OAAO,gBACd,CAAC,QAAQ,KAAK,CACZ,yEAED,6BAA6B,CAAC,CAAE;QACrC;QACA,cAAc,SAAS;YACrB,MAAM;gBAAE,OAAO;gBAAO,aAAa;YAAY;QACjD;QACA,IAAI,OAAO,WAAW,CAAC,EAAE,CAAC,IAAI,EAC5B,cAAc,WAAW,CAAC,EAAE;QAC9B,gBACE;YACE,KAAK,KAAK,GAAG;YACb,KAAK,WAAW,GAAG;YACnB,uBAAuB,SAAS,YAAY;gBAAE,MAAM;YAAK;QAC3D,GACA;YAAC;YAAW;YAAO;SAAY;QAEjC,UACE;YACE,uBAAuB,SAAS,YAAY;gBAAE,MAAM;YAAK;YACzD,OAAO,UAAU;gBACf,uBAAuB,SAAS,YAAY;oBAAE,MAAM;gBAAK;YAC3D;QACF,GACA;YAAC;SAAU;QAEb,cAAc;QACd,OAAO;IACT;IACA,SAAS,uBAAuB,IAAI;QAClC,IAAI,oBAAoB,KAAK,WAAW;QACxC,OAAO,KAAK,KAAK;QACjB,IAAI;YACF,IAAI,YAAY;YAChB,OAAO,CAAC,SAAS,MAAM;QACzB,EAAE,OAAO,OAAO;YACd,OAAO,CAAC;QACV;IACF;IACA,SAAS,uBAAuB,SAAS,EAAE,WAAW;QACpD,OAAO;IACT;IACA,gBAAgB,OAAO,kCACrB,eACE,OAAO,+BAA+B,2BAA2B,IACnE,+BAA+B,2BAA2B,CAAC;IAC7D,IAAI,8IACF,WAAW,eAAe,OAAO,OAAO,EAAE,GAAG,OAAO,EAAE,GAAG,IACzD,WAAW,MAAM,QAAQ,EACzB,YAAY,MAAM,SAAS,EAC3B,kBAAkB,MAAM,eAAe,EACvC,gBAAgB,MAAM,aAAa,EACnC,oBAAoB,CAAC,GACrB,6BAA6B,CAAC,GAC9B,OACE,gBAAgB,OAAO,UACvB,gBAAgB,OAAO,OAAO,QAAQ,IACtC,gBAAgB,OAAO,OAAO,QAAQ,CAAC,aAAa,GAChD,yBACA;IACR,QAAQ,oBAAoB,GAC1B,KAAK,MAAM,MAAM,oBAAoB,GAAG,MAAM,oBAAoB,GAAG;IACvE,gBAAgB,OAAO,kCACrB,eACE,OAAO,+BAA+B,0BAA0B,IAClE,+BAA+B,0BAA0B,CAAC;AAC9D", "ignoreList": [0]}}, {"offset": {"line": 6069, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 6074, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/use-sync-external-store/shim/index.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('../cjs/use-sync-external-store-shim.production.js');\n} else {\n  module.exports = require('../cjs/use-sync-external-store-shim.development.js');\n}\n"], "names": [], "mappings": "AAAA;AAEA,uCAA2C;;AAE3C,OAAO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0]}}, {"offset": {"line": 6080, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 6086, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/%40radix-ui/react-use-is-hydrated/src/use-is-hydrated.tsx"], "sourcesContent": ["import { useSyncExternalStore } from 'use-sync-external-store/shim';\n\n/**\n * Determines whether or not the component tree has been hydrated.\n */\nexport function useIsHydrated() {\n  return useSyncExternalStore(\n    subscribe,\n    () => true,\n    () => false\n  );\n}\n\nfunction subscribe() {\n  return () => {};\n}\n"], "names": [], "mappings": ";;;;AAAA,SAAS,4BAA4B;;AAK9B,SAAS,gBAAgB;IAC9B,6KAAO,uBAAA,EACL,WACA,IAAM,MACN,IAAM;AAEV;AAEA,SAAS,YAAY;IACnB,OAAO,KAAO,CAAD;AACf", "ignoreList": [0]}}, {"offset": {"line": 6100, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 6106, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/node_modules/%40radix-ui/react-avatar/src/avatar.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { useIsHydrated } from '@radix-ui/react-use-is-hydrated';\n\nimport type { Scope } from '@radix-ui/react-context';\n\n/* -------------------------------------------------------------------------------------------------\n * Avatar\n * -----------------------------------------------------------------------------------------------*/\n\nconst AVATAR_NAME = 'Avatar';\n\ntype ScopedProps<P> = P & { __scopeAvatar?: Scope };\nconst [createAvatarContext, createAvatarScope] = createContextScope(AVATAR_NAME);\n\ntype ImageLoadingStatus = 'idle' | 'loading' | 'loaded' | 'error';\n\ntype AvatarContextValue = {\n  imageLoadingStatus: ImageLoadingStatus;\n  onImageLoadingStatusChange(status: ImageLoadingStatus): void;\n};\n\nconst [AvatarProvider, useAvatarContext] = createAvatarContext<AvatarContextValue>(AVATAR_NAME);\n\ntype AvatarElement = React.ComponentRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = React.ComponentPropsWithoutRef<typeof Primitive.span>;\ninterface AvatarProps extends PrimitiveSpanProps {}\n\nconst Avatar = React.forwardRef<AvatarElement, AvatarProps>(\n  (props: ScopedProps<AvatarProps>, forwardedRef) => {\n    const { __scopeAvatar, ...avatarProps } = props;\n    const [imageLoadingStatus, setImageLoadingStatus] = React.useState<ImageLoadingStatus>('idle');\n    return (\n      <AvatarProvider\n        scope={__scopeAvatar}\n        imageLoadingStatus={imageLoadingStatus}\n        onImageLoadingStatusChange={setImageLoadingStatus}\n      >\n        <Primitive.span {...avatarProps} ref={forwardedRef} />\n      </AvatarProvider>\n    );\n  }\n);\n\nAvatar.displayName = AVATAR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * AvatarImage\n * -----------------------------------------------------------------------------------------------*/\n\nconst IMAGE_NAME = 'AvatarImage';\n\ntype AvatarImageElement = React.ComponentRef<typeof Primitive.img>;\ntype PrimitiveImageProps = React.ComponentPropsWithoutRef<typeof Primitive.img>;\ninterface AvatarImageProps extends PrimitiveImageProps {\n  onLoadingStatusChange?: (status: ImageLoadingStatus) => void;\n}\n\nconst AvatarImage = React.forwardRef<AvatarImageElement, AvatarImageProps>(\n  (props: ScopedProps<AvatarImageProps>, forwardedRef) => {\n    const { __scopeAvatar, src, onLoadingStatusChange = () => {}, ...imageProps } = props;\n    const context = useAvatarContext(IMAGE_NAME, __scopeAvatar);\n    const imageLoadingStatus = useImageLoadingStatus(src, imageProps);\n    const handleLoadingStatusChange = useCallbackRef((status: ImageLoadingStatus) => {\n      onLoadingStatusChange(status);\n      context.onImageLoadingStatusChange(status);\n    });\n\n    useLayoutEffect(() => {\n      if (imageLoadingStatus !== 'idle') {\n        handleLoadingStatusChange(imageLoadingStatus);\n      }\n    }, [imageLoadingStatus, handleLoadingStatusChange]);\n\n    return imageLoadingStatus === 'loaded' ? (\n      <Primitive.img {...imageProps} ref={forwardedRef} src={src} />\n    ) : null;\n  }\n);\n\nAvatarImage.displayName = IMAGE_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * AvatarFallback\n * -----------------------------------------------------------------------------------------------*/\n\nconst FALLBACK_NAME = 'AvatarFallback';\n\ntype AvatarFallbackElement = React.ComponentRef<typeof Primitive.span>;\ninterface AvatarFallbackProps extends PrimitiveSpanProps {\n  delayMs?: number;\n}\n\nconst AvatarFallback = React.forwardRef<AvatarFallbackElement, AvatarFallbackProps>(\n  (props: ScopedProps<AvatarFallbackProps>, forwardedRef) => {\n    const { __scopeAvatar, delayMs, ...fallbackProps } = props;\n    const context = useAvatarContext(FALLBACK_NAME, __scopeAvatar);\n    const [canRender, setCanRender] = React.useState(delayMs === undefined);\n\n    React.useEffect(() => {\n      if (delayMs !== undefined) {\n        const timerId = window.setTimeout(() => setCanRender(true), delayMs);\n        return () => window.clearTimeout(timerId);\n      }\n    }, [delayMs]);\n\n    return canRender && context.imageLoadingStatus !== 'loaded' ? (\n      <Primitive.span {...fallbackProps} ref={forwardedRef} />\n    ) : null;\n  }\n);\n\nAvatarFallback.displayName = FALLBACK_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction resolveLoadingStatus(image: HTMLImageElement | null, src?: string): ImageLoadingStatus {\n  if (!image) {\n    return 'idle';\n  }\n  if (!src) {\n    return 'error';\n  }\n  if (image.src !== src) {\n    image.src = src;\n  }\n  return image.complete && image.naturalWidth > 0 ? 'loaded' : 'loading';\n}\n\nfunction useImageLoadingStatus(\n  src: string | undefined,\n  { referrerPolicy, crossOrigin }: AvatarImageProps\n) {\n  const isHydrated = useIsHydrated();\n  const imageRef = React.useRef<HTMLImageElement | null>(null);\n  const image = (() => {\n    if (!isHydrated) return null;\n    if (!imageRef.current) {\n      imageRef.current = new window.Image();\n    }\n    return imageRef.current;\n  })();\n\n  const [loadingStatus, setLoadingStatus] = React.useState<ImageLoadingStatus>(() =>\n    resolveLoadingStatus(image, src)\n  );\n\n  useLayoutEffect(() => {\n    setLoadingStatus(resolveLoadingStatus(image, src));\n  }, [image, src]);\n\n  useLayoutEffect(() => {\n    const updateStatus = (status: ImageLoadingStatus) => () => {\n      setLoadingStatus(status);\n    };\n\n    if (!image) return;\n\n    const handleLoad = updateStatus('loaded');\n    const handleError = updateStatus('error');\n    image.addEventListener('load', handleLoad);\n    image.addEventListener('error', handleError);\n    if (referrerPolicy) {\n      image.referrerPolicy = referrerPolicy;\n    }\n    if (typeof crossOrigin === 'string') {\n      image.crossOrigin = crossOrigin;\n    }\n\n    return () => {\n      image.removeEventListener('load', handleLoad);\n      image.removeEventListener('error', handleError);\n    };\n  }, [image, crossOrigin, referrerPolicy]);\n\n  return loadingStatus;\n}\n\nconst Root = Avatar;\nconst Image = AvatarImage;\nconst Fallback = AvatarFallback;\n\nexport {\n  createAvatarScope,\n  //\n  Avatar,\n  AvatarImage,\n  AvatarFallback,\n  //\n  Root,\n  Image,\n  Fallback,\n};\nexport type { AvatarProps, AvatarImageProps, AvatarFallbackProps };\n"], "names": [], "mappings": ";;;;;;;;;;AAAA,YAAY,WAAW;AAyCf;AAxCR,SAAS,0BAA0B;AAGnC,SAAS,iBAAiB;AAF1B,SAAS,sBAAsB;AAC/B,SAAS,uBAAuB;AAEhC,SAAS,qBAAqB;;;;;;;;;AAQ9B,IAAM,cAAc;AAGpB,IAAM,CAAC,qBAAqB,iBAAiB,CAAA,2KAAI,qBAAA,EAAmB,WAAW;AAS/E,IAAM,CAAC,gBAAgB,gBAAgB,CAAA,GAAI,oBAAwC,WAAW;AAM9F,IAAM,SAAe,sMAAA,UAAA,CACnB,CAAC,OAAiC,iBAAiB;IACjD,MAAM,EAAE,aAAA,EAAe,GAAG,YAAY,CAAA,GAAI;IAC1C,MAAM,CAAC,oBAAoB,qBAAqB,CAAA,GAAU,sMAAA,QAAA,CAA6B,MAAM;IAC7F,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,gBAAA;QACC,OAAO;QACP;QACA,4BAA4B;QAE5B,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,wKAAC,YAAA,CAAU,IAAA,EAAV;YAAgB,GAAG,WAAA;YAAa,KAAK;QAAA,CAAc;IAAA;AAG1D;AAGF,OAAO,WAAA,GAAc;AAMrB,IAAM,aAAa;AAQnB,IAAM,cAAoB,sMAAA,UAAA,CACxB,CAAC,OAAsC,iBAAiB;IACtD,MAAM,EAAE,aAAA,EAAe,GAAA,EAAK,wBAAwB,KAAO,CAAD,AAAC,EAAG,GAAG,WAAW,CAAA,GAAI;IAChF,MAAM,UAAU,iBAAiB,YAAY,aAAa;IAC1D,MAAM,qBAAqB,sBAAsB,KAAK,UAAU;IAChE,MAAM,mNAA4B,iBAAA,EAAe,CAAC,WAA+B;QAC/E,sBAAsB,MAAM;QAC5B,QAAQ,0BAAA,CAA2B,MAAM;IAC3C,CAAC;IAED,CAAA,GAAA,mLAAA,CAAA,kBAAA,EAAgB,MAAM;QACpB,IAAI,uBAAuB,QAAQ;YACjC,0BAA0B,kBAAkB;QAC9C;IACF,GAAG;QAAC;QAAoB,yBAAyB;KAAC;IAElD,OAAO,uBAAuB,WAC5B,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,wKAAC,YAAA,CAAU,GAAA,EAAV;QAAe,GAAG,UAAA;QAAY,KAAK;QAAc;IAAA,CAAU,IAC1D;AACN;AAGF,YAAY,WAAA,GAAc;AAM1B,IAAM,gBAAgB;AAOtB,IAAM,iBAAuB,sMAAA,UAAA,CAC3B,CAAC,OAAyC,iBAAiB;IACzD,MAAM,EAAE,aAAA,EAAe,OAAA,EAAS,GAAG,cAAc,CAAA,GAAI;IACrD,MAAM,UAAU,iBAAiB,eAAe,aAAa;IAC7D,MAAM,CAAC,WAAW,YAAY,CAAA,GAAU,sMAAA,QAAA,CAAS,YAAY,KAAA,CAAS;IAEhE,sMAAA,SAAA,CAAU,MAAM;QACpB,IAAI,YAAY,KAAA,GAAW;YACzB,MAAM,UAAU,OAAO,UAAA,CAAW,IAAM,aAAa,IAAI,GAAG,OAAO;YACnE,OAAO,IAAM,OAAO,YAAA,CAAa,OAAO;QAC1C;IACF,GAAG;QAAC,OAAO;KAAC;IAEZ,OAAO,aAAa,QAAQ,kBAAA,KAAuB,WACjD,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,wKAAC,YAAA,CAAU,IAAA,EAAV;QAAgB,GAAG,aAAA;QAAe,KAAK;IAAA,CAAc,IACpD;AACN;AAGF,eAAe,WAAA,GAAc;AAI7B,SAAS,qBAAqB,KAAA,EAAgC,GAAA,EAAkC;IAC9F,IAAI,CAAC,OAAO;QACV,OAAO;IACT;IACA,IAAI,CAAC,KAAK;QACR,OAAO;IACT;IACA,IAAI,MAAM,GAAA,KAAQ,KAAK;QACrB,MAAM,GAAA,GAAM;IACd;IACA,OAAO,MAAM,QAAA,IAAY,MAAM,YAAA,GAAe,IAAI,WAAW;AAC/D;AAEA,SAAS,sBACP,GAAA,EACA,EAAE,cAAA,EAAgB,WAAA,CAAY,CAAA,EAC9B;IACA,MAAM,aAAa,sMAAA,CAAc;IACjC,MAAM,WAAiB,sMAAA,MAAA,CAAgC,IAAI;IAC3D,MAAM,QAAA,CAAS,MAAM;QACnB,IAAI,CAAC,WAAY,CAAA,OAAO;QACxB,IAAI,CAAC,SAAS,OAAA,EAAS;YACrB,SAAS,OAAA,GAAU,IAAI,OAAO,KAAA,CAAM;QACtC;QACA,OAAO,SAAS,OAAA;IAClB,CAAA,EAAG;IAEH,MAAM,CAAC,eAAe,gBAAgB,CAAA,GAAU,sMAAA,QAAA,CAA6B,IAC3E,qBAAqB,OAAO,GAAG;IAGjC,CAAA,GAAA,mLAAA,CAAA,kBAAA,EAAgB,MAAM;QACpB,iBAAiB,qBAAqB,OAAO,GAAG,CAAC;IACnD,GAAG;QAAC;QAAO,GAAG;KAAC;IAEf,CAAA,GAAA,mLAAA,CAAA,kBAAA,EAAgB,MAAM;QACpB,MAAM,eAAe,CAAC,SAA+B,MAAM;gBACzD,iBAAiB,MAAM;YACzB;QAEA,IAAI,CAAC,MAAO,CAAA;QAEZ,MAAM,aAAa,aAAa,QAAQ;QACxC,MAAM,cAAc,aAAa,OAAO;QACxC,MAAM,gBAAA,CAAiB,QAAQ,UAAU;QACzC,MAAM,gBAAA,CAAiB,SAAS,WAAW;QAC3C,IAAI,gBAAgB;YAClB,MAAM,cAAA,GAAiB;QACzB;QACA,IAAI,OAAO,gBAAgB,UAAU;YACnC,MAAM,WAAA,GAAc;QACtB;QAEA,OAAO,MAAM;YACX,MAAM,mBAAA,CAAoB,QAAQ,UAAU;YAC5C,MAAM,mBAAA,CAAoB,SAAS,WAAW;QAChD;IACF,GAAG;QAAC;QAAO;QAAa,cAAc;KAAC;IAEvC,OAAO;AACT;AAEA,IAAM,OAAO;AACb,IAAM,QAAQ;AACd,IAAM,WAAW", "ignoreList": [0]}}, {"offset": {"line": 6251, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}
{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/Work/Repositories/templgen/src/components/common/CookiesToast.tsx"], "sourcesContent": ["'use client';\n\nimport { AnimatePresence, motion } from 'framer-motion';\nimport { useCallback, useEffect, useState } from 'react';\n\ntype Props = {\n  defaultOpen?: boolean;      // default: true\n  autoHideMs?: number;        // optional: e.g., 6000 to auto-close after 6s\n};\n\nexport default function CookiesToast({ defaultOpen = true, autoHideMs }: Props) {\n  const [open, setOpen] = useState<boolean>(defaultOpen);\n\n  // Optional auto-hide timer\n  useEffect(() => {\n    if (!open || !autoHideMs) return;\n    const t = setTimeout(() => setOpen(false), autoHideMs);\n    return () => clearTimeout(t);\n  }, [open, autoHideMs]);\n\n  const close = useCallback(() => setOpen(false), []);\n\n  // Optional: close with Esc\n  useEffect(() => {\n    if (!open) return;\n    const onKey = (e: KeyboardEvent) => {\n      if (e.key === 'Escape') setOpen(false);\n    };\n    window.addEventListener('keydown', onKey);\n    return () => window.removeEventListener('keydown', onKey);\n  }, [open]);\n\n  return (\n    <AnimatePresence>\n      {open && (\n        <motion.div\n          key=\"cookies-toast\"\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          exit={{ opacity: 0 }}\n          transition={{ type: 'spring', stiffness: 420, damping: 28 }}\n          style={{\n            position: 'fixed',\n            left: 16,\n            bottom: 16,\n            zIndex: 9999,\n            pointerEvents: 'none',\n          }}\n          aria-live=\"polite\"\n          className='max-w-[360px]'\n        >\n          <div\n            className=\"pointer-events-auto flex   rounded-xl shadow-lg flex-row p-4\"\n            role=\"dialog\"\n            aria-label=\"Cookies notice\"\n            style={{\n              // Use tokens from :root\n              background: 'var(--card-background)',\n              color: 'var(--main)',\n              boxShadow: '0 10px 15px -3px rgba(0,0,0,0.3), 0 4px 6px -4px rgba(0,0,0,0.3)',\n              border: '1px solid var(--card-border-color)',\n              opacity: 1,\n              transform: 'none',\n            }}\n          >\n            <p\n              className=\"text-body-sm\"\n              style={{\n                margin: 0,\n                lineHeight: 1.5,\n                color: 'var(--card-paragraph)',\n                flex: 1,\n              }}\n            >\n              We use cookies to personalize content, run ads, and analyze traffic.\n            </p>\n\n            <button\n              onClick={close}\n              className=\"\"\n              style={{\n                background: 'var(--button2)',        // subtle button background\n                color: 'var(--button-text)',\n                border: '1px solid var(--button-border)',\n                borderRadius: 10,\n                padding: '8px 12px',\n                fontSize: 14,\n                fontWeight: 600,\n                cursor: 'pointer',\n                whiteSpace: 'nowrap',\n                transition: 'background 150ms ease, color 150ms ease, border-color 150ms ease',\n              }}\n              onMouseEnter={(e) => {\n                (e.currentTarget as HTMLButtonElement).style.background = 'var(--button)';\n                (e.currentTarget as HTMLButtonElement).style.color = 'var(--button-text)';\n                (e.currentTarget as HTMLButtonElement).style.borderColor = 'var(--button)';\n              }}\n              onMouseLeave={(e) => {\n                (e.currentTarget as HTMLButtonElement).style.background = 'var(--button2)';\n                (e.currentTarget as HTMLButtonElement).style.color = 'var(--button-text)';\n                (e.currentTarget as HTMLButtonElement).style.borderColor = 'var(--button-border)';\n              }}\n            >\n              Okay\n            </button>\n          </div>\n        </motion.div>\n      )}\n    </AnimatePresence>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AADA;AAAA;AAFA;;;;AAUe,SAAS,aAAa,EAAE,cAAc,IAAI,EAAE,UAAU,EAAS;IAC5E,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAE1C,2BAA2B;IAC3B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,QAAQ,CAAC,YAAY;QAC1B,MAAM,IAAI,WAAW,IAAM,QAAQ,QAAQ;QAC3C,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;QAAM;KAAW;IAErB,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,IAAM,QAAQ,QAAQ,EAAE;IAElD,2BAA2B;IAC3B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,MAAM;QACX,MAAM,QAAQ,CAAC;YACb,IAAI,EAAE,GAAG,KAAK,UAAU,QAAQ;QAClC;QACA,OAAO,gBAAgB,CAAC,WAAW;QACnC,OAAO,IAAM,OAAO,mBAAmB,CAAC,WAAW;IACrD,GAAG;QAAC;KAAK;IAET,qBACE,8OAAC,yLAAA,CAAA,kBAAe;kBACb,sBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YAET,SAAS;gBAAE,SAAS;YAAE;YACtB,SAAS;gBAAE,SAAS;YAAE;YACtB,MAAM;gBAAE,SAAS;YAAE;YACnB,YAAY;gBAAE,MAAM;gBAAU,WAAW;gBAAK,SAAS;YAAG;YAC1D,OAAO;gBACL,UAAU;gBACV,MAAM;gBACN,QAAQ;gBACR,QAAQ;gBACR,eAAe;YACjB;YACA,aAAU;YACV,WAAU;sBAEV,cAAA,8OAAC;gBACC,WAAU;gBACV,MAAK;gBACL,cAAW;gBACX,OAAO;oBACL,wBAAwB;oBACxB,YAAY;oBACZ,OAAO;oBACP,WAAW;oBACX,QAAQ;oBACR,SAAS;oBACT,WAAW;gBACb;;kCAEA,8OAAC;wBACC,WAAU;wBACV,OAAO;4BACL,QAAQ;4BACR,YAAY;4BACZ,OAAO;4BACP,MAAM;wBACR;kCACD;;;;;;kCAID,8OAAC;wBACC,SAAS;wBACT,WAAU;wBACV,OAAO;4BACL,YAAY;4BACZ,OAAO;4BACP,QAAQ;4BACR,cAAc;4BACd,SAAS;4BACT,UAAU;4BACV,YAAY;4BACZ,QAAQ;4BACR,YAAY;4BACZ,YAAY;wBACd;wBACA,cAAc,CAAC;4BACZ,EAAE,aAAa,CAAuB,KAAK,CAAC,UAAU,GAAG;4BACzD,EAAE,aAAa,CAAuB,KAAK,CAAC,KAAK,GAAG;4BACpD,EAAE,aAAa,CAAuB,KAAK,CAAC,WAAW,GAAG;wBAC7D;wBACA,cAAc,CAAC;4BACZ,EAAE,aAAa,CAAuB,KAAK,CAAC,UAAU,GAAG;4BACzD,EAAE,aAAa,CAAuB,KAAK,CAAC,KAAK,GAAG;4BACpD,EAAE,aAAa,CAAuB,KAAK,CAAC,WAAW,GAAG;wBAC7D;kCACD;;;;;;;;;;;;WAlEC;;;;;;;;;;AA0Ed"}}, {"offset": {"line": 142, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}
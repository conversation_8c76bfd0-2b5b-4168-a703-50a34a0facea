<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <radialGradient id="star-gradient" cx="50%" cy="50%" r="60%">
      <stop offset="0%" stop-color="white" stop-opacity="1"/>
      <stop offset="100%" stop-color="white" stop-opacity="0"/>
    </radialGradient>
  </defs>
  <g>
    <animateTransform
      attributeName="transform"
      type="scale"
      values="1;1.03;1"
      keyTimes="0;0.5;1"
      dur="2s"
      repeatCount="indefinite"
      calcMode="spline"
      keySplines="0.42 0 0.58 1;0.42 0 0.58 1"
    />
    <animate
      attributeName="opacity"
      values="0.7;1;0.7"
      keyTimes="0;0.5;1"
      dur="2s"
      repeatCount="indefinite"
      calcMode="spline"
      keySplines="0.42 0 0.58 1;0.42 0 0.58 1"
    />
    <path d="M16 4 L18 14 L28 16 L18 18 L16 28 L14 18 L4 16 L14 14 Z" fill="url(#star-gradient)"/>
  </g>
</svg>

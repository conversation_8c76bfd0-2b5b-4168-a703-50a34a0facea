{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/Work/Repositories/templgen/src/components/common/Footer.tsx"], "sourcesContent": ["import React from \"react\";\n\nexport default function Footer() {\n  return (\n    <div>\n      <h1>footer</h1>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEe,SAAS;IACtB,qBACE,6LAAC;kBACC,cAAA,6LAAC;sBAAG;;;;;;;;;;;AAGV;KANwB"}}, {"offset": {"line": 33, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/Work/Repositories/templgen/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\";\nimport { twMerge } from \"tailwind-merge\";\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB"}}, {"offset": {"line": 52, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 58, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/Work/Repositories/templgen/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport { cva, type VariantProps } from \"class-variance-authority\";\nimport { Slot } from \"@radix-ui/react-slot\";\n\nimport { cn } from \"@/lib/utils\";\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-[8px] text-sm font-medium transition-[color,box-shadow] disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 [&_svg]:shrink-0 outline-none focus-visible:border-[var(--highlight)] focus-visible:ring-[var(--highlight)]/50 focus-visible:ring-[3px]\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-[var(--button)] text-[var(--button-text)] shadow-sm hover:bg-[color-mix(in_srgb,var(--button),#00000020)]\",\n        destructive:\n          \"bg-[var(--tertiary)] text-[var(--button-text)] shadow-xs hover:bg-[color-mix(in_srgb,var(--tertiary),#00000020)] focus-visible:ring-[var(--tertiary)]/20\",\n        outline:\n          \"border border-[var(--input-border-color)] bg-[var(--background)] shadow-xs hover:bg-[var(--card-hover)] hover:text-[var(--highlight)]\",\n        secondary:\n          \"bg-[var(--secondary)] text-[var(--button-text)] shadow-xs hover:bg-[color-mix(in_srgb,var(--secondary),#00000020)]\",\n        ghost: \"hover:bg-[var(--card-hover)] hover:text-[var(--highlight)]\",\n        link: \"text-[var(--link-color)] underline-offset-4 hover:text-[var(--link-hover)] hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2\",\n        sm: \"h-8 rounded-[8px] px-3 text-xs\",\n        lg: \"h-10 rounded-[8px] px-8\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n);\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean;\n  }) {\n  const Comp = asChild ? Slot : \"button\";\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  );\n}\n\nexport { Button, buttonVariants };\n"], "names": [], "mappings": ";;;;;AACA;AAGA;AAFA;;;;;AAIA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,6XACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS"}}, {"offset": {"line": 115, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 121, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/Work/Repositories/templgen/src/components/common/navbar/AuthButtons.tsx"], "sourcesContent": ["\"use client\";\nimport Link from \"next/link\";\nimport { But<PERSON> } from \"@/components/ui/button\";\n\nexport default function AuthButtons() {\n  return (\n    <>\n      <Button\n        asChild\n        variant=\"ghost\"\n        size=\"sm\"\n        className=\"text-sm text-[var(--button-text)] hover:bg-[var(--card-hover)]\"\n      >\n        <Link href=\"#\">Sign In</Link>\n      </Button>\n      <Button\n        asChild\n        size=\"sm\"\n        className=\"text-sm bg-[var(--button)] text-[var(--button-text)]\"\n      >\n        <Link href=\"#\">Signup</Link>\n      </Button>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AAFA;;;;AAIe,SAAS;IACtB,qBACE;;0BACE,6LAAC,qIAAA,CAAA,SAAM;gBACL,OAAO;gBACP,SAAQ;gBACR,MAAK;gBACL,WAAU;0BAEV,cAAA,6LAAC,+JAAA,CAAA,UAAI;oBAAC,MAAK;8BAAI;;;;;;;;;;;0BAEjB,6LAAC,qIAAA,CAAA,SAAM;gBACL,OAAO;gBACP,MAAK;gBACL,WAAU;0BAEV,cAAA,6LAAC,+JAAA,CAAA,UAAI;oBAAC,MAAK;8BAAI;;;;;;;;;;;;;AAIvB;KApBwB"}}, {"offset": {"line": 178, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 184, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/Work/Repositories/templgen/src/components/common/Logo.tsx"], "sourcesContent": ["export default function Logo() {\n  return (\n    <div>\n      <h1>Logo</h1>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAe,SAAS;IACtB,qBACE,6LAAC;kBACC,cAAA,6LAAC;sBAAG;;;;;;;;;;;AAGV;KANwB"}}, {"offset": {"line": 210, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 216, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/Work/Repositories/templgen/src/components/common/navbar/LogoLink.tsx"], "sourcesContent": ["\"use client\";\nimport Link from \"next/link\";\nimport Logo from \"../Logo\";\n\nexport default function LogoLink() {\n  return (\n    <Link\n      href=\"/home\"\n      aria-label=\"Cosmos\"\n      className=\"text-[var(--link-color)] hover:text-[var(--link-hover)]\"\n    >\n      <Logo />\n    </Link>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AAFA;;;;AAIe,SAAS;IACtB,qBACE,6LAAC,+JAAA,CAAA,UAAI;QACH,MAAK;QACL,cAAW;QACX,WAAU;kBAEV,cAAA,6LAAC,uIAAA,CAAA,UAAI;;;;;;;;;;AAGX;KAVwB"}}, {"offset": {"line": 248, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 254, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/Work/Repositories/templgen/src/components/ui/navigation-menu.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, VariantProps } from \"class-variance-authority\"\nimport { ChevronDownIcon } from \"lucide-react\"\nimport * as NavigationMenuPrimitive from \"@radix-ui/react-navigation-menu\"\n\nimport { cn } from \"@/lib/utils\"\n\ninterface NavigationMenuProps\n  extends React.ComponentProps<typeof NavigationMenuPrimitive.Root> {\n  viewport?: boolean\n}\n\nfunction NavigationMenu({\n  className,\n  children,\n  viewport = true,\n  ...props\n}: NavigationMenuProps) {\n  return (\n    <NavigationMenuPrimitive.Root\n      data-slot=\"navigation-menu\"\n      data-viewport={viewport}\n      className={cn(\n        \"group/navigation-menu relative flex max-w-max flex-1 items-center justify-center\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      {viewport && <NavigationMenuViewport />}\n    </NavigationMenuPrimitive.Root>\n  )\n}\n\ntype NavigationMenuListProps = React.ComponentProps<\n  typeof NavigationMenuPrimitive.List\n>\n\nfunction NavigationMenuList({ className, ...props }: NavigationMenuListProps) {\n  return (\n    <NavigationMenuPrimitive.List\n      data-slot=\"navigation-menu-list\"\n      className={cn(\n        \"group flex flex-1 list-none items-center justify-center gap-1\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\ntype NavigationMenuItemProps = React.ComponentProps<\n  typeof NavigationMenuPrimitive.Item\n>\n\nfunction NavigationMenuItem({ className, ...props }: NavigationMenuItemProps) {\n  return (\n    <NavigationMenuPrimitive.Item\n      data-slot=\"navigation-menu-item\"\n      className={cn(\"relative\", className)}\n      {...props}\n    />\n  )\n}\n\nconst navigationMenuTriggerStyle = cva(\n  \"group inline-flex h-9 w-max items-center justify-center rounded-md px-4 py-2 text-sm font-medium outline-none transition-[color,box-shadow]\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-[var(--background)] text-[var(--headline)]\",\n      },\n      state: {\n        hover:\n          \"hover:bg-[var(--button)] hover:text-[var(--button-text)] focus:bg-[var(--button)] focus:text-[var(--button-text)]\",\n        disabled: \"disabled:pointer-events-none disabled:opacity-50\",\n        open:\n          \"data-[state=open]:bg-[var(--button2)] data-[state=open]:hover:bg-[var(--button)] data-[state=open]:text-[var(--button-text)] focus-visible:ring-[3px] focus-visible:ring-[var(--button)]\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\ntype NavigationMenuTriggerProps = React.ComponentProps<\n  typeof NavigationMenuPrimitive.Trigger\n>\n\nfunction NavigationMenuTrigger({\n  className,\n  children,\n  ...props\n}: NavigationMenuTriggerProps) {\n  return (\n    <NavigationMenuPrimitive.Trigger\n      data-slot=\"navigation-menu-trigger\"\n      className={cn(navigationMenuTriggerStyle(), \"group\", className)}\n      {...props}\n    >\n      {children}{\" \"}\n      <ChevronDownIcon\n        className=\"relative top-[1px] ml-1 h-3 w-3 transition duration-300 group-data-[state=open]:rotate-180 text-[var(--button)]\"\n        aria-hidden=\"true\"\n      />\n    </NavigationMenuPrimitive.Trigger>\n  )\n}\n\ntype NavigationMenuContentProps = React.ComponentProps<\n  typeof NavigationMenuPrimitive.Content\n>\n\nfunction NavigationMenuContent({ className, ...props }: NavigationMenuContentProps) {\n  return (\n    <NavigationMenuPrimitive.Content\n      data-slot=\"navigation-menu-content\"\n      className={cn(\n        \"data-[motion^=from-]:animate-in data-[motion^=to-]:animate-out data-[motion^=from-]:fade-in data-[motion^=to-]:fade-out data-[motion=from-end]:slide-in-from-right-52 data-[motion=from-start]:slide-in-from-left-52 data-[motion=to-end]:slide-out-to-right-52 data-[motion=to-start]:slide-out-to-left-52 top-0 left-0 w-full p-2 pr-2.5 md:absolute md:w-auto\",\n        \"group-data-[viewport=false]/navigation-menu:bg-[var(--card-background)] group-data-[viewport=false]/navigation-menu:text-[var(--headline)] group-data-[viewport=false]/navigation-menu:data-[state=open]:animate-in group-data-[viewport=false]/navigation-menu:data-[state=closed]:animate-out group-data-[viewport=false]/navigation-menu:data-[state=closed]:zoom-out-95 group-data-[viewport=false]/navigation-menu:data-[state=open]:zoom-in-95 group-data-[viewport=false]/navigation-menu:data-[state=open]:fade-in-0 group-data-[viewport=false]/navigation-menu:data-[state=closed]:fade-out-0 group-data-[viewport=false]/navigation-menu:top-full group-data-[viewport=false]/navigation-menu:mt-1.5 group-data-[viewport=false]/navigation-menu:overflow-hidden group-data-[viewport=false]/navigation-menu:rounded-md group-data-[viewport=false]/navigation-menu:border group-data-[viewport=false]/navigation-menu:border-[var(--card-border-color)] group-data-[viewport=false]/navigation-menu:shadow group-data-[viewport=false]/navigation-menu:duration-200 **:data-[slot=navigation-menu-link]:focus:ring-0 **:data-[slot=navigation-menu-link]:focus:outline-none\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\ntype NavigationMenuViewportProps = React.ComponentProps<\n  typeof NavigationMenuPrimitive.Viewport\n>\n\nfunction NavigationMenuViewport({ className, ...props }: NavigationMenuViewportProps) {\n  return (\n    <div className={cn(\"absolute top-full left-0 isolate z-50 flex justify-center\")}>\n      <NavigationMenuPrimitive.Viewport\n        data-slot=\"navigation-menu-viewport\"\n        className={cn(\n          \"origin-top-center bg-[var(--card-background)] text-[var(--headline)] data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-90 relative mt-1.5 h-[var(--radix-navigation-menu-viewport-height)] w-full overflow-hidden rounded-md border border-[var(--card-border-color)] shadow md:w-[var(--radix-navigation-menu-viewport-width)]\",\n          className\n        )}\n        {...props}\n      />\n    </div>\n  )\n}\n\ntype NavigationMenuLinkProps = React.ComponentProps<\n  typeof NavigationMenuPrimitive.Link\n>\n\nfunction NavigationMenuLink({ className, ...props }: NavigationMenuLinkProps) {\n  return (\n    <NavigationMenuPrimitive.Link\n      data-slot=\"navigation-menu-link\"\n      className={cn(\n        \"data-[active]:focus:bg-[var(--button)] data-[active]:hover:bg-[var(--button)] data-[active]:bg-[var(--button)] data-[active]:text-[var(--button-text)] hover:bg-[var(--button2)] focus:bg-[var(--button)] focus:text-[var(--button-text)] focus-visible:ring-[3px] focus-visible:ring-[var(--button)] flex flex-col gap-1 rounded-sm p-2 text-sm outline-none transition-all [&_svg:not([class*='text-'])]:text-[var(--menu-color)] [&_svg:not([class*='size-'])]:h-4 [&_svg:not([class*='size-'])]:w-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\ntype NavigationMenuIndicatorProps = React.ComponentProps<\n  typeof NavigationMenuPrimitive.Indicator\n>\n\nfunction NavigationMenuIndicator({ className, ...props }: NavigationMenuIndicatorProps) {\n  return (\n    <NavigationMenuPrimitive.Indicator\n      data-slot=\"navigation-menu-indicator\"\n      className={cn(\n        \"data-[state=visible]:animate-in data-[state=hidden]:animate-out data-[state=hidden]:fade-out data-[state=visible]:fade-in top-full z-[1] flex h-1.5 items-end justify-center overflow-hidden\",\n        className\n      )}\n      {...props}\n    >\n      <div className=\"bg-[var(--border)] relative top-[60%] h-2 w-2 rotate-45 rounded-tl-sm shadow-md\" />\n    </NavigationMenuPrimitive.Indicator>\n  )\n}\n\nexport {\n  NavigationMenu,\n  NavigationMenuList,\n  NavigationMenuItem,\n  NavigationMenuContent,\n  NavigationMenuTrigger,\n  NavigationMenuLink,\n  NavigationMenuIndicator,\n  NavigationMenuViewport,\n  navigationMenuTriggerStyle,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AACA;AAIA;AAFA;AADA;;;;;;AAUA,SAAS,eAAe,EACtB,SAAS,EACT,QAAQ,EACR,WAAW,IAAI,EACf,GAAG,OACiB;IACpB,qBACE,6LAAC,kLAAwB,IAAI;QAC3B,aAAU;QACV,iBAAe;QACf,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oFACA;QAED,GAAG,KAAK;;YAER;YACA,0BAAY,6LAAC;;;;;;;;;;;AAGpB;KApBS;AA0BT,SAAS,mBAAmB,EAAE,SAAS,EAAE,GAAG,OAAgC;IAC1E,qBACE,6LAAC,kLAAwB,IAAI;QAC3B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAiBT,SAAS,mBAAmB,EAAE,SAAS,EAAE,GAAG,OAAgC;IAC1E,qBACE,6LAAC,kLAAwB,IAAI;QAC3B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QACzB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,MAAM,6BAA6B,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACnC,+IACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;QACX;QACA,OAAO;YACL,OACE;YACF,UAAU;YACV,MACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OACwB;IAC3B,qBACE,6LAAC,kLAAwB,OAAO;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B,SAAS;QACpD,GAAG,KAAK;;YAER;YAAU;0BACX,6LAAC,2NAAA,CAAA,kBAAe;gBACd,WAAU;gBACV,eAAY;;;;;;;;;;;;AAIpB;MAlBS;AAwBT,SAAS,sBAAsB,EAAE,SAAS,EAAE,GAAG,OAAmC;IAChF,qBACE,6LAAC,kLAAwB,OAAO;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oWACA,2nCACA;QAED,GAAG,KAAK;;;;;;AAGf;MAZS;AAkBT,SAAS,uBAAuB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAClF,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE;kBACjB,cAAA,6LAAC,kLAAwB,QAAQ;YAC/B,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wYACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;MAbS;AAmBT,SAAS,mBAAmB,EAAE,SAAS,EAAE,GAAG,OAAgC;IAC1E,qBACE,6LAAC,kLAAwB,IAAI;QAC3B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2eACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAiBT,SAAS,wBAAwB,EAAE,SAAS,EAAE,GAAG,OAAqC;IACpF,qBACE,6LAAC,kLAAwB,SAAS;QAChC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gMACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC;YAAI,WAAU;;;;;;;;;;;AAGrB;MAbS"}}, {"offset": {"line": 434, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 440, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/Work/Repositories/templgen/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\";\nimport { Check, ChevronRight, Circle } from \"lucide-react\";\n\nimport { cn } from \"@/lib/utils\";\n\nconst DropdownMenu = DropdownMenuPrimitive.Root;\n\nconst DropdownMenuTrigger = DropdownMenuPrimitive.Trigger;\n\nconst DropdownMenuGroup = DropdownMenuPrimitive.Group;\n\nconst DropdownMenuPortal = DropdownMenuPrimitive.Portal;\n\nconst DropdownMenuSub = DropdownMenuPrimitive.Sub;\n\nconst DropdownMenuRadioGroup = DropdownMenuPrimitive.RadioGroup;\n\nconst DropdownMenuSubTrigger = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.SubTrigger>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubTrigger> & {\n    inset?: boolean;\n  }\n>(({ className, inset, children, ...props }, ref) => (\n  <DropdownMenuPrimitive.SubTrigger\n    ref={ref}\n    className={cn(\n      \"flex cursor-default  select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent\",\n      inset && \"pl-8\",\n      className,\n    )}\n    {...props}\n  >\n    {children}\n    <ChevronRight className=\"ml-auto h-4 w-4\" />\n  </DropdownMenuPrimitive.SubTrigger>\n));\nDropdownMenuSubTrigger.displayName =\n  DropdownMenuPrimitive.SubTrigger.displayName;\n\nconst DropdownMenuSubContent = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.SubContent>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubContent>\n>(({ className, ...props }, ref) => (\n  <DropdownMenuPrimitive.SubContent\n    ref={ref}\n    className={cn(\n      \"z-50 min-w-[8rem] overflow-hidden rounded-md border p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n      className,\n    )}\n    {...props}\n  />\n));\nDropdownMenuSubContent.displayName =\n  DropdownMenuPrimitive.SubContent.displayName;\n\nconst DropdownMenuContent = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Content>\n>(({ className, sideOffset = 4, ...props }, ref) => (\n  <DropdownMenuPrimitive.Portal>\n    <DropdownMenuPrimitive.Content\n      ref={ref}\n      sideOffset={sideOffset}\n      className={cn(\n        \"z-50 min-w-[8rem] bg-[var(--card-background)]  overflow-hidden  border  p-1 text-[var(--paragraph)] border-[var(--border)] rounded-[12px] shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        className,\n      )}\n      {...props}\n    />\n  </DropdownMenuPrimitive.Portal>\n));\nDropdownMenuContent.displayName = DropdownMenuPrimitive.Content.displayName;\n\nconst DropdownMenuItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Item> & {\n    inset?: boolean;\n  }\n>(({ className, inset, ...props }, ref) => (\n  <DropdownMenuPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex hover:bg-[var(--card-hover)]  cursor-pointer select-none flex-row  items-start justify-start rounded-sm px-2 py-1.5 text-start text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      inset && \"pl-8\",\n      className,\n    )}\n    {...props}\n  />\n));\nDropdownMenuItem.displayName = DropdownMenuPrimitive.Item.displayName;\n\nconst DropdownMenuCheckboxItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.CheckboxItem>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.CheckboxItem>\n>(({ className, children, checked, ...props }, ref) => (\n  <DropdownMenuPrimitive.CheckboxItem\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className,\n    )}\n    checked={checked}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <DropdownMenuPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </DropdownMenuPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </DropdownMenuPrimitive.CheckboxItem>\n));\nDropdownMenuCheckboxItem.displayName =\n  DropdownMenuPrimitive.CheckboxItem.displayName;\n\nconst DropdownMenuRadioItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.RadioItem>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.RadioItem>\n>(({ className, children, ...props }, ref) => (\n  <DropdownMenuPrimitive.RadioItem\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className,\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <DropdownMenuPrimitive.ItemIndicator>\n        <Circle className=\"h-2 w-2 fill-current\" />\n      </DropdownMenuPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </DropdownMenuPrimitive.RadioItem>\n));\nDropdownMenuRadioItem.displayName = DropdownMenuPrimitive.RadioItem.displayName;\n\nconst DropdownMenuLabel = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Label> & {\n    inset?: boolean;\n  }\n>(({ className, inset, ...props }, ref) => (\n  <DropdownMenuPrimitive.Label\n    ref={ref}\n    className={cn(\n      \"px-2 py-1.5 text-sm font-semibold\",\n      inset && \"pl-8\",\n      className,\n    )}\n    {...props}\n  />\n));\nDropdownMenuLabel.displayName = DropdownMenuPrimitive.Label.displayName;\n\nconst DropdownMenuSeparator = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <DropdownMenuPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-[var(--border)]\", className)}\n    {...props}\n  />\n));\nDropdownMenuSeparator.displayName = DropdownMenuPrimitive.Separator.displayName;\n\nconst DropdownMenuShortcut = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLSpanElement>) => {\n  return (\n    <span\n      className={cn(\"ml-auto  text-xs tracking-widest opacity-60\", className)}\n      {...props}\n    />\n  );\n};\nDropdownMenuShortcut.displayName = \"DropdownMenuShortcut\";\n\nexport {\n  DropdownMenu,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuGroup,\n  DropdownMenuPortal,\n  DropdownMenuSub,\n  DropdownMenuSubContent,\n  DropdownMenuSubTrigger,\n  DropdownMenuRadioGroup,\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAIA;AAHA;AACA;AAAA;AAAA;;;;;;AAIA,MAAM,eAAe,gLAAsB,IAAI;AAE/C,MAAM,sBAAsB,gLAAsB,OAAO;AAEzD,MAAM,oBAAoB,gLAAsB,KAAK;AAErD,MAAM,qBAAqB,gLAAsB,MAAM;AAEvD,MAAM,kBAAkB,gLAAsB,GAAG;AAEjD,MAAM,yBAAyB,gLAAsB,UAAU;AAE/D,MAAM,uCAAyB,8JAAM,UAAU,MAK7C,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAC3C,6LAAC,gLAAsB,UAAU;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,yIACA,SAAS,QACT;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,yNAAA,CAAA,eAAY;gBAAC,WAAU;;;;;;;;;;;;;AAG5B,uBAAuB,WAAW,GAChC,gLAAsB,UAAU,CAAC,WAAW;AAE9C,MAAM,uCAAyB,8JAAM,UAAU,OAG7C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,gLAAsB,UAAU;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8aACA;QAED,GAAG,KAAK;;;;;;;AAGb,uBAAuB,WAAW,GAChC,gLAAsB,UAAU,CAAC,WAAW;AAE9C,MAAM,oCAAsB,8JAAM,UAAU,OAG1C,CAAC,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC1C,6LAAC,gLAAsB,MAAM;kBAC3B,cAAA,6LAAC,gLAAsB,OAAO;YAC5B,KAAK;YACL,YAAY;YACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,weACA;YAED,GAAG,KAAK;;;;;;;;;;;;AAIf,oBAAoB,WAAW,GAAG,gLAAsB,OAAO,CAAC,WAAW;AAE3E,MAAM,iCAAmB,8JAAM,UAAU,OAKvC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,6LAAC,gLAAsB,IAAI;QACzB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mSACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;;AAGb,iBAAiB,WAAW,GAAG,gLAAsB,IAAI,CAAC,WAAW;AAErE,MAAM,yCAA2B,8JAAM,UAAU,OAG/C,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBAC7C,6LAAC,gLAAsB,YAAY;QACjC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wOACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,gLAAsB,aAAa;8BAClC,cAAA,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGpB;;;;;;;;AAGL,yBAAyB,WAAW,GAClC,gLAAsB,YAAY,CAAC,WAAW;AAEhD,MAAM,sCAAwB,8JAAM,UAAU,QAG5C,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC,gLAAsB,SAAS;QAC9B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wOACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,gLAAsB,aAAa;8BAClC,cAAA,6LAAC,yMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGrB;;;;;;;;AAGL,sBAAsB,WAAW,GAAG,gLAAsB,SAAS,CAAC,WAAW;AAE/E,MAAM,kCAAoB,8JAAM,UAAU,QAKxC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,6LAAC,gLAAsB,KAAK;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qCACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;;AAGb,kBAAkB,WAAW,GAAG,gLAAsB,KAAK,CAAC,WAAW;AAEvE,MAAM,sCAAwB,8JAAM,UAAU,QAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,gLAAsB,SAAS;QAC9B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;;AAGb,sBAAsB,WAAW,GAAG,gLAAsB,SAAS,CAAC,WAAW;AAE/E,MAAM,uBAAuB,CAAC,EAC5B,SAAS,EACT,GAAG,OACmC;IACtC,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,+CAA+C;QAC5D,GAAG,KAAK;;;;;;AAGf;OAVM;AAWN,qBAAqB,WAAW,GAAG"}}, {"offset": {"line": 661, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 667, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/Work/Repositories/templgen/src/components/common/navbar/NavItems.tsx"], "sourcesContent": ["\"use client\";\n\nimport {\n  NavigationMenu,\n  NavigationMenuItem,\n  NavigationMenuLink,\n  NavigationMenuList,\n  NavigationMenuTrigger,\n} from \"@/components/ui/navigation-menu\";\n\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuItem,\n  DropdownMenuTrigger as DropdownTrigger,\n} from \"@/components/ui/dropdown-menu\";\n\nimport { MdKeyboardArrowRight } from \"react-icons/md\";\n\nimport { cn } from \"@/lib/utils\";\nimport { AnimatePresence, motion } from \"framer-motion\";\nimport { BookOpenIcon, InfoIcon, LifeBuoyIcon } from \"lucide-react\";\nimport { useState } from \"react\";\nimport { FiArrowLeft } from \"react-icons/fi\";\n\ntype NavLinkItem = {\n  href: string;\n  label: string;\n  description?: string;\n  icon?: \"BookOpenIcon\" | \"LifeBuoyIcon\" | \"InfoIcon\";\n};\n\ntype NavLink = {\n  href?: string;\n  label: string;\n  submenu?: boolean;\n  type?: \"description\" | \"simple\" | \"icon\";\n  items?: NavLinkItem[];\n};\n\nconst navigationLinks: NavLink[] = [\n  { href: \"#\", label: \"Home\" },\n  {\n    label: \"Features\",\n    submenu: true,\n    type: \"description\",\n    items: [\n      { href: \"#\", label: \"Components\", description: \"Browse all components.\" },\n      { href: \"#\", label: \"Documentation\", description: \"Learn to use it.\" },\n      { href: \"#\", label: \"Templates\", description: \"Pre-built layouts.\" },\n    ],\n  },\n  {\n    label: \"Pricing\",\n    submenu: true,\n    type: \"simple\",\n    items: [\n      { href: \"#\", label: \"Product A\" },\n      { href: \"#\", label: \"Product B\" },\n      { href: \"#\", label: \"Product C\" },\n      { href: \"#\", label: \"Product D\" },\n    ],\n  },\n  {\n    label: \"About\",\n    submenu: true,\n    type: \"icon\",\n    items: [\n      { href: \"#\", label: \"Getting Started\", icon: \"BookOpenIcon\" },\n      { href: \"#\", label: \"Tutorials\", icon: \"LifeBuoyIcon\" },\n      { href: \"#\", label: \"About Us\", icon: \"InfoIcon\" },\n    ],\n  },\n];\n\nconst renderIcon = (icon?: NavLinkItem[\"icon\"]) => {\n  switch (icon) {\n    case \"BookOpenIcon\":\n      return (\n        <BookOpenIcon\n          size={16}\n          className=\"text-[var(--menu-color)] opacity-60\"\n          aria-hidden=\"true\"\n        />\n      );\n    case \"LifeBuoyIcon\":\n      return (\n        <LifeBuoyIcon\n          size={16}\n          className=\"text-[var(--menu-color)] opacity-60\"\n          aria-hidden=\"true\"\n        />\n      );\n    case \"InfoIcon\":\n      return (\n        <InfoIcon\n          size={16}\n          className=\"text-[var(--menu-color)] opacity-60\"\n          aria-hidden=\"true\"\n        />\n      );\n    default:\n      return null;\n  }\n};\n\nexport default function NavItems({ isMobile = false }: { isMobile?: boolean }) {\n  const [openDropdownIndex, setOpenDropdownIndex] = useState<number | null>(\n    null\n  );\n\n  if (!isMobile) {\n    // Desktop: DropdownMenu without hover styles\n    return (\n      <NavigationMenu viewport={false}>\n        <NavigationMenuList className=\"gap-2 flex\">\n          {navigationLinks.map((link, index) =>\n            link.submenu ? (\n              <NavigationMenuItem key={index}>\n                <DropdownMenu>\n                  <DropdownTrigger asChild>\n                    <NavigationMenuTrigger className=\"text-[var(--nav-item)] bg-transparent px-2 py-1.5 font-medium cursor-pointer *:[svg]:-me-0.5 *:[svg]:size-3.5 inline-flex items-center gap-1\">\n                      {link.label}\n                    </NavigationMenuTrigger>\n                  </DropdownTrigger>\n\n                  <DropdownMenuContent\n                    side=\"bottom\"\n                    align=\"start\"\n                    className={cn(\n                      \"bg-[var(--background)] border border-[var(--border)] p-2 min-w-[12rem]\"\n                    )}\n                  >\n                    {link.type === \"description\" && (\n                      <DropdownMenuGroup>\n                        {link.items?.map((item, idx) => (\n                          <DropdownMenuItem\n                            key={idx}\n                            asChild\n                            className=\"flex flex-col items-start gap-0.5 py-2 px-3 rounded cursor-pointer\"\n                          >\n                            <a\n                              href={item.href}\n                              className=\"w-full text-[var(--paragraph)]\"\n                            >\n                              <span>{item.label}</span>\n                              {item.description && (\n                                <span className=\"text-xs text-[var(--muted)]\">\n                                  {item.description}\n                                </span>\n                              )}\n                            </a>\n                          </DropdownMenuItem>\n                        ))}\n                      </DropdownMenuGroup>\n                    )}\n\n                    {link.type === \"simple\" && (\n                      <DropdownMenuGroup>\n                        {link.items?.map((item, idx) => (\n                          <DropdownMenuItem\n                            key={idx}\n                            asChild\n                            className=\"py-2 px-3 rounded cursor-pointer\"\n                          >\n                            <a\n                              href={item.href}\n                              className=\"text-[var(--paragraph)] block w-full\"\n                            >\n                              {item.label}\n                            </a>\n                          </DropdownMenuItem>\n                        ))}\n                      </DropdownMenuGroup>\n                    )}\n\n                    {link.type === \"icon\" && (\n                      <DropdownMenuGroup>\n                        {link.items?.map((item, idx) => (\n                          <DropdownMenuItem\n                            key={idx}\n                            asChild\n                            className=\"flex items-center gap-2 py-2 px-3 rounded cursor-pointer\"\n                          >\n                            <a\n                              href={item.href}\n                              className=\"flex items-center gap-2 w-full text-[var(--paragraph)]\"\n                            >\n                              {item.icon && renderIcon(item.icon)}\n                              {item.label}\n                            </a>\n                          </DropdownMenuItem>\n                        ))}\n                      </DropdownMenuGroup>\n                    )}\n                  </DropdownMenuContent>\n                </DropdownMenu>\n              </NavigationMenuItem>\n            ) : (\n              <NavigationMenuItem key={index}>\n                <NavigationMenuLink\n                  href={link.href}\n                  className=\"text-[var(--nav-item)] py-1.5 font-medium\"\n                >\n                  {link.label}\n                </NavigationMenuLink>\n              </NavigationMenuItem>\n            )\n          )}\n        </NavigationMenuList>\n      </NavigationMenu>\n    );\n  }\n\n  // Mobile: drilldown with full width buttons and links\n  return (\n    <NavigationMenu\n      className=\"fixed top-0 left-0 w-full h-screen z-50 bg-[var(--background)] p-4 overflow-hidden\"\n      role=\"navigation\"\n      aria-label=\"Mobile navigation\"\n    >\n      <AnimatePresence initial={false} mode=\"wait\">\n        {openDropdownIndex === null ? (\n          <motion.div\n            key=\"main-menu\"\n            initial={{ x: 300, opacity: 0 }}\n            animate={{ x: 0, opacity: 1 }}\n            exit={{ x: -300, opacity: 0 }}\n            transition={{ duration: 0.2 }}\n            className=\"flex flex-col h-full    w-screen\"\n          >\n            <header>\n              <h1>test</h1>\n            </header>\n            <NavigationMenuList className=\"flex flex-col  gap-3 w-full overflow-auto\">\n              {navigationLinks.map((link, i) => (\n                <div key={i} className=\"flex flex-col w-full\">\n                  {link.submenu ? (\n                    <button\n                      onClick={() => setOpenDropdownIndex(i)}\n                      aria-label={`Open submenu for ${link.label}`}\n                      className=\"flex justify-between  items-center w-full text-[var(--nav-item)] px-3 py-4 text-base font-medium rounded touch-manipulation\"\n                      style={{ touchAction: \"manipulation\" }}\n                    >\n                      <span>{link.label}</span>\n\n                      <span aria-hidden=\"true\" style={{ fontSize: 20 }}>\n                        <MdKeyboardArrowRight />\n                      </span>\n                    </button>\n                  ) : (\n                    <NavigationMenuLink\n                      href={link.href}\n                      tabIndex={0}\n                      className=\"py-4 px-3 text-[var(--paragraph)] block font-medium w-full\"\n                    >\n                      {link.label}\n                    </NavigationMenuLink>\n                  )}\n                </div>\n              ))}\n            </NavigationMenuList>\n          </motion.div>\n        ) : (\n          <motion.div\n            key=\"submenu\"\n            initial={{ x: 300, opacity: 0 }}\n            animate={{ x: 0, opacity: 1 }}\n            exit={{ x: 300, opacity: 0 }}\n            transition={{ duration: 0.2 }}\n            className=\"flex flex-col w-full h-full\"\n          >\n            <button\n              onClick={() => setOpenDropdownIndex(null)}\n              aria-label=\"Go back to main menu\"\n              className=\"flex items-center gap-2 text-[var(--nav-item)] mb-4 px-3 py-3 font-medium rounded touch-manipulation w-full\"\n              style={{ touchAction: \"manipulation\" }}\n            >\n              <FiArrowLeft size={20} aria-hidden=\"true\" />\n            </button>\n            <NavigationMenuList className=\"flex flex-col gap-3 overflow-auto\">\n              {navigationLinks[openDropdownIndex].items?.map((item, idx) => (\n                <NavigationMenuLink\n                  key={idx}\n                  href={item.href}\n                  tabIndex={0}\n                  className=\"py-4 px-3 text-[var(--paragraph)]  font-medium flex items-center gap-2 w-full\"\n                >\n                  {navigationLinks[openDropdownIndex].type === \"icon\" &&\n                    item.icon &&\n                    renderIcon(item.icon)}\n                  {item.label}\n                </NavigationMenuLink>\n              ))}\n            </NavigationMenuList>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </NavigationMenu>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAQA;AAUA;AAGA;AADA;AAAA;AAAA;AADA;AAAA;AAGA;AANA;;;AAlBA;;;;;;;;;AAyCA,MAAM,kBAA6B;IACjC;QAAE,MAAM;QAAK,OAAO;IAAO;IAC3B;QACE,OAAO;QACP,SAAS;QACT,MAAM;QACN,OAAO;YACL;gBAAE,MAAM;gBAAK,OAAO;gBAAc,aAAa;YAAyB;YACxE;gBAAE,MAAM;gBAAK,OAAO;gBAAiB,aAAa;YAAmB;YACrE;gBAAE,MAAM;gBAAK,OAAO;gBAAa,aAAa;YAAqB;SACpE;IACH;IACA;QACE,OAAO;QACP,SAAS;QACT,MAAM;QACN,OAAO;YACL;gBAAE,MAAM;gBAAK,OAAO;YAAY;YAChC;gBAAE,MAAM;gBAAK,OAAO;YAAY;YAChC;gBAAE,MAAM;gBAAK,OAAO;YAAY;YAChC;gBAAE,MAAM;gBAAK,OAAO;YAAY;SACjC;IACH;IACA;QACE,OAAO;QACP,SAAS;QACT,MAAM;QACN,OAAO;YACL;gBAAE,MAAM;gBAAK,OAAO;gBAAmB,MAAM;YAAe;YAC5D;gBAAE,MAAM;gBAAK,OAAO;gBAAa,MAAM;YAAe;YACtD;gBAAE,MAAM;gBAAK,OAAO;gBAAY,MAAM;YAAW;SAClD;IACH;CACD;AAED,MAAM,aAAa,CAAC;IAClB,OAAQ;QACN,KAAK;YACH,qBACE,6LAAC,qNAAA,CAAA,eAAY;gBACX,MAAM;gBACN,WAAU;gBACV,eAAY;;;;;;QAGlB,KAAK;YACH,qBACE,6LAAC,qNAAA,CAAA,eAAY;gBACX,MAAM;gBACN,WAAU;gBACV,eAAY;;;;;;QAGlB,KAAK;YACH,qBACE,6LAAC,yMAAA,CAAA,WAAQ;gBACP,MAAM;gBACN,WAAU;gBACV,eAAY;;;;;;QAGlB;YACE,OAAO;IACX;AACF;AAEe,SAAS,SAAS,EAAE,WAAW,KAAK,EAA0B;;IAC3E,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EACvD;IAGF,IAAI,CAAC,UAAU;QACb,6CAA6C;QAC7C,qBACE,6LAAC,iJAAA,CAAA,iBAAc;YAAC,UAAU;sBACxB,cAAA,6LAAC,iJAAA,CAAA,qBAAkB;gBAAC,WAAU;0BAC3B,gBAAgB,GAAG,CAAC,CAAC,MAAM,QAC1B,KAAK,OAAO,iBACV,6LAAC,iJAAA,CAAA,qBAAkB;kCACjB,cAAA,6LAAC,+IAAA,CAAA,eAAY;;8CACX,6LAAC,+IAAA,CAAA,sBAAe;oCAAC,OAAO;8CACtB,cAAA,6LAAC,iJAAA,CAAA,wBAAqB;wCAAC,WAAU;kDAC9B,KAAK,KAAK;;;;;;;;;;;8CAIf,6LAAC,+IAAA,CAAA,sBAAmB;oCAClB,MAAK;oCACL,OAAM;oCACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV;;wCAGD,KAAK,IAAI,KAAK,+BACb,6LAAC,+IAAA,CAAA,oBAAiB;sDACf,KAAK,KAAK,EAAE,IAAI,CAAC,MAAM,oBACtB,6LAAC,+IAAA,CAAA,mBAAgB;oDAEf,OAAO;oDACP,WAAU;8DAEV,cAAA,6LAAC;wDACC,MAAM,KAAK,IAAI;wDACf,WAAU;;0EAEV,6LAAC;0EAAM,KAAK,KAAK;;;;;;4DAChB,KAAK,WAAW,kBACf,6LAAC;gEAAK,WAAU;0EACb,KAAK,WAAW;;;;;;;;;;;;mDAXlB;;;;;;;;;;wCAoBZ,KAAK,IAAI,KAAK,0BACb,6LAAC,+IAAA,CAAA,oBAAiB;sDACf,KAAK,KAAK,EAAE,IAAI,CAAC,MAAM,oBACtB,6LAAC,+IAAA,CAAA,mBAAgB;oDAEf,OAAO;oDACP,WAAU;8DAEV,cAAA,6LAAC;wDACC,MAAM,KAAK,IAAI;wDACf,WAAU;kEAET,KAAK,KAAK;;;;;;mDARR;;;;;;;;;;wCAeZ,KAAK,IAAI,KAAK,wBACb,6LAAC,+IAAA,CAAA,oBAAiB;sDACf,KAAK,KAAK,EAAE,IAAI,CAAC,MAAM,oBACtB,6LAAC,+IAAA,CAAA,mBAAgB;oDAEf,OAAO;oDACP,WAAU;8DAEV,cAAA,6LAAC;wDACC,MAAM,KAAK,IAAI;wDACf,WAAU;;4DAET,KAAK,IAAI,IAAI,WAAW,KAAK,IAAI;4DACjC,KAAK,KAAK;;;;;;;mDATR;;;;;;;;;;;;;;;;;;;;;;uBA9DM;;;;6CAiFzB,6LAAC,iJAAA,CAAA,qBAAkB;kCACjB,cAAA,6LAAC,iJAAA,CAAA,qBAAkB;4BACjB,MAAM,KAAK,IAAI;4BACf,WAAU;sCAET,KAAK,KAAK;;;;;;uBALU;;;;;;;;;;;;;;;IAarC;IAEA,sDAAsD;IACtD,qBACE,6LAAC,iJAAA,CAAA,iBAAc;QACb,WAAU;QACV,MAAK;QACL,cAAW;kBAEX,cAAA,6LAAC,4LAAA,CAAA,kBAAe;YAAC,SAAS;YAAO,MAAK;sBACnC,sBAAsB,qBACrB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBAET,SAAS;oBAAE,GAAG;oBAAK,SAAS;gBAAE;gBAC9B,SAAS;oBAAE,GAAG;oBAAG,SAAS;gBAAE;gBAC5B,MAAM;oBAAE,GAAG,CAAC;oBAAK,SAAS;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,WAAU;;kCAEV,6LAAC;kCACC,cAAA,6LAAC;sCAAG;;;;;;;;;;;kCAEN,6LAAC,iJAAA,CAAA,qBAAkB;wBAAC,WAAU;kCAC3B,gBAAgB,GAAG,CAAC,CAAC,MAAM,kBAC1B,6LAAC;gCAAY,WAAU;0CACpB,KAAK,OAAO,iBACX,6LAAC;oCACC,SAAS,IAAM,qBAAqB;oCACpC,cAAY,CAAC,iBAAiB,EAAE,KAAK,KAAK,EAAE;oCAC5C,WAAU;oCACV,OAAO;wCAAE,aAAa;oCAAe;;sDAErC,6LAAC;sDAAM,KAAK,KAAK;;;;;;sDAEjB,6LAAC;4CAAK,eAAY;4CAAO,OAAO;gDAAE,UAAU;4CAAG;sDAC7C,cAAA,6LAAC,iJAAA,CAAA,uBAAoB;;;;;;;;;;;;;;;yDAIzB,6LAAC,iJAAA,CAAA,qBAAkB;oCACjB,MAAM,KAAK,IAAI;oCACf,UAAU;oCACV,WAAU;8CAET,KAAK,KAAK;;;;;;+BApBP;;;;;;;;;;;eAZV;;;;qCAwCN,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBAET,SAAS;oBAAE,GAAG;oBAAK,SAAS;gBAAE;gBAC9B,SAAS;oBAAE,GAAG;oBAAG,SAAS;gBAAE;gBAC5B,MAAM;oBAAE,GAAG;oBAAK,SAAS;gBAAE;gBAC3B,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,WAAU;;kCAEV,6LAAC;wBACC,SAAS,IAAM,qBAAqB;wBACpC,cAAW;wBACX,WAAU;wBACV,OAAO;4BAAE,aAAa;wBAAe;kCAErC,cAAA,6LAAC,iJAAA,CAAA,cAAW;4BAAC,MAAM;4BAAI,eAAY;;;;;;;;;;;kCAErC,6LAAC,iJAAA,CAAA,qBAAkB;wBAAC,WAAU;kCAC3B,eAAe,CAAC,kBAAkB,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,oBACpD,6LAAC,iJAAA,CAAA,qBAAkB;gCAEjB,MAAM,KAAK,IAAI;gCACf,UAAU;gCACV,WAAU;;oCAET,eAAe,CAAC,kBAAkB,CAAC,IAAI,KAAK,UAC3C,KAAK,IAAI,IACT,WAAW,KAAK,IAAI;oCACrB,KAAK,KAAK;;+BARN;;;;;;;;;;;eAlBP;;;;;;;;;;;;;;;AAmChB;GAlMwB;KAAA"}}, {"offset": {"line": 1149, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1155, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/Work/Repositories/templgen/src/components/ui/RotatingTextRef.tsx"], "sourcesContent": ["import React, {\n  forwardRef,\n  useCallback,\n  useEffect,\n  useImperative<PERSON>andle,\n  useMemo,\n  useState,\n} from \"react\";\nimport {\n  motion,\n  AnimatePresence,\n  Transition,\n  type VariantLabels,\n  type Target,\n  type AnimationControls,\n  type TargetAndTransition,\n} from \"framer-motion\";\n\nfunction cn(...classes: (string | undefined | null | boolean)[]): string {\n  return classes.filter(Boolean).join(\" \");\n}\n\nexport interface RotatingTextRef {\n  next: () => void;\n  previous: () => void;\n  jumpTo: (index: number) => void;\n  reset: () => void;\n}\n\nexport interface RotatingTextProps\n  extends Omit<\n    React.ComponentPropsWithoutRef<typeof motion.span>,\n    \"children\" | \"transition\" | \"initial\" | \"animate\" | \"exit\"\n  > {\n  texts: string[];\n  transition?: Transition;\n  initial?: boolean | Target | VariantLabels;\n  animate?: boolean | VariantLabels | AnimationControls | TargetAndTransition;\n  exit?: Target | VariantLabels;\n  animatePresenceMode?: \"sync\" | \"wait\";\n  animatePresenceInitial?: boolean;\n  rotationInterval?: number;\n  staggerDuration?: number;\n  staggerFrom?: \"first\" | \"last\" | \"center\" | \"random\" | number;\n  loop?: boolean;\n  auto?: boolean;\n  splitBy?: string;\n  onNext?: (index: number) => void;\n  mainClassName?: string;\n  splitLevelClassName?: string;\n  elementLevelClassName?: string;\n}\n\nconst RotatingText = forwardRef<RotatingTextRef, RotatingTextProps>(\n  (\n    {\n      texts,\n      transition = { type: \"spring\", damping: 25, stiffness: 300 },\n      initial = { y: \"100%\", opacity: 0 },\n      animate = { y: 0, opacity: 1 },\n      exit = { y: \"-120%\", opacity: 0 },\n      animatePresenceMode = \"wait\",\n      animatePresenceInitial = false,\n      rotationInterval = 2000,\n      staggerDuration = 0,\n      staggerFrom = \"first\",\n      loop = true,\n      auto = true,\n      splitBy = \"characters\",\n      onNext,\n      mainClassName,\n      splitLevelClassName,\n      elementLevelClassName,\n      ...rest\n    },\n    ref\n  ) => {\n    const [currentTextIndex, setCurrentTextIndex] = useState<number>(0);\n\n    const splitIntoCharacters = (text: string): string[] => {\n      if (typeof Intl !== \"undefined\" && Intl.Segmenter) {\n        const segmenter = new Intl.Segmenter(\"en\", { granularity: \"grapheme\" });\n        return Array.from(\n          segmenter.segment(text),\n          (segment) => segment.segment\n        );\n      }\n      return Array.from(text);\n    };\n\n    const elements = useMemo(() => {\n      const currentText: string = texts[currentTextIndex];\n      if (splitBy === \"characters\") {\n        const words = currentText.split(\" \");\n        return words.map((word, i) => ({\n          characters: splitIntoCharacters(word),\n          needsSpace: i !== words.length - 1,\n        }));\n      }\n      if (splitBy === \"words\") {\n        return currentText.split(\" \").map((word, i, arr) => ({\n          characters: [word],\n          needsSpace: i !== arr.length - 1,\n        }));\n      }\n      if (splitBy === \"lines\") {\n        return currentText.split(\"\\n\").map((line, i, arr) => ({\n          characters: [line],\n          needsSpace: i !== arr.length - 1,\n        }));\n      }\n\n      return currentText.split(splitBy).map((part, i, arr) => ({\n        characters: [part],\n        needsSpace: i !== arr.length - 1,\n      }));\n    }, [texts, currentTextIndex, splitBy]);\n\n    const getStaggerDelay = useCallback(\n      (index: number, totalChars: number): number => {\n        const total = totalChars;\n        if (staggerFrom === \"first\") return index * staggerDuration;\n        if (staggerFrom === \"last\")\n          return (total - 1 - index) * staggerDuration;\n        if (staggerFrom === \"center\") {\n          const center = Math.floor(total / 2);\n          return Math.abs(center - index) * staggerDuration;\n        }\n        if (staggerFrom === \"random\") {\n          const randomIndex = Math.floor(Math.random() * total);\n          return Math.abs(randomIndex - index) * staggerDuration;\n        }\n        return Math.abs((staggerFrom as number) - index) * staggerDuration;\n      },\n      [staggerFrom, staggerDuration]\n    );\n\n    const handleIndexChange = useCallback(\n      (newIndex: number) => {\n        setCurrentTextIndex(newIndex);\n        if (onNext) onNext(newIndex);\n      },\n      [onNext]\n    );\n\n    const next = useCallback(() => {\n      const nextIndex =\n        currentTextIndex === texts.length - 1\n          ? loop\n            ? 0\n            : currentTextIndex\n          : currentTextIndex + 1;\n      if (nextIndex !== currentTextIndex) {\n        handleIndexChange(nextIndex);\n      }\n    }, [currentTextIndex, texts.length, loop, handleIndexChange]);\n\n    const previous = useCallback(() => {\n      const prevIndex =\n        currentTextIndex === 0\n          ? loop\n            ? texts.length - 1\n            : currentTextIndex\n          : currentTextIndex - 1;\n      if (prevIndex !== currentTextIndex) {\n        handleIndexChange(prevIndex);\n      }\n    }, [currentTextIndex, texts.length, loop, handleIndexChange]);\n\n    const jumpTo = useCallback(\n      (index: number) => {\n        const validIndex = Math.max(0, Math.min(index, texts.length - 1));\n        if (validIndex !== currentTextIndex) {\n          handleIndexChange(validIndex);\n        }\n      },\n      [texts.length, currentTextIndex, handleIndexChange]\n    );\n\n    const reset = useCallback(() => {\n      if (currentTextIndex !== 0) {\n        handleIndexChange(0);\n      }\n    }, [currentTextIndex, handleIndexChange]);\n\n    useImperativeHandle(\n      ref,\n      () => ({\n        next,\n        previous,\n        jumpTo,\n        reset,\n      }),\n      [next, previous, jumpTo, reset]\n    );\n\n    useEffect(() => {\n      if (!auto) return;\n      const intervalId = setInterval(next, rotationInterval);\n      return () => clearInterval(intervalId);\n    }, [next, rotationInterval, auto]);\n\n    return (\n      <motion.span\n        className={cn(\n          \"flex flex-wrap whitespace-pre-wrap relative\",\n          mainClassName\n        )}\n        {...rest}\n        layout\n        transition={transition}\n      >\n        <span className=\"sr-only\">{texts[currentTextIndex]}</span>\n        <AnimatePresence\n          mode={animatePresenceMode}\n          initial={animatePresenceInitial}\n        >\n          <motion.span\n            key={currentTextIndex}\n            className={cn(\n              splitBy === \"lines\"\n                ? \"flex flex-col w-full\"\n                : \"flex flex-wrap whitespace-pre-wrap relative\"\n            )}\n            layout\n            aria-hidden=\"true\"\n          >\n            {elements.map((wordObj, wordIndex, array) => {\n              const previousCharsCount = array\n                .slice(0, wordIndex)\n                .reduce((sum, word) => sum + word.characters.length, 0);\n              return (\n                <span\n                  key={wordIndex}\n                  className={cn(\"inline-flex\", splitLevelClassName)}\n                >\n                  {wordObj.characters.map((char, charIndex) => (\n                    <motion.span\n                      key={charIndex}\n                      initial={initial}\n                      animate={animate}\n                      exit={exit}\n                      transition={{\n                        ...transition,\n                        delay: getStaggerDelay(\n                          previousCharsCount + charIndex,\n                          array.reduce(\n                            (sum, word) => sum + word.characters.length,\n                            0\n                          )\n                        ),\n                      }}\n                      className={cn(\"inline-block\", elementLevelClassName)}\n                    >\n                      {char}\n                    </motion.span>\n                  ))}\n                  {wordObj.needsSpace && (\n                    <span className=\"whitespace-pre\"> </span>\n                  )}\n                </span>\n              );\n            })}\n          </motion.span>\n        </AnimatePresence>\n      </motion.span>\n    );\n  }\n);\n\nRotatingText.displayName = \"RotatingText\";\nexport default RotatingText;\n"], "names": [], "mappings": ";;;;AAAA;AAQA;AAAA;;;;;AAUA,SAAS,GAAG,GAAG,OAAgD;IAC7D,OAAO,QAAQ,MAAM,CAAC,SAAS,IAAI,CAAC;AACtC;AAiCA,MAAM,6BAAe,GAAA,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,UAC5B,CACE,EACE,KAAK,EACL,aAAa;IAAE,MAAM;IAAU,SAAS;IAAI,WAAW;AAAI,CAAC,EAC5D,UAAU;IAAE,GAAG;IAAQ,SAAS;AAAE,CAAC,EACnC,UAAU;IAAE,GAAG;IAAG,SAAS;AAAE,CAAC,EAC9B,OAAO;IAAE,GAAG;IAAS,SAAS;AAAE,CAAC,EACjC,sBAAsB,MAAM,EAC5B,yBAAyB,KAAK,EAC9B,mBAAmB,IAAI,EACvB,kBAAkB,CAAC,EACnB,cAAc,OAAO,EACrB,OAAO,IAAI,EACX,OAAO,IAAI,EACX,UAAU,YAAY,EACtB,MAAM,EACN,aAAa,EACb,mBAAmB,EACnB,qBAAqB,EACrB,GAAG,MACJ,EACD;;IAEA,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAEjE,MAAM,sBAAsB,CAAC;QAC3B,IAAI,OAAO,SAAS,eAAe,KAAK,SAAS,EAAE;YACjD,MAAM,YAAY,IAAI,KAAK,SAAS,CAAC,MAAM;gBAAE,aAAa;YAAW;YACrE,OAAO,MAAM,IAAI,CACf,UAAU,OAAO,CAAC,OAClB,CAAC,UAAY,QAAQ,OAAO;QAEhC;QACA,OAAO,MAAM,IAAI,CAAC;IACpB;IAEA,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;0CAAE;YACvB,MAAM,cAAsB,KAAK,CAAC,iBAAiB;YACnD,IAAI,YAAY,cAAc;gBAC5B,MAAM,QAAQ,YAAY,KAAK,CAAC;gBAChC,OAAO,MAAM,GAAG;sDAAC,CAAC,MAAM,IAAM,CAAC;4BAC7B,YAAY,oBAAoB;4BAChC,YAAY,MAAM,MAAM,MAAM,GAAG;wBACnC,CAAC;;YACH;YACA,IAAI,YAAY,SAAS;gBACvB,OAAO,YAAY,KAAK,CAAC,KAAK,GAAG;sDAAC,CAAC,MAAM,GAAG,MAAQ,CAAC;4BACnD,YAAY;gCAAC;6BAAK;4BAClB,YAAY,MAAM,IAAI,MAAM,GAAG;wBACjC,CAAC;;YACH;YACA,IAAI,YAAY,SAAS;gBACvB,OAAO,YAAY,KAAK,CAAC,MAAM,GAAG;sDAAC,CAAC,MAAM,GAAG,MAAQ,CAAC;4BACpD,YAAY;gCAAC;6BAAK;4BAClB,YAAY,MAAM,IAAI,MAAM,GAAG;wBACjC,CAAC;;YACH;YAEA,OAAO,YAAY,KAAK,CAAC,SAAS,GAAG;kDAAC,CAAC,MAAM,GAAG,MAAQ,CAAC;wBACvD,YAAY;4BAAC;yBAAK;wBAClB,YAAY,MAAM,IAAI,MAAM,GAAG;oBACjC,CAAC;;QACH;yCAAG;QAAC;QAAO;QAAkB;KAAQ;IAErC,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAChC,CAAC,OAAe;YACd,MAAM,QAAQ;YACd,IAAI,gBAAgB,SAAS,OAAO,QAAQ;YAC5C,IAAI,gBAAgB,QAClB,OAAO,CAAC,QAAQ,IAAI,KAAK,IAAI;YAC/B,IAAI,gBAAgB,UAAU;gBAC5B,MAAM,SAAS,KAAK,KAAK,CAAC,QAAQ;gBAClC,OAAO,KAAK,GAAG,CAAC,SAAS,SAAS;YACpC;YACA,IAAI,gBAAgB,UAAU;gBAC5B,MAAM,cAAc,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;gBAC/C,OAAO,KAAK,GAAG,CAAC,cAAc,SAAS;YACzC;YACA,OAAO,KAAK,GAAG,CAAC,AAAC,cAAyB,SAAS;QACrD;oDACA;QAAC;QAAa;KAAgB;IAGhC,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAClC,CAAC;YACC,oBAAoB;YACpB,IAAI,QAAQ,OAAO;QACrB;sDACA;QAAC;KAAO;IAGV,MAAM,OAAO,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0CAAE;YACvB,MAAM,YACJ,qBAAqB,MAAM,MAAM,GAAG,IAChC,OACE,IACA,mBACF,mBAAmB;YACzB,IAAI,cAAc,kBAAkB;gBAClC,kBAAkB;YACpB;QACF;yCAAG;QAAC;QAAkB,MAAM,MAAM;QAAE;QAAM;KAAkB;IAE5D,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8CAAE;YAC3B,MAAM,YACJ,qBAAqB,IACjB,OACE,MAAM,MAAM,GAAG,IACf,mBACF,mBAAmB;YACzB,IAAI,cAAc,kBAAkB;gBAClC,kBAAkB;YACpB;QACF;6CAAG;QAAC;QAAkB,MAAM,MAAM;QAAE;QAAM;KAAkB;IAE5D,MAAM,SAAS,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4CACvB,CAAC;YACC,MAAM,aAAa,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,OAAO,MAAM,MAAM,GAAG;YAC9D,IAAI,eAAe,kBAAkB;gBACnC,kBAAkB;YACpB;QACF;2CACA;QAAC,MAAM,MAAM;QAAE;QAAkB;KAAkB;IAGrD,MAAM,QAAQ,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2CAAE;YACxB,IAAI,qBAAqB,GAAG;gBAC1B,kBAAkB;YACpB;QACF;0CAAG;QAAC;QAAkB;KAAkB;IAExC,CAAA,GAAA,6JAAA,CAAA,sBAAmB,AAAD,EAChB;4CACA,IAAM,CAAC;gBACL;gBACA;gBACA;gBACA;YACF,CAAC;2CACD;QAAC;QAAM;QAAU;QAAQ;KAAM;IAGjC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,CAAC,MAAM;YACX,MAAM,aAAa,YAAY,MAAM;YACrC;0CAAO,IAAM,cAAc;;QAC7B;iCAAG;QAAC;QAAM;QAAkB;KAAK;IAEjC,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;QACV,WAAW,GACT,+CACA;QAED,GAAG,IAAI;QACR,MAAM;QACN,YAAY;;0BAEZ,6LAAC;gBAAK,WAAU;0BAAW,KAAK,CAAC,iBAAiB;;;;;;0BAClD,6LAAC,4LAAA,CAAA,kBAAe;gBACd,MAAM;gBACN,SAAS;0BAET,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;oBAEV,WAAW,GACT,YAAY,UACR,yBACA;oBAEN,MAAM;oBACN,eAAY;8BAEX,SAAS,GAAG,CAAC,CAAC,SAAS,WAAW;wBACjC,MAAM,qBAAqB,MACxB,KAAK,CAAC,GAAG,WACT,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,UAAU,CAAC,MAAM,EAAE;wBACvD,qBACE,6LAAC;4BAEC,WAAW,GAAG,eAAe;;gCAE5B,QAAQ,UAAU,CAAC,GAAG,CAAC,CAAC,MAAM,0BAC7B,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;wCAEV,SAAS;wCACT,SAAS;wCACT,MAAM;wCACN,YAAY;4CACV,GAAG,UAAU;4CACb,OAAO,gBACL,qBAAqB,WACrB,MAAM,MAAM,CACV,CAAC,KAAK,OAAS,MAAM,KAAK,UAAU,CAAC,MAAM,EAC3C;wCAGN;wCACA,WAAW,GAAG,gBAAgB;kDAE7B;uCAhBI;;;;;gCAmBR,QAAQ,UAAU,kBACjB,6LAAC;oCAAK,WAAU;8CAAiB;;;;;;;2BAzB9B;;;;;oBA6BX;mBA5CK;;;;;;;;;;;;;;;;AAiDf;;AAGF,aAAa,WAAW,GAAG;uCACZ"}}, {"offset": {"line": 1423, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1429, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/Work/Repositories/templgen/src/components/common/navbar/SearchBarWrapper.tsx"], "sourcesContent": ["\"use client\";\n\nimport RotatingText from \"@/components/ui/RotatingTextRef\";\nimport { cn } from \"@/lib/utils\";\nimport { AnimatePresence, motion } from \"framer-motion\";\nimport Image from \"next/image\";\nimport { useRef, useState } from \"react\";\nimport { FiX } from \"react-icons/fi\";\n\nexport default function SearchBarWrapper() {\n  const [query, setQuery] = useState(\"\");\n  const [focused, setFocused] = useState(false);\n  const inputRef = useRef<HTMLInputElement>(null);\n\n  const animatedSuggestions = [\n    \"Search Cosmos...\",\n    \"Explore subtle feelings...\",\n    \"Discover blockchain ideas...\",\n    \"Find NFTs & decentralization...\",\n    \"Dive into melancholy thoughts...\",\n  ];\n\n  const suggestions = [\n    \"subtle feelings of melancholy\",\n    \"cosmos\",\n    \"blockchain\",\n    \"NFT\",\n    \"decentralized\",\n  ].filter((s) => s.toLowerCase().includes(query.toLowerCase()));\n\n  const selectSuggestion = (item: string) => {\n    setQuery(item);\n    setFocused(false);\n    inputRef.current?.focus();\n  };\n\n  const onFocus = () => setFocused(true);\n  const onBlur = () => setTimeout(() => setFocused(false), 150);\n\n  return (\n    <>\n      {/* Overlay with smooth fade */}\n      <AnimatePresence>\n        {focused && (\n          <motion.div\n            key=\"overlay\"\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 0.3 }}\n            exit={{ opacity: 0 }}\n            transition={{ duration: 0.3 }}\n            className=\"fixed inset-0 bg-black z-40\"\n            onClick={() => {\n              setFocused(false);\n              inputRef.current?.blur();\n            }}\n          />\n        )}\n      </AnimatePresence>\n\n      {/* Search Box Container */}\n      <div\n        role=\"combobox\"\n        aria-expanded={focused}\n        aria-controls=\"search-listbox\"\n        aria-haspopup=\"listbox\"\n        className={cn(\n          \"relative w-full max-md:w-full\",\n          focused ? \"md:max-w-xl z-50 relative\" : \"md:max-w-lg\"\n        )}\n      >\n        <div\n          className={cn(\n            \"relative flex items-center rounded-[12px] px-3 py-2 border transition-all duration-300 ease-in-out bg-[var(--input-background)]\",\n            focused\n              ? \"ring-2 ring-[var(--highlight)] border-[var(--input-border-color)]\"\n              : \"border-[var(--input-border-color)]\"\n          )}\n          style={{ position: \"relative\", zIndex: 50 }}\n        >\n          <Image\n            src=\"/icons/star.svg\"\n            alt=\"Star Icon\"\n            width={20}\n            height={20}\n            className=\"mr-2 flex-shrink-0 transition-transform duration-300 ease-in-out hover:scale-125 focus-within:scale-125\"\n          />\n          <input\n            ref={inputRef}\n            type=\"search\"\n            role=\"searchbox\"\n            aria-autocomplete=\"list\"\n            aria-controls=\"search-listbox\"\n            placeholder={focused || query ? \"\" : \" \"}\n            className=\"flex-grow bg-transparent text-[var(--input-text)] placeholder-transparent outline-none text-sm transition-colors duration-500\"\n            value={query}\n            onChange={(e) => setQuery(e.target.value)}\n            onFocus={onFocus}\n            onBlur={onBlur}\n            autoComplete=\"off\"\n          />\n\n          {/* Static 'Try' + RotatingText with fade effect */}\n          {!focused && !query && (\n            <div\n              className=\"pointer-events-none absolute left-[38px] top-1/2 -translate-y-1/2 flex items-center space-x-1 text-[var(--paragraph)] text-sm select-none overflow-hidden whitespace-nowrap\"\n              style={{ width: \"calc(100% - 38px)\" }}\n            >\n              <span>Try</span>\n              <RotatingText\n                texts={animatedSuggestions}\n                rotationInterval={4000}\n                mainClassName=\"inline-block\"\n                staggerFrom=\"first\"\n                staggerDuration={0}\n                initial={{ opacity: 0 }}\n                animate={{ opacity: 1 }}\n                exit={{ opacity: 0 }}\n                transition={{ duration: 1 }}\n                animatePresenceMode=\"wait\"\n                splitBy=\"lines\"\n              />\n            </div>\n          )}\n\n          {query && (\n            <button\n              aria-label=\"Clear search\"\n              onClick={() => {\n                setQuery(\"\");\n                inputRef.current?.focus();\n              }}\n              className=\"ml-2 text-[var(--menu-color)] hover:text-[var(--highlight)]\"\n            >\n              <FiX />\n            </button>\n          )}\n        </div>\n\n        {focused && suggestions.length > 0 && (\n          <ul\n            id=\"search-listbox\"\n            role=\"listbox\"\n            className=\"absolute z-50 mt-1 max-h-48 w-full overflow-auto rounded-[12px] border border-[var(--input-border-color)] bg-[var(--input-background)] shadow-lg\"\n          >\n            {suggestions.map((item, i) => (\n              <li\n                key={i}\n                role=\"option\"\n                tabIndex={-1}\n                className=\"cursor-pointer px-4 py-2 text-sm text-[var(--paragraph)] hover:bg-[var(--highlight)] hover:text-[var(--active-text)]\"\n                onPointerDown={(e) => {\n                  e.preventDefault();\n                  selectSuggestion(item);\n                }}\n              >\n                {item}\n              </li>\n            ))}\n          </ul>\n        )}\n      </div>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AACA;AAFA;AAAA;AAGA;;;AAPA;;;;;;;AASe,SAAS;;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAE1C,MAAM,sBAAsB;QAC1B;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,cAAc;QAClB;QACA;QACA;QACA;QACA;KACD,CAAC,MAAM,CAAC,CAAC,IAAM,EAAE,WAAW,GAAG,QAAQ,CAAC,MAAM,WAAW;IAE1D,MAAM,mBAAmB,CAAC;QACxB,SAAS;QACT,WAAW;QACX,SAAS,OAAO,EAAE;IACpB;IAEA,MAAM,UAAU,IAAM,WAAW;IACjC,MAAM,SAAS,IAAM,WAAW,IAAM,WAAW,QAAQ;IAEzD,qBACE;;0BAEE,6LAAC,4LAAA,CAAA,kBAAe;0BACb,yBACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBAET,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAI;oBACxB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;oBACV,SAAS;wBACP,WAAW;wBACX,SAAS,OAAO,EAAE;oBACpB;mBATI;;;;;;;;;;0BAeV,6LAAC;gBACC,MAAK;gBACL,iBAAe;gBACf,iBAAc;gBACd,iBAAc;gBACd,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iCACA,UAAU,8BAA8B;;kCAG1C,6LAAC;wBACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mIACA,UACI,sEACA;wBAEN,OAAO;4BAAE,UAAU;4BAAY,QAAQ;wBAAG;;0CAE1C,6LAAC,gIAAA,CAAA,UAAK;gCACJ,KAAI;gCACJ,KAAI;gCACJ,OAAO;gCACP,QAAQ;gCACR,WAAU;;;;;;0CAEZ,6LAAC;gCACC,KAAK;gCACL,MAAK;gCACL,MAAK;gCACL,qBAAkB;gCAClB,iBAAc;gCACd,aAAa,WAAW,QAAQ,KAAK;gCACrC,WAAU;gCACV,OAAO;gCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;gCACxC,SAAS;gCACT,QAAQ;gCACR,cAAa;;;;;;4BAId,CAAC,WAAW,CAAC,uBACZ,6LAAC;gCACC,WAAU;gCACV,OAAO;oCAAE,OAAO;gCAAoB;;kDAEpC,6LAAC;kDAAK;;;;;;kDACN,6LAAC,8IAAA,CAAA,UAAY;wCACX,OAAO;wCACP,kBAAkB;wCAClB,eAAc;wCACd,aAAY;wCACZ,iBAAiB;wCACjB,SAAS;4CAAE,SAAS;wCAAE;wCACtB,SAAS;4CAAE,SAAS;wCAAE;wCACtB,MAAM;4CAAE,SAAS;wCAAE;wCACnB,YAAY;4CAAE,UAAU;wCAAE;wCAC1B,qBAAoB;wCACpB,SAAQ;;;;;;;;;;;;4BAKb,uBACC,6LAAC;gCACC,cAAW;gCACX,SAAS;oCACP,SAAS;oCACT,SAAS,OAAO,EAAE;gCACpB;gCACA,WAAU;0CAEV,cAAA,6LAAC,iJAAA,CAAA,MAAG;;;;;;;;;;;;;;;;oBAKT,WAAW,YAAY,MAAM,GAAG,mBAC/B,6LAAC;wBACC,IAAG;wBACH,MAAK;wBACL,WAAU;kCAET,YAAY,GAAG,CAAC,CAAC,MAAM,kBACtB,6LAAC;gCAEC,MAAK;gCACL,UAAU,CAAC;gCACX,WAAU;gCACV,eAAe,CAAC;oCACd,EAAE,cAAc;oCAChB,iBAAiB;gCACnB;0CAEC;+BATI;;;;;;;;;;;;;;;;;;AAiBrB;GA1JwB;KAAA"}}, {"offset": {"line": 1655, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1661, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/Work/Repositories/templgen/src/components/common/navbar/Navbar.tsx"], "sourcesContent": ["\"use client\";\n\nimport { AnimatePresence, motion } from \"framer-motion\";\nimport { useState } from \"react\";\nimport { FiMenu, FiX } from \"react-icons/fi\";\nimport AuthButtons from \"./AuthButtons\";\nimport LogoLink from \"./LogoLink\";\nimport NavItems from \"./NavItems\";\nimport SearchBarWrapper from \"./SearchBarWrapper\";\n\nexport default function Navbar() {\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);\n\n  return (\n    <header className=\"border-b border-[var(--border)] bg-[var(--background)]\">\n      <div className=\"container mx-auto px-4\">\n        {/* Mobile */}\n        <div className=\"flex items-center justify-between h-16 md:hidden gap-2\">\n          <LogoLink />\n          <SearchBarWrapper />\n          <button\n            aria-label={mobileMenuOpen ? \"Close menu\" : \"Open menu\"}\n            className=\"text-[var(--menu-color)]\"\n            onClick={() => setMobileMenuOpen(!mobileMenuOpen)}\n          >\n            {mobileMenuOpen ? <FiX size={24} /> : <FiMenu size={24} />}\n          </button>\n        </div>\n\n        <AnimatePresence>\n          {mobileMenuOpen && (\n            <motion.nav\n              key=\"mobile-menu\"\n              initial={{ x: \"100%\", opacity: 0 }}\n              animate={{ x: 0, opacity: 1 }}\n              exit={{ x: \"100%\", opacity: 0 }}\n              transition={{ duration: 0.3, ease: \"easeInOut\" }}\n              className=\"fixed top-0 left-0 w-full h-screen bg-[var(--background)] z-50 m-auto overflow-y-auto p-4 flex justify-center\"\n              aria-label=\"Mobile navigation\"\n            >\n              <div className=\"w-full max-w-md\">\n                <div className=\"flex justify-end mb-4\">\n                  <button\n                    aria-label=\"Close menu\"\n                    className=\"text-[var(--menu-color)]\"\n                    onClick={() => setMobileMenuOpen(false)}\n                  >\n                    <FiX size={28} />\n                  </button>\n                </div>\n                <NavItems isMobile />\n              </div>\n            </motion.nav>\n          )}\n        </AnimatePresence>\n\n        {/* Desktop */}\n        <div className=\"hidden md:flex h-16 items-center justify-between gap-4\">\n          <div className=\"flex items-center gap-6 flex-1\">\n            <LogoLink />\n            <NavItems />\n          </div>\n          <div className=\"flex-1 flex justify-center\">\n            <SearchBarWrapper />\n          </div>\n          <div className=\"flex items-center gap-2 flex-1 justify-end\">\n            <AuthButtons />\n          </div>\n        </div>\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AACA;AACA;AACA;AAJA;AAFA;AAAA;;;AAFA;;;;;;;;AAUe,SAAS;;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,qJAAA,CAAA,UAAQ;;;;;sCACT,6LAAC,6JAAA,CAAA,UAAgB;;;;;sCACjB,6LAAC;4BACC,cAAY,iBAAiB,eAAe;4BAC5C,WAAU;4BACV,SAAS,IAAM,kBAAkB,CAAC;sCAEjC,+BAAiB,6LAAC,iJAAA,CAAA,MAAG;gCAAC,MAAM;;;;;qDAAS,6LAAC,iJAAA,CAAA,SAAM;gCAAC,MAAM;;;;;;;;;;;;;;;;;8BAIxD,6LAAC,4LAAA,CAAA,kBAAe;8BACb,gCACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,SAAS;4BAAE,GAAG;4BAAQ,SAAS;wBAAE;wBACjC,SAAS;4BAAE,GAAG;4BAAG,SAAS;wBAAE;wBAC5B,MAAM;4BAAE,GAAG;4BAAQ,SAAS;wBAAE;wBAC9B,YAAY;4BAAE,UAAU;4BAAK,MAAM;wBAAY;wBAC/C,WAAU;wBACV,cAAW;kCAEX,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,cAAW;wCACX,WAAU;wCACV,SAAS,IAAM,kBAAkB;kDAEjC,cAAA,6LAAC,iJAAA,CAAA,MAAG;4CAAC,MAAM;;;;;;;;;;;;;;;;8CAGf,6LAAC,qJAAA,CAAA,UAAQ;oCAAC,QAAQ;;;;;;;;;;;;uBAlBhB;;;;;;;;;;8BAyBV,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qJAAA,CAAA,UAAQ;;;;;8CACT,6LAAC,qJAAA,CAAA,UAAQ;;;;;;;;;;;sCAEX,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,6JAAA,CAAA,UAAgB;;;;;;;;;;sCAEnB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,wJAAA,CAAA,UAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMxB;GA9DwB;KAAA"}}, {"offset": {"line": 1872, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1878, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/Work/Repositories/templgen/src/components/ui/backgroundEffect.tsx"], "sourcesContent": ["\"use client\";\n\nimport { memo } from \"react\";\n\nconst BackgroundEffect = () => {\n  return (\n    <div className=\"absolute hidden inset-0 isolate z-10 contain-strict max-md:hidden\">\n      <div className=\"absolute left-0 top-0 h-[1280px] w-[560px] -translate-y-[350px] -rotate-45 rounded-full bg-[radial-gradient(68.54%_68.72%_at_55.02%_31.46%,hsla(0,0%,85%,.08)_0,hsla(0,0%,55%,.02)_50%,hsla(0,0%,45%,0)_80%)]\"></div>\n      <div className=\"absolute left-0 top-0 h-[1280px] w-[240px] -rotate-45 rounded-full bg-[radial-gradient(50%_50%_at_50%_50%,hsla(0,0%,85%,.06)_0,hsla(0,0%,45%,.02)_80%,transparent_100%)] [translate:5%_-50%]\"></div>\n      <div className=\"absolute left-0 top-0 h-[1280px] w-[240px] -translate-y-[350px] -rotate-45 bg-[radial-gradient(50%_50%_at_50%_50%,hsla(0,0%,85%,.04)_0,hsla(0,0%,45%,.02)_80%,transparent_100%)]\"></div>\n    </div>\n  );\n};\n\n// Memoize the component to prevent unnecessary re-renders\nexport default memo(BackgroundEffect);\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIA,MAAM,mBAAmB;IACvB,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB;KARM;2DAWS,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,EAAE"}}, {"offset": {"line": 1926, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1932, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/Work/Repositories/templgen/src/components/ui/FloatingActionButton.tsx"], "sourcesContent": ["\"use client\";\n\nimport { AnimatePresence, motion } from \"framer-motion\";\nimport { Briefcase, FileText, Home, Mail, Menu, X } from \"lucide-react\";\nimport Link from \"next/link\";\nimport { useEffect, useState } from \"react\";\n\ninterface FloatingActionButtonProps {\n  threshold?: number;\n}\n\nexport default function FloatingActionButton({\n  threshold = 300,\n}: FloatingActionButtonProps) {\n  const [isVisible, setIsVisible] = useState(false);\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      if (window.scrollY > threshold) {\n        setIsVisible(true);\n      } else {\n        setIsVisible(false);\n        if (isMenuOpen) setIsMenuOpen(false);\n      }\n    };\n\n    window.addEventListener(\"scroll\", handleScroll);\n    return () => window.removeEventListener(\"scroll\", handleScroll);\n  }, [threshold, isMenuOpen]);\n\n  const menuItems = [\n    { icon: <Home className=\"h-5 w-5\" />, label: \"Home\", href: \"/\" },\n    { icon: <Briefcase className=\"h-5 w-5\" />, label: \"Work\", href: \"/#work\" },\n    {\n      icon: <FileText className=\"h-5 w-5\" />,\n      label: \"Projects\",\n      href: \"/projects\",\n    },\n    { icon: <Mail className=\"h-5 w-5\" />, label: \"Contact\", href: \"/contact\" },\n  ];\n\n  const handleMenuItemClick = (href: string) => {\n    setIsMenuOpen(false);\n    if (href === \"/#work\") {\n      const workSection = document.getElementById(\"work\");\n      if (workSection) {\n        workSection.scrollIntoView({ behavior: \"smooth\" });\n      }\n    }\n  };\n\n  return (\n    <AnimatePresence>\n      {isVisible && (\n        <motion.div\n          className=\"fixed bottom-6 right-6 z-30 flex flex-col items-end hidden\"\n          initial={{ opacity: 0, scale: 0.8 }}\n          animate={{ opacity: 1, scale: 1 }}\n          exit={{ opacity: 0, scale: 0.8 }}\n          transition={{ duration: 0.3 }}\n        >\n          {/* Menu items */}\n          <AnimatePresence>\n            {isMenuOpen && (\n              <motion.div\n                className=\"mb-4 flex flex-col gap-3\"\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                exit={{ opacity: 0, y: 20 }}\n                transition={{ duration: 0.3 }}\n              >\n                {menuItems.map((item, index) => (\n                  <motion.div\n                    key={item.label}\n                    initial={{ opacity: 0, x: 20 }}\n                    animate={{ opacity: 1, x: 0 }}\n                    transition={{ delay: index * 0.05 }}\n                  >\n                    <Link\n                      href={item.href}\n                      onClick={() => handleMenuItemClick(item.href)}\n                      className=\"flex items-center gap-2 rounded-full bg-[var(--card-background)] px-4 py-2 text-sm text-[var(--headline)] shadow-md hover:bg-[var(--link-color)] hover:text-white transition-colors\"\n                    >\n                      {item.icon}\n                      <span>{item.label}</span>\n                    </Link>\n                  </motion.div>\n                ))}\n              </motion.div>\n            )}\n          </AnimatePresence>\n\n          {/* Main button */}\n          <motion.button\n            className=\"flex h-12 w-12 items-center justify-center rounded-full bg-[var(--link-color)] text-white shadow-lg hover:bg-[var(--button)] transition-colors\"\n            onClick={() =>\n              isMenuOpen ? setIsMenuOpen(false) : setIsMenuOpen(true)\n            }\n            whileTap={{ scale: 0.9 }}\n            aria-label={isMenuOpen ? \"Close menu\" : \"Open menu\"}\n          >\n            {isMenuOpen ? (\n              <X className=\"h-5 w-5\" />\n            ) : (\n              <Menu className=\"h-5 w-5\" />\n            )}\n          </motion.button>\n\n          {/* Back to top button */}\n        </motion.div>\n      )}\n    </AnimatePresence>\n  );\n}\n"], "names": [], "mappings": ";;;;AAIA;AACA;AAFA;AAAA;AAAA;AAAA;AADA;AAAA;AACA;AAAA;;;AAHA;;;;;AAWe,SAAS,qBAAqB,EAC3C,YAAY,GAAG,EACW;;IAC1B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;YACR,MAAM;+DAAe;oBACnB,IAAI,OAAO,OAAO,GAAG,WAAW;wBAC9B,aAAa;oBACf,OAAO;wBACL,aAAa;wBACb,IAAI,YAAY,cAAc;oBAChC;gBACF;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC;kDAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;yCAAG;QAAC;QAAW;KAAW;IAE1B,MAAM,YAAY;QAChB;YAAE,oBAAM,6LAAC,sMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;YAAc,OAAO;YAAQ,MAAM;QAAI;QAC/D;YAAE,oBAAM,6LAAC,+MAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;YAAc,OAAO;YAAQ,MAAM;QAAS;QACzE;YACE,oBAAM,6LAAC,iNAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;YAC1B,OAAO;YACP,MAAM;QACR;QACA;YAAE,oBAAM,6LAAC,qMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;YAAc,OAAO;YAAW,MAAM;QAAW;KAC1E;IAED,MAAM,sBAAsB,CAAC;QAC3B,cAAc;QACd,IAAI,SAAS,UAAU;YACrB,MAAM,cAAc,SAAS,cAAc,CAAC;YAC5C,IAAI,aAAa;gBACf,YAAY,cAAc,CAAC;oBAAE,UAAU;gBAAS;YAClD;QACF;IACF;IAEA,qBACE,6LAAC,4LAAA,CAAA,kBAAe;kBACb,2BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,WAAU;YACV,SAAS;gBAAE,SAAS;gBAAG,OAAO;YAAI;YAClC,SAAS;gBAAE,SAAS;gBAAG,OAAO;YAAE;YAChC,MAAM;gBAAE,SAAS;gBAAG,OAAO;YAAI;YAC/B,YAAY;gBAAE,UAAU;YAAI;;8BAG5B,6LAAC,4LAAA,CAAA,kBAAe;8BACb,4BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,MAAM;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC1B,YAAY;4BAAE,UAAU;wBAAI;kCAE3B,UAAU,GAAG,CAAC,CAAC,MAAM,sBACpB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,OAAO,QAAQ;gCAAK;0CAElC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAM,KAAK,IAAI;oCACf,SAAS,IAAM,oBAAoB,KAAK,IAAI;oCAC5C,WAAU;;wCAET,KAAK,IAAI;sDACV,6LAAC;sDAAM,KAAK,KAAK;;;;;;;;;;;;+BAXd,KAAK,KAAK;;;;;;;;;;;;;;;8BAoBzB,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oBACZ,WAAU;oBACV,SAAS,IACP,aAAa,cAAc,SAAS,cAAc;oBAEpD,UAAU;wBAAE,OAAO;oBAAI;oBACvB,cAAY,aAAa,eAAe;8BAEvC,2BACC,6LAAC,+LAAA,CAAA,IAAC;wBAAC,WAAU;;;;;6CAEb,6LAAC,qMAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;AAS9B;GAvGwB;KAAA"}}, {"offset": {"line": 2162, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2168, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/Work/Repositories/templgen/src/contexts/NavbarContext.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { createContext, useContext, useState, ReactNode } from \"react\";\n\ntype NavbarContextType = {\n  mobileMenuOpen: boolean;\n  setMobileMenuOpen: (open: boolean) => void;\n  openDropdownIndex: number | null;\n  setOpenDropdownIndex: (index: number | null) => void;\n};\n\nconst NavbarContext = createContext<NavbarContextType | undefined>(undefined);\n\nexport function NavbarProvider({ children }: { children: ReactNode }) {\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);\n  const [openDropdownIndex, setOpenDropdownIndex] = useState<number | null>(\n    null\n  );\n\n  return (\n    <NavbarContext.Provider\n      value={{\n        mobileMenuOpen,\n        setMobileMenuOpen,\n        openDropdownIndex,\n        setOpenDropdownIndex,\n      }}\n    >\n      {children}\n    </NavbarContext.Provider>\n  );\n}\n\nexport function useNavbarContext() {\n  const context = useContext(NavbarContext);\n  if (!context) {\n    throw new Error(\"useNavbarContext must be used within a NavbarProvider\");\n  }\n  return context;\n}\n"], "names": [], "mappings": ";;;;;AAEA;;;AAFA;;AAWA,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAiC;AAE5D,SAAS,eAAe,EAAE,QAAQ,EAA2B;;IAClE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EACvD;IAGF,qBACE,6LAAC,cAAc,QAAQ;QACrB,OAAO;YACL;YACA;YACA;YACA;QACF;kBAEC;;;;;;AAGP;GAlBgB;KAAA;AAoBT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB"}}, {"offset": {"line": 2213, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2219, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/Work/Repositories/templgen/src/app/layout.tsx"], "sourcesContent": ["'use client';\n\nimport type { Metadata, Viewport } from 'next';\nimport './globals.css';\n\nimport Footer from '@/components/common/Footer';\nimport Navbar from '@/components/common/navbar/Navbar';\nimport BackgroundEffect from '@/components/ui/backgroundEffect';\nimport FloatingActionButton from '@/components/ui/FloatingActionButton';\nimport { NavbarProvider } from '@/contexts/NavbarContext';\nimport { Analytics } from '@vercel/analytics/react';\nimport { SpeedInsights } from '@vercel/speed-insights/next';\nimport { Toaster } from 'sonner';\nimport { usePathname } from 'next/navigation';\n\nconst domain = 'templgen.com';\nconst websitePath = {\n  main: `https://${domain}`,\n};\nconst webImage = `${websitePath.main}/images/profile.jpg`;\nconst email = '<EMAIL>';\n\nconst keywords = [\n  'templgen',\n  'website templates',\n  'mobile templates',\n  'React templates',\n  'Next.js templates',\n  'MERN templates',\n  'UI templates',\n  'frontend templates',\n  'backend templates',\n  'software templates',\n  'web development',\n  'template marketplace',\n  'responsive templates',\n];\n\nexport const metadata: Metadata = {\n  metadataBase: new URL(websitePath.main),\n  title: {\n    template: 'TemplGen - %s',\n    default: 'TemplGen - Website & Mobile Templates Marketplace',\n  },\n  description:\n    'TemplGen offers premium website and mobile templates for developers and businesses. Explore React, Next.js, and MERN templates to accelerate your projects.',\n  keywords: keywords.join(', '),\n  authors: [{ name: 'TemplGen', url: websitePath.main }],\n  creator: 'TemplGen',\n  publisher: 'TemplGen',\n  formatDetection: {\n    email: false,\n    address: false,\n    telephone: false,\n  },\n  alternates: {\n    canonical: websitePath.main,\n    languages: {\n      en: `${websitePath.main}/en`,\n      ar: `${websitePath.main}/ar`,\n    },\n  },\n  openGraph: {\n    type: 'website',\n    locale: 'en_US',\n    alternateLocale: 'ar_SA',\n    title: 'TemplGen Marketplace',\n    description:\n      'Browse and purchase premium website and mobile templates from TemplGen. High-quality React, Next.js, and MERN templates ready to use.',\n    url: websitePath.main,\n    siteName: 'TemplGen',\n    images: [\n      {\n        url: webImage,\n        width: 400,\n        height: 400,\n        alt: 'TemplGen Logo',\n      },\n    ],\n    countryName: 'Global',\n    emails: [email],\n  },\n  twitter: {\n    card: 'summary_large_image',\n    title: 'TemplGen Marketplace',\n    description:\n      'Discover and buy premium templates for web and mobile development at TemplGen.',\n    images: webImage,\n    creator: '@templgen',\n  },\n  robots: {\n    index: true,\n    follow: true,\n    googleBot: {\n      index: true,\n      follow: true,\n      'max-image-preview': 'large',\n      'max-snippet': -1,\n    },\n  },\n  verification: {\n    // Add verification tokens here if any\n  },\n};\n\nexport const viewport: Viewport = {\n  themeColor: '#16161a',\n  width: 'device-width',\n  initialScale: 1,\n  maximumScale: 5,\n};\n\nexport default function RootLayout({ children }: { children: React.ReactNode }) {\n  const pathname = usePathname();\n  const isAuthRoute = pathname?.startsWith('/auth');\n\n  return (\n    <html lang=\"en\" dir=\"ltr\">\n      <head>\n        <link rel=\"preconnect\" href=\"https://fonts.googleapis.com\" />\n        <link\n          rel=\"preconnect\"\n          href=\"https://fonts.gstatic.com\"\n          crossOrigin=\"anonymous\"\n        />\n        <meta name=\"apple-mobile-web-app-title\" content=\"TemplGen\" />\n\n        {/* Grammarly attribute cleaner (kept as-is) */}\n        <script\n          dangerouslySetInnerHTML={{\n            __html: `\n              (function() {\n                const observer = new MutationObserver((mutations) => {\n                  mutations.forEach(({ target }) => {\n                    if (target.nodeType === 1) {\n                      const elem = target;\n                      if (elem.hasAttribute('data-gr-ext-installed') ||\n                          elem.hasAttribute('data-new-gr-c-s-check-loaded')) {\n                        elem.removeAttribute('data-gr-ext-installed');\n                        elem.removeAttribute('data-new-gr-c-s-check-loaded');\n                      }\n                    }\n                  });\n                });\n                observer.observe(document.documentElement, {\n                  attributes: true,\n                  subtree: true,\n                  attributeFilter: ['data-gr-ext-installed', 'data-new-gr-c-s-check-loaded']\n                });\n              })();\n            `,\n          }}\n        />\n\n        {/* JSON-LD Organization schema (kept as-is) */}\n        <script\n          type=\"application/ld+json\"\n          dangerouslySetInnerHTML={{\n            __html: JSON.stringify({\n              '@context': 'https://schema.org',\n              '@type': 'Organization',\n              name: 'TemplGen',\n              url: websitePath.main,\n              logo: webImage,\n              sameAs: [\n                'https://github.com/templgen',\n                'https://www.linkedin.com/company/templgen',\n                'https://www.youtube.com/@templgen',\n              ],\n              description:\n                'Marketplace for premium website and mobile templates including React, Next.js, and MERN stacks.',\n            }),\n          }}\n        />\n      </head>\n\n      <body className=\"flex relative dark flex-col min-h-screen bg-[var(--background)] text-[var(--main)]\">\n        <SpeedInsights />\n        <Analytics />\n        <Toaster />\n\n        <NavbarProvider>\n          {!isAuthRoute && <Navbar />}\n          {!isAuthRoute && <BackgroundEffect />}\n\n          <main className=\"z-40 max-md:z-30 mx-auto w-full flex-grow\">\n            {children}\n          </main>\n\n          {!isAuthRoute && <Footer />}\n        </NavbarProvider>\n\n        {!isAuthRoute && <FloatingActionButton threshold={400} />}\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAbA;;;;;;;;;;;AAeA,MAAM,SAAS;AACf,MAAM,cAAc;IAClB,MAAM,CAAC,QAAQ,EAAE,QAAQ;AAC3B;AACA,MAAM,WAAW,GAAG,YAAY,IAAI,CAAC,mBAAmB,CAAC;AACzD,MAAM,QAAQ;AAEd,MAAM,WAAW;IACf;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEM,MAAM,WAAqB;IAChC,cAAc,IAAI,IAAI,YAAY,IAAI;IACtC,OAAO;QACL,UAAU;QACV,SAAS;IACX;IACA,aACE;IACF,UAAU,SAAS,IAAI,CAAC;IACxB,SAAS;QAAC;YAAE,MAAM;YAAY,KAAK,YAAY,IAAI;QAAC;KAAE;IACtD,SAAS;IACT,WAAW;IACX,iBAAiB;QACf,OAAO;QACP,SAAS;QACT,WAAW;IACb;IACA,YAAY;QACV,WAAW,YAAY,IAAI;QAC3B,WAAW;YACT,IAAI,GAAG,YAAY,IAAI,CAAC,GAAG,CAAC;YAC5B,IAAI,GAAG,YAAY,IAAI,CAAC,GAAG,CAAC;QAC9B;IACF;IACA,WAAW;QACT,MAAM;QACN,QAAQ;QACR,iBAAiB;QACjB,OAAO;QACP,aACE;QACF,KAAK,YAAY,IAAI;QACrB,UAAU;QACV,QAAQ;YACN;gBACE,KAAK;gBACL,OAAO;gBACP,QAAQ;gBACR,KAAK;YACP;SACD;QACD,aAAa;QACb,QAAQ;YAAC;SAAM;IACjB;IACA,SAAS;QACP,MAAM;QACN,OAAO;QACP,aACE;QACF,QAAQ;QACR,SAAS;IACX;IACA,QAAQ;QACN,OAAO;QACP,QAAQ;QACR,WAAW;YACT,OAAO;YACP,QAAQ;YACR,qBAAqB;YACrB,eAAe,CAAC;QAClB;IACF;IACA,cAAc;IAEd;AACF;AAEO,MAAM,WAAqB;IAChC,YAAY;IACZ,OAAO;IACP,cAAc;IACd,cAAc;AAChB;AAEe,SAAS,WAAW,EAAE,QAAQ,EAAiC;;IAC5E,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,cAAc,UAAU,WAAW;IAEzC,qBACE,6LAAC;QAAK,MAAK;QAAK,KAAI;;0BAClB,6LAAC;;kCACC,6LAAC;wBAAK,KAAI;wBAAa,MAAK;;;;;;kCAC5B,6LAAC;wBACC,KAAI;wBACJ,MAAK;wBACL,aAAY;;;;;;kCAEd,6LAAC;wBAAK,MAAK;wBAA6B,SAAQ;;;;;;kCAGhD,6LAAC;wBACC,yBAAyB;4BACvB,QAAQ,CAAC;;;;;;;;;;;;;;;;;;;;YAoBT,CAAC;wBACH;;;;;;kCAIF,6LAAC;wBACC,MAAK;wBACL,yBAAyB;4BACvB,QAAQ,KAAK,SAAS,CAAC;gCACrB,YAAY;gCACZ,SAAS;gCACT,MAAM;gCACN,KAAK,YAAY,IAAI;gCACrB,MAAM;gCACN,QAAQ;oCACN;oCACA;oCACA;iCACD;gCACD,aACE;4BACJ;wBACF;;;;;;;;;;;;0BAIJ,6LAAC;gBAAK,WAAU;;kCACd,6LAAC,0KAAA,CAAA,gBAAa;;;;;kCACd,6LAAC,mKAAA,CAAA,YAAS;;;;;kCACV,6LAAC,2IAAA,CAAA,UAAO;;;;;kCAER,6LAAC,oIAAA,CAAA,iBAAc;;4BACZ,CAAC,6BAAe,6LAAC,mJAAA,CAAA,UAAM;;;;;4BACvB,CAAC,6BAAe,6LAAC,+IAAA,CAAA,UAAgB;;;;;0CAElC,6LAAC;gCAAK,WAAU;0CACb;;;;;;4BAGF,CAAC,6BAAe,6LAAC,yIAAA,CAAA,UAAM;;;;;;;;;;;oBAGzB,CAAC,6BAAe,6LAAC,mJAAA,CAAA,UAAoB;wBAAC,WAAW;;;;;;;;;;;;;;;;;;AAI1D;GApFwB;;QACL,qIAAA,CAAA,cAAW;;;KADN"}}, {"offset": {"line": 2515, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}
{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/Work/Repositories/templgen/src/components/layouts/website/IconGoogle.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\n\nexport default function IconGoogle({ className }: { className?: string }) {\n  return (\n    <svg\n      className={className}\n      viewBox=\"0 0 48 48\"\n      aria-hidden=\"true\"\n      fill=\"none\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n    >\n      <path fill=\"#FFC107\" d=\"M43.6 20.5H42V20H24v8h11.3C33.9 32.6 29.3 36 24 36c-6.6 0-12-5.4-12-12S17.4 12 24 12c3 0 5.7 1.1 7.7 3l5.7-5.7C33.9 6.1 29.2 4 24 4 12.9 4 4 12.9 4 24s8.9 20 20 20 20-8.9 20-20c0-1.2-.1-2.3-.4-3.5z\"/>\n      <path fill=\"#FF3D00\" d=\"M6.3 14.7l6.6 4.8C14.6 16.2 18.9 12 24 12c3 0 5.7 1.1 7.7 3l5.7-5.7C33.9 6.1 29.2 4 24 4c-7.7 0-14.3 4.3-17.7 10.7z\"/>\n      <path fill=\"#4CAF50\" d=\"M24 44c5.2 0 9.9-2 13.3-5.3l-6.2-5.1C29.1 35.6 26.7 36 24 36c-5.3 0-9.9-3.4-11.3-8H6.3l-6.6 5C3.7 39.6 13 44 24 44z\"/>\n      <path fill=\"#1976D2\" d=\"M43.6 20.5H42V20H24v8h11.3c-1.3 3.8-5.1 6.5-9.3 6.5-5.3 0-9.9-3.4-11.3-8H6.3l-6.6 5C3.7 39.6 13 44 24 44c11 0 20-9 20-20 0-1.2-.1-2.3-.4-3.5z\"/>\n    </svg>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAIe,SAAS,WAAW,EAAE,SAAS,EAA0B;IACtE,qBACE,6LAAC;QACC,WAAW;QACX,SAAQ;QACR,eAAY;QACZ,MAAK;QACL,OAAM;;0BAEN,6LAAC;gBAAK,MAAK;gBAAU,GAAE;;;;;;0BACvB,6LAAC;gBAAK,MAAK;gBAAU,GAAE;;;;;;0BACvB,6LAAC;gBAAK,MAAK;gBAAU,GAAE;;;;;;0BACvB,6LAAC;gBAAK,MAAK;gBAAU,GAAE;;;;;;;;;;;;AAG7B;KAfwB"}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 72, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/Work/Repositories/templgen/src/app/auth/signup/page.tsx"], "sourcesContent": ["'use client';\n\nimport IconGoogle from '@/components/layouts/website/IconGoogle';\nimport React, { useState } from 'react';\n\nexport default function SignupPage() {\n  const [email, setEmail] = useState('');\n\n  const onSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    // TODO: handle email sign-up\n    // e.g., call API route: fetch('/api/signup', { method: 'POST', body: JSON.stringify({ email }) })\n  };\n\n  const continueWithGoogle = async () => {\n    // TODO: hook up to your auth provider (NextAuth.js / custom OAuth)\n  };\n\n  return (\n    <main className=\"min-h-screen grid md:grid-cols-2 bg-[var(--background)] text-[var(--main)]\">\n      {/* Left: Form */}\n      <section className=\"flex flex-col items-center justify-center px-6 py-12 md:px-12\">\n        {/* Logo + Title */}\n        <div className=\"w-full max-w-md\">\n          <div className=\"mb-6 flex items-center gap-3\">\n            {/* Minimal logo block simulating the reference */}\n            <div\n              className=\"w-6 h-8 rounded-sm\"\n              style={{ background: 'linear-gradient(180deg, var(--highlight), var(--gradient1))' }}\n            />\n            <h1 className=\"text-xl font-medium text-[var(--headline)]\">Welcome to Framer</h1>\n          </div>\n\n          {/* Google button */}\n          <button\n            onClick={continueWithGoogle}\n            className=\"w-full inline-flex items-center justify-center gap-3 rounded-lg px-4 py-3 font-medium transition-colors\n                       bg-[var(--button)] text-[var(--button-text)]\n                       hover:bg-[var(--highlight)]\n                       border border-[var(--button-border)]\"\n          >\n            <IconGoogle className=\"w-5 h-5\" />\n            <span>Continue with Google</span>\n          </button>\n\n          {/* Divider */}\n          <div className=\"relative my-6\">\n            <div className=\"absolute inset-0 flex items-center\">\n              <div className=\"w-full border-t border-[var(--border)]\" />\n            </div>\n            <div className=\"relative flex justify-center\">\n              <span className=\"px-3 text-sm text-[var(--paragraph)] bg-[var(--background)]\">or</span>\n            </div>\n          </div>\n\n          {/* Email form */}\n          <form onSubmit={onSubmit} noValidate>\n            <label className=\"block text-sm mb-2 text-[var(--paragraph)]\" htmlFor=\"email\">\n              Email\n            </label>\n            <input\n              id=\"email\"\n              type=\"email\"\n              required\n              autoComplete=\"email\"\n              placeholder=\"Email\"\n              value={email}\n              onChange={(e) => setEmail(e.target.value)}\n              className=\"w-full rounded-lg px-4 py-3 outline-none\n                         bg-[var(--input-background)] text-[var(--input-text)]\n                         placeholder-[var(--paragraph)]\n                         border border-[var(--input-border-color)]\n                         focus:border-[var(--selectBox-border)]\"\n              style={{ boxShadow: 'inset 0 0 0 9999px rgba(0,0,0,0)' }}\n            />\n\n            <button\n              type=\"submit\"\n              className=\"mt-5 w-full rounded-lg px-4 py-3 font-medium\n                         bg-[var(--button)] text-[var(--button-text)]\n                         hover:bg-[var(--highlight)]\n                         border border-[var(--button-border)]\n                         transition-[background,border-color] duration-150\"\n            >\n              Continue\n            </button>\n          </form>\n\n          {/* ReCAPTCHA container (invisible placeholder) */}\n          <div id=\"recaptcha-container\" className=\"sr-only\" aria-hidden=\"true\" />\n\n          {/* Footer note */}\n          <p className=\"mt-6 text-sm text-[var(--paragraph)]\">\n            By continuing, consent to our Terms and Privacy Policy.\n          </p>\n        </div>\n      </section>\n\n      {/* Right: Visual iframe (optional) */}\n      <section className=\"hidden md:block bg-[var(--card-background)]\">\n        <iframe\n          title=\"visual\"\n          src=\"https://signup2.framer.website\"\n          className=\"w-full h-full\"\n          style={{ border: '0', background: 'transparent' }}\n        />\n      </section>\n    </main>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKe,SAAS;;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,WAAW,CAAC;QAChB,EAAE,cAAc;IAChB,6BAA6B;IAC7B,kGAAkG;IACpG;IAEA,MAAM,qBAAqB;IACzB,mEAAmE;IACrE;IAEA,qBACE,6LAAC;QAAK,WAAU;;0BAEd,6LAAC;gBAAQ,WAAU;0BAEjB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCACC,WAAU;oCACV,OAAO;wCAAE,YAAY;oCAA8D;;;;;;8CAErF,6LAAC;oCAAG,WAAU;8CAA6C;;;;;;;;;;;;sCAI7D,6LAAC;4BACC,SAAS;4BACT,WAAU;;8CAKV,6LAAC,yJAAA,CAAA,UAAU;oCAAC,WAAU;;;;;;8CACtB,6LAAC;8CAAK;;;;;;;;;;;;sCAIR,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;;;;;;;;;;8CAEjB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAK,WAAU;kDAA8D;;;;;;;;;;;;;;;;;sCAKlF,6LAAC;4BAAK,UAAU;4BAAU,UAAU;;8CAClC,6LAAC;oCAAM,WAAU;oCAA6C,SAAQ;8CAAQ;;;;;;8CAG9E,6LAAC;oCACC,IAAG;oCACH,MAAK;oCACL,QAAQ;oCACR,cAAa;oCACb,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;oCACxC,WAAU;oCAKV,OAAO;wCAAE,WAAW;oCAAmC;;;;;;8CAGzD,6LAAC;oCACC,MAAK;oCACL,WAAU;8CAKX;;;;;;;;;;;;sCAMH,6LAAC;4BAAI,IAAG;4BAAsB,WAAU;4BAAU,eAAY;;;;;;sCAG9D,6LAAC;4BAAE,WAAU;sCAAuC;;;;;;;;;;;;;;;;;0BAOxD,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBACC,OAAM;oBACN,KAAI;oBACJ,WAAU;oBACV,OAAO;wBAAE,QAAQ;wBAAK,YAAY;oBAAc;;;;;;;;;;;;;;;;;AAK1D;GAxGwB;KAAA"}}, {"offset": {"line": 298, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}
{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/Work/Repositories/templgen/src/app/loading.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\n\nexport default function LoadingPage() {\n  const direction = \"en\";\n\n  return (\n    <div className=\"fixed inset-0 z-50 m-auto flex h-[100vh] w-[100vw] flex-col items-center justify-center overflow-hidden bg-[var(--background)] font-semibold text-[var(--headline)]\">\n      <div dir={direction} className=\"flex items-center justify-center gap-2\">\n        <motion.span\n          className=\"text-[var(--headline)] font-normal\"\n          animate={{ opacity: [0.3, 1, 0.3] }}\n          transition={{ duration: 1.5, repeat: Infinity, ease: \"easeInOut\" }}\n        >\n          Loading...\n        </motion.span>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,MAAM,YAAY;IAElB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,KAAK;YAAW,WAAU;sBAC7B,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;gBACV,WAAU;gBACV,SAAS;oBAAE,SAAS;wBAAC;wBAAK;wBAAG;qBAAI;gBAAC;gBAClC,YAAY;oBAAE,UAAU;oBAAK,QAAQ;oBAAU,MAAM;gBAAY;0BAClE;;;;;;;;;;;;;;;;AAMT;KAhBwB"}}, {"offset": {"line": 59, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}
(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["static/chunks/src_413b3a._.js", {

"[project]/src/components/common/CookiesToast.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>CookiesToast)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$components$2f$AnimatePresence$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-client] (ecmascript)");
;
var _s = __turbopack_refresh__.signature();
'use client';
;
;
function CookiesToast({ defaultOpen = true, autoHideMs }) {
    _s();
    const [open, setOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(defaultOpen);
    // Optional auto-hide timer
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "CookiesToast.useEffect": ()=>{
            if (!open || !autoHideMs) return;
            const t = setTimeout({
                "CookiesToast.useEffect.t": ()=>setOpen(false)
            }["CookiesToast.useEffect.t"], autoHideMs);
            return ({
                "CookiesToast.useEffect": ()=>clearTimeout(t)
            })["CookiesToast.useEffect"];
        }
    }["CookiesToast.useEffect"], [
        open,
        autoHideMs
    ]);
    const close = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "CookiesToast.useCallback[close]": ()=>setOpen(false)
    }["CookiesToast.useCallback[close]"], []);
    // Optional: close with Esc
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "CookiesToast.useEffect": ()=>{
            if (!open) return;
            const onKey = {
                "CookiesToast.useEffect.onKey": (e)=>{
                    if (e.key === 'Escape') setOpen(false);
                }
            }["CookiesToast.useEffect.onKey"];
            window.addEventListener('keydown', onKey);
            return ({
                "CookiesToast.useEffect": ()=>window.removeEventListener('keydown', onKey)
            })["CookiesToast.useEffect"];
        }
    }["CookiesToast.useEffect"], [
        open
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$components$2f$AnimatePresence$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AnimatePresence"], {
        children: open && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
            initial: {
                opacity: 0
            },
            animate: {
                opacity: 1
            },
            exit: {
                opacity: 0
            },
            transition: {
                type: 'spring',
                stiffness: 420,
                damping: 28
            },
            style: {
                position: 'fixed',
                left: 16,
                bottom: 16,
                zIndex: 9999,
                pointerEvents: 'none'
            },
            "aria-live": "polite",
            className: "max-w-[360px]",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "pointer-events-auto flex   rounded-xl shadow-lg flex-row p-4",
                role: "dialog",
                "aria-label": "Cookies notice",
                style: {
                    // Use tokens from :root
                    background: 'var(--card-background)',
                    color: 'var(--main)',
                    boxShadow: '0 10px 15px -3px rgba(0,0,0,0.3), 0 4px 6px -4px rgba(0,0,0,0.3)',
                    border: '1px solid var(--card-border-color)',
                    opacity: 1,
                    transform: 'none'
                },
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-body-sm",
                        style: {
                            margin: 0,
                            lineHeight: 1.5,
                            color: 'var(--card-paragraph)',
                            flex: 1
                        },
                        children: "We use cookies to personalize content, run ads, and analyze traffic."
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/CookiesToast.tsx",
                        lineNumber: 66,
                        columnNumber: 13
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: close,
                        className: "",
                        style: {
                            background: 'var(--button2)',
                            color: 'var(--button-text)',
                            border: '1px solid var(--button-border)',
                            borderRadius: 10,
                            padding: '8px 12px',
                            fontSize: 14,
                            fontWeight: 600,
                            cursor: 'pointer',
                            whiteSpace: 'nowrap',
                            transition: 'background 150ms ease, color 150ms ease, border-color 150ms ease'
                        },
                        onMouseEnter: (e)=>{
                            e.currentTarget.style.background = 'var(--button)';
                            e.currentTarget.style.color = 'var(--button-text)';
                            e.currentTarget.style.borderColor = 'var(--button)';
                        },
                        onMouseLeave: (e)=>{
                            e.currentTarget.style.background = 'var(--button2)';
                            e.currentTarget.style.color = 'var(--button-text)';
                            e.currentTarget.style.borderColor = 'var(--button-border)';
                        },
                        children: "Okay"
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/CookiesToast.tsx",
                        lineNumber: 78,
                        columnNumber: 13
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/common/CookiesToast.tsx",
                lineNumber: 52,
                columnNumber: 11
            }, this)
        }, "cookies-toast", false, {
            fileName: "[project]/src/components/common/CookiesToast.tsx",
            lineNumber: 36,
            columnNumber: 9
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/common/CookiesToast.tsx",
        lineNumber: 34,
        columnNumber: 5
    }, this);
}
_s(CookiesToast, "573f8HvwqhxahKpH7XpRRX0nXNo=");
_c = CookiesToast;
var _c;
__turbopack_refresh__.register(_c, "CookiesToast");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/page.tsx [app-rsc] (ecmascript, Next.js server component, client modules)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
}}),
}]);

//# sourceMappingURL=src_413b3a._.js.map
{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/src/components/ui/backgroundEffect.tsx"], "sourcesContent": ["\"use client\";\n\nimport { memo } from \"react\";\n\nconst BackgroundEffect = () => {\n  return (\n    <div className=\"absolute hidden inset-0 isolate z-10 contain-strict max-md:hidden\">\n      <div className=\"absolute left-0 top-0 h-[1280px] w-[560px] -translate-y-[350px] -rotate-45 rounded-full bg-[radial-gradient(68.54%_68.72%_at_55.02%_31.46%,hsla(0,0%,85%,.08)_0,hsla(0,0%,55%,.02)_50%,hsla(0,0%,45%,0)_80%)]\"></div>\n      <div className=\"absolute left-0 top-0 h-[1280px] w-[240px] -rotate-45 rounded-full bg-[radial-gradient(50%_50%_at_50%_50%,hsla(0,0%,85%,.06)_0,hsla(0,0%,45%,.02)_80%,transparent_100%)] [translate:5%_-50%]\"></div>\n      <div className=\"absolute left-0 top-0 h-[1280px] w-[240px] -translate-y-[350px] -rotate-45 bg-[radial-gradient(50%_50%_at_50%_50%,hsla(0,0%,85%,.04)_0,hsla(0,0%,45%,.02)_80%,transparent_100%)]\"></div>\n    </div>\n  );\n};\n\n// Memoize the component to prevent unnecessary re-renders\nexport default memo(BackgroundEffect);\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIA,MAAM,mBAAmB;IACvB,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB;KARM;2DAWS,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,EAAE"}}, {"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 61, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/src/components/ui/FloatingActionButton.tsx"], "sourcesContent": ["\"use client\";\n\nimport { AnimatePresence, motion } from \"framer-motion\";\nimport { Briefcase, FileText, Home, Mail, Menu, X } from \"lucide-react\";\nimport Link from \"next/link\";\nimport { useEffect, useState } from \"react\";\n\ninterface FloatingActionButtonProps {\n  threshold?: number;\n}\n\nexport default function FloatingActionButton({\n  threshold = 300,\n}: FloatingActionButtonProps) {\n  const [isVisible, setIsVisible] = useState(false);\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      if (window.scrollY > threshold) {\n        setIsVisible(true);\n      } else {\n        setIsVisible(false);\n        if (isMenuOpen) setIsMenuOpen(false);\n      }\n    };\n\n    window.addEventListener(\"scroll\", handleScroll);\n    return () => window.removeEventListener(\"scroll\", handleScroll);\n  }, [threshold, isMenuOpen]);\n\n  const menuItems = [\n    { icon: <Home className=\"h-5 w-5\" />, label: \"Home\", href: \"/\" },\n    { icon: <Briefcase className=\"h-5 w-5\" />, label: \"Work\", href: \"/#work\" },\n    {\n      icon: <FileText className=\"h-5 w-5\" />,\n      label: \"Projects\",\n      href: \"/projects\",\n    },\n    { icon: <Mail className=\"h-5 w-5\" />, label: \"Contact\", href: \"/contact\" },\n  ];\n\n  const handleMenuItemClick = (href: string) => {\n    setIsMenuOpen(false);\n    if (href === \"/#work\") {\n      const workSection = document.getElementById(\"work\");\n      if (workSection) {\n        workSection.scrollIntoView({ behavior: \"smooth\" });\n      }\n    }\n  };\n\n  return (\n    <AnimatePresence>\n      {isVisible && (\n        <motion.div\n          className=\"fixed bottom-6 right-6 z-30 flex flex-col items-end hidden\"\n          initial={{ opacity: 0, scale: 0.8 }}\n          animate={{ opacity: 1, scale: 1 }}\n          exit={{ opacity: 0, scale: 0.8 }}\n          transition={{ duration: 0.3 }}\n        >\n          {/* Menu items */}\n          <AnimatePresence>\n            {isMenuOpen && (\n              <motion.div\n                className=\"mb-4 flex flex-col gap-3\"\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                exit={{ opacity: 0, y: 20 }}\n                transition={{ duration: 0.3 }}\n              >\n                {menuItems.map((item, index) => (\n                  <motion.div\n                    key={item.label}\n                    initial={{ opacity: 0, x: 20 }}\n                    animate={{ opacity: 1, x: 0 }}\n                    transition={{ delay: index * 0.05 }}\n                  >\n                    <Link\n                      href={item.href}\n                      onClick={() => handleMenuItemClick(item.href)}\n                      className=\"flex items-center gap-2 rounded-full bg-[var(--card-background)] px-4 py-2 text-sm text-[var(--headline)] shadow-md hover:bg-[var(--link-color)] hover:text-white transition-colors\"\n                    >\n                      {item.icon}\n                      <span>{item.label}</span>\n                    </Link>\n                  </motion.div>\n                ))}\n              </motion.div>\n            )}\n          </AnimatePresence>\n\n          {/* Main button */}\n          <motion.button\n            className=\"flex h-12 w-12 items-center justify-center rounded-full bg-[var(--link-color)] text-white shadow-lg hover:bg-[var(--button)] transition-colors\"\n            onClick={() =>\n              isMenuOpen ? setIsMenuOpen(false) : setIsMenuOpen(true)\n            }\n            whileTap={{ scale: 0.9 }}\n            aria-label={isMenuOpen ? \"Close menu\" : \"Open menu\"}\n          >\n            {isMenuOpen ? (\n              <X className=\"h-5 w-5\" />\n            ) : (\n              <Menu className=\"h-5 w-5\" />\n            )}\n          </motion.button>\n\n          {/* Back to top button */}\n        </motion.div>\n      )}\n    </AnimatePresence>\n  );\n}\n"], "names": [], "mappings": ";;;;AAIA;AACA;AAFA;AAAA;AAAA;AAAA;AADA;AAAA;AACA;AAAA;;;AAHA;;;;;AAWe,SAAS,qBAAqB,EAC3C,YAAY,GAAG,EACW;;IAC1B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;YACR,MAAM;+DAAe;oBACnB,IAAI,OAAO,OAAO,GAAG,WAAW;wBAC9B,aAAa;oBACf,OAAO;wBACL,aAAa;wBACb,IAAI,YAAY,cAAc;oBAChC;gBACF;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC;kDAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;yCAAG;QAAC;QAAW;KAAW;IAE1B,MAAM,YAAY;QAChB;YAAE,oBAAM,6LAAC,sMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;YAAc,OAAO;YAAQ,MAAM;QAAI;QAC/D;YAAE,oBAAM,6LAAC,+MAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;YAAc,OAAO;YAAQ,MAAM;QAAS;QACzE;YACE,oBAAM,6LAAC,iNAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;YAC1B,OAAO;YACP,MAAM;QACR;QACA;YAAE,oBAAM,6LAAC,qMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;YAAc,OAAO;YAAW,MAAM;QAAW;KAC1E;IAED,MAAM,sBAAsB,CAAC;QAC3B,cAAc;QACd,IAAI,SAAS,UAAU;YACrB,MAAM,cAAc,SAAS,cAAc,CAAC;YAC5C,IAAI,aAAa;gBACf,YAAY,cAAc,CAAC;oBAAE,UAAU;gBAAS;YAClD;QACF;IACF;IAEA,qBACE,6LAAC,4LAAA,CAAA,kBAAe;kBACb,2BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,WAAU;YACV,SAAS;gBAAE,SAAS;gBAAG,OAAO;YAAI;YAClC,SAAS;gBAAE,SAAS;gBAAG,OAAO;YAAE;YAChC,MAAM;gBAAE,SAAS;gBAAG,OAAO;YAAI;YAC/B,YAAY;gBAAE,UAAU;YAAI;;8BAG5B,6LAAC,4LAAA,CAAA,kBAAe;8BACb,4BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,MAAM;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC1B,YAAY;4BAAE,UAAU;wBAAI;kCAE3B,UAAU,GAAG,CAAC,CAAC,MAAM,sBACpB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,OAAO,QAAQ;gCAAK;0CAElC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAM,KAAK,IAAI;oCACf,SAAS,IAAM,oBAAoB,KAAK,IAAI;oCAC5C,WAAU;;wCAET,KAAK,IAAI;sDACV,6LAAC;sDAAM,KAAK,KAAK;;;;;;;;;;;;+BAXd,KAAK,KAAK;;;;;;;;;;;;;;;8BAoBzB,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oBACZ,WAAU;oBACV,SAAS,IACP,aAAa,cAAc,SAAS,cAAc;oBAEpD,UAAU;wBAAE,OAAO;oBAAI;oBACvB,cAAY,aAAa,eAAe;8BAEvC,2BACC,6LAAC,+LAAA,CAAA,IAAC;wBAAC,WAAU;;;;;6CAEb,6LAAC,qMAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;AAS9B;GAvGwB;KAAA"}}, {"offset": {"line": 291, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 297, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\";\nimport { twMerge } from \"tailwind-merge\";\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB"}}, {"offset": {"line": 310, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 316, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport { cva, type VariantProps } from \"class-variance-authority\";\nimport { Slot } from \"@radix-ui/react-slot\";\n\nimport { cn } from \"@/lib/utils\";\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-[8px] text-sm font-medium transition-[color,box-shadow] disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 [&_svg]:shrink-0 outline-none focus-visible:border-[var(--highlight)] focus-visible:ring-[var(--highlight)]/50 focus-visible:ring-[3px]\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-[var(--button)] text-[var(--button-text)] shadow-sm hover:bg-[color-mix(in_srgb,var(--button),#00000020)]\",\n        destructive:\n          \"bg-[var(--tertiary)] text-[var(--button-text)] shadow-xs hover:bg-[color-mix(in_srgb,var(--tertiary),#00000020)] focus-visible:ring-[var(--tertiary)]/20\",\n        outline:\n          \"border border-[var(--input-border-color)] bg-[var(--background)] shadow-xs hover:bg-[var(--card-hover)] hover:text-[var(--highlight)]\",\n        secondary:\n          \"bg-[var(--secondary)] text-[var(--button-text)] shadow-xs hover:bg-[color-mix(in_srgb,var(--secondary),#00000020)]\",\n        ghost: \"hover:bg-[var(--card-hover)] hover:text-[var(--highlight)]\",\n        link: \"text-[var(--link-color)] underline-offset-4 hover:text-[var(--link-hover)] hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2\",\n        sm: \"h-8 rounded-[8px] px-3 text-xs\",\n        lg: \"h-10 rounded-[8px] px-8\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n);\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean;\n  }) {\n  const Comp = asChild ? Slot : \"button\";\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  );\n}\n\nexport { Button, buttonVariants };\n"], "names": [], "mappings": ";;;;;AACA;AAGA;AAFA;;;;;AAIA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,6XACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS"}}, {"offset": {"line": 373, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 379, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/src/components/ui/navigation-menu.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva } from \"class-variance-authority\"\nimport { ChevronDownIcon } from \"lucide-react\"\nimport { NavigationMenu as NavigationMenuPrimitive } from \"radix-ui\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction NavigationMenu({\n  className,\n  children,\n  viewport = true,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Root> & {\n  viewport?: boolean\n}) {\n  return (\n    <NavigationMenuPrimitive.Root\n      data-slot=\"navigation-menu\"\n      data-viewport={viewport}\n      className={cn(\n        \"group/navigation-menu relative flex max-w-max flex-1 items-center justify-center\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      {viewport && <NavigationMenuViewport />}\n    </NavigationMenuPrimitive.Root>\n  )\n}\n\nfunction NavigationMenuList({\n  className,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.List>) {\n  return (\n    <NavigationMenuPrimitive.List\n      data-slot=\"navigation-menu-list\"\n      className={cn(\n        \"group flex flex-1 list-none items-center justify-center gap-1\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction NavigationMenuItem({\n  className,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Item>) {\n  return (\n    <NavigationMenuPrimitive.Item\n      data-slot=\"navigation-menu-item\"\n      className={cn(\"relative\", className)}\n      {...props}\n    />\n  )\n}\n\nconst navigationMenuTriggerStyle = cva(\n  \"group inline-flex h-9 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground disabled:pointer-events-none disabled:opacity-50 data-[state=open]:hover:bg-accent data-[state=open]:text-accent-foreground data-[state=open]:focus:bg-accent data-[state=open]:bg-accent/50 focus-visible:ring-ring/50 outline-none transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1\"\n)\n\nfunction NavigationMenuTrigger({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Trigger>) {\n  return (\n    <NavigationMenuPrimitive.Trigger\n      data-slot=\"navigation-menu-trigger\"\n      className={cn(navigationMenuTriggerStyle(), \"group\", className)}\n      {...props}\n    >\n      {children}{\" \"}\n      <ChevronDownIcon\n        className=\"relative top-[1px] ml-1 size-3 transition duration-300 group-data-[state=open]:rotate-180\"\n        aria-hidden=\"true\"\n      />\n    </NavigationMenuPrimitive.Trigger>\n  )\n}\n\nfunction NavigationMenuContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Content>) {\n  return (\n    <NavigationMenuPrimitive.Content\n      data-slot=\"navigation-menu-content\"\n      className={cn(\n        \"data-[motion^=from-]:animate-in data-[motion^=to-]:animate-out data-[motion^=from-]:fade-in data-[motion^=to-]:fade-out data-[motion=from-end]:slide-in-from-right-52 data-[motion=from-start]:slide-in-from-left-52 data-[motion=to-end]:slide-out-to-right-52 data-[motion=to-start]:slide-out-to-left-52 top-0 left-0 w-full p-2 pr-2.5 md:absolute md:w-auto\",\n        \"group-data-[viewport=false]/navigation-menu:bg-popover group-data-[viewport=false]/navigation-menu:text-popover-foreground group-data-[viewport=false]/navigation-menu:data-[state=open]:animate-in group-data-[viewport=false]/navigation-menu:data-[state=closed]:animate-out group-data-[viewport=false]/navigation-menu:data-[state=closed]:zoom-out-95 group-data-[viewport=false]/navigation-menu:data-[state=open]:zoom-in-95 group-data-[viewport=false]/navigation-menu:data-[state=open]:fade-in-0 group-data-[viewport=false]/navigation-menu:data-[state=closed]:fade-out-0 group-data-[viewport=false]/navigation-menu:top-full group-data-[viewport=false]/navigation-menu:mt-1.5 group-data-[viewport=false]/navigation-menu:overflow-hidden group-data-[viewport=false]/navigation-menu:rounded-md group-data-[viewport=false]/navigation-menu:border group-data-[viewport=false]/navigation-menu:shadow group-data-[viewport=false]/navigation-menu:duration-200 **:data-[slot=navigation-menu-link]:focus:ring-0 **:data-[slot=navigation-menu-link]:focus:outline-none\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction NavigationMenuViewport({\n  className,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Viewport>) {\n  return (\n    <div\n      className={cn(\n        \"absolute top-full left-0 isolate z-50 flex justify-center\"\n      )}\n    >\n      <NavigationMenuPrimitive.Viewport\n        data-slot=\"navigation-menu-viewport\"\n        className={cn(\n          \"origin-top-center bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-90 relative mt-1.5 h-[var(--radix-navigation-menu-viewport-height)] w-full overflow-hidden rounded-md border shadow md:w-[var(--radix-navigation-menu-viewport-width)]\",\n          className\n        )}\n        {...props}\n      />\n    </div>\n  )\n}\n\nfunction NavigationMenuLink({\n  className,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Link>) {\n  return (\n    <NavigationMenuPrimitive.Link\n      data-slot=\"navigation-menu-link\"\n      className={cn(\n        \"data-[active]:focus:bg-accent data-[active]:hover:bg-accent data-[active]:bg-accent data-[active]:text-accent-foreground hover:bg-accent focus:bg-accent focus:text-accent-foreground focus-visible:ring-ring/50 [&_svg:not([class*='text-'])]:text-muted-foreground flex flex-col gap-1 rounded-sm p-2 text-sm transition-all outline-none focus-visible:ring-[3px] focus-visible:outline-1 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction NavigationMenuIndicator({\n  className,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Indicator>) {\n  return (\n    <NavigationMenuPrimitive.Indicator\n      data-slot=\"navigation-menu-indicator\"\n      className={cn(\n        \"data-[state=visible]:animate-in data-[state=hidden]:animate-out data-[state=hidden]:fade-out data-[state=visible]:fade-in top-full z-[1] flex h-1.5 items-end justify-center overflow-hidden\",\n        className\n      )}\n      {...props}\n    >\n      <div className=\"bg-border relative top-[60%] h-2 w-2 rotate-45 rounded-tl-sm shadow-md\" />\n    </NavigationMenuPrimitive.Indicator>\n  )\n}\n\nexport {\n  NavigationMenu,\n  NavigationMenuList,\n  NavigationMenuItem,\n  NavigationMenuContent,\n  NavigationMenuTrigger,\n  NavigationMenuLink,\n  NavigationMenuIndicator,\n  NavigationMenuViewport,\n  navigationMenuTriggerStyle,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AACA;AAIA;AAFA;AADA;;;;;;AAKA,SAAS,eAAe,EACtB,SAAS,EACT,QAAQ,EACR,WAAW,IAAI,EACf,GAAG,OAGJ;IACC,qBACE,6LAAC,2NAAA,CAAA,iBAAuB,CAAC,IAAI;QAC3B,aAAU;QACV,iBAAe;QACf,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oFACA;QAED,GAAG,KAAK;;YAER;YACA,0BAAY,6LAAC;;;;;;;;;;;AAGpB;KAtBS;AAwBT,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACuD;IAC1D,qBACE,6LAAC,2NAAA,CAAA,iBAAuB,CAAC,IAAI;QAC3B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACuD;IAC1D,qBACE,6LAAC,2NAAA,CAAA,iBAAuB,CAAC,IAAI;QAC3B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QACzB,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,MAAM,6BAA6B,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACnC;AAGF,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,6LAAC,2NAAA,CAAA,iBAAuB,CAAC,OAAO;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B,SAAS;QACpD,GAAG,KAAK;;YAER;YAAU;0BACX,6LAAC,2NAAA,CAAA,kBAAe;gBACd,WAAU;gBACV,eAAY;;;;;;;;;;;;AAIpB;MAlBS;AAoBT,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,6LAAC,2NAAA,CAAA,iBAAuB,CAAC,OAAO;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oWACA,6hCACA;QAED,GAAG,KAAK;;;;;;AAGf;MAfS;AAiBT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV;kBAGF,cAAA,6LAAC,2NAAA,CAAA,iBAAuB,CAAC,QAAQ;YAC/B,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sVACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;MApBS;AAsBT,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACuD;IAC1D,qBACE,6LAAC,2NAAA,CAAA,iBAAuB,CAAC,IAAI;QAC3B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qaACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,wBAAwB,EAC/B,SAAS,EACT,GAAG,OAC4D;IAC/D,qBACE,6LAAC,2NAAA,CAAA,iBAAuB,CAAC,SAAS;QAChC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gMACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC;YAAI,WAAU;;;;;;;;;;;AAGrB;MAhBS"}}, {"offset": {"line": 545, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 551, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/src/components/common/Logo.tsx"], "sourcesContent": ["export default function Logo() {\n  return (\n    <div>\n      <h1>Logo</h1>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAe,SAAS;IACtB,qBACE,6LAAC;kBACC,cAAA,6LAAC;sBAAG;;;;;;;;;;;AAGV;KANwB"}}, {"offset": {"line": 577, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 583, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/src/components/common/Navbar.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport Link from \"next/link\";\nimport { BookOpenIcon, InfoIcon, LifeBuoyIcon } from \"lucide-react\";\nimport { FiMenu, FiX } from \"react-icons/fi\";\n\nimport { cn } from \"@/lib/utils\";\nimport { Button } from \"@/components/ui/button\";\nimport {\n  NavigationMenu,\n  NavigationMenuContent,\n  NavigationMenuItem,\n  NavigationMenuLink,\n  NavigationMenuList,\n  NavigationMenuTrigger,\n} from \"@/components/ui/navigation-menu\";\nimport {\n  Popover,\n  PopoverContent,\n  PopoverTrigger,\n} from \"@/components/ui/popover\";\nimport Logo from \"./Logo\";\n\nconst navigationLinks = [\n  { href: \"#\", label: \"Home\" },\n  {\n    label: \"Features\",\n    submenu: true,\n    type: \"description\",\n    items: [\n      {\n        href: \"#\",\n        label: \"Components\",\n        description: \"Browse all components in the library.\",\n      },\n      {\n        href: \"#\",\n        label: \"Documentation\",\n        description: \"Learn how to use the library.\",\n      },\n      {\n        href: \"#\",\n        label: \"Templates\",\n        description: \"Pre-built layouts for common use cases.\",\n      },\n    ],\n  },\n  {\n    label: \"Pricing\",\n    submenu: true,\n    type: \"simple\",\n    items: [\n      { href: \"#\", label: \"Product A\" },\n      { href: \"#\", label: \"Product B\" },\n      { href: \"#\", label: \"Product C\" },\n      { href: \"#\", label: \"Product D\" },\n    ],\n  },\n  {\n    label: \"About\",\n    submenu: true,\n    type: \"icon\",\n    items: [\n      { href: \"#\", label: \"Getting Started\", icon: \"BookOpenIcon\" },\n      { href: \"#\", label: \"Tutorials\", icon: \"LifeBuoyIcon\" },\n      { href: \"#\", label: \"About Us\", icon: \"InfoIcon\" },\n    ],\n  },\n];\n\nexport default function Navbar() {\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);\n  const [searchQuery, setSearchQuery] = useState(\"\");\n\n  return (\n    <header className=\"border-b border-[var(--border)] bg-[var(--background)]\">\n      <div className=\"container mx-auto px-4\">\n        {/* MOBILE NAV */}\n        <div className=\"flex items-center justify-between h-16 md:hidden gap-2\">\n          <Link\n            href=\"/home\"\n            aria-label=\"Cosmos\"\n            data-testid=\"TopNavBar_CosmosLogo\"\n            className=\"text-[var(--link-color)]\"\n          >\n            <Logo />\n          </Link>\n\n          <form className=\"flex-grow\">\n            <input\n              type=\"search\"\n              name=\"search\"\n              placeholder=\"Search...\"\n              enterKeyHint=\"search\"\n              autoComplete=\"off\"\n              value={searchQuery}\n              onChange={(e) => setSearchQuery(e.target.value)}\n              className=\"w-full rounded-md border border-[var(--input-border-color)] bg-[var(--input-background)] px-3 py-2 text-[var(--input-text)] placeholder-[var(--paragraph)] text-sm outline-none focus:ring-2 focus:ring-[var(--highlight)]\"\n            />\n          </form>\n\n          <Button\n            aria-label=\"Menu\"\n            variant=\"ghost\"\n            size=\"icon\"\n            className=\"text-[var(--menu-color)]\"\n            onClick={() => setMobileMenuOpen(!mobileMenuOpen)}\n          >\n            {mobileMenuOpen ? <FiX size={24} /> : <FiMenu size={24} />}\n          </Button>\n        </div>\n\n        {/* MOBILE MENU POPUP */}\n        {mobileMenuOpen && (\n          <nav className=\"md:hidden border-t border-[var(--border)] bg-[var(--background)]\">\n            <NavigationMenu className=\"max-w-none p-4\">\n              <NavigationMenuList className=\"flex flex-col gap-3\">\n                {navigationLinks.map((link, i) => (\n                  <NavigationMenuItem key={i}>\n                    {link.submenu ? (\n                      <>\n                        <div className=\"text-[var(--nav-item)] px-2 py-1.5 text-xs font-medium\">\n                          {link.label}\n                        </div>\n                        <ul className=\"pl-4\">\n                          {link.items.map((item, idx) => (\n                            <li key={idx}>\n                              <NavigationMenuLink\n                                href={item.href}\n                                className=\"py-1.5 text-[var(--paragraph)] hover:text-[var(--highlight)] block\"\n                              >\n                                {item.label}\n                              </NavigationMenuLink>\n                            </li>\n                          ))}\n                        </ul>\n                      </>\n                    ) : (\n                      <NavigationMenuLink\n                        href={link.href}\n                        className=\"py-1.5 text-[var(--paragraph)] hover:text-[var(--highlight)] block font-medium\"\n                      >\n                        {link.label}\n                      </NavigationMenuLink>\n                    )}\n                  </NavigationMenuItem>\n                ))}\n              </NavigationMenuList>\n            </NavigationMenu>\n          </nav>\n        )}\n\n        {/* DESKTOP NAV */}\n        <div className=\"hidden md:flex h-16 items-center justify-between gap-4\">\n          <div className=\"flex items-center gap-6\">\n            <Link\n              href=\"/home\"\n              aria-label=\"Cosmos\"\n              className=\"text-[var(--link-color)] hover:text-[var(--link-hover)]\"\n            >\n              <Logo />\n            </Link>\n\n            <NavigationMenu viewport={false}>\n              <NavigationMenuList className=\"gap-2\">\n                {navigationLinks.map((link, index) => (\n                  <NavigationMenuItem key={index}>\n                    {link.submenu ? (\n                      <>\n                        <NavigationMenuTrigger className=\"text-[var(--nav-item)] hover:text-[var(--highlight)] bg-transparent px-2 py-1.5 font-medium *:[svg]:-me-0.5 *:[svg]:size-3.5\">\n                          {link.label}\n                        </NavigationMenuTrigger>\n                        <NavigationMenuContent className=\"z-50 p-1 bg-[var(--background)] border border-[var(--border)]\">\n                          <ul\n                            className={cn(\n                              link.type === \"description\"\n                                ? \"min-w-64\"\n                                : \"min-w-48\"\n                            )}\n                          >\n                            {link.items.map((item, itemIndex) => (\n                              <li key={itemIndex}>\n                                <NavigationMenuLink\n                                  href={item.href}\n                                  className=\"py-1.5 text-[var(--paragraph)] hover:text-[var(--highlight)] flex items-center gap-2\"\n                                >\n                                  {link.type === \"icon\" && \"icon\" in item && (\n                                    <>\n                                      {item.icon === \"BookOpenIcon\" && (\n                                        <BookOpenIcon\n                                          size={16}\n                                          className=\"text-[var(--menu-color)] opacity-60\"\n                                          aria-hidden=\"true\"\n                                        />\n                                      )}\n                                      {item.icon === \"LifeBuoyIcon\" && (\n                                        <LifeBuoyIcon\n                                          size={16}\n                                          className=\"text-[var(--menu-color)] opacity-60\"\n                                          aria-hidden=\"true\"\n                                        />\n                                      )}\n                                      {item.icon === \"InfoIcon\" && (\n                                        <InfoIcon\n                                          size={16}\n                                          className=\"text-[var(--menu-color)] opacity-60\"\n                                          aria-hidden=\"true\"\n                                        />\n                                      )}\n                                    </>\n                                  )}\n                                  <span>{item.label}</span>\n                                </NavigationMenuLink>\n                              </li>\n                            ))}\n                          </ul>\n                        </NavigationMenuContent>\n                      </>\n                    ) : (\n                      <NavigationMenuLink\n                        href={link.href}\n                        className=\"text-[var(--nav-item)] hover:text-[var(--highlight)] py-1.5 font-medium\"\n                      >\n                        {link.label}\n                      </NavigationMenuLink>\n                    )}\n                  </NavigationMenuItem>\n                ))}\n              </NavigationMenuList>\n            </NavigationMenu>\n          </div>\n\n          <div className=\"flex items-center gap-2\">\n            <Button\n              asChild\n              variant=\"ghost\"\n              size=\"sm\"\n              className=\"text-sm text-[var(--button-text)] hover:bg-[var(--card-hover)]\"\n            >\n              <Link href=\"#\">Sign In</Link>\n            </Button>\n            <Button\n              asChild\n              size=\"sm\"\n              className=\"text-sm bg-[var(--button)] text-[var(--button-text)]\"\n            >\n              <Link href=\"#\">Get Started</Link>\n            </Button>\n          </div>\n        </div>\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAIA;AACA;AACA;AAaA;AAjBA;AADA;AAAA;AAAA;;;AAJA;;;;;;;;;AAwBA,MAAM,kBAAkB;IACtB;QAAE,MAAM;QAAK,OAAO;IAAO;IAC3B;QACE,OAAO;QACP,SAAS;QACT,MAAM;QACN,OAAO;YACL;gBACE,MAAM;gBACN,OAAO;gBACP,aAAa;YACf;YACA;gBACE,MAAM;gBACN,OAAO;gBACP,aAAa;YACf;YACA;gBACE,MAAM;gBACN,OAAO;gBACP,aAAa;YACf;SACD;IACH;IACA;QACE,OAAO;QACP,SAAS;QACT,MAAM;QACN,OAAO;YACL;gBAAE,MAAM;gBAAK,OAAO;YAAY;YAChC;gBAAE,MAAM;gBAAK,OAAO;YAAY;YAChC;gBAAE,MAAM;gBAAK,OAAO;YAAY;YAChC;gBAAE,MAAM;gBAAK,OAAO;YAAY;SACjC;IACH;IACA;QACE,OAAO;QACP,SAAS;QACT,MAAM;QACN,OAAO;YACL;gBAAE,MAAM;gBAAK,OAAO;gBAAmB,MAAM;YAAe;YAC5D;gBAAE,MAAM;gBAAK,OAAO;gBAAa,MAAM;YAAe;YACtD;gBAAE,MAAM;gBAAK,OAAO;gBAAY,MAAM;YAAW;SAClD;IACH;CACD;AAEc,SAAS;;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,+JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,cAAW;4BACX,eAAY;4BACZ,WAAU;sCAEV,cAAA,6LAAC,uIAAA,CAAA,UAAI;;;;;;;;;;sCAGP,6LAAC;4BAAK,WAAU;sCACd,cAAA,6LAAC;gCACC,MAAK;gCACL,MAAK;gCACL,aAAY;gCACZ,cAAa;gCACb,cAAa;gCACb,OAAO;gCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gCAC9C,WAAU;;;;;;;;;;;sCAId,6LAAC,qIAAA,CAAA,SAAM;4BACL,cAAW;4BACX,SAAQ;4BACR,MAAK;4BACL,WAAU;4BACV,SAAS,IAAM,kBAAkB,CAAC;sCAEjC,+BAAiB,6LAAC,iJAAA,CAAA,MAAG;gCAAC,MAAM;;;;;qDAAS,6LAAC,iJAAA,CAAA,SAAM;gCAAC,MAAM;;;;;;;;;;;;;;;;;gBAKvD,gCACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,iJAAA,CAAA,iBAAc;wBAAC,WAAU;kCACxB,cAAA,6LAAC,iJAAA,CAAA,qBAAkB;4BAAC,WAAU;sCAC3B,gBAAgB,GAAG,CAAC,CAAC,MAAM,kBAC1B,6LAAC,iJAAA,CAAA,qBAAkB;8CAChB,KAAK,OAAO,iBACX;;0DACE,6LAAC;gDAAI,WAAU;0DACZ,KAAK,KAAK;;;;;;0DAEb,6LAAC;gDAAG,WAAU;0DACX,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,oBACrB,6LAAC;kEACC,cAAA,6LAAC,iJAAA,CAAA,qBAAkB;4DACjB,MAAM,KAAK,IAAI;4DACf,WAAU;sEAET,KAAK,KAAK;;;;;;uDALN;;;;;;;;;;;qEAYf,6LAAC,iJAAA,CAAA,qBAAkB;wCACjB,MAAM,KAAK,IAAI;wCACf,WAAU;kDAET,KAAK,KAAK;;;;;;mCAxBQ;;;;;;;;;;;;;;;;;;;;8BAmCnC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,cAAW;oCACX,WAAU;8CAEV,cAAA,6LAAC,uIAAA,CAAA,UAAI;;;;;;;;;;8CAGP,6LAAC,iJAAA,CAAA,iBAAc;oCAAC,UAAU;8CACxB,cAAA,6LAAC,iJAAA,CAAA,qBAAkB;wCAAC,WAAU;kDAC3B,gBAAgB,GAAG,CAAC,CAAC,MAAM,sBAC1B,6LAAC,iJAAA,CAAA,qBAAkB;0DAChB,KAAK,OAAO,iBACX;;sEACE,6LAAC,iJAAA,CAAA,wBAAqB;4DAAC,WAAU;sEAC9B,KAAK,KAAK;;;;;;sEAEb,6LAAC,iJAAA,CAAA,wBAAqB;4DAAC,WAAU;sEAC/B,cAAA,6LAAC;gEACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,KAAK,IAAI,KAAK,gBACV,aACA;0EAGL,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,0BACrB,6LAAC;kFACC,cAAA,6LAAC,iJAAA,CAAA,qBAAkB;4EACjB,MAAM,KAAK,IAAI;4EACf,WAAU;;gFAET,KAAK,IAAI,KAAK,UAAU,UAAU,sBACjC;;wFACG,KAAK,IAAI,KAAK,gCACb,6LAAC,qNAAA,CAAA,eAAY;4FACX,MAAM;4FACN,WAAU;4FACV,eAAY;;;;;;wFAGf,KAAK,IAAI,KAAK,gCACb,6LAAC,qNAAA,CAAA,eAAY;4FACX,MAAM;4FACN,WAAU;4FACV,eAAY;;;;;;wFAGf,KAAK,IAAI,KAAK,4BACb,6LAAC,yMAAA,CAAA,WAAQ;4FACP,MAAM;4FACN,WAAU;4FACV,eAAY;;;;;;;;8FAKpB,6LAAC;8FAAM,KAAK,KAAK;;;;;;;;;;;;uEA9BZ;;;;;;;;;;;;;;;;iFAsCjB,6LAAC,iJAAA,CAAA,qBAAkB;oDACjB,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,KAAK;;;;;;+CAzDQ;;;;;;;;;;;;;;;;;;;;;sCAkEjC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCACL,OAAO;oCACP,SAAQ;oCACR,MAAK;oCACL,WAAU;8CAEV,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;kDAAI;;;;;;;;;;;8CAEjB,6LAAC,qIAAA,CAAA,SAAM;oCACL,OAAO;oCACP,MAAK;oCACL,WAAU;8CAEV,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;kDAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO7B;GAvLwB;KAAA"}}, {"offset": {"line": 1031, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}
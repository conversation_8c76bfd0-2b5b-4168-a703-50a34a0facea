{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/Work/Repositories/templgen/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\";\n\nimport { cn } from \"@/lib/utils\";\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-[12px] border-2 border-[var(--input-border-color)] bg-[var(--input-background)] px-3 py-2 text-sm text-[var(--input-text)] placeholder-[var(--paragraph)] ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className,\n        )}\n        ref={ref}\n        {...props}\n      />\n    );\n  },\n);\nInput.displayName = \"Input\";\n\nexport { Input };\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAKA,MAAM,sBAAQ,8JAAM,UAAU,MAC5B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,6LAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iaACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG"}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 43, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/Work/Repositories/templgen/src/components/ui/label.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport * as LabelPrimitive from \"@radix-ui/react-label\";\nimport { cva, type VariantProps } from \"class-variance-authority\";\n\nimport { cn } from \"@/lib/utils\";\n\nconst labelVariants = cva(\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\",\n);\n\nconst Label = React.forwardRef<\n  React.ElementRef<typeof LabelPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\n    VariantProps<typeof labelVariants>\n>(({ className, ...props }, ref) => (\n  <LabelPrimitive.Root\n    ref={ref}\n    className={cn(labelVariants(), className)}\n    {...props}\n  />\n));\nLabel.displayName = LabelPrimitive.Root.displayName;\n\nexport { Label };\n"], "names": [], "mappings": ";;;;AAAA;AAEA;AAEA;AAHA;;;;;;AAKA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB;AAGF,MAAM,sBAAQ,8JAAM,UAAU,MAI5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAe,IAAI;QAClB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC9B,GAAG,KAAK;;;;;;;AAGb,MAAM,WAAW,GAAG,qKAAe,IAAI,CAAC,WAAW"}}, {"offset": {"line": 75, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 81, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/Work/Repositories/templgen/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\";\n\nimport { cn } from \"@/lib/utils\";\n\nexport interface TextareaProps\n  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {}\n\nconst Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(\n  ({ className, ...props }, ref) => {\n    return (\n      <textarea\n        className={cn(\n          \"flex h-10 min-h-[80px] w-full rounded-[12px] border-2 border-[var(--input-border-color)] bg-[var(--input-background)] px-3 py-2 text-sm text-[var(--input-text)] placeholder-[var(--paragraph)] ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className,\n        )}\n        ref={ref}\n        {...props}\n      />\n    );\n  },\n);\nTextarea.displayName = \"Textarea\";\n\nexport { Textarea };\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAKA,MAAM,yBAAW,8JAAM,UAAU,MAC/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACxB,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8aACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,SAAS,WAAW,GAAG"}}, {"offset": {"line": 110, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 116, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/Work/Repositories/templgen/src/components/ui/select.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport * as SelectPrimitive from \"@radix-ui/react-select\";\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\";\n\nimport { cn } from \"@/lib/utils\";\n\nconst Select = SelectPrimitive.Root;\n\nconst SelectGroup = SelectPrimitive.Group;\n\nconst SelectValue = SelectPrimitive.Value;\n\nconst SelectTrigger = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"flex h-10 w-full items-center justify-between rounded-[10px] border border-[var(--border)] bg-background px-3 py-2 text-sm text-[var(--headline)] ring-offset-background placeholder:text-[var(--paragraph)] focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\n      className,\n    )}\n    {...props}\n  >\n    {children}\n    <SelectPrimitive.Icon asChild>\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\n    </SelectPrimitive.Icon>\n  </SelectPrimitive.Trigger>\n));\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName;\n\nconst SelectScrollUpButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollUpButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className,\n    )}\n    {...props}\n  >\n    <ChevronUp className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollUpButton>\n));\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName;\n\nconst SelectScrollDownButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollDownButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className,\n    )}\n    {...props}\n  >\n    <ChevronDown className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollDownButton>\n));\nSelectScrollDownButton.displayName =\n  SelectPrimitive.ScrollDownButton.displayName;\n\nconst SelectContent = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\n>(({ className, children, position = \"popper\", ...props }, ref) => (\n  <SelectPrimitive.Portal>\n    <SelectPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-[10px] border border-[var(--border)] bg-[var(--card-background)] text-[var(--card-headline)] shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        position === \"popper\" &&\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n        className,\n      )}\n      position={position}\n      {...props}\n    >\n      <SelectScrollUpButton />\n      <SelectPrimitive.Viewport\n        className={cn(\n          \"p-1\",\n          position === \"popper\" &&\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\",\n        )}\n      >\n        {children}\n      </SelectPrimitive.Viewport>\n      <SelectScrollDownButton />\n    </SelectPrimitive.Content>\n  </SelectPrimitive.Portal>\n));\nSelectContent.displayName = SelectPrimitive.Content.displayName;\n\nconst SelectLabel = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Label\n    ref={ref}\n    className={cn(\"py-1.5 pl-8 pr-2 text-sm font-semibold\", className)}\n    {...props}\n  />\n));\nSelectLabel.displayName = SelectPrimitive.Label.displayName;\n\nconst SelectItem = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className,\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <SelectPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </SelectPrimitive.ItemIndicator>\n    </span>\n\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n  </SelectPrimitive.Item>\n));\nSelectItem.displayName = SelectPrimitive.Item.displayName;\n\nconst SelectSeparator = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n));\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName;\n\nexport {\n  Select,\n  SelectGroup,\n  SelectValue,\n  SelectTrigger,\n  SelectContent,\n  SelectLabel,\n  SelectItem,\n  SelectSeparator,\n  SelectScrollUpButton,\n  SelectScrollDownButton,\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;AAIA;AAHA;AACA;AAAA;AAAA;;;;;;AAIA,MAAM,SAAS,sKAAgB,IAAI;AAEnC,MAAM,cAAc,sKAAgB,KAAK;AAEzC,MAAM,cAAc,sKAAgB,KAAK;AAEzC,MAAM,8BAAgB,8JAAM,UAAU,MAGpC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC,sKAAgB,OAAO;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0VACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,sKAAgB,IAAI;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,sKAAgB,OAAO,CAAC,WAAW;AAE/D,MAAM,qCAAuB,8JAAM,UAAU,CAG3C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,sKAAgB,cAAc;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,mNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;MAZnB;AAeN,qBAAqB,WAAW,GAAG,sKAAgB,cAAc,CAAC,WAAW;AAE7E,MAAM,uCAAyB,8JAAM,UAAU,CAG7C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,sKAAgB,gBAAgB;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;MAZrB;AAeN,uBAAuB,WAAW,GAChC,sKAAgB,gBAAgB,CAAC,WAAW;AAE9C,MAAM,8BAAgB,8JAAM,UAAU,OAGpC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,6LAAC,sKAAgB,MAAM;kBACrB,cAAA,6LAAC,sKAAgB,OAAO;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ufACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,sKAAgB,QAAQ;oBACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,sKAAgB,OAAO,CAAC,WAAW;AAE/D,MAAM,4BAAc,8JAAM,UAAU,OAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,sKAAgB,KAAK;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACvD,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,sKAAgB,KAAK,CAAC,WAAW;AAE3D,MAAM,2BAAa,8JAAM,UAAU,OAGjC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC,sKAAgB,IAAI;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6NACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,sKAAgB,aAAa;8BAC5B,cAAA,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAIrB,6LAAC,sKAAgB,QAAQ;0BAAE;;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,sKAAgB,IAAI,CAAC,WAAW;AAEzD,MAAM,gCAAkB,8JAAM,UAAU,QAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,sKAAgB,SAAS;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG,sKAAgB,SAAS,CAAC,WAAW"}}, {"offset": {"line": 324, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 330, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/Work/Repositories/templgen/src/components/ui/checkbox.tsx"], "sourcesContent": ["\"use client\";\n\nimport * as React from \"react\";\nimport * as CheckboxPrimitive from \"@radix-ui/react-checkbox\";\nimport { Check } from \"lucide-react\";\n\nimport { cn } from \"@/lib/utils\";\n\nconst Checkbox = React.forwardRef<\n  React.ElementRef<typeof CheckboxPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof CheckboxPrimitive.Root>\n>(({ className, ...props }, ref) => (\n  <CheckboxPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground\",\n      className,\n    )}\n    {...props}\n  >\n    <CheckboxPrimitive.Indicator\n      className={cn(\"flex items-center justify-center text-current\")}\n    >\n      <Check className=\"h-4 w-4\" />\n    </CheckboxPrimitive.Indicator>\n  </CheckboxPrimitive.Root>\n));\nCheckbox.displayName = CheckboxPrimitive.Root.displayName;\n\nexport { Checkbox };\n"], "names": [], "mappings": ";;;;AAEA;AAIA;AAHA;AACA;AAJA;;;;;;AAQA,MAAM,yBAAW,8JAAM,UAAU,MAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,wKAAkB,IAAI;QACrB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kTACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,wKAAkB,SAAS;YAC1B,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE;sBAEd,cAAA,6LAAC,uMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;;;;;;;;;;;;AAIvB,SAAS,WAAW,GAAG,wKAAkB,IAAI,CAAC,WAAW"}}, {"offset": {"line": 376, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 382, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/Work/Repositories/templgen/src/components/ui/radio-group.tsx"], "sourcesContent": ["\"use client\";\n\nimport * as React from \"react\";\nimport * as RadioGroupPrimitive from \"@radix-ui/react-radio-group\";\nimport { Circle } from \"lucide-react\";\n\nimport { cn } from \"@/lib/utils\";\n\nconst RadioGroup = React.forwardRef<\n  React.ElementRef<typeof RadioGroupPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof RadioGroupPrimitive.Root>\n>(({ className, ...props }, ref) => {\n  return (\n    <RadioGroupPrimitive.Root\n      className={cn(\"grid gap-2\", className)}\n      {...props}\n      ref={ref}\n    />\n  );\n});\nRadioGroup.displayName = RadioGroupPrimitive.Root.displayName;\n\nconst RadioGroupItem = React.forwardRef<\n  React.ElementRef<typeof RadioGroupPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof RadioGroupPrimitive.Item>\n>(({ className, ...props }, ref) => {\n  return (\n    <RadioGroupPrimitive.Item\n      ref={ref}\n      className={cn(\n        \"aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n        className,\n      )}\n      {...props}\n    >\n      <RadioGroupPrimitive.Indicator className=\"flex items-center justify-center\">\n        <Circle className=\"h-2.5 w-2.5 fill-current text-current\" />\n      </RadioGroupPrimitive.Indicator>\n    </RadioGroupPrimitive.Item>\n  );\n});\nRadioGroupItem.displayName = RadioGroupPrimitive.Item.displayName;\n\nexport { RadioGroup, RadioGroupItem };\n"], "names": [], "mappings": ";;;;;AAEA;AAIA;AAHA;AACA;AAJA;;;;;;AAQA,MAAM,2BAAa,8JAAM,UAAU,MAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,6LAAC,8KAAoB,IAAI;QACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;QAC3B,GAAG,KAAK;QACT,KAAK;;;;;;AAGX;;AACA,WAAW,WAAW,GAAG,8KAAoB,IAAI,CAAC,WAAW;AAE7D,MAAM,+BAAiB,8JAAM,UAAU,OAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,6LAAC,8KAAoB,IAAI;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4OACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,8KAAoB,SAAS;YAAC,WAAU;sBACvC,cAAA,6LAAC,yMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;;;;;;;;;;;AAI1B;;AACA,eAAe,WAAW,GAAG,8KAAoB,IAAI,CAAC,WAAW"}}, {"offset": {"line": 446, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 452, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/Work/Repositories/templgen/src/components/ui/switch.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport * as SwitchPrimitives from \"@radix-ui/react-switch\";\n\nimport { cn } from \"@/lib/utils\";\n\nconst Switch = React.forwardRef<\n  React.ElementRef<typeof SwitchPrimitives.Root>,\n  React.ComponentPropsWithoutRef<typeof SwitchPrimitives.Root>\n>(({ className, ...props }, ref) => (\n  <SwitchPrimitives.Root\n    className={cn(\n      \"peer inline-flex h-6 w-11 bg-[var(--card-background)] border-2 border-[var(--border)] shrink-0 cursor-pointer items-center rounded-full  transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-[var(--illustration-stroke)] data-[state=unchecked]:bg-[var(--card-background)] data-[state=checked:border-[var(--border)]]:\",\n      className,\n    )}\n    {...props}\n    ref={ref}\n  >\n    <SwitchPrimitives.Thumb\n      className={cn(\n        \"pointer-events-none  block h-5 w-5 rounded-full bg-[var(--button)] shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0\",\n      )}\n    />\n  </SwitchPrimitives.Root>\n));\nSwitch.displayName = SwitchPrimitives.Root.displayName;\n\nexport { Switch };\n"], "names": [], "mappings": ";;;;AAAA;AAGA;AAFA;;;;;AAIA,MAAM,uBAAS,8JAAM,UAAU,MAG7B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,sKAAiB,IAAI;QACpB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4eACA;QAED,GAAG,KAAK;QACT,KAAK;kBAEL,cAAA,6LAAC,sKAAiB,KAAK;YACrB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV;;;;;;;;;;;;AAKR,OAAO,WAAW,GAAG,sKAAiB,IAAI,CAAC,WAAW"}}, {"offset": {"line": 488, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 494, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/Work/Repositories/templgen/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\";\n\nimport { cn } from \"@/lib/utils\";\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"bg-[var(--card-background)] border-[var(--card-border-color)] border rounded-xl p-4\",\n      className,\n    )}\n    {...props}\n  />\n));\nCard.displayName = \"Card\";\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"flex items-center  justify-between py-2 max-md:flex-col max-md:items-start\",\n      className,\n    )}\n    {...props}\n  />\n));\nCardHeader.displayName = \"CardHeader\";\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-base font-semibold max-md:p-0 leading-6 tracking-tight text-[var(--headline)]\",\n      className,\n    )}\n    {...props}\n  />\n));\nCardTitle.displayName = \"CardTitle\";\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\n      \"text-sm text-[var(--paragraph)] max-md:mb-0 max-md:mt-1\",\n      className,\n    )}\n    {...props}\n  />\n));\nCardDescription.displayName = \"CardDescription\";\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-0\", className)} {...props} />\n));\nCardContent.displayName = \"CardContent\";\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"flex h-max flex-wrap  items-start max-md:flex-row max-md:px-4 gap-2 pt-2 max-md:m-0 max-md:mt-2 max-md:items-end \",\n      className,\n    )}\n    {...props}\n  />\n));\nCardFooter.displayName = \"CardFooter\";\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardDescription,\n  CardContent,\n};\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,8JAAM,UAAU,MAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uFACA;QAED,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,8JAAM,UAAU,OAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8EACA;QAED,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,8JAAM,UAAU,OAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sFACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,8JAAM,UAAU,OAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,8JAAM,UAAU,OAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,OAAO;QAAa,GAAG,KAAK;;;;;;;AAE3D,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,8JAAM,UAAU,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qHACA;QAED,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG"}}, {"offset": {"line": 591, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 597, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/Work/Repositories/templgen/src/components/ui/separator.tsx"], "sourcesContent": ["\"use client\";\n\nimport * as React from \"react\";\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\";\n\nimport { cn } from \"@/lib/utils\";\n\nconst Separator = React.forwardRef<\n  React.ElementRef<typeof SeparatorPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof SeparatorPrimitive.Root>\n>(\n  (\n    { className, orientation = \"horizontal\", decorative = true, ...props },\n    ref,\n  ) => (\n    <SeparatorPrimitive.Root\n      ref={ref}\n      decorative={decorative}\n      orientation={orientation}\n      className={cn(\n        \"shrink-0 bg-border\",\n        orientation === \"horizontal\" ? \"h-[1px] w-full\" : \"h-full w-[1px]\",\n        className,\n      )}\n      {...props}\n    />\n  ),\n);\nSeparator.displayName = SeparatorPrimitive.Root.displayName;\n\nexport { Separator };\n"], "names": [], "mappings": ";;;;AAEA;AAGA;AAFA;AAHA;;;;;AAOA,MAAM,0BAAY,8JAAM,UAAU,MAIhC,CACE,EAAE,SAAS,EAAE,cAAc,YAAY,EAAE,aAAa,IAAI,EAAE,GAAG,OAAO,EACtE,oBAEA,6LAAC,yKAAmB,IAAI;QACtB,KAAK;QACL,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sBACA,gBAAgB,eAAe,mBAAmB,kBAClD;QAED,GAAG,KAAK;;;;;;;AAIf,UAAU,WAAW,GAAG,yKAAmB,IAAI,CAAC,WAAW"}}, {"offset": {"line": 629, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 635, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/Work/Repositories/templgen/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\";\n\nimport * as React from \"react\";\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\";\n\nimport { cn } from \"@/lib/utils\";\n\nconst Tabs = TabsPrimitive.Root;\n\nconst TabsList = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.List>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.List\n    ref={ref}\n    className={cn(\n      \"inline-flex h-10  bg-[var(--card-background)] items-center justify-center rounded-[12px]  p-1 text-[var(--paragraph)]\",\n      className,\n    )}\n    {...props}\n  />\n));\nTabsList.displayName = TabsPrimitive.List.displayName;\n\nconst TabsTrigger = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"inline-flex bg-[var(--card-background)]  items-center justify-center whitespace-nowrap  px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-[var(--highlight)] data-[state=active]:text-[var(--headline)] data-[state=active]:rounded-[12px]  data-[state=active]:shadow-sm\",\n      className,\n    )}\n    {...props}\n  />\n));\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName;\n\nconst TabsContent = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Content\n    ref={ref}\n    className={cn(\n      \"mt-2 ring-offset-background  focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\n      className,\n    )}\n    {...props}\n  />\n));\nTabsContent.displayName = TabsPrimitive.Content.displayName;\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent };\n"], "names": [], "mappings": ";;;;;;;AAEA;AAGA;AAFA;AAHA;;;;;AAOA,MAAM,OAAO,oKAAc,IAAI;AAE/B,MAAM,yBAAW,8JAAM,UAAU,MAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,oKAAc,IAAI;QACjB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,yHACA;QAED,GAAG,KAAK;;;;;;;AAGb,SAAS,WAAW,GAAG,oKAAc,IAAI,CAAC,WAAW;AAErD,MAAM,4BAAc,8JAAM,UAAU,OAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,oKAAc,OAAO;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6cACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,oKAAc,OAAO,CAAC,WAAW;AAE3D,MAAM,4BAAc,8JAAM,UAAU,OAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,oKAAc,OAAO;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oIACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,oKAAc,OAAO,CAAC,WAAW"}}, {"offset": {"line": 695, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 701, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/Work/Repositories/templgen/src/components/ui/accordion.tsx"], "sourcesContent": ["\"use client\";\n\nimport * as React from \"react\";\nimport * as AccordionPrimitive from \"@radix-ui/react-accordion\";\nimport { ChevronDown } from \"lucide-react\";\n\nimport { cn } from \"@/lib/utils\";\n\nconst Accordion = AccordionPrimitive.Root;\n\nconst AccordionItem = React.forwardRef<\n  React.ElementRef<typeof AccordionPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Item>\n>(({ className, ...props }, ref) => (\n  <AccordionPrimitive.Item\n    ref={ref}\n    className={cn(\"border-b\", className)}\n    {...props}\n  />\n));\nAccordionItem.displayName = \"AccordionItem\";\n\nconst AccordionTrigger = React.forwardRef<\n  React.ElementRef<typeof AccordionPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Trigger>\n>(({ className, children, ...props }, ref) => (\n  <AccordionPrimitive.Header className=\"flex\">\n    <AccordionPrimitive.Trigger\n      ref={ref}\n      className={cn(\n        \"flex flex-1 items-center justify-between py-4 font-medium transition-all hover:underline [&[data-state=open]>svg]:rotate-180\",\n        className,\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronDown className=\"h-4 w-4 shrink-0 transition-transform duration-200\" />\n    </AccordionPrimitive.Trigger>\n  </AccordionPrimitive.Header>\n));\nAccordionTrigger.displayName = AccordionPrimitive.Trigger.displayName;\n\nconst AccordionContent = React.forwardRef<\n  React.ElementRef<typeof AccordionPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Content>\n>(({ className, children, ...props }, ref) => (\n  <AccordionPrimitive.Content\n    ref={ref}\n    className=\"overflow-hidden text-sm transition-all data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down\"\n    {...props}\n  >\n    <div className={cn(\"pb-4 pt-0\", className)}>{children}</div>\n  </AccordionPrimitive.Content>\n));\n\nAccordionContent.displayName = AccordionPrimitive.Content.displayName;\n\nexport { Accordion, AccordionItem, AccordionTrigger, AccordionContent };\n"], "names": [], "mappings": ";;;;;;;AAEA;AAIA;AAHA;AACA;AAJA;;;;;;AAQA,MAAM,YAAY,yKAAmB,IAAI;AAEzC,MAAM,8BAAgB,8JAAM,UAAU,MAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,yKAAmB,IAAI;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QACzB,GAAG,KAAK;;;;;;;AAGb,cAAc,WAAW,GAAG;AAE5B,MAAM,iCAAmB,8JAAM,UAAU,OAGvC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC,yKAAmB,MAAM;QAAC,WAAU;kBACnC,cAAA,6LAAC,yKAAmB,OAAO;YACzB,KAAK;YACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gIACA;YAED,GAAG,KAAK;;gBAER;8BACD,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;;AAI7B,iBAAiB,WAAW,GAAG,yKAAmB,OAAO,CAAC,WAAW;AAErE,MAAM,iCAAmB,8JAAM,UAAU,OAGvC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC,yKAAmB,OAAO;QACzB,KAAK;QACL,WAAU;QACT,GAAG,KAAK;kBAET,cAAA,6LAAC;YAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;sBAAa;;;;;;;;;;;;AAIjD,iBAAiB,WAAW,GAAG,yKAAmB,OAAO,CAAC,WAAW"}}, {"offset": {"line": 788, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 794, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/Work/Repositories/templgen/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport { cva, type VariantProps } from \"class-variance-authority\";\nimport { cn } from \"@/lib/utils\";\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"flex items-center justify-center gap-2 border-transparent bg-[var(--badge-background)]  text-[var(--badge-text)] \",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground bg-[var(--badge-background)] text-[var(--badge-text)]  \",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground  hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  },\n);\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  );\n}\n\nexport { Badge, badgeVariants };\n"], "names": [], "mappings": ";;;;;AACA;AACA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE;KAJS"}}, {"offset": {"line": 836, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 842, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/Work/Repositories/templgen/src/components/ui/progress.tsx"], "sourcesContent": ["\"use client\";\n\nimport * as React from \"react\";\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\";\n\nimport { cn } from \"@/lib/utils\";\n\nconst Progress = React.forwardRef<\n  React.ElementRef<typeof ProgressPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root>\n>(({ className, value, ...props }, ref) => (\n  <ProgressPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"relative h-4 w-full overflow-hidden rounded-full bg-secondary\",\n      className,\n    )}\n    {...props}\n  >\n    <ProgressPrimitive.Indicator\n      className=\"h-full w-full flex-1 bg-primary transition-all\"\n      style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\n    />\n  </ProgressPrimitive.Root>\n));\nProgress.displayName = ProgressPrimitive.Root.displayName;\n\nexport { Progress };\n"], "names": [], "mappings": ";;;;AAEA;AAGA;AAFA;AAHA;;;;;AAOA,MAAM,yBAAW,8JAAM,UAAU,MAG/B,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,6LAAC,wKAAkB,IAAI;QACrB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,wKAAkB,SAAS;YAC1B,WAAU;YACV,OAAO;gBAAE,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;YAAC;;;;;;;;;;;;AAIhE,SAAS,WAAW,GAAG,wKAAkB,IAAI,CAAC,WAAW"}}, {"offset": {"line": 882, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 888, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/Work/Repositories/templgen/src/components/ui/skeleton.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\";\n\nfunction Skeleton({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) {\n  return (\n    <div\n      className={cn(\n        \"animate-pulse rounded-[10px] bg-[var(--card-background)]\",\n        className,\n      )}\n      {...props}\n    />\n  );\n}\n\nexport { Skeleton };\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OACkC;IACrC,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGf;KAbS"}}, {"offset": {"line": 912, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 918, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/Work/Repositories/templgen/src/components/ui/alert.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport { cva, type VariantProps } from \"class-variance-authority\";\n\nimport { cn } from \"@/lib/utils\";\n\nconst alertVariants = cva(\n  \"relative w-full rounded-[12px] border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-background text-foreground\",\n        destructive:\n          \"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  },\n);\n\nconst Alert = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement> & VariantProps<typeof alertVariants>\n>(({ className, variant, ...props }, ref) => (\n  <div\n    ref={ref}\n    role=\"alert\"\n    className={cn(alertVariants({ variant }), className)}\n    {...props}\n  />\n));\nAlert.displayName = \"Alert\";\n\nconst AlertTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h5\n    ref={ref}\n    className={cn(\"mb-1 font-medium leading-none tracking-tight\", className)}\n    {...props}\n  />\n));\nAlertTitle.displayName = \"AlertTitle\";\n\nconst AlertDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"text-sm [&_p]:leading-relaxed\", className)}\n    {...props}\n  />\n));\nAlertDescription.displayName = \"AlertDescription\";\n\nexport { Alert, AlertTitle, AlertDescription };\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,iKACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,MAAM,sBAAQ,8JAAM,UAAU,MAG5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBACnC,6LAAC;QACC,KAAK;QACL,MAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;;AAGb,MAAM,WAAW,GAAG;AAEpB,MAAM,2BAAa,8JAAM,UAAU,OAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,iCAAmB,8JAAM,UAAU,OAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,iBAAiB,WAAW,GAAG"}}, {"offset": {"line": 989, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 995, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/Work/Repositories/templgen/src/components/ui/use-toast.ts"], "sourcesContent": ["\"use client\";\n\n// Inspired by react-hot-toast library\nimport * as React from \"react\";\n\nimport type { ToastActionElement, ToastProps } from \"@/components/ui/toast\";\n\nconst TOAST_LIMIT = 1;\nconst TOAST_REMOVE_DELAY = 1000000;\n\ntype ToasterToast = ToastProps & {\n  id: string;\n  title?: React.ReactNode;\n  description?: React.ReactNode;\n  action?: ToastActionElement;\n};\n\nconst actionTypes = {\n  ADD_TOAST: \"ADD_TOAST\",\n  UPDATE_TOAST: \"UPDATE_TOAST\",\n  DISMISS_TOAST: \"DISMISS_TOAST\",\n  REMOVE_TOAST: \"REMOVE_TOAST\",\n} as const;\n\nlet count = 0;\n\nfunction genId() {\n  count = (count + 1) % Number.MAX_SAFE_INTEGER;\n  return count.toString();\n}\n\ntype ActionType = typeof actionTypes;\n\ntype Action =\n  | {\n      type: ActionType[\"ADD_TOAST\"];\n      toast: ToasterToast;\n    }\n  | {\n      type: ActionType[\"UPDATE_TOAST\"];\n      toast: Partial<ToasterToast>;\n    }\n  | {\n      type: ActionType[\"DISMISS_TOAST\"];\n      toastId?: ToasterToast[\"id\"];\n    }\n  | {\n      type: ActionType[\"REMOVE_TOAST\"];\n      toastId?: ToasterToast[\"id\"];\n    };\n\ninterface State {\n  toasts: ToasterToast[];\n}\n\nconst toastTimeouts = new Map<string, ReturnType<typeof setTimeout>>();\n\nconst addToRemoveQueue = (toastId: string) => {\n  if (toastTimeouts.has(toastId)) {\n    return;\n  }\n\n  const timeout = setTimeout(() => {\n    toastTimeouts.delete(toastId);\n    dispatch({\n      type: \"REMOVE_TOAST\",\n      toastId: toastId,\n    });\n  }, TOAST_REMOVE_DELAY);\n\n  toastTimeouts.set(toastId, timeout);\n};\n\nexport const reducer = (state: State, action: Action): State => {\n  switch (action.type) {\n    case \"ADD_TOAST\":\n      return {\n        ...state,\n        toasts: [action.toast, ...state.toasts].slice(0, TOAST_LIMIT),\n      };\n\n    case \"UPDATE_TOAST\":\n      return {\n        ...state,\n        toasts: state.toasts.map((t) =>\n          t.id === action.toast.id ? { ...t, ...action.toast } : t,\n        ),\n      };\n\n    case \"DISMISS_TOAST\": {\n      const { toastId } = action;\n\n      // ! Side effects ! - This could be extracted into a dismissToast() action,\n      // but I'll keep it here for simplicity\n      if (toastId) {\n        addToRemoveQueue(toastId);\n      } else {\n        state.toasts.forEach((toast) => {\n          addToRemoveQueue(toast.id);\n        });\n      }\n\n      return {\n        ...state,\n        toasts: state.toasts.map((t) =>\n          t.id === toastId || toastId === undefined\n            ? {\n                ...t,\n                open: false,\n              }\n            : t,\n        ),\n      };\n    }\n    case \"REMOVE_TOAST\":\n      if (action.toastId === undefined) {\n        return {\n          ...state,\n          toasts: [],\n        };\n      }\n      return {\n        ...state,\n        toasts: state.toasts.filter((t) => t.id !== action.toastId),\n      };\n  }\n};\n\nconst listeners: Array<(state: State) => void> = [];\n\nlet memoryState: State = { toasts: [] };\n\nfunction dispatch(action: Action) {\n  memoryState = reducer(memoryState, action);\n  listeners.forEach((listener) => {\n    listener(memoryState);\n  });\n}\n\ntype Toast = Omit<ToasterToast, \"id\">;\n\nfunction toast({ ...props }: Toast) {\n  const id = genId();\n\n  const update = (props: ToasterToast) =>\n    dispatch({\n      type: \"UPDATE_TOAST\",\n      toast: { ...props, id },\n    });\n  const dismiss = () => dispatch({ type: \"DISMISS_TOAST\", toastId: id });\n\n  dispatch({\n    type: \"ADD_TOAST\",\n    toast: {\n      ...props,\n      id,\n      open: true,\n      onOpenChange: (open) => {\n        if (!open) dismiss();\n      },\n    },\n  });\n\n  return {\n    id: id,\n    dismiss,\n    update,\n  };\n}\n\nfunction useToast() {\n  const [state, setState] = React.useState<State>(memoryState);\n\n  React.useEffect(() => {\n    listeners.push(setState);\n    return () => {\n      const index = listeners.indexOf(setState);\n      if (index > -1) {\n        listeners.splice(index, 1);\n      }\n    };\n  }, [state]);\n\n  return {\n    ...state,\n    toast,\n    dismiss: (toastId?: string) => dispatch({ type: \"DISMISS_TOAST\", toastId }),\n  };\n}\n\nexport { useToast, toast };\n"], "names": [], "mappings": ";;;;;AAEA,sCAAsC;AACtC;;AAHA;;AAOA,MAAM,cAAc;AACpB,MAAM,qBAAqB;AAS3B,MAAM,cAAc;IAClB,WAAW;IACX,cAAc;IACd,eAAe;IACf,cAAc;AAChB;AAEA,IAAI,QAAQ;AAEZ,SAAS;IACP,QAAQ,CAAC,QAAQ,CAAC,IAAI,OAAO,gBAAgB;IAC7C,OAAO,MAAM,QAAQ;AACvB;AA0BA,MAAM,gBAAgB,IAAI;AAE1B,MAAM,mBAAmB,CAAC;IACxB,IAAI,cAAc,GAAG,CAAC,UAAU;QAC9B;IACF;IAEA,MAAM,UAAU,WAAW;QACzB,cAAc,MAAM,CAAC;QACrB,SAAS;YACP,MAAM;YACN,SAAS;QACX;IACF,GAAG;IAEH,cAAc,GAAG,CAAC,SAAS;AAC7B;AAEO,MAAM,UAAU,CAAC,OAAc;IACpC,OAAQ,OAAO,IAAI;QACjB,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,QAAQ;oBAAC,OAAO,KAAK;uBAAK,MAAM,MAAM;iBAAC,CAAC,KAAK,CAAC,GAAG;YACnD;QAEF,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,QAAQ,MAAM,MAAM,CAAC,GAAG,CAAC,CAAC,IACxB,EAAE,EAAE,KAAK,OAAO,KAAK,CAAC,EAAE,GAAG;wBAAE,GAAG,CAAC;wBAAE,GAAG,OAAO,KAAK;oBAAC,IAAI;YAE3D;QAEF,KAAK;YAAiB;gBACpB,MAAM,EAAE,OAAO,EAAE,GAAG;gBAEpB,2EAA2E;gBAC3E,uCAAuC;gBACvC,IAAI,SAAS;oBACX,iBAAiB;gBACnB,OAAO;oBACL,MAAM,MAAM,CAAC,OAAO,CAAC,CAAC;wBACpB,iBAAiB,MAAM,EAAE;oBAC3B;gBACF;gBAEA,OAAO;oBACL,GAAG,KAAK;oBACR,QAAQ,MAAM,MAAM,CAAC,GAAG,CAAC,CAAC,IACxB,EAAE,EAAE,KAAK,WAAW,YAAY,YAC5B;4BACE,GAAG,CAAC;4BACJ,MAAM;wBACR,IACA;gBAER;YACF;QACA,KAAK;YACH,IAAI,OAAO,OAAO,KAAK,WAAW;gBAChC,OAAO;oBACL,GAAG,KAAK;oBACR,QAAQ,EAAE;gBACZ;YACF;YACA,OAAO;gBACL,GAAG,KAAK;gBACR,QAAQ,MAAM,MAAM,CAAC,MAAM,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,OAAO,OAAO;YAC5D;IACJ;AACF;AAEA,MAAM,YAA2C,EAAE;AAEnD,IAAI,cAAqB;IAAE,QAAQ,EAAE;AAAC;AAEtC,SAAS,SAAS,MAAc;IAC9B,cAAc,QAAQ,aAAa;IACnC,UAAU,OAAO,CAAC,CAAC;QACjB,SAAS;IACX;AACF;AAIA,SAAS,MAAM,EAAE,GAAG,OAAc;IAChC,MAAM,KAAK;IAEX,MAAM,SAAS,CAAC,QACd,SAAS;YACP,MAAM;YACN,OAAO;gBAAE,GAAG,KAAK;gBAAE;YAAG;QACxB;IACF,MAAM,UAAU,IAAM,SAAS;YAAE,MAAM;YAAiB,SAAS;QAAG;IAEpE,SAAS;QACP,MAAM;QACN,OAAO;YACL,GAAG,KAAK;YACR;YACA,MAAM;YACN,cAAc,CAAC;gBACb,IAAI,CAAC,MAAM;YACb;QACF;IACF;IAEA,OAAO;QACL,IAAI;QACJ;QACA;IACF;AACF;AAEA,SAAS;;IACP,MAAM,CAAC,OAAO,SAAS,GAAG,8JAAM,QAAQ,CAAQ;IAEhD,8JAAM,SAAS;8BAAC;YACd,UAAU,IAAI,CAAC;YACf;sCAAO;oBACL,MAAM,QAAQ,UAAU,OAAO,CAAC;oBAChC,IAAI,QAAQ,CAAC,GAAG;wBACd,UAAU,MAAM,CAAC,OAAO;oBAC1B;gBACF;;QACF;6BAAG;QAAC;KAAM;IAEV,OAAO;QACL,GAAG,KAAK;QACR;QACA,SAAS,CAAC,UAAqB,SAAS;gBAAE,MAAM;gBAAiB;YAAQ;IAC3E;AACF;GAlBS"}}, {"offset": {"line": 1155, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1161, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/Work/Repositories/templgen/src/components/ui/dialog.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\";\n\nimport { cn } from \"@/lib/utils\";\n\nconst Dialog = DialogPrimitive.Root;\n\nconst DialogTrigger = DialogPrimitive.Trigger;\n\nconst DialogPortal = DialogPrimitive.Portal;\n\nconst DialogClose = DialogPrimitive.Close;\n\nconst DiaLogoverlay = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Overlay>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Overlay\n    ref={ref}\n    className={cn(\n      \"fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\n      className,\n    )}\n    {...props}\n  />\n));\nDiaLogoverlay.displayName = DialogPrimitive.Overlay.displayName;\n\nconst DialogContent = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>\n>(({ className, children, ...props }, ref) => (\n  <DialogPortal>\n    <DiaLogoverlay />\n    <DialogPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"fixed left-[50%] top-[50%] rounded-[12px] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-[var(--background)] p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] \",\n        className,\n      )}\n      {...props}\n    >\n      {children}\n      <DialogPrimitive.Close className=\"absolute right-4 top-4 rounded-[20px] opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-[var(--paragraph)]\">\n        <span className=\"sr-only\">Close</span>\n      </DialogPrimitive.Close>\n    </DialogPrimitive.Content>\n  </DialogPortal>\n));\nDialogContent.displayName = DialogPrimitive.Content.displayName;\n\nconst DialogHeader = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col space-y-1.5 text-center sm:text-left\",\n      className,\n    )}\n    {...props}\n  />\n);\nDialogHeader.displayName = \"DialogHeader\";\n\nconst DialogFooter = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\n      className,\n    )}\n    {...props}\n  />\n);\nDialogFooter.displayName = \"DialogFooter\";\n\nconst DialogTitle = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Title\n    ref={ref}\n    className={cn(\n      \"text-lg font-bold leading-none tracking-tight text-[var(--headline)]\",\n      className,\n    )}\n    {...props}\n  />\n));\nDialogTitle.displayName = DialogPrimitive.Title.displayName;\n\nconst DialogDescription = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Description\n    ref={ref}\n    className={cn(\"text-sm text-[var(--paragraph)]\", className)}\n    {...props}\n  />\n));\nDialogDescription.displayName = DialogPrimitive.Description.displayName;\n\nexport {\n  Dialog,\n  DialogPortal,\n  DiaLogoverlay,\n  DialogClose,\n  DialogTrigger,\n  DialogContent,\n  DialogHeader,\n  DialogFooter,\n  DialogTitle,\n  DialogDescription,\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AAFA;;;;;AAIA,MAAM,SAAS,sKAAgB,IAAI;AAEnC,MAAM,gBAAgB,sKAAgB,OAAO;AAE7C,MAAM,eAAe,sKAAgB,MAAM;AAE3C,MAAM,cAAc,sKAAgB,KAAK;AAEzC,MAAM,8BAAgB,8JAAM,UAAU,CAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,sKAAgB,OAAO;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;KAVP;AAaN,cAAc,WAAW,GAAG,sKAAgB,OAAO,CAAC,WAAW;AAE/D,MAAM,8BAAgB,8JAAM,UAAU,OAGpC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC;;0BACC,6LAAC;;;;;0BACD,6LAAC,sKAAgB,OAAO;gBACtB,KAAK;gBACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0gBACA;gBAED,GAAG,KAAK;;oBAER;kCACD,6LAAC,sKAAgB,KAAK;wBAAC,WAAU;kCAC/B,cAAA,6LAAC;4BAAK,WAAU;sCAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKlC,cAAc,WAAW,GAAG,sKAAgB,OAAO,CAAC,WAAW;AAE/D,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;MATP;AAYN,aAAa,WAAW,GAAG;AAE3B,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;MATP;AAYN,aAAa,WAAW,GAAG;AAE3B,MAAM,4BAAc,8JAAM,UAAU,OAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,sKAAgB,KAAK;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wEACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,sKAAgB,KAAK,CAAC,WAAW;AAE3D,MAAM,kCAAoB,8JAAM,UAAU,OAGxC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,sKAAgB,WAAW;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,mCAAmC;QAChD,GAAG,KAAK;;;;;;;AAGb,kBAAkB,WAAW,GAAG,sKAAgB,WAAW,CAAC,WAAW"}}, {"offset": {"line": 1294, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1300, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/Work/Repositories/templgen/src/components/ui/sheet.tsx"], "sourcesContent": ["\"use client\";\n\nimport * as React from \"react\";\nimport * as SheetPrimitive from \"@radix-ui/react-dialog\";\nimport { cva, type VariantProps } from \"class-variance-authority\";\nimport { X } from \"lucide-react\";\n\nimport { cn } from \"@/lib/utils\";\n\nconst Sheet = SheetPrimitive.Root;\n\nconst SheetTrigger = SheetPrimitive.Trigger;\n\nconst SheetClose = SheetPrimitive.Close;\n\nconst SheetPortal = SheetPrimitive.Portal;\n\nconst SheetOverlay = React.forwardRef<\n  React.ElementRef<typeof SheetPrimitive.Overlay>,\n  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Overlay>\n>(({ className, ...props }, ref) => (\n  <SheetPrimitive.Overlay\n    className={cn(\n      \"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\n      className,\n    )}\n    {...props}\n    ref={ref}\n  />\n));\nSheetOverlay.displayName = SheetPrimitive.Overlay.displayName;\n\nconst sheetVariants = cva(\n  \"fixed z-50 gap-4 bg-background p-6 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500 data-[state=open]:animate-in data-[state=closed]:animate-out\",\n  {\n    variants: {\n      side: {\n        top: \"inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top\",\n        bottom:\n          \"inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom\",\n        left: \"inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-sm\",\n        right:\n          \"inset-y-0 right-0 h-full w-3/4 border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-sm\",\n      },\n    },\n    defaultVariants: {\n      side: \"right\",\n    },\n  },\n);\n\ninterface SheetContentProps\n  extends React.ComponentPropsWithoutRef<typeof SheetPrimitive.Content>,\n    VariantProps<typeof sheetVariants> {}\n\nconst SheetContent = React.forwardRef<\n  React.ElementRef<typeof SheetPrimitive.Content>,\n  SheetContentProps\n>(({ side = \"right\", className, children, ...props }, ref) => (\n  <SheetPortal>\n    <SheetOverlay />\n    <SheetPrimitive.Content\n      ref={ref}\n      className={cn(sheetVariants({ side }), className)}\n      {...props}\n    >\n      <SheetPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary\">\n        <X className=\"h-4 w-4\" />\n        <span className=\"sr-only\">Close</span>\n      </SheetPrimitive.Close>\n      {children}\n    </SheetPrimitive.Content>\n  </SheetPortal>\n));\nSheetContent.displayName = SheetPrimitive.Content.displayName;\n\nconst SheetHeader = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col space-y-2 text-center sm:text-left\",\n      className,\n    )}\n    {...props}\n  />\n);\nSheetHeader.displayName = \"SheetHeader\";\n\nconst SheetFooter = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\n      className,\n    )}\n    {...props}\n  />\n);\nSheetFooter.displayName = \"SheetFooter\";\n\nconst SheetTitle = React.forwardRef<\n  React.ElementRef<typeof SheetPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <SheetPrimitive.Title\n    ref={ref}\n    className={cn(\"text-lg font-semibold text-foreground\", className)}\n    {...props}\n  />\n));\nSheetTitle.displayName = SheetPrimitive.Title.displayName;\n\nconst SheetDescription = React.forwardRef<\n  React.ElementRef<typeof SheetPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <SheetPrimitive.Description\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n));\nSheetDescription.displayName = SheetPrimitive.Description.displayName;\n\nexport {\n  Sheet,\n  SheetPortal,\n  SheetOverlay,\n  SheetTrigger,\n  SheetClose,\n  SheetContent,\n  SheetHeader,\n  SheetFooter,\n  SheetTitle,\n  SheetDescription,\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AAEA;AAGA;AAJA;AAEA;AALA;;;;;;;AASA,MAAM,QAAQ,sKAAe,IAAI;AAEjC,MAAM,eAAe,sKAAe,OAAO;AAE3C,MAAM,aAAa,sKAAe,KAAK;AAEvC,MAAM,cAAc,sKAAe,MAAM;AAEzC,MAAM,6BAAe,8JAAM,UAAU,CAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,sKAAe,OAAO;QACrB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2JACA;QAED,GAAG,KAAK;QACT,KAAK;;;;;;KAVH;AAaN,aAAa,WAAW,GAAG,sKAAe,OAAO,CAAC,WAAW;AAE7D,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,oMACA;IACE,UAAU;QACR,MAAM;YACJ,KAAK;YACL,QACE;YACF,MAAM;YACN,OACE;QACJ;IACF;IACA,iBAAiB;QACf,MAAM;IACR;AACF;AAOF,MAAM,6BAAe,8JAAM,UAAU,OAGnC,CAAC,EAAE,OAAO,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpD,6LAAC;;0BACC,6LAAC;;;;;0BACD,6LAAC,sKAAe,OAAO;gBACrB,KAAK;gBACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;oBAAE;gBAAK,IAAI;gBACtC,GAAG,KAAK;;kCAET,6LAAC,sKAAe,KAAK;wBAAC,WAAU;;0CAC9B,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;0CACb,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;oBAE3B;;;;;;;;;;;;;;AAIP,aAAa,WAAW,GAAG,sKAAe,OAAO,CAAC,WAAW;AAE7D,MAAM,cAAc,CAAC,EACnB,SAAS,EACT,GAAG,OACkC,iBACrC,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oDACA;QAED,GAAG,KAAK;;;;;;MATP;AAYN,YAAY,WAAW,GAAG;AAE1B,MAAM,cAAc,CAAC,EACnB,SAAS,EACT,GAAG,OACkC,iBACrC,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;MATP;AAYN,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,8JAAM,UAAU,OAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,sKAAe,KAAK;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,yCAAyC;QACtD,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG,sKAAe,KAAK,CAAC,WAAW;AAEzD,MAAM,iCAAmB,8JAAM,UAAU,OAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,sKAAe,WAAW;QACzB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,iBAAiB,WAAW,GAAG,sKAAe,WAAW,CAAC,WAAW"}}, {"offset": {"line": 1462, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1468, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/Work/Repositories/templgen/src/components/ui/popover.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { Popover as PopoverPrimitive } from \"radix-ui\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Popover({\n  ...props\n}: React.ComponentProps<typeof PopoverPrimitive.Root>) {\n  return <PopoverPrimitive.Root data-slot=\"popover\" {...props} />\n}\n\nfunction PopoverTrigger({\n  ...props\n}: React.ComponentProps<typeof PopoverPrimitive.Trigger>) {\n  return <PopoverPrimitive.Trigger data-slot=\"popover-trigger\" {...props} />\n}\n\nfunction PopoverContent({\n  className,\n  align = \"center\",\n  sideOffset = 4,\n  showArrow = false,\n  ...props\n}: React.ComponentProps<typeof PopoverPrimitive.Content> & {\n  showArrow?: boolean\n}) {\n  return (\n    <PopoverPrimitive.Portal>\n      <PopoverPrimitive.Content\n        data-slot=\"popover-content\"\n        align={align}\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 rounded-md border p-4 shadow-md outline-hidden\",\n          className\n        )}\n        {...props}\n      >\n        {props.children}\n        {showArrow && (\n          <PopoverPrimitive.Arrow className=\"fill-popover -my-px drop-shadow-[0_1px_0_var(--border)]\" />\n        )}\n      </PopoverPrimitive.Content>\n    </PopoverPrimitive.Portal>\n  )\n}\n\nfunction PopoverAnchor({\n  ...props\n}: React.ComponentProps<typeof PopoverPrimitive.Anchor>) {\n  return <PopoverPrimitive.Anchor data-slot=\"popover-anchor\" {...props} />\n}\n\nexport { Popover, PopoverAnchor, PopoverContent, PopoverTrigger }\n"], "names": [], "mappings": ";;;;;;;AAKA;AAFA;AAHA;;;;AAOA,SAAS,QAAQ,EACf,GAAG,OACgD;IACnD,qBAAO,6LAAC,yMAAA,CAAA,UAAgB,CAAC,IAAI;QAAC,aAAU;QAAW,GAAG,KAAK;;;;;;AAC7D;KAJS;AAMT,SAAS,eAAe,EACtB,GAAG,OACmD;IACtD,qBAAO,6LAAC,yMAAA,CAAA,UAAgB,CAAC,OAAO;QAAC,aAAU;QAAmB,GAAG,KAAK;;;;;;AACxE;MAJS;AAMT,SAAS,eAAe,EACtB,SAAS,EACT,QAAQ,QAAQ,EAChB,aAAa,CAAC,EACd,YAAY,KAAK,EACjB,GAAG,OAGJ;IACC,qBACE,6LAAC,yMAAA,CAAA,UAAgB,CAAC,MAAM;kBACtB,cAAA,6LAAC,yMAAA,CAAA,UAAgB,CAAC,OAAO;YACvB,aAAU;YACV,OAAO;YACP,YAAY;YACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gbACA;YAED,GAAG,KAAK;;gBAER,MAAM,QAAQ;gBACd,2BACC,6LAAC,yMAAA,CAAA,UAAgB,CAAC,KAAK;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAK5C;MA5BS;AA8BT,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,6LAAC,yMAAA,CAAA,UAAgB,CAAC,MAAM;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;MAJS"}}, {"offset": {"line": 1553, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1559, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/Work/Repositories/templgen/src/components/ui/tooltip.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport * as TooltipPrimitive from \"@radix-ui/react-tooltip\";\n\nimport { cn } from \"@/lib/utils\";\n\nconst TooltipProvider = TooltipPrimitive.Provider;\n\nconst Tooltip = TooltipPrimitive.Root;\n\nconst TooltipTrigger = TooltipPrimitive.Trigger;\n\nconst TooltipContent = React.forwardRef<\n  React.ElementRef<typeof TooltipPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TooltipPrimitive.Content>\n>(({ className, sideOffset = 4, ...props }, ref) => (\n  <TooltipPrimitive.Content\n    ref={ref}\n    sideOffset={sideOffset}\n    className={cn(\n      \"z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n      className,\n    )}\n    {...props}\n  />\n));\nTooltipContent.displayName = TooltipPrimitive.Content.displayName;\n\nexport { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider };\n"], "names": [], "mappings": ";;;;;;;AAAA;AAGA;AAFA;;;;;AAIA,MAAM,kBAAkB,uKAAiB,QAAQ;AAEjD,MAAM,UAAU,uKAAiB,IAAI;AAErC,MAAM,iBAAiB,uKAAiB,OAAO;AAE/C,MAAM,+BAAiB,8JAAM,UAAU,MAGrC,CAAC,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC1C,6LAAC,uKAAiB,OAAO;QACvB,KAAK;QACL,YAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sYACA;QAED,GAAG,KAAK;;;;;;;AAGb,eAAe,WAAW,GAAG,uKAAiB,OAAO,CAAC,WAAW"}}, {"offset": {"line": 1595, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1601, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/Work/Repositories/templgen/src/components/ui/table.tsx"], "sourcesContent": ["import * as React from \"react\";\n\nimport { cn } from \"@/lib/utils\";\n\nconst Table = React.forwardRef<\n  HTMLTableElement,\n  React.HTMLAttributes<HTMLTableElement>\n>(({ className, ...props }, ref) => (\n  <div className=\"relative w-full overflow-auto\">\n    <table\n      ref={ref}\n      className={cn(\"w-full caption-bottom text-sm\", className)}\n      {...props}\n    />\n  </div>\n));\nTable.displayName = \"Table\";\n\nconst TableHeader = React.forwardRef<\n  HTMLTableSectionElement,\n  React.HTMLAttributes<HTMLTableSectionElement>\n>(({ className, ...props }, ref) => (\n  <thead ref={ref} className={cn(\"[&_tr]:border-b\", className)} {...props} />\n));\nTableHeader.displayName = \"TableHeader\";\n\nconst TableBody = React.forwardRef<\n  HTMLTableSectionElement,\n  React.HTMLAttributes<HTMLTableSectionElement>\n>(({ className, ...props }, ref) => (\n  <tbody\n    ref={ref}\n    className={cn(\"[&_tr:last-child]:border-0\", className)}\n    {...props}\n  />\n));\nTableBody.displayName = \"TableBody\";\n\nconst TableFooter = React.forwardRef<\n  HTMLTableSectionElement,\n  React.HTMLAttributes<HTMLTableSectionElement>\n>(({ className, ...props }, ref) => (\n  <tfoot\n    ref={ref}\n    className={cn(\n      \"border-t bg-muted/50 font-medium [&>tr]:last:border-b-0\",\n      className,\n    )}\n    {...props}\n  />\n));\nTableFooter.displayName = \"TableFooter\";\n\nconst TableRow = React.forwardRef<\n  HTMLTableRowElement,\n  React.HTMLAttributes<HTMLTableRowElement>\n>(({ className, ...props }, ref) => (\n  <tr\n    ref={ref}\n    className={cn(\n      \"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\n      className,\n    )}\n    {...props}\n  />\n));\nTableRow.displayName = \"TableRow\";\n\nconst TableHead = React.forwardRef<\n  HTMLTableCellElement,\n  React.ThHTMLAttributes<HTMLTableCellElement>\n>(({ className, ...props }, ref) => (\n  <th\n    ref={ref}\n    className={cn(\n      \"h-12 px-4 text-left align-middle font-medium text-[var(--paragraph)] [&:has([role=checkbox])]:pr-0\",\n      className,\n    )}\n    {...props}\n  />\n));\nTableHead.displayName = \"TableHead\";\n\nconst TableCell = React.forwardRef<\n  HTMLTableCellElement,\n  React.TdHTMLAttributes<HTMLTableCellElement>\n>(({ className, ...props }, ref) => (\n  <td\n    ref={ref}\n    className={cn(\"p-4 align-middle [&:has([role=checkbox])]:pr-0\", className)}\n    {...props}\n  />\n));\nTableCell.displayName = \"TableCell\";\n\nconst TableCaption = React.forwardRef<\n  HTMLTableCaptionElement,\n  React.HTMLAttributes<HTMLTableCaptionElement>\n>(({ className, ...props }, ref) => (\n  <caption\n    ref={ref}\n    className={cn(\"mt-4 text-sm text-[var(--paragraph)]\", className)}\n    {...props}\n  />\n));\nTableCaption.displayName = \"TableCaption\";\n\nexport {\n  Table,\n  TableHeader,\n  TableBody,\n  TableFooter,\n  TableHead,\n  TableRow,\n  TableCell,\n  TableCaption,\n};\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,sBAAQ,8JAAM,UAAU,MAG5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YACC,KAAK;YACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;;AAIf,MAAM,WAAW,GAAG;AAEpB,MAAM,4BAAc,8JAAM,UAAU,OAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAM,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAAa,GAAG,KAAK;;;;;;;AAEzE,YAAY,WAAW,GAAG;AAE1B,MAAM,0BAAY,8JAAM,UAAU,OAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,4BAAc,8JAAM,UAAU,OAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG;AAE1B,MAAM,yBAAW,8JAAM,UAAU,OAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;;AAGb,SAAS,WAAW,GAAG;AAEvB,MAAM,0BAAY,8JAAM,UAAU,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sGACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,0BAAY,8JAAM,UAAU,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,kDAAkD;QAC/D,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,6BAAe,8JAAM,UAAU,QAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,wCAAwC;QACrD,GAAG,KAAK;;;;;;;AAGb,aAAa,WAAW,GAAG"}}, {"offset": {"line": 1733, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1739, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/Work/Repositories/templgen/src/components/ui/avatar.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\";\n\nimport { cn } from \"@/lib/utils\";\n\nconst Avatar = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Root>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full\",\n      className,\n    )}\n    {...props}\n  />\n));\nAvatar.displayName = AvatarPrimitive.Root.displayName;\n\nconst AvatarImage = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Image>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Image>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Image\n    ref={ref}\n    className={cn(\"aspect-square h-full w-full\", className)}\n    {...props}\n  />\n));\nAvatarImage.displayName = AvatarPrimitive.Image.displayName;\n\nconst AvatarFallback = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Fallback>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Fallback>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Fallback\n    ref={ref}\n    className={cn(\n      \"flex h-full w-full items-center justify-center rounded-full\",\n      className,\n    )}\n    {...props}\n  />\n));\nAvatarFallback.displayName = AvatarPrimitive.Fallback.displayName;\n\nexport { Avatar, AvatarImage, AvatarFallback };\n"], "names": [], "mappings": ";;;;;;AAAA;AAGA;AAFA;;;;;AAIA,MAAM,uBAAS,8JAAM,UAAU,MAG7B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,sKAAgB,IAAI;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;;AAGb,OAAO,WAAW,GAAG,sKAAgB,IAAI,CAAC,WAAW;AAErD,MAAM,4BAAc,8JAAM,UAAU,OAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,sKAAgB,KAAK;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC5C,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,sKAAgB,KAAK,CAAC,WAAW;AAE3D,MAAM,+BAAiB,8JAAM,UAAU,OAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,sKAAgB,QAAQ;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+DACA;QAED,GAAG,KAAK;;;;;;;AAGb,eAAe,WAAW,GAAG,sKAAgB,QAAQ,CAAC,WAAW"}}, {"offset": {"line": 1796, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1802, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/Work/Repositories/templgen/src/hooks/use-toast.ts"], "sourcesContent": ["\"use client\";\n\nimport * as React from \"react\";\n\nimport type { ToastActionElement, ToastProps } from \"@/components/ui/toast\";\n\nconst TOAST_LIMIT = 5;\nconst TOAST_REMOVE_DELAY = 5000;\n\ntype ToasterToast = ToastProps & {\n  id: string;\n  title?: React.ReactNode;\n  description?: React.ReactNode;\n  action?: ToastActionElement;\n};\n\nconst actionTypes = {\n  ADD_TOAST: \"ADD_TOAST\",\n  UPDATE_TOAST: \"UPDATE_TOAST\",\n  DISMISS_TOAST: \"DISMISS_TOAST\",\n  REMOVE_TOAST: \"REMOVE_TOAST\",\n} as const;\n\nlet count = 0;\n\nfunction genId() {\n  count = (count + 1) % Number.MAX_SAFE_INTEGER;\n  return count.toString();\n}\n\ntype ActionType = typeof actionTypes;\n\ntype Action =\n  | {\n      type: ActionType[\"ADD_TOAST\"];\n      toast: ToasterToast;\n    }\n  | {\n      type: ActionType[\"UPDATE_TOAST\"];\n      toast: Partial<ToasterToast>;\n    }\n  | {\n      type: ActionType[\"DISMISS_TOAST\"];\n      toastId?: ToasterToast[\"id\"];\n    }\n  | {\n      type: ActionType[\"REMOVE_TOAST\"];\n      toastId?: ToasterToast[\"id\"];\n    };\n\ninterface State {\n  toasts: ToasterToast[];\n}\n\nconst toastTimeouts = new Map<string, ReturnType<typeof setTimeout>>();\n\nconst reducer = (state: State, action: Action): State => {\n  switch (action.type) {\n    case actionTypes.ADD_TOAST:\n      return {\n        ...state,\n        toasts: [action.toast, ...state.toasts].slice(0, TOAST_LIMIT),\n      };\n\n    case actionTypes.UPDATE_TOAST:\n      return {\n        ...state,\n        toasts: state.toasts.map((t) =>\n          t.id === action.toast.id ? { ...t, ...action.toast } : t,\n        ),\n      };\n\n    case actionTypes.DISMISS_TOAST:\n      return {\n        ...state,\n        toasts: state.toasts.map((t) =>\n          t.id === action.toastId || action.toastId === undefined\n            ? {\n                ...t,\n                open: false,\n              }\n            : t,\n        ),\n      };\n\n    case actionTypes.REMOVE_TOAST:\n      if (action.toastId === undefined) {\n        return {\n          ...state,\n          toasts: [],\n        };\n      }\n      return {\n        ...state,\n        toasts: state.toasts.filter((t) => t.id !== action.toastId),\n      };\n  }\n};\n\nconst listeners: Array<(state: State) => void> = [];\n\nlet memoryState: State = { toasts: [] };\n\nfunction dispatch(action: Action) {\n  memoryState = reducer(memoryState, action);\n  listeners.forEach((listener) => {\n    listener(memoryState);\n  });\n}\n\ntype Toast = Omit<ToasterToast, \"id\">;\n\nfunction toast({ ...props }: Toast) {\n  const id = genId();\n\n  const update = (props: ToasterToast) =>\n    dispatch({\n      type: actionTypes.UPDATE_TOAST,\n      toast: { ...props, id },\n    });\n  const dismiss = () =>\n    dispatch({ type: actionTypes.DISMISS_TOAST, toastId: id });\n\n  dispatch({\n    type: actionTypes.ADD_TOAST,\n    toast: {\n      ...props,\n      id,\n      open: true,\n      onOpenChange: (open) => {\n        if (!open) dismiss();\n      },\n    },\n  });\n\n  return {\n    id,\n    dismiss,\n    update,\n  };\n}\n\nfunction useToast() {\n  const [state, setState] = React.useState<State>(memoryState);\n\n  React.useEffect(() => {\n    listeners.push(setState);\n    return () => {\n      const index = listeners.indexOf(setState);\n      if (index > -1) {\n        listeners.splice(index, 1);\n      }\n    };\n  }, [state]);\n\n  return {\n    ...state,\n    toast,\n    dismiss: (toastId?: string) =>\n      dispatch({ type: actionTypes.DISMISS_TOAST, toastId }),\n    remove: (toastId?: string) =>\n      dispatch({ type: actionTypes.REMOVE_TOAST, toastId }),\n  };\n}\n\nexport { useToast, toast };\n"], "names": [], "mappings": ";;;;AAEA;;AAFA;;AAMA,MAAM,cAAc;AACpB,MAAM,qBAAqB;AAS3B,MAAM,cAAc;IAClB,WAAW;IACX,cAAc;IACd,eAAe;IACf,cAAc;AAChB;AAEA,IAAI,QAAQ;AAEZ,SAAS;IACP,QAAQ,CAAC,QAAQ,CAAC,IAAI,OAAO,gBAAgB;IAC7C,OAAO,MAAM,QAAQ;AACvB;AA0BA,MAAM,gBAAgB,IAAI;AAE1B,MAAM,UAAU,CAAC,OAAc;IAC7B,OAAQ,OAAO,IAAI;QACjB,KAAK,YAAY,SAAS;YACxB,OAAO;gBACL,GAAG,KAAK;gBACR,QAAQ;oBAAC,OAAO,KAAK;uBAAK,MAAM,MAAM;iBAAC,CAAC,KAAK,CAAC,GAAG;YACnD;QAEF,KAAK,YAAY,YAAY;YAC3B,OAAO;gBACL,GAAG,KAAK;gBACR,QAAQ,MAAM,MAAM,CAAC,GAAG,CAAC,CAAC,IACxB,EAAE,EAAE,KAAK,OAAO,KAAK,CAAC,EAAE,GAAG;wBAAE,GAAG,CAAC;wBAAE,GAAG,OAAO,KAAK;oBAAC,IAAI;YAE3D;QAEF,KAAK,YAAY,aAAa;YAC5B,OAAO;gBACL,GAAG,KAAK;gBACR,QAAQ,MAAM,MAAM,CAAC,GAAG,CAAC,CAAC,IACxB,EAAE,EAAE,KAAK,OAAO,OAAO,IAAI,OAAO,OAAO,KAAK,YAC1C;wBACE,GAAG,CAAC;wBACJ,MAAM;oBACR,IACA;YAER;QAEF,KAAK,YAAY,YAAY;YAC3B,IAAI,OAAO,OAAO,KAAK,WAAW;gBAChC,OAAO;oBACL,GAAG,KAAK;oBACR,QAAQ,EAAE;gBACZ;YACF;YACA,OAAO;gBACL,GAAG,KAAK;gBACR,QAAQ,MAAM,MAAM,CAAC,MAAM,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,OAAO,OAAO;YAC5D;IACJ;AACF;AAEA,MAAM,YAA2C,EAAE;AAEnD,IAAI,cAAqB;IAAE,QAAQ,EAAE;AAAC;AAEtC,SAAS,SAAS,MAAc;IAC9B,cAAc,QAAQ,aAAa;IACnC,UAAU,OAAO,CAAC,CAAC;QACjB,SAAS;IACX;AACF;AAIA,SAAS,MAAM,EAAE,GAAG,OAAc;IAChC,MAAM,KAAK;IAEX,MAAM,SAAS,CAAC,QACd,SAAS;YACP,MAAM,YAAY,YAAY;YAC9B,OAAO;gBAAE,GAAG,KAAK;gBAAE;YAAG;QACxB;IACF,MAAM,UAAU,IACd,SAAS;YAAE,MAAM,YAAY,aAAa;YAAE,SAAS;QAAG;IAE1D,SAAS;QACP,MAAM,YAAY,SAAS;QAC3B,OAAO;YACL,GAAG,KAAK;YACR;YACA,MAAM;YACN,cAAc,CAAC;gBACb,IAAI,CAAC,MAAM;YACb;QACF;IACF;IAEA,OAAO;QACL;QACA;QACA;IACF;AACF;AAEA,SAAS;;IACP,MAAM,CAAC,OAAO,SAAS,GAAG,8JAAM,QAAQ,CAAQ;IAEhD,8JAAM,SAAS;8BAAC;YACd,UAAU,IAAI,CAAC;YACf;sCAAO;oBACL,MAAM,QAAQ,UAAU,OAAO,CAAC;oBAChC,IAAI,QAAQ,CAAC,GAAG;wBACd,UAAU,MAAM,CAAC,OAAO;oBAC1B;gBACF;;QACF;6BAAG;QAAC;KAAM;IAEV,OAAO;QACL,GAAG,KAAK;QACR;QACA,SAAS,CAAC,UACR,SAAS;gBAAE,MAAM,YAAY,aAAa;gBAAE;YAAQ;QACtD,QAAQ,CAAC,UACP,SAAS;gBAAE,MAAM,YAAY,YAAY;gBAAE;YAAQ;IACvD;AACF;GArBS"}}, {"offset": {"line": 1939, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1945, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/Work/Repositories/templgen/src/components/ui/file-upload.tsx"], "sourcesContent": ["\"use client\";\n\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Progress } from \"@/components/ui/progress\";\nimport { useToast } from \"@/hooks/use-toast\";\nimport { AnimatePresence, motion } from \"framer-motion\";\nimport {\n  AlertCircle,\n  CheckCircle,\n  File,\n  Loader2,\n  Upload,\n  X,\n} from \"lucide-react\";\nimport { useCallback, useRef, useState } from \"react\";\n\ninterface FileUploadProps {\n  onUpload: (urls: string[]) => void;\n  onRemove?: (url: string) => void;\n  existingFiles?: string[];\n  accept?: string;\n  multiple?: boolean;\n  maxFiles?: number;\n  maxSize?: number; // in MB\n  className?: string;\n  disabled?: boolean;\n  uploadType?: \"profile\" | \"project\" | \"document\" | \"general\";\n  entityId?: string;\n}\n\ninterface UploadFile {\n  file: File;\n  url: string;\n  progress: number;\n  status: \"uploading\" | \"success\" | \"error\";\n  id: string;\n}\n\nexport function FileUpload({\n  onUpload,\n  onRemove,\n  existingFiles = [],\n  accept = \"image/*\",\n  multiple = true,\n  maxFiles = 5,\n  maxSize = 10, // 10MB default for Cloudinary\n  className = \"\",\n  disabled = false,\n  uploadType = \"general\",\n  entityId = \"default\",\n}: FileUploadProps) {\n  const { toast } = useToast();\n  const fileInputRef = useRef<HTMLInputElement>(null);\n  const [uploadingFiles, setUploadingFiles] = useState<UploadFile[]>([]);\n  const [isDragOver, setIsDragOver] = useState(false);\n\n  const uploadFile = async (file: File): Promise<string> => {\n    const formData = new FormData();\n    formData.append(\"file\", file);\n    formData.append(\"type\", uploadType);\n    formData.append(\"entityId\", entityId);\n\n    const response = await fetch(\"/api/upload\", {\n      method: \"POST\",\n      body: formData,\n    });\n\n    if (!response.ok) {\n      throw new Error(\"Upload failed\");\n    }\n\n    const data = await response.json();\n    if (!data.success) {\n      throw new Error(data.error || \"Upload failed\");\n    }\n\n    return data.url;\n  };\n\n  const handleFileSelect = useCallback(\n    async (files: FileList | null) => {\n      if (!files || disabled) return;\n\n      const fileArray = Array.from(files);\n      const totalFiles =\n        existingFiles.length + uploadingFiles.length + fileArray.length;\n\n      if (totalFiles > maxFiles) {\n        toast({\n          title: \"Too many files\",\n          description: `Maximum ${maxFiles} files allowed`,\n          variant: \"destructive\",\n        });\n        return;\n      }\n\n      // Validate file sizes\n      const oversizedFiles = fileArray.filter(\n        (file) => file.size > maxSize * 1024 * 1024\n      );\n      if (oversizedFiles.length > 0) {\n        toast({\n          title: \"File too large\",\n          description: `Maximum file size is ${maxSize}MB`,\n          variant: \"destructive\",\n        });\n        return;\n      }\n\n      // Create upload file objects\n      const newUploadFiles: UploadFile[] = fileArray.map((file) => ({\n        file,\n        url: URL.createObjectURL(file),\n        progress: 0,\n        status: \"uploading\" as const,\n        id: Math.random().toString(36).substr(2, 9),\n      }));\n\n      setUploadingFiles((prev) => [...prev, ...newUploadFiles]);\n\n      // Upload files\n      const uploadPromises = newUploadFiles.map(async (uploadFileItem) => {\n        try {\n          // Simulate progress\n          const progressInterval = setInterval(() => {\n            setUploadingFiles((prev) =>\n              prev.map((f) =>\n                f.id === uploadFileItem.id\n                  ? { ...f, progress: Math.min(f.progress + 10, 90) }\n                  : f\n              )\n            );\n          }, 200);\n\n          const uploadedUrl = await uploadFile(uploadFileItem.file);\n\n          clearInterval(progressInterval);\n\n          setUploadingFiles((prev) =>\n            prev.map((f) =>\n              f.id === uploadFileItem.id\n                ? {\n                    ...f,\n                    progress: 100,\n                    status: \"success\" as const,\n                    url: uploadedUrl,\n                  }\n                : f\n            )\n          );\n\n          // Clean up object URL\n          URL.revokeObjectURL(uploadFileItem.url);\n\n          return uploadedUrl;\n        } catch (error) {\n          setUploadingFiles((prev) =>\n            prev.map((f) =>\n              f.id === uploadFileItem.id\n                ? { ...f, status: \"error\" as const }\n                : f\n            )\n          );\n\n          toast({\n            title: \"Upload failed\",\n            description: `Failed to upload ${uploadFileItem.file.name}`,\n            variant: \"destructive\",\n          });\n\n          return null;\n        }\n      });\n\n      const results = await Promise.all(uploadPromises);\n      const successfulUploads = results.filter(Boolean) as string[];\n\n      if (successfulUploads.length > 0) {\n        onUpload(successfulUploads);\n        toast({\n          title: \"Upload successful\",\n          description: `${successfulUploads.length} file(s) uploaded successfully`,\n        });\n      }\n\n      // Clean up completed uploads after a delay\n      setTimeout(() => {\n        setUploadingFiles((prev) =>\n          prev.filter((f) => f.status === \"uploading\")\n        );\n      }, 2000);\n    },\n    [\n      disabled,\n      existingFiles.length,\n      uploadingFiles.length,\n      maxFiles,\n      maxSize,\n      onUpload,\n      toast,\n    ]\n  );\n\n  const handleDragOver = useCallback((e: React.DragEvent) => {\n    e.preventDefault();\n    setIsDragOver(true);\n  }, []);\n\n  const handleDragLeave = useCallback((e: React.DragEvent) => {\n    e.preventDefault();\n    setIsDragOver(false);\n  }, []);\n\n  const handleDrop = useCallback(\n    (e: React.DragEvent) => {\n      e.preventDefault();\n      setIsDragOver(false);\n      handleFileSelect(e.dataTransfer.files);\n    },\n    [handleFileSelect]\n  );\n\n  const isImage = (url: string) => {\n    return /\\.(jpg|jpeg|png|gif|webp)$/i.test(url);\n  };\n\n  const getFileName = (url: string) => {\n    return url.split(\"/\").pop() || \"Unknown file\";\n  };\n\n  return (\n    <div className={`space-y-4 ${className}`}>\n      {/* Upload Area */}\n      <div\n        className={`\n          border-2 border-dashed rounded-[12px] p-6 text-center transition-colors\n          ${\n            isDragOver\n              ? \"border-[var(--link-color)] bg-[var(--link-color)]/5\"\n              : \"border-[var(--input-border-color)] hover:border-[var(--link-color)]\"\n          }\n          ${disabled ? \"opacity-50 cursor-not-allowed\" : \"cursor-pointer\"}\n        `}\n        onDragOver={handleDragOver}\n        onDragLeave={handleDragLeave}\n        onDrop={handleDrop}\n        onClick={() => !disabled && fileInputRef.current?.click()}\n      >\n        <Upload className=\"h-8 w-8 mx-auto mb-2 text-[var(--paragraph)]\" />\n        <p className=\"text-sm text-[var(--paragraph)] mb-1\">\n          {isDragOver ? \"Drop files here\" : \"Click to upload or drag and drop\"}\n        </p>\n        <p className=\"text-xs text-[var(--paragraph)]\">\n          {accept === \"image/*\" ? \"Images\" : \"Files\"} up to {maxSize}MB each\n          (max {maxFiles} files)\n        </p>\n      </div>\n\n      <input\n        ref={fileInputRef}\n        type=\"file\"\n        accept={accept}\n        multiple={multiple}\n        onChange={(e) => handleFileSelect(e.target.files)}\n        className=\"hidden\"\n        disabled={disabled}\n      />\n\n      {/* Uploading Files */}\n      <AnimatePresence>\n        {uploadingFiles.map((uploadFile) => (\n          <motion.div\n            key={uploadFile.id}\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            exit={{ opacity: 0, y: -20 }}\n            className=\"flex items-center gap-3 p-3 bg-[var(--card-background)] border border-[var(--card-border-color)] rounded-[12px]2px]\"\n          >\n            <div className=\"flex-shrink-0\">\n              {uploadFile.status === \"uploading\" && (\n                <Loader2 className=\"h-5 w-5 animate-spin text-[var(--link-color)]\" />\n              )}\n              {uploadFile.status === \"success\" && (\n                <CheckCircle className=\"h-5 w-5 text-green-500\" />\n              )}\n              {uploadFile.status === \"error\" && (\n                <AlertCircle className=\"h-5 w-5 text-red-500\" />\n              )}\n            </div>\n\n            <div className=\"flex-1 min-w-0\">\n              <p className=\"text-sm font-medium truncate\">\n                {uploadFile.file.name}\n              </p>\n              {uploadFile.status === \"uploading\" && (\n                <Progress value={uploadFile.progress} className=\"mt-1\" />\n              )}\n              {uploadFile.status === \"success\" && (\n                <p className=\"text-xs text-green-600\">Upload complete</p>\n              )}\n              {uploadFile.status === \"error\" && (\n                <p className=\"text-xs text-red-600\">Upload failed</p>\n              )}\n            </div>\n          </motion.div>\n        ))}\n      </AnimatePresence>\n\n      {/* Existing Files */}\n      {existingFiles.length > 0 && (\n        <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4\">\n          <AnimatePresence>\n            {existingFiles.map((url, index) => (\n              <motion.div\n                key={url}\n                initial={{ opacity: 0, scale: 0.8 }}\n                animate={{ opacity: 1, scale: 1 }}\n                exit={{ opacity: 0, scale: 0.8 }}\n                className=\"relative group rounded-[12px]2px] overflow-hidden bg-[var(--card-background)] border border-[var(--card-border-color)]\"\n              >\n                {isImage(url) ? (\n                  <div className=\"aspect-video\">\n                    <img\n                      src={url}\n                      alt={`Upload ${index + 1}`}\n                      className=\"w-full h-full object-cover\"\n                      onError={(e) => {\n                        e.currentTarget.src = \"/placeholder.svg\";\n                      }}\n                    />\n                  </div>\n                ) : (\n                  <div className=\"aspect-video flex items-center justify-center\">\n                    <File className=\"h-8 w-8 text-[var(--paragraph)]\" />\n                  </div>\n                )}\n\n                <div className=\"p-2\">\n                  <p className=\"text-xs text-[var(--paragraph)] truncate\">\n                    {getFileName(url)}\n                  </p>\n                </div>\n\n                {onRemove && (\n                  <div className=\"absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center\">\n                    <Button\n                      type=\"button\"\n                      variant=\"destructive\"\n                      size=\"sm\"\n                      onClick={(e) => {\n                        e.stopPropagation();\n                        onRemove(url);\n                      }}\n                    >\n                      <X className=\"h-4 w-4\" />\n                    </Button>\n                  </div>\n                )}\n              </motion.div>\n            ))}\n          </AnimatePresence>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAUA;AARA;AADA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;;;AANA;;;;;;;AAsCO,SAAS,WAAW,EACzB,QAAQ,EACR,QAAQ,EACR,gBAAgB,EAAE,EAClB,SAAS,SAAS,EAClB,WAAW,IAAI,EACf,WAAW,CAAC,EACZ,UAAU,EAAE,EACZ,YAAY,EAAE,EACd,WAAW,KAAK,EAChB,aAAa,SAAS,EACtB,WAAW,SAAS,EACJ;;IAChB,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAC9C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IACrE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,aAAa,OAAO;QACxB,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,QAAQ;QACxB,SAAS,MAAM,CAAC,QAAQ;QACxB,SAAS,MAAM,CAAC,YAAY;QAE5B,MAAM,WAAW,MAAM,MAAM,eAAe;YAC1C,QAAQ;YACR,MAAM;QACR;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,IAAI,CAAC,KAAK,OAAO,EAAE;YACjB,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;QAChC;QAEA,OAAO,KAAK,GAAG;IACjB;IAEA,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;oDACjC,OAAO;YACL,IAAI,CAAC,SAAS,UAAU;YAExB,MAAM,YAAY,MAAM,IAAI,CAAC;YAC7B,MAAM,aACJ,cAAc,MAAM,GAAG,eAAe,MAAM,GAAG,UAAU,MAAM;YAEjE,IAAI,aAAa,UAAU;gBACzB,MAAM;oBACJ,OAAO;oBACP,aAAa,CAAC,QAAQ,EAAE,SAAS,cAAc,CAAC;oBAChD,SAAS;gBACX;gBACA;YACF;YAEA,sBAAsB;YACtB,MAAM,iBAAiB,UAAU,MAAM;2EACrC,CAAC,OAAS,KAAK,IAAI,GAAG,UAAU,OAAO;;YAEzC,IAAI,eAAe,MAAM,GAAG,GAAG;gBAC7B,MAAM;oBACJ,OAAO;oBACP,aAAa,CAAC,qBAAqB,EAAE,QAAQ,EAAE,CAAC;oBAChD,SAAS;gBACX;gBACA;YACF;YAEA,6BAA6B;YAC7B,MAAM,iBAA+B,UAAU,GAAG;2EAAC,CAAC,OAAS,CAAC;wBAC5D;wBACA,KAAK,IAAI,eAAe,CAAC;wBACzB,UAAU;wBACV,QAAQ;wBACR,IAAI,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;oBAC3C,CAAC;;YAED;4DAAkB,CAAC,OAAS;2BAAI;2BAAS;qBAAe;;YAExD,eAAe;YACf,MAAM,iBAAiB,eAAe,GAAG;2EAAC,OAAO;oBAC/C,IAAI;wBACF,oBAAoB;wBACpB,MAAM,mBAAmB;wGAAY;gCACnC;gHAAkB,CAAC,OACjB,KAAK,GAAG;wHAAC,CAAC,IACR,EAAE,EAAE,KAAK,eAAe,EAAE,GACtB;oDAAE,GAAG,CAAC;oDAAE,UAAU,KAAK,GAAG,CAAC,EAAE,QAAQ,GAAG,IAAI;gDAAI,IAChD;;;4BAGV;uGAAG;wBAEH,MAAM,cAAc,MAAM,WAAW,eAAe,IAAI;wBAExD,cAAc;wBAEd;uFAAkB,CAAC,OACjB,KAAK,GAAG;+FAAC,CAAC,IACR,EAAE,EAAE,KAAK,eAAe,EAAE,GACtB;4CACE,GAAG,CAAC;4CACJ,UAAU;4CACV,QAAQ;4CACR,KAAK;wCACP,IACA;;;wBAIR,sBAAsB;wBACtB,IAAI,eAAe,CAAC,eAAe,GAAG;wBAEtC,OAAO;oBACT,EAAE,OAAO,OAAO;wBACd;uFAAkB,CAAC,OACjB,KAAK,GAAG;+FAAC,CAAC,IACR,EAAE,EAAE,KAAK,eAAe,EAAE,GACtB;4CAAE,GAAG,CAAC;4CAAE,QAAQ;wCAAiB,IACjC;;;wBAIR,MAAM;4BACJ,OAAO;4BACP,aAAa,CAAC,iBAAiB,EAAE,eAAe,IAAI,CAAC,IAAI,EAAE;4BAC3D,SAAS;wBACX;wBAEA,OAAO;oBACT;gBACF;;YAEA,MAAM,UAAU,MAAM,QAAQ,GAAG,CAAC;YAClC,MAAM,oBAAoB,QAAQ,MAAM,CAAC;YAEzC,IAAI,kBAAkB,MAAM,GAAG,GAAG;gBAChC,SAAS;gBACT,MAAM;oBACJ,OAAO;oBACP,aAAa,GAAG,kBAAkB,MAAM,CAAC,8BAA8B,CAAC;gBAC1E;YACF;YAEA,2CAA2C;YAC3C;4DAAW;oBACT;oEAAkB,CAAC,OACjB,KAAK,MAAM;4EAAC,CAAC,IAAM,EAAE,MAAM,KAAK;;;gBAEpC;2DAAG;QACL;mDACA;QACE;QACA,cAAc,MAAM;QACpB,eAAe,MAAM;QACrB;QACA;QACA;QACA;KACD;IAGH,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAAE,CAAC;YAClC,EAAE,cAAc;YAChB,cAAc;QAChB;iDAAG,EAAE;IAEL,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAAE,CAAC;YACnC,EAAE,cAAc;YAChB,cAAc;QAChB;kDAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8CAC3B,CAAC;YACC,EAAE,cAAc;YAChB,cAAc;YACd,iBAAiB,EAAE,YAAY,CAAC,KAAK;QACvC;6CACA;QAAC;KAAiB;IAGpB,MAAM,UAAU,CAAC;QACf,OAAO,8BAA8B,IAAI,CAAC;IAC5C;IAEA,MAAM,cAAc,CAAC;QACnB,OAAO,IAAI,KAAK,CAAC,KAAK,GAAG,MAAM;IACjC;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAC,UAAU,EAAE,WAAW;;0BAEtC,6LAAC;gBACC,WAAW,CAAC;;UAEV,EACE,aACI,wDACA,sEACL;UACD,EAAE,WAAW,kCAAkC,iBAAiB;QAClE,CAAC;gBACD,YAAY;gBACZ,aAAa;gBACb,QAAQ;gBACR,SAAS,IAAM,CAAC,YAAY,aAAa,OAAO,EAAE;;kCAElD,6LAAC,yMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;kCAClB,6LAAC;wBAAE,WAAU;kCACV,aAAa,oBAAoB;;;;;;kCAEpC,6LAAC;wBAAE,WAAU;;4BACV,WAAW,YAAY,WAAW;4BAAQ;4BAAQ;4BAAQ;4BACrD;4BAAS;;;;;;;;;;;;;0BAInB,6LAAC;gBACC,KAAK;gBACL,MAAK;gBACL,QAAQ;gBACR,UAAU;gBACV,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;gBAChD,WAAU;gBACV,UAAU;;;;;;0BAIZ,6LAAC,4LAAA,CAAA,kBAAe;0BACb,eAAe,GAAG,CAAC,CAAC,2BACnB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,MAAM;4BAAE,SAAS;4BAAG,GAAG,CAAC;wBAAG;wBAC3B,WAAU;;0CAEV,6LAAC;gCAAI,WAAU;;oCACZ,WAAW,MAAM,KAAK,6BACrB,6LAAC,oNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAEpB,WAAW,MAAM,KAAK,2BACrB,6LAAC,8NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;oCAExB,WAAW,MAAM,KAAK,yBACrB,6LAAC,uNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;;0CAI3B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDACV,WAAW,IAAI,CAAC,IAAI;;;;;;oCAEtB,WAAW,MAAM,KAAK,6BACrB,6LAAC,uIAAA,CAAA,WAAQ;wCAAC,OAAO,WAAW,QAAQ;wCAAE,WAAU;;;;;;oCAEjD,WAAW,MAAM,KAAK,2BACrB,6LAAC;wCAAE,WAAU;kDAAyB;;;;;;oCAEvC,WAAW,MAAM,KAAK,yBACrB,6LAAC;wCAAE,WAAU;kDAAuB;;;;;;;;;;;;;uBA7BnC,WAAW,EAAE;;;;;;;;;;YAqCvB,cAAc,MAAM,GAAG,mBACtB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,4LAAA,CAAA,kBAAe;8BACb,cAAc,GAAG,CAAC,CAAC,KAAK,sBACvB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAI;4BAClC,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAE;4BAChC,MAAM;gCAAE,SAAS;gCAAG,OAAO;4BAAI;4BAC/B,WAAU;;gCAET,QAAQ,qBACP,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,KAAK;wCACL,KAAK,CAAC,OAAO,EAAE,QAAQ,GAAG;wCAC1B,WAAU;wCACV,SAAS,CAAC;4CACR,EAAE,aAAa,CAAC,GAAG,GAAG;wCACxB;;;;;;;;;;yDAIJ,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;8CAIpB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAE,WAAU;kDACV,YAAY;;;;;;;;;;;gCAIhB,0BACC,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,CAAC;4CACR,EAAE,eAAe;4CACjB,SAAS;wCACX;kDAEA,cAAA,6LAAC,+LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;;2BAxCd;;;;;;;;;;;;;;;;;;;;;AAmDrB;GAvUgB;;QAaI,+HAAA,CAAA,WAAQ;;;KAbZ"}}, {"offset": {"line": 2428, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2434, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/Work/Repositories/templgen/src/components/ui/custom-dialog.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport {\n  <PERSON><PERSON>,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  <PERSON><PERSON>Header,\n  DialogTitle,\n} from \"@/components/ui/dialog\";\nimport { Button } from \"@/components/ui/button\";\nimport { Input } from \"@/components/ui/input\";\nimport { Label } from \"@/components/ui/label\";\nimport { AlertTriangle, CheckCircle, Info, X } from \"lucide-react\";\nimport { motion } from \"framer-motion\";\n\ninterface DialogOptions {\n  title: string;\n  message: string;\n  type?: \"alert\" | \"confirm\" | \"prompt\";\n  variant?: \"default\" | \"destructive\" | \"success\" | \"warning\";\n  confirmText?: string;\n  cancelText?: string;\n  placeholder?: string;\n  defaultValue?: string;\n}\n\ninterface DialogState extends DialogOptions {\n  isOpen: boolean;\n  resolve?: (value: boolean | string | null) => void;\n}\n\nlet dialogState: DialogState = {\n  title: \"\",\n  message: \"\",\n  type: \"alert\",\n  variant: \"default\",\n  isOpen: false,\n};\n\nlet setDialogState: React.Dispatch<React.SetStateAction<DialogState>> | null =\n  null;\n\n// Global dialog functions\nexport const showAlert = (\n  title: string,\n  message: string,\n  variant: DialogOptions[\"variant\"] = \"default\",\n): Promise<boolean> => {\n  return new Promise((resolve) => {\n    if (setDialogState) {\n      setDialogState({\n        title,\n        message,\n        type: \"alert\",\n        variant,\n        isOpen: true,\n        confirmText: \"OK\",\n        resolve: () => {\n          resolve(true);\n          if (setDialogState) {\n            setDialogState((prev) => ({ ...prev, isOpen: false }));\n          }\n        },\n      });\n    }\n  });\n};\n\nexport const showConfirm = (\n  title: string,\n  message: string,\n  options: {\n    variant?: DialogOptions[\"variant\"];\n    confirmText?: string;\n    cancelText?: string;\n  } = {},\n): Promise<boolean> => {\n  return new Promise((resolve) => {\n    if (setDialogState) {\n      setDialogState({\n        title,\n        message,\n        type: \"confirm\",\n        variant: options.variant || \"default\",\n        confirmText: options.confirmText || \"Confirm\",\n        cancelText: options.cancelText || \"Cancel\",\n        isOpen: true,\n        resolve: (value) => {\n          resolve(value as boolean);\n          if (setDialogState) {\n            setDialogState((prev) => ({ ...prev, isOpen: false }));\n          }\n        },\n      });\n    }\n  });\n};\n\nexport const showPrompt = (\n  title: string,\n  message: string,\n  options: {\n    placeholder?: string;\n    defaultValue?: string;\n    confirmText?: string;\n    cancelText?: string;\n  } = {},\n): Promise<string | null> => {\n  return new Promise((resolve) => {\n    if (setDialogState) {\n      setDialogState({\n        title,\n        message,\n        type: \"prompt\",\n        variant: \"default\",\n        placeholder: options.placeholder,\n        defaultValue: options.defaultValue,\n        confirmText: options.confirmText || \"OK\",\n        cancelText: options.cancelText || \"Cancel\",\n        isOpen: true,\n        resolve: (value) => {\n          resolve(value as string | null);\n          if (setDialogState) {\n            setDialogState((prev) => ({ ...prev, isOpen: false }));\n          }\n        },\n      });\n    }\n  });\n};\n\nexport function CustomDialogProvider() {\n  const [state, setState] = useState<DialogState>(dialogState);\n  const [inputValue, setInputValue] = useState(\"\");\n\n  useEffect(() => {\n    setDialogState = setState;\n    return () => {\n      setDialogState = null;\n    };\n  }, []);\n\n  useEffect(() => {\n    if (state.isOpen && state.type === \"prompt\") {\n      setInputValue(state.defaultValue || \"\");\n    }\n  }, [state.isOpen, state.type, state.defaultValue]);\n\n  const handleConfirm = () => {\n    if (state.resolve) {\n      if (state.type === \"prompt\") {\n        state.resolve(inputValue);\n      } else {\n        state.resolve(true);\n      }\n    }\n  };\n\n  const handleCancel = () => {\n    if (state.resolve) {\n      if (state.type === \"prompt\") {\n        state.resolve(null);\n      } else {\n        state.resolve(false);\n      }\n    }\n  };\n\n  const getIcon = () => {\n    switch (state.variant) {\n      case \"destructive\":\n        return <AlertTriangle className=\"h-6 w-6 text-red-500\" />;\n      case \"success\":\n        return <CheckCircle className=\"h-6 w-6 text-green-500\" />;\n      case \"warning\":\n        return <AlertTriangle className=\"h-6 w-6 text-yellow-500\" />;\n      default:\n        return <Info className=\"h-6 w-6 text-blue-500\" />;\n    }\n  };\n\n  const getButtonVariant = () => {\n    switch (state.variant) {\n      case \"destructive\":\n        return \"destructive\";\n      default:\n        return \"default\";\n    }\n  };\n\n  return (\n    <Dialog\n      open={state.isOpen}\n      onOpenChange={(open) => {\n        if (!open && state.resolve) {\n          handleCancel();\n        }\n      }}\n    >\n      <DialogContent className=\"sm:max-w-md\">\n        <DialogHeader>\n          <DialogTitle className=\"flex items-center gap-3\">\n            {getIcon()}\n            {state.title}\n          </DialogTitle>\n          <DialogDescription className=\"text-left\">\n            {state.message}\n          </DialogDescription>\n        </DialogHeader>\n\n        {state.type === \"prompt\" && (\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"prompt-input\">Enter value:</Label>\n            <Input\n              id=\"prompt-input\"\n              value={inputValue}\n              onChange={(e) => setInputValue(e.target.value)}\n              placeholder={state.placeholder}\n              onKeyDown={(e) => {\n                if (e.key === \"Enter\") {\n                  handleConfirm();\n                } else if (e.key === \"Escape\") {\n                  handleCancel();\n                }\n              }}\n              autoFocus\n            />\n          </div>\n        )}\n\n        <DialogFooter className=\"flex gap-2\">\n          {state.type !== \"alert\" && (\n            <Button variant=\"outline\" onClick={handleCancel}>\n              {state.cancelText}\n            </Button>\n          )}\n          <Button variant={getButtonVariant()} onClick={handleConfirm}>\n            {state.confirmText}\n          </Button>\n        </DialogFooter>\n      </DialogContent>\n    </Dialog>\n  );\n}\n\n// Hook for using dialogs in components\nexport function useDialog() {\n  return {\n    showAlert,\n    showConfirm,\n    showPrompt,\n  };\n}\n\n// Simple CustomDialog component for direct use\ninterface CustomDialogProps {\n  isOpen: boolean;\n  onClose: () => void;\n  onConfirm?: () => void;\n  title: string;\n  description: string;\n  confirmText?: string;\n  cancelText?: string;\n  confirmVariant?:\n    | \"default\"\n    | \"destructive\"\n    | \"outline\"\n    | \"secondary\"\n    | \"ghost\"\n    | \"link\";\n  isLoading?: boolean;\n  showHeader?: boolean;\n  className?: string;\n  children?: React.ReactNode;\n}\n\nexport function CustomDialog({\n  isOpen,\n  onClose,\n  onConfirm,\n  title,\n  description,\n  confirmText = \"Confirm\",\n  cancelText = \"Cancel\",\n  confirmVariant = \"default\",\n  isLoading = false,\n  showHeader = true,\n  className = \"\",\n  children,\n}: CustomDialogProps) {\n  const handleConfirm = () => {\n    if (onConfirm && !isLoading) {\n      onConfirm();\n    }\n  };\n\n  return (\n    <Dialog open={isOpen} onOpenChange={onClose}>\n      <DialogContent className={`sm:max-w-md ${className}`}>\n        {showHeader && (\n          <DialogHeader>\n            <DialogTitle>{title}</DialogTitle>\n            <DialogDescription>{description}</DialogDescription>\n          </DialogHeader>\n        )}\n\n        {children}\n\n        {onConfirm && (\n          <DialogFooter className=\"flex gap-2\">\n            <Button variant=\"outline\" onClick={onClose} disabled={isLoading}>\n              {cancelText}\n            </Button>\n            <Button\n              variant={confirmVariant}\n              onClick={handleConfirm}\n              disabled={isLoading}\n            >\n              {isLoading ? \"Loading...\" : confirmText}\n            </Button>\n          </DialogFooter>\n        )}\n      </DialogContent>\n    </Dialog>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;AAEA;AACA;AAQA;AACA;AACA;AACA;AAAA;AAAA;;;AAdA;;;;;;;AAiCA,IAAI,cAA2B;IAC7B,OAAO;IACP,SAAS;IACT,MAAM;IACN,SAAS;IACT,QAAQ;AACV;AAEA,IAAI,iBACF;AAGK,MAAM,YAAY,CACvB,OACA,SACA,UAAoC,SAAS;IAE7C,OAAO,IAAI,QAAQ,CAAC;QAClB,IAAI,gBAAgB;YAClB,eAAe;gBACb;gBACA;gBACA,MAAM;gBACN;gBACA,QAAQ;gBACR,aAAa;gBACb,SAAS;oBACP,QAAQ;oBACR,IAAI,gBAAgB;wBAClB,eAAe,CAAC,OAAS,CAAC;gCAAE,GAAG,IAAI;gCAAE,QAAQ;4BAAM,CAAC;oBACtD;gBACF;YACF;QACF;IACF;AACF;AAEO,MAAM,cAAc,CACzB,OACA,SACA,UAII,CAAC,CAAC;IAEN,OAAO,IAAI,QAAQ,CAAC;QAClB,IAAI,gBAAgB;YAClB,eAAe;gBACb;gBACA;gBACA,MAAM;gBACN,SAAS,QAAQ,OAAO,IAAI;gBAC5B,aAAa,QAAQ,WAAW,IAAI;gBACpC,YAAY,QAAQ,UAAU,IAAI;gBAClC,QAAQ;gBACR,SAAS,CAAC;oBACR,QAAQ;oBACR,IAAI,gBAAgB;wBAClB,eAAe,CAAC,OAAS,CAAC;gCAAE,GAAG,IAAI;gCAAE,QAAQ;4BAAM,CAAC;oBACtD;gBACF;YACF;QACF;IACF;AACF;AAEO,MAAM,aAAa,CACxB,OACA,SACA,UAKI,CAAC,CAAC;IAEN,OAAO,IAAI,QAAQ,CAAC;QAClB,IAAI,gBAAgB;YAClB,eAAe;gBACb;gBACA;gBACA,MAAM;gBACN,SAAS;gBACT,aAAa,QAAQ,WAAW;gBAChC,cAAc,QAAQ,YAAY;gBAClC,aAAa,QAAQ,WAAW,IAAI;gBACpC,YAAY,QAAQ,UAAU,IAAI;gBAClC,QAAQ;gBACR,SAAS,CAAC;oBACR,QAAQ;oBACR,IAAI,gBAAgB;wBAClB,eAAe,CAAC,OAAS,CAAC;gCAAE,GAAG,IAAI;gCAAE,QAAQ;4BAAM,CAAC;oBACtD;gBACF;YACF;QACF;IACF;AACF;AAEO,SAAS;;IACd,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAChD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;YACR,iBAAiB;YACjB;kDAAO;oBACL,iBAAiB;gBACnB;;QACF;yCAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;YACR,IAAI,MAAM,MAAM,IAAI,MAAM,IAAI,KAAK,UAAU;gBAC3C,cAAc,MAAM,YAAY,IAAI;YACtC;QACF;yCAAG;QAAC,MAAM,MAAM;QAAE,MAAM,IAAI;QAAE,MAAM,YAAY;KAAC;IAEjD,MAAM,gBAAgB;QACpB,IAAI,MAAM,OAAO,EAAE;YACjB,IAAI,MAAM,IAAI,KAAK,UAAU;gBAC3B,MAAM,OAAO,CAAC;YAChB,OAAO;gBACL,MAAM,OAAO,CAAC;YAChB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,MAAM,OAAO,EAAE;YACjB,IAAI,MAAM,IAAI,KAAK,UAAU;gBAC3B,MAAM,OAAO,CAAC;YAChB,OAAO;gBACL,MAAM,OAAO,CAAC;YAChB;QACF;IACF;IAEA,MAAM,UAAU;QACd,OAAQ,MAAM,OAAO;YACnB,KAAK;gBACH,qBAAO,6LAAC,2NAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YAClC,KAAK;gBACH,qBAAO,6LAAC,8NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,6LAAC,2NAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YAClC;gBACE,qBAAO,6LAAC,qMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;QAC3B;IACF;IAEA,MAAM,mBAAmB;QACvB,OAAQ,MAAM,OAAO;YACnB,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC,qIAAA,CAAA,SAAM;QACL,MAAM,MAAM,MAAM;QAClB,cAAc,CAAC;YACb,IAAI,CAAC,QAAQ,MAAM,OAAO,EAAE;gBAC1B;YACF;QACF;kBAEA,cAAA,6LAAC,qIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,qIAAA,CAAA,eAAY;;sCACX,6LAAC,qIAAA,CAAA,cAAW;4BAAC,WAAU;;gCACpB;gCACA,MAAM,KAAK;;;;;;;sCAEd,6LAAC,qIAAA,CAAA,oBAAiB;4BAAC,WAAU;sCAC1B,MAAM,OAAO;;;;;;;;;;;;gBAIjB,MAAM,IAAI,KAAK,0BACd,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,oIAAA,CAAA,QAAK;4BAAC,SAAQ;sCAAe;;;;;;sCAC9B,6LAAC,oIAAA,CAAA,QAAK;4BACJ,IAAG;4BACH,OAAO;4BACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4BAC7C,aAAa,MAAM,WAAW;4BAC9B,WAAW,CAAC;gCACV,IAAI,EAAE,GAAG,KAAK,SAAS;oCACrB;gCACF,OAAO,IAAI,EAAE,GAAG,KAAK,UAAU;oCAC7B;gCACF;4BACF;4BACA,SAAS;;;;;;;;;;;;8BAKf,6LAAC,qIAAA,CAAA,eAAY;oBAAC,WAAU;;wBACrB,MAAM,IAAI,KAAK,yBACd,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,SAAS;sCAChC,MAAM,UAAU;;;;;;sCAGrB,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAS;4BAAoB,SAAS;sCAC3C,MAAM,WAAW;;;;;;;;;;;;;;;;;;;;;;;AAM9B;GAhHgB;KAAA;AAmHT,SAAS;IACd,OAAO;QACL;QACA;QACA;IACF;AACF;AAwBO,SAAS,aAAa,EAC3B,MAAM,EACN,OAAO,EACP,SAAS,EACT,KAAK,EACL,WAAW,EACX,cAAc,SAAS,EACvB,aAAa,QAAQ,EACrB,iBAAiB,SAAS,EAC1B,YAAY,KAAK,EACjB,aAAa,IAAI,EACjB,YAAY,EAAE,EACd,QAAQ,EACU;IAClB,MAAM,gBAAgB;QACpB,IAAI,aAAa,CAAC,WAAW;YAC3B;QACF;IACF;IAEA,qBACE,6LAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,cAAc;kBAClC,cAAA,6LAAC,qIAAA,CAAA,gBAAa;YAAC,WAAW,CAAC,YAAY,EAAE,WAAW;;gBACjD,4BACC,6LAAC,qIAAA,CAAA,eAAY;;sCACX,6LAAC,qIAAA,CAAA,cAAW;sCAAE;;;;;;sCACd,6LAAC,qIAAA,CAAA,oBAAiB;sCAAE;;;;;;;;;;;;gBAIvB;gBAEA,2BACC,6LAAC,qIAAA,CAAA,eAAY;oBAAC,WAAU;;sCACtB,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,SAAS;4BAAS,UAAU;sCACnD;;;;;;sCAEH,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,SAAS;4BACT,UAAU;sCAET,YAAY,eAAe;;;;;;;;;;;;;;;;;;;;;;;AAO1C;MAjDgB"}}, {"offset": {"line": 2831, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2837, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/Work/Repositories/templgen/src/components/ui/otp-input.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useRef, useEffect } from \"react\";\nimport { Input } from \"@/components/ui/input\";\nimport { Button } from \"@/components/ui/button\";\nimport { motion } from \"framer-motion\";\n\ninterface OTPInputProps {\n  length?: number;\n  onComplete: (otp: string) => void;\n  onCancel?: () => void;\n  expectedOTP?: string;\n  title?: string;\n  description?: string;\n  disabled?: boolean;\n}\n\nexport function OTPInput({\n  length = 4,\n  onComplete,\n  onCancel,\n  expectedOTP = \"2132\",\n  title = \"Enter Confirmation Code\",\n  description = \"Please enter the confirmation code to proceed\",\n  disabled = false,\n}: OTPInputProps) {\n  const [otp, setOtp] = useState<string[]>(new Array(length).fill(\"\"));\n  const [error, setError] = useState(\"\");\n  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);\n\n  useEffect(() => {\n    // Focus first input on mount\n    if (inputRefs.current[0]) {\n      inputRefs.current[0].focus();\n    }\n  }, []);\n\n  const handleChange = (index: number, value: string) => {\n    if (disabled) return;\n\n    // Only allow numbers\n    if (value && !/^\\d$/.test(value)) return;\n\n    const newOtp = [...otp];\n    newOtp[index] = value;\n    setOtp(newOtp);\n    setError(\"\");\n\n    // Move to next input if value is entered\n    if (value && index < length - 1) {\n      inputRefs.current[index + 1]?.focus();\n    }\n\n    // Check if OTP is complete\n    if (newOtp.every((digit) => digit !== \"\")) {\n      const otpString = newOtp.join(\"\");\n      if (otpString === expectedOTP) {\n        onComplete(otpString);\n      } else {\n        setError(\"Invalid confirmation code. Please try again.\");\n        // Clear OTP after a delay\n        setTimeout(() => {\n          setOtp(new Array(length).fill(\"\"));\n          inputRefs.current[0]?.focus();\n        }, 1000);\n      }\n    }\n  };\n\n  const handleKeyDown = (index: number, e: React.KeyboardEvent) => {\n    if (disabled) return;\n\n    if (e.key === \"Backspace\") {\n      if (otp[index]) {\n        // Clear current input\n        const newOtp = [...otp];\n        newOtp[index] = \"\";\n        setOtp(newOtp);\n      } else if (index > 0) {\n        // Move to previous input and clear it\n        const newOtp = [...otp];\n        newOtp[index - 1] = \"\";\n        setOtp(newOtp);\n        inputRefs.current[index - 1]?.focus();\n      }\n    } else if (e.key === \"ArrowLeft\" && index > 0) {\n      inputRefs.current[index - 1]?.focus();\n    } else if (e.key === \"ArrowRight\" && index < length - 1) {\n      inputRefs.current[index + 1]?.focus();\n    } else if (e.key === \"Escape\" && onCancel) {\n      onCancel();\n    }\n  };\n\n  const handlePaste = (e: React.ClipboardEvent) => {\n    if (disabled) return;\n\n    e.preventDefault();\n    const pastedData = e.clipboardData.getData(\"text\");\n    const pastedDigits = pastedData.replace(/\\D/g, \"\").slice(0, length);\n\n    if (pastedDigits.length === length) {\n      const newOtp = pastedDigits.split(\"\");\n      setOtp(newOtp);\n      setError(\"\");\n\n      // Check if pasted OTP is correct\n      if (pastedDigits === expectedOTP) {\n        onComplete(pastedDigits);\n      } else {\n        setError(\"Invalid confirmation code. Please try again.\");\n        setTimeout(() => {\n          setOtp(new Array(length).fill(\"\"));\n          inputRefs.current[0]?.focus();\n        }, 1000);\n      }\n    }\n  };\n\n  const clearOTP = () => {\n    setOtp(new Array(length).fill(\"\"));\n    setError(\"\");\n    inputRefs.current[0]?.focus();\n  };\n\n  return (\n    <div className=\"space-y-4\">\n      <div className=\"text-center\">\n        <h3 className=\"text-lg font-semibold text-[var(--card-headline)]\">\n          {title}\n        </h3>\n        <p className=\"text-sm text-[var(--card-paragraph)] mt-1\">\n          {description}\n        </p>\n        <p className=\"text-xs text-[var(--paragraph)] mt-2\">\n          Expected code:{\" \"}\n          <span className=\"font-mono font-bold\">{expectedOTP}</span>\n        </p>\n      </div>\n\n      <div className=\"flex justify-center gap-2\">\n        {otp.map((digit, index) => (\n          <motion.div\n            key={index}\n            initial={{ scale: 0.8, opacity: 0 }}\n            animate={{ scale: 1, opacity: 1 }}\n            transition={{ delay: index * 0.1 }}\n          >\n            <Input\n              ref={(el) => {\n                inputRefs.current[index] = el;\n              }}\n              type=\"text\"\n              inputMode=\"numeric\"\n              maxLength={1}\n              value={digit}\n              onChange={(e) => handleChange(index, e.target.value)}\n              onKeyDown={(e) => handleKeyDown(index, e)}\n              onPaste={handlePaste}\n              disabled={disabled}\n              className={`\n                w-12 h-12 text-center text-lg font-bold\n                bg-[var(--input-background)] border-[var(--input-border-color)]\n                focus:border-[var(--link-color)] focus:ring-[var(--link-color)]\n                ${error ? \"border-red-500\" : \"\"}\n                ${disabled ? \"opacity-50 cursor-not-allowed\" : \"\"}\n              `}\n            />\n          </motion.div>\n        ))}\n      </div>\n\n      {error && (\n        <motion.div\n          initial={{ opacity: 0, y: -10 }}\n          animate={{ opacity: 1, y: 0 }}\n          className=\"text-center\"\n        >\n          <p className=\"text-sm text-red-500\">{error}</p>\n        </motion.div>\n      )}\n\n      <div className=\"flex justify-center gap-2\">\n        <Button\n          type=\"button\"\n          variant=\"outline\"\n          onClick={clearOTP}\n          disabled={disabled || otp.every((digit) => digit === \"\")}\n          className=\"text-sm\"\n        >\n          Clear\n        </Button>\n        {onCancel && (\n          <Button\n            type=\"button\"\n            variant=\"outline\"\n            onClick={onCancel}\n            disabled={disabled}\n            className=\"text-sm\"\n          >\n            Cancel\n          </Button>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAiBO,SAAS,SAAS,EACvB,SAAS,CAAC,EACV,UAAU,EACV,QAAQ,EACR,cAAc,MAAM,EACpB,QAAQ,yBAAyB,EACjC,cAAc,+CAA+C,EAC7D,WAAW,KAAK,EACF;;IACd,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,IAAI,MAAM,QAAQ,IAAI,CAAC;IAChE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAA+B,EAAE;IAExD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,6BAA6B;YAC7B,IAAI,UAAU,OAAO,CAAC,EAAE,EAAE;gBACxB,UAAU,OAAO,CAAC,EAAE,CAAC,KAAK;YAC5B;QACF;6BAAG,EAAE;IAEL,MAAM,eAAe,CAAC,OAAe;QACnC,IAAI,UAAU;QAEd,qBAAqB;QACrB,IAAI,SAAS,CAAC,OAAO,IAAI,CAAC,QAAQ;QAElC,MAAM,SAAS;eAAI;SAAI;QACvB,MAAM,CAAC,MAAM,GAAG;QAChB,OAAO;QACP,SAAS;QAET,yCAAyC;QACzC,IAAI,SAAS,QAAQ,SAAS,GAAG;YAC/B,UAAU,OAAO,CAAC,QAAQ,EAAE,EAAE;QAChC;QAEA,2BAA2B;QAC3B,IAAI,OAAO,KAAK,CAAC,CAAC,QAAU,UAAU,KAAK;YACzC,MAAM,YAAY,OAAO,IAAI,CAAC;YAC9B,IAAI,cAAc,aAAa;gBAC7B,WAAW;YACb,OAAO;gBACL,SAAS;gBACT,0BAA0B;gBAC1B,WAAW;oBACT,OAAO,IAAI,MAAM,QAAQ,IAAI,CAAC;oBAC9B,UAAU,OAAO,CAAC,EAAE,EAAE;gBACxB,GAAG;YACL;QACF;IACF;IAEA,MAAM,gBAAgB,CAAC,OAAe;QACpC,IAAI,UAAU;QAEd,IAAI,EAAE,GAAG,KAAK,aAAa;YACzB,IAAI,GAAG,CAAC,MAAM,EAAE;gBACd,sBAAsB;gBACtB,MAAM,SAAS;uBAAI;iBAAI;gBACvB,MAAM,CAAC,MAAM,GAAG;gBAChB,OAAO;YACT,OAAO,IAAI,QAAQ,GAAG;gBACpB,sCAAsC;gBACtC,MAAM,SAAS;uBAAI;iBAAI;gBACvB,MAAM,CAAC,QAAQ,EAAE,GAAG;gBACpB,OAAO;gBACP,UAAU,OAAO,CAAC,QAAQ,EAAE,EAAE;YAChC;QACF,OAAO,IAAI,EAAE,GAAG,KAAK,eAAe,QAAQ,GAAG;YAC7C,UAAU,OAAO,CAAC,QAAQ,EAAE,EAAE;QAChC,OAAO,IAAI,EAAE,GAAG,KAAK,gBAAgB,QAAQ,SAAS,GAAG;YACvD,UAAU,OAAO,CAAC,QAAQ,EAAE,EAAE;QAChC,OAAO,IAAI,EAAE,GAAG,KAAK,YAAY,UAAU;YACzC;QACF;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,IAAI,UAAU;QAEd,EAAE,cAAc;QAChB,MAAM,aAAa,EAAE,aAAa,CAAC,OAAO,CAAC;QAC3C,MAAM,eAAe,WAAW,OAAO,CAAC,OAAO,IAAI,KAAK,CAAC,GAAG;QAE5D,IAAI,aAAa,MAAM,KAAK,QAAQ;YAClC,MAAM,SAAS,aAAa,KAAK,CAAC;YAClC,OAAO;YACP,SAAS;YAET,iCAAiC;YACjC,IAAI,iBAAiB,aAAa;gBAChC,WAAW;YACb,OAAO;gBACL,SAAS;gBACT,WAAW;oBACT,OAAO,IAAI,MAAM,QAAQ,IAAI,CAAC;oBAC9B,UAAU,OAAO,CAAC,EAAE,EAAE;gBACxB,GAAG;YACL;QACF;IACF;IAEA,MAAM,WAAW;QACf,OAAO,IAAI,MAAM,QAAQ,IAAI,CAAC;QAC9B,SAAS;QACT,UAAU,OAAO,CAAC,EAAE,EAAE;IACxB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCACX;;;;;;kCAEH,6LAAC;wBAAE,WAAU;kCACV;;;;;;kCAEH,6LAAC;wBAAE,WAAU;;4BAAuC;4BACnC;0CACf,6LAAC;gCAAK,WAAU;0CAAuB;;;;;;;;;;;;;;;;;;0BAI3C,6LAAC;gBAAI,WAAU;0BACZ,IAAI,GAAG,CAAC,CAAC,OAAO,sBACf,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,SAAS;4BAAE,OAAO;4BAAK,SAAS;wBAAE;wBAClC,SAAS;4BAAE,OAAO;4BAAG,SAAS;wBAAE;wBAChC,YAAY;4BAAE,OAAO,QAAQ;wBAAI;kCAEjC,cAAA,6LAAC,oIAAA,CAAA,QAAK;4BACJ,KAAK,CAAC;gCACJ,UAAU,OAAO,CAAC,MAAM,GAAG;4BAC7B;4BACA,MAAK;4BACL,WAAU;4BACV,WAAW;4BACX,OAAO;4BACP,UAAU,CAAC,IAAM,aAAa,OAAO,EAAE,MAAM,CAAC,KAAK;4BACnD,WAAW,CAAC,IAAM,cAAc,OAAO;4BACvC,SAAS;4BACT,UAAU;4BACV,WAAW,CAAC;;;;gBAIV,EAAE,QAAQ,mBAAmB,GAAG;gBAChC,EAAE,WAAW,kCAAkC,GAAG;cACpD,CAAC;;;;;;uBAvBE;;;;;;;;;;YA6BV,uBACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG,CAAC;gBAAG;gBAC9B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,WAAU;0BAEV,cAAA,6LAAC;oBAAE,WAAU;8BAAwB;;;;;;;;;;;0BAIzC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,qIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAQ;wBACR,SAAS;wBACT,UAAU,YAAY,IAAI,KAAK,CAAC,CAAC,QAAU,UAAU;wBACrD,WAAU;kCACX;;;;;;oBAGA,0BACC,6LAAC,qIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAQ;wBACR,SAAS;wBACT,UAAU;wBACV,WAAU;kCACX;;;;;;;;;;;;;;;;;;AAOX;GA7LgB;KAAA"}}, {"offset": {"line": 3112, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3118, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/Work/Repositories/templgen/src/components/ui/secure-bulk-delete.tsx"], "sourcesContent": ["\"use client\";\n\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { CustomDialog } from \"@/components/ui/custom-dialog\";\nimport { OTPInput } from \"@/components/ui/otp-input\";\nimport { AnimatePresence, motion } from \"framer-motion\";\nimport { <PERSON><PERSON><PERSON>riangle, CheckCircle, Shield, Trash2 } from \"lucide-react\";\nimport { useState } from \"react\";\nimport { toast } from \"sonner\";\n\ninterface SecureBulkDeleteProps {\n  isOpen: boolean;\n  onClose: () => void;\n  onConfirm: () => Promise<void>;\n  title: string;\n  description: string;\n  itemCount: number;\n  itemType: string;\n  isDeleting?: boolean;\n  requireOTP?: boolean;\n  otpCode?: string;\n}\n\nexport function SecureBulkDelete({\n  isOpen,\n  onClose,\n  onConfirm,\n  title,\n  description,\n  itemCount,\n  itemType,\n  isDeleting = false,\n  requireOTP = true,\n  otpCode = \"2132\",\n}: SecureBulkDeleteProps) {\n  const [step, setStep] = useState<\"warning\" | \"otp\" | \"deleting\" | \"success\">(\n    \"warning\"\n  );\n  const [error, setError] = useState(\"\");\n\n  const handleClose = () => {\n    if (!isDeleting) {\n      setStep(\"warning\");\n      setError(\"\");\n      onClose();\n    }\n  };\n\n  const handleProceedToOTP = () => {\n    if (requireOTP) {\n      setStep(\"otp\");\n    } else {\n      handleConfirmDelete();\n    }\n    setError(\"\");\n  };\n\n  const handleOTPComplete = async (otp: string) => {\n    if (otp === otpCode) {\n      await handleConfirmDelete();\n    } else {\n      setError(\"Invalid confirmation code. Please try again.\");\n      toast.error(\"Invalid confirmation code\");\n    }\n  };\n\n  const handleConfirmDelete = async () => {\n    try {\n      setStep(\"deleting\");\n      setError(\"\");\n      await onConfirm();\n      setStep(\"success\");\n\n      toast.success(\n        `Successfully deleted ${itemCount} ${itemType}${itemCount !== 1 ? \"s\" : \"\"}`\n      );\n\n      // Auto-close after success\n      setTimeout(() => {\n        handleClose();\n      }, 2000);\n    } catch (error) {\n      setError(\"Failed to delete items. Please try again.\");\n      setStep(requireOTP ? \"otp\" : \"warning\");\n      toast.error(\"Failed to delete items\");\n    }\n  };\n\n  const handleOTPCancel = () => {\n    setStep(\"warning\");\n    setError(\"\");\n  };\n\n  const getStepContent = () => {\n    switch (step) {\n      case \"warning\":\n        return (\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            exit={{ opacity: 0, y: -20 }}\n            className=\"space-y-6\"\n          >\n            <div className=\"flex items-center justify-center\">\n              <div className=\"rounded-full bg-red-100 dark:bg-red-900/20 p-4\">\n                <AlertTriangle className=\"h-10 w-10 text-red-600\" />\n              </div>\n            </div>\n\n            <div className=\"text-center space-y-3\">\n              <h3 className=\"text-xl font-semibold text-[var(--card-headline)]\">\n                {title}\n              </h3>\n              <p className=\"text-[var(--card-paragraph)]\">{description}</p>\n\n              <div className=\"bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800 rounded-[12px] p-4 mt-4\">\n                <div className=\"flex items-start gap-3\">\n                  <AlertTriangle className=\"h-5 w-5 text-red-600 flex-shrink-0 mt-0.5\" />\n                  <div className=\"text-left\">\n                    <p className=\"text-sm font-medium text-red-800 dark:text-red-200\">\n                      Permanent Deletion Warning\n                    </p>\n                    <p className=\"text-sm text-red-700 dark:text-red-300 mt-1\">\n                      You are about to permanently delete{\" \"}\n                      <span className=\"font-bold\">{itemCount}</span> {itemType}\n                      {itemCount !== 1 ? \"s\" : \"\"}. This action cannot be undone\n                      and all data will be lost forever.\n                    </p>\n                  </div>\n                </div>\n              </div>\n\n              {requireOTP && (\n                <div className=\"bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-[12px]2px] p-4\">\n                  <div className=\"flex items-start gap-3\">\n                    <Shield className=\"h-5 w-5 text-blue-600 flex-shrink-0 mt-0.5\" />\n                    <div className=\"text-left\">\n                      <p className=\"text-sm font-medium text-blue-800 dark:text-blue-200\">\n                        Security Verification Required\n                      </p>\n                      <p className=\"text-sm text-blue-700 dark:text-blue-300 mt-1\">\n                        You will need to enter a confirmation code to proceed\n                        with this deletion.\n                      </p>\n                    </div>\n                  </div>\n                </div>\n              )}\n            </div>\n\n            <div className=\"flex gap-3 justify-center pt-4\">\n              <Button\n                variant=\"outline\"\n                onClick={handleClose}\n                disabled={isDeleting}\n                className=\"min-w-[100px]\"\n              >\n                Cancel\n              </Button>\n              <Button\n                variant=\"destructive\"\n                onClick={handleProceedToOTP}\n                disabled={isDeleting}\n                className=\"bg-red-600 hover:bg-red-700 min-w-[140px]\"\n              >\n                <Trash2 className=\"h-4 w-4 mr-2\" />\n                {requireOTP ? \"Continue\" : \"Delete Now\"}\n              </Button>\n            </div>\n          </motion.div>\n        );\n\n      case \"otp\":\n        return (\n          <motion.div\n            initial={{ opacity: 0, x: 20 }}\n            animate={{ opacity: 1, x: 0 }}\n            exit={{ opacity: 0, x: -20 }}\n            className=\"space-y-6\"\n          >\n            <div className=\"text-center space-y-2\">\n              <div className=\"flex items-center justify-center mb-4\">\n                <div className=\"rounded-full bg-blue-100 dark:bg-blue-900/20 p-4\">\n                  <Shield className=\"h-10 w-10 text-blue-600\" />\n                </div>\n              </div>\n\n              <h3 className=\"text-xl font-semibold text-[var(--card-headline)]\">\n                Security Confirmation\n              </h3>\n              <p className=\"text-[var(--card-paragraph)]\">\n                Enter the confirmation code to delete {itemCount} {itemType}\n                {itemCount !== 1 ? \"s\" : \"\"}\n              </p>\n            </div>\n\n            <OTPInput\n              length={4}\n              onComplete={handleOTPComplete}\n              onCancel={handleOTPCancel}\n              expectedOTP={otpCode}\n              title=\"\"\n              description=\"Enter the 4-digit confirmation code\"\n              disabled={isDeleting}\n            />\n\n            {error && (\n              <motion.div\n                initial={{ opacity: 0, y: 10 }}\n                animate={{ opacity: 1, y: 0 }}\n                className=\"text-center\"\n              >\n                <p className=\"text-sm text-red-500 flex items-center justify-center gap-2\">\n                  <AlertTriangle className=\"h-4 w-4\" />\n                  {error}\n                </p>\n              </motion.div>\n            )}\n          </motion.div>\n        );\n\n      case \"deleting\":\n        return (\n          <motion.div\n            initial={{ opacity: 0, scale: 0.9 }}\n            animate={{ opacity: 1, scale: 1 }}\n            className=\"space-y-6 text-center\"\n          >\n            <div className=\"flex items-center justify-center\">\n              <div className=\"rounded-full bg-blue-100 dark:bg-blue-900/20 p-4\">\n                <motion.div\n                  animate={{ rotate: 360 }}\n                  transition={{ duration: 2, repeat: Infinity, ease: \"linear\" }}\n                >\n                  <Trash2 className=\"h-10 w-10 text-blue-600\" />\n                </motion.div>\n              </div>\n            </div>\n\n            <div className=\"space-y-2\">\n              <h3 className=\"text-xl font-semibold text-[var(--card-headline)]\">\n                Deleting {itemType}s...\n              </h3>\n              <p className=\"text-[var(--card-paragraph)]\">\n                Please wait while we securely delete {itemCount} {itemType}\n                {itemCount !== 1 ? \"s\" : \"\"}\n              </p>\n            </div>\n\n            <div className=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3\">\n              <motion.div\n                className=\"bg-blue-600 h-3 rounded-full\"\n                initial={{ width: 0 }}\n                animate={{ width: \"100%\" }}\n                transition={{ duration: 3, ease: \"easeInOut\" }}\n              />\n            </div>\n          </motion.div>\n        );\n\n      case \"success\":\n        return (\n          <motion.div\n            initial={{ opacity: 0, scale: 0.9 }}\n            animate={{ opacity: 1, scale: 1 }}\n            className=\"space-y-6 text-center\"\n          >\n            <div className=\"flex items-center justify-center\">\n              <motion.div\n                initial={{ scale: 0 }}\n                animate={{ scale: 1 }}\n                transition={{ delay: 0.2, type: \"spring\", stiffness: 200 }}\n                className=\"rounded-full bg-green-100 dark:bg-green-900/20 p-4\"\n              >\n                <CheckCircle className=\"h-10 w-10 text-green-600\" />\n              </motion.div>\n            </div>\n\n            <div className=\"space-y-2\">\n              <h3 className=\"text-xl font-semibold text-green-600\">\n                Successfully Deleted!\n              </h3>\n              <p className=\"text-[var(--card-paragraph)]\">\n                {itemCount} {itemType}\n                {itemCount !== 1 ? \"s\" : \"\"} have been permanently deleted.\n              </p>\n              <p className=\"text-sm text-[var(--card-paragraph)]\">\n                This dialog will close automatically...\n              </p>\n            </div>\n          </motion.div>\n        );\n\n      default:\n        return null;\n    }\n  };\n\n  return (\n    <CustomDialog\n      isOpen={isOpen}\n      onClose={handleClose}\n      title=\"Secure Bulk Delete\"\n      description=\"Confirm bulk deletion with security verification\"\n      showHeader={false}\n      className=\"sm:max-w-lg\"\n    >\n      <AnimatePresence mode=\"wait\">{getStepContent()}</AnimatePresence>\n    </CustomDialog>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAGA;AACA;AAHA;AACA;AAAA;AAAA;AAAA;AADA;;;AALA;;;;;;;;AAuBO,SAAS,iBAAiB,EAC/B,MAAM,EACN,OAAO,EACP,SAAS,EACT,KAAK,EACL,WAAW,EACX,SAAS,EACT,QAAQ,EACR,aAAa,KAAK,EAClB,aAAa,IAAI,EACjB,UAAU,MAAM,EACM;;IACtB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAC7B;IAEF,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,cAAc;QAClB,IAAI,CAAC,YAAY;YACf,QAAQ;YACR,SAAS;YACT;QACF;IACF;IAEA,MAAM,qBAAqB;QACzB,IAAI,YAAY;YACd,QAAQ;QACV,OAAO;YACL;QACF;QACA,SAAS;IACX;IAEA,MAAM,oBAAoB,OAAO;QAC/B,IAAI,QAAQ,SAAS;YACnB,MAAM;QACR,OAAO;YACL,SAAS;YACT,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,sBAAsB;QAC1B,IAAI;YACF,QAAQ;YACR,SAAS;YACT,MAAM;YACN,QAAQ;YAER,2IAAA,CAAA,QAAK,CAAC,OAAO,CACX,CAAC,qBAAqB,EAAE,UAAU,CAAC,EAAE,WAAW,cAAc,IAAI,MAAM,IAAI;YAG9E,2BAA2B;YAC3B,WAAW;gBACT;YACF,GAAG;QACL,EAAE,OAAO,OAAO;YACd,SAAS;YACT,QAAQ,aAAa,QAAQ;YAC7B,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,kBAAkB;QACtB,QAAQ;QACR,SAAS;IACX;IAEA,MAAM,iBAAiB;QACrB,OAAQ;YACN,KAAK;gBACH,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,MAAM;wBAAE,SAAS;wBAAG,GAAG,CAAC;oBAAG;oBAC3B,WAAU;;sCAEV,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,2NAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;;;;;;;;;;;sCAI7B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CACX;;;;;;8CAEH,6LAAC;oCAAE,WAAU;8CAAgC;;;;;;8CAE7C,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,2NAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;0DACzB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAqD;;;;;;kEAGlE,6LAAC;wDAAE,WAAU;;4DAA8C;4DACrB;0EACpC,6LAAC;gEAAK,WAAU;0EAAa;;;;;;4DAAiB;4DAAE;4DAC/C,cAAc,IAAI,MAAM;4DAAG;;;;;;;;;;;;;;;;;;;;;;;;gCAOnC,4BACC,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAuD;;;;;;kEAGpE,6LAAC;wDAAE,WAAU;kEAAgD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAUvE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS;oCACT,UAAU;oCACV,WAAU;8CACX;;;;;;8CAGD,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS;oCACT,UAAU;oCACV,WAAU;;sDAEV,6LAAC,6MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCACjB,aAAa,aAAa;;;;;;;;;;;;;;;;;;;YAMrC,KAAK;gBACH,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,MAAM;wBAAE,SAAS;wBAAG,GAAG,CAAC;oBAAG;oBAC3B,WAAU;;sCAEV,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;;;;;;8CAItB,6LAAC;oCAAG,WAAU;8CAAoD;;;;;;8CAGlE,6LAAC;oCAAE,WAAU;;wCAA+B;wCACH;wCAAU;wCAAE;wCAClD,cAAc,IAAI,MAAM;;;;;;;;;;;;;sCAI7B,6LAAC,2IAAA,CAAA,WAAQ;4BACP,QAAQ;4BACR,YAAY;4BACZ,UAAU;4BACV,aAAa;4BACb,OAAM;4BACN,aAAY;4BACZ,UAAU;;;;;;wBAGX,uBACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,WAAU;sCAEV,cAAA,6LAAC;gCAAE,WAAU;;kDACX,6LAAC,2NAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;oCACxB;;;;;;;;;;;;;;;;;;YAOb,KAAK;gBACH,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,OAAO;oBAAI;oBAClC,SAAS;wBAAE,SAAS;wBAAG,OAAO;oBAAE;oBAChC,WAAU;;sCAEV,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,QAAQ;oCAAI;oCACvB,YAAY;wCAAE,UAAU;wCAAG,QAAQ;wCAAU,MAAM;oCAAS;8CAE5D,cAAA,6LAAC,6MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;sCAKxB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;;wCAAoD;wCACtD;wCAAS;;;;;;;8CAErB,6LAAC;oCAAE,WAAU;;wCAA+B;wCACJ;wCAAU;wCAAE;wCACjD,cAAc,IAAI,MAAM;;;;;;;;;;;;;sCAI7B,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCAAE,OAAO;gCAAE;gCACpB,SAAS;oCAAE,OAAO;gCAAO;gCACzB,YAAY;oCAAE,UAAU;oCAAG,MAAM;gCAAY;;;;;;;;;;;;;;;;;YAMvD,KAAK;gBACH,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,OAAO;oBAAI;oBAClC,SAAS;wBAAE,SAAS;wBAAG,OAAO;oBAAE;oBAChC,WAAU;;sCAEV,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,OAAO;gCAAE;gCACpB,SAAS;oCAAE,OAAO;gCAAE;gCACpB,YAAY;oCAAE,OAAO;oCAAK,MAAM;oCAAU,WAAW;gCAAI;gCACzD,WAAU;0CAEV,cAAA,6LAAC,8NAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;;;;;;;;;;;sCAI3B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAuC;;;;;;8CAGrD,6LAAC;oCAAE,WAAU;;wCACV;wCAAU;wCAAE;wCACZ,cAAc,IAAI,MAAM;wCAAG;;;;;;;8CAE9B,6LAAC;oCAAE,WAAU;8CAAuC;;;;;;;;;;;;;;;;;;YAO5D;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC,+IAAA,CAAA,eAAY;QACX,QAAQ;QACR,SAAS;QACT,OAAM;QACN,aAAY;QACZ,YAAY;QACZ,WAAU;kBAEV,cAAA,6LAAC,4LAAA,CAAA,kBAAe;YAAC,MAAK;sBAAQ;;;;;;;;;;;AAGpC;GA/RgB;KAAA"}}, {"offset": {"line": 3764, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3770, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/Work/Repositories/templgen/src/components/ui/page-transition.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { usePathname } from \"next/navigation\";\nimport { ReactNode } from \"react\";\n\ninterface PageTransitionProps {\n  children: ReactNode;\n  className?: string;\n}\n\n// Professional transition variants\nconst pageVariants = {\n  initial: {\n    opacity: 0,\n    y: 20,\n    scale: 0.98,\n  },\n  in: {\n    opacity: 1,\n    y: 0,\n    scale: 1,\n  },\n  out: {\n    opacity: 0,\n    y: -20,\n    scale: 1.02,\n  },\n};\n\nconst pageTransition = {\n  type: \"tween\",\n  ease: \"anticipate\",\n  duration: 0.4,\n};\n\n// Slide transition for sidebar navigation\nconst slideVariants = {\n  initial: {\n    opacity: 0,\n    x: -30,\n  },\n  in: {\n    opacity: 1,\n    x: 0,\n  },\n  out: {\n    opacity: 0,\n    x: 30,\n  },\n};\n\nconst slideTransition = {\n  type: \"tween\",\n  ease: \"easeInOut\",\n  duration: 0.3,\n};\n\n// Fade transition for subtle changes\nconst fadeVariants = {\n  initial: {\n    opacity: 0,\n  },\n  in: {\n    opacity: 1,\n  },\n  out: {\n    opacity: 0,\n  },\n};\n\nconst fadeTransition = {\n  type: \"tween\",\n  ease: \"easeInOut\",\n  duration: 0.2,\n};\n\nexport function PageTransition({\n  children,\n  className = \"\",\n}: PageTransitionProps) {\n  const pathname = usePathname();\n\n  return (\n    <AnimatePresence mode=\"wait\" initial={false}>\n      <motion.div\n        key={pathname}\n        initial=\"initial\"\n        animate=\"in\"\n        exit=\"out\"\n        variants={pageVariants}\n        transition={pageTransition}\n        className={className}\n      >\n        {children}\n      </motion.div>\n    </AnimatePresence>\n  );\n}\n\nexport function SlideTransition({\n  children,\n  className = \"\",\n}: PageTransitionProps) {\n  const pathname = usePathname();\n\n  return (\n    <AnimatePresence mode=\"wait\" initial={false}>\n      <motion.div\n        key={pathname}\n        initial=\"initial\"\n        animate=\"in\"\n        exit=\"out\"\n        variants={slideVariants}\n        transition={slideTransition}\n        className={className}\n      >\n        {children}\n      </motion.div>\n    </AnimatePresence>\n  );\n}\n\nexport function FadeTransition({\n  children,\n  className = \"\",\n}: PageTransitionProps) {\n  const pathname = usePathname();\n\n  return (\n    <AnimatePresence mode=\"wait\" initial={false}>\n      <motion.div\n        key={pathname}\n        initial=\"initial\"\n        animate=\"in\"\n        exit=\"out\"\n        variants={fadeVariants}\n        transition={fadeTransition}\n        className={className}\n      >\n        {children}\n      </motion.div>\n    </AnimatePresence>\n  );\n}\n\n// Staggered children animation for lists and grids\nexport const staggerContainer = {\n  initial: {},\n  animate: {\n    transition: {\n      staggerChildren: 0.1,\n    },\n  },\n};\n\nexport const staggerItem = {\n  initial: {\n    opacity: 0,\n    y: 20,\n  },\n  animate: {\n    opacity: 1,\n    y: 0,\n    transition: {\n      type: \"tween\",\n      ease: \"easeOut\",\n      duration: 0.3,\n    },\n  },\n};\n\n// Loading transition\nexport const loadingVariants = {\n  initial: {\n    opacity: 0,\n    scale: 0.8,\n  },\n  animate: {\n    opacity: 1,\n    scale: 1,\n    transition: {\n      type: \"spring\",\n      stiffness: 300,\n      damping: 20,\n    },\n  },\n  exit: {\n    opacity: 0,\n    scale: 0.8,\n    transition: {\n      type: \"tween\",\n      ease: \"easeInOut\",\n      duration: 0.2,\n    },\n  },\n};\n\n// Modal/Dialog transitions\nexport const modalVariants = {\n  initial: {\n    opacity: 0,\n    scale: 0.8,\n    y: 50,\n  },\n  animate: {\n    opacity: 1,\n    scale: 1,\n    y: 0,\n    transition: {\n      type: \"spring\",\n      stiffness: 300,\n      damping: 25,\n    },\n  },\n  exit: {\n    opacity: 0,\n    scale: 0.8,\n    y: 50,\n    transition: {\n      type: \"tween\",\n      ease: \"easeInOut\",\n      duration: 0.2,\n    },\n  },\n};\n\nexport const backdropVariants = {\n  initial: {\n    opacity: 0,\n  },\n  animate: {\n    opacity: 1,\n    transition: {\n      duration: 0.2,\n    },\n  },\n  exit: {\n    opacity: 0,\n    transition: {\n      duration: 0.2,\n    },\n  },\n};\n\n// Card hover animations\nexport const cardHoverVariants = {\n  initial: {\n    scale: 1,\n    y: 0,\n    boxShadow: \"0 1px 3px rgba(0, 0, 0, 0.1)\",\n  },\n  hover: {\n    scale: 1.02,\n    y: -5,\n    boxShadow: \"0 10px 25px rgba(0, 0, 0, 0.15)\",\n    transition: {\n      type: \"spring\",\n      stiffness: 300,\n      damping: 20,\n    },\n  },\n};\n\n// Button press animation\nexport const buttonPressVariants = {\n  initial: {\n    scale: 1,\n  },\n  tap: {\n    scale: 0.95,\n    transition: {\n      type: \"spring\",\n      stiffness: 400,\n      damping: 17,\n    },\n  },\n};\n\n// Navigation link active state\nexport const navLinkVariants = {\n  initial: {\n    backgroundColor: \"transparent\",\n    color: \"var(--paragraph)\",\n  },\n  active: {\n    backgroundColor: \"var(--link-color)\",\n    color: \"white\",\n    transition: {\n      type: \"tween\",\n      ease: \"easeInOut\",\n      duration: 0.2,\n    },\n  },\n  hover: {\n    backgroundColor: \"var(--card-background-effect)\",\n    transition: {\n      type: \"tween\",\n      ease: \"easeInOut\",\n      duration: 0.15,\n    },\n  },\n};\n\n// Notification/Toast animations\nexport const toastVariants = {\n  initial: {\n    opacity: 0,\n    y: 50,\n    scale: 0.3,\n  },\n  animate: {\n    opacity: 1,\n    y: 0,\n    scale: 1,\n    transition: {\n      type: \"spring\",\n      stiffness: 500,\n      damping: 30,\n    },\n  },\n  exit: {\n    opacity: 0,\n    y: 20,\n    scale: 0.5,\n    transition: {\n      type: \"tween\",\n      ease: \"easeIn\",\n      duration: 0.2,\n    },\n  },\n};\n\n// Sidebar collapse/expand animation\nexport const sidebarVariants = {\n  expanded: {\n    width: \"280px\",\n    transition: {\n      type: \"spring\",\n      stiffness: 300,\n      damping: 30,\n    },\n  },\n  collapsed: {\n    width: \"80px\",\n    transition: {\n      type: \"spring\",\n      stiffness: 300,\n      damping: 30,\n    },\n  },\n};\n\n// Form field focus animation\nexport const fieldFocusVariants = {\n  initial: {\n    borderColor: \"var(--input-border-color)\",\n    boxShadow: \"none\",\n  },\n  focus: {\n    borderColor: \"var(--link-color)\",\n    boxShadow: \"0 0 0 3px rgba(59, 130, 246, 0.1)\",\n    transition: {\n      type: \"tween\",\n      ease: \"easeOut\",\n      duration: 0.15,\n    },\n  },\n};\n\n// Success/Error state animations\nexport const statusVariants = {\n  success: {\n    borderColor: \"#10b981\",\n    backgroundColor: \"#f0fdf4\",\n    transition: {\n      type: \"tween\",\n      ease: \"easeOut\",\n      duration: 0.3,\n    },\n  },\n  error: {\n    borderColor: \"#ef4444\",\n    backgroundColor: \"#fef2f2\",\n    transition: {\n      type: \"tween\",\n      ease: \"easeOut\",\n      duration: 0.3,\n    },\n  },\n  initial: {\n    borderColor: \"var(--input-border-color)\",\n    backgroundColor: \"var(--input-background)\",\n  },\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AADA;AAAA;;;AAFA;;;AAWA,mCAAmC;AACnC,MAAM,eAAe;IACnB,SAAS;QACP,SAAS;QACT,GAAG;QACH,OAAO;IACT;IACA,IAAI;QACF,SAAS;QACT,GAAG;QACH,OAAO;IACT;IACA,KAAK;QACH,SAAS;QACT,GAAG,CAAC;QACJ,OAAO;IACT;AACF;AAEA,MAAM,iBAAiB;IACrB,MAAM;IACN,MAAM;IACN,UAAU;AACZ;AAEA,0CAA0C;AAC1C,MAAM,gBAAgB;IACpB,SAAS;QACP,SAAS;QACT,GAAG,CAAC;IACN;IACA,IAAI;QACF,SAAS;QACT,GAAG;IACL;IACA,KAAK;QACH,SAAS;QACT,GAAG;IACL;AACF;AAEA,MAAM,kBAAkB;IACtB,MAAM;IACN,MAAM;IACN,UAAU;AACZ;AAEA,qCAAqC;AACrC,MAAM,eAAe;IACnB,SAAS;QACP,SAAS;IACX;IACA,IAAI;QACF,SAAS;IACX;IACA,KAAK;QACH,SAAS;IACX;AACF;AAEA,MAAM,iBAAiB;IACrB,MAAM;IACN,MAAM;IACN,UAAU;AACZ;AAEO,SAAS,eAAe,EAC7B,QAAQ,EACR,YAAY,EAAE,EACM;;IACpB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,qBACE,6LAAC,4LAAA,CAAA,kBAAe;QAAC,MAAK;QAAO,SAAS;kBACpC,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YAET,SAAQ;YACR,SAAQ;YACR,MAAK;YACL,UAAU;YACV,YAAY;YACZ,WAAW;sBAEV;WARI;;;;;;;;;;AAYb;GArBgB;;QAIG,qIAAA,CAAA,cAAW;;;KAJd;AAuBT,SAAS,gBAAgB,EAC9B,QAAQ,EACR,YAAY,EAAE,EACM;;IACpB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,qBACE,6LAAC,4LAAA,CAAA,kBAAe;QAAC,MAAK;QAAO,SAAS;kBACpC,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YAET,SAAQ;YACR,SAAQ;YACR,MAAK;YACL,UAAU;YACV,YAAY;YACZ,WAAW;sBAEV;WARI;;;;;;;;;;AAYb;IArBgB;;QAIG,qIAAA,CAAA,cAAW;;;MAJd;AAuBT,SAAS,eAAe,EAC7B,QAAQ,EACR,YAAY,EAAE,EACM;;IACpB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,qBACE,6LAAC,4LAAA,CAAA,kBAAe;QAAC,MAAK;QAAO,SAAS;kBACpC,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YAET,SAAQ;YACR,SAAQ;YACR,MAAK;YACL,UAAU;YACV,YAAY;YACZ,WAAW;sBAEV;WARI;;;;;;;;;;AAYb;IArBgB;;QAIG,qIAAA,CAAA,cAAW;;;MAJd;AAwBT,MAAM,mBAAmB;IAC9B,SAAS,CAAC;IACV,SAAS;QACP,YAAY;YACV,iBAAiB;QACnB;IACF;AACF;AAEO,MAAM,cAAc;IACzB,SAAS;QACP,SAAS;QACT,GAAG;IACL;IACA,SAAS;QACP,SAAS;QACT,GAAG;QACH,YAAY;YACV,MAAM;YACN,MAAM;YACN,UAAU;QACZ;IACF;AACF;AAGO,MAAM,kBAAkB;IAC7B,SAAS;QACP,SAAS;QACT,OAAO;IACT;IACA,SAAS;QACP,SAAS;QACT,OAAO;QACP,YAAY;YACV,MAAM;YACN,WAAW;YACX,SAAS;QACX;IACF;IACA,MAAM;QACJ,SAAS;QACT,OAAO;QACP,YAAY;YACV,MAAM;YACN,MAAM;YACN,UAAU;QACZ;IACF;AACF;AAGO,MAAM,gBAAgB;IAC3B,SAAS;QACP,SAAS;QACT,OAAO;QACP,GAAG;IACL;IACA,SAAS;QACP,SAAS;QACT,OAAO;QACP,GAAG;QACH,YAAY;YACV,MAAM;YACN,WAAW;YACX,SAAS;QACX;IACF;IACA,MAAM;QACJ,SAAS;QACT,OAAO;QACP,GAAG;QACH,YAAY;YACV,MAAM;YACN,MAAM;YACN,UAAU;QACZ;IACF;AACF;AAEO,MAAM,mBAAmB;IAC9B,SAAS;QACP,SAAS;IACX;IACA,SAAS;QACP,SAAS;QACT,YAAY;YACV,UAAU;QACZ;IACF;IACA,MAAM;QACJ,SAAS;QACT,YAAY;YACV,UAAU;QACZ;IACF;AACF;AAGO,MAAM,oBAAoB;IAC/B,SAAS;QACP,OAAO;QACP,GAAG;QACH,WAAW;IACb;IACA,OAAO;QACL,OAAO;QACP,GAAG,CAAC;QACJ,WAAW;QACX,YAAY;YACV,MAAM;YACN,WAAW;YACX,SAAS;QACX;IACF;AACF;AAGO,MAAM,sBAAsB;IACjC,SAAS;QACP,OAAO;IACT;IACA,KAAK;QACH,OAAO;QACP,YAAY;YACV,MAAM;YACN,WAAW;YACX,SAAS;QACX;IACF;AACF;AAGO,MAAM,kBAAkB;IAC7B,SAAS;QACP,iBAAiB;QACjB,OAAO;IACT;IACA,QAAQ;QACN,iBAAiB;QACjB,OAAO;QACP,YAAY;YACV,MAAM;YACN,MAAM;YACN,UAAU;QACZ;IACF;IACA,OAAO;QACL,iBAAiB;QACjB,YAAY;YACV,MAAM;YACN,MAAM;YACN,UAAU;QACZ;IACF;AACF;AAGO,MAAM,gBAAgB;IAC3B,SAAS;QACP,SAAS;QACT,GAAG;QACH,OAAO;IACT;IACA,SAAS;QACP,SAAS;QACT,GAAG;QACH,OAAO;QACP,YAAY;YACV,MAAM;YACN,WAAW;YACX,SAAS;QACX;IACF;IACA,MAAM;QACJ,SAAS;QACT,GAAG;QACH,OAAO;QACP,YAAY;YACV,MAAM;YACN,MAAM;YACN,UAAU;QACZ;IACF;AACF;AAGO,MAAM,kBAAkB;IAC7B,UAAU;QACR,OAAO;QACP,YAAY;YACV,MAAM;YACN,WAAW;YACX,SAAS;QACX;IACF;IACA,WAAW;QACT,OAAO;QACP,YAAY;YACV,MAAM;YACN,WAAW;YACX,SAAS;QACX;IACF;AACF;AAGO,MAAM,qBAAqB;IAChC,SAAS;QACP,aAAa;QACb,WAAW;IACb;IACA,OAAO;QACL,aAAa;QACb,WAAW;QACX,YAAY;YACV,MAAM;YACN,MAAM;YACN,UAAU;QACZ;IACF;AACF;AAGO,MAAM,iBAAiB;IAC5B,SAAS;QACP,aAAa;QACb,iBAAiB;QACjB,YAAY;YACV,MAAM;YACN,MAAM;YACN,UAAU;QACZ;IACF;IACA,OAAO;QACL,aAAa;QACb,iBAAiB;QACjB,YAAY;YACV,MAAM;YACN,MAAM;YACN,UAAU;QACZ;IACF;IACA,SAAS;QACP,aAAa;QACb,iBAAiB;IACnB;AACF"}}, {"offset": {"line": 4184, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4190, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/Work/Repositories/templgen/src/components/ui/index.ts"], "sourcesContent": ["// UI Components Barrel Export\n// This file provides a centralized export for all UI components\n\n// Core UI Components\nexport { Button } from \"./button\";\nexport { Input } from \"./input\";\nexport { Label } from \"./label\";\nexport { Textarea } from \"./textarea\";\nexport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from \"./select\";\nexport { Checkbox } from \"./checkbox\";\nexport { RadioGroup, RadioGroupItem } from \"./radio-group\";\nexport { Switch } from \"./switch\";\n\n// Layout Components\nexport {\n  Card,\n  CardContent,\n  CardDescription,\n  CardFooter,\n  CardHeader,\n  CardTitle,\n} from \"./card\";\nexport { Separator } from \"./separator\";\nexport { Tabs, TabsContent, TabsList, TabsTrigger } from \"./tabs\";\nexport {\n  Accordion,\n  AccordionContent,\n  AccordionItem,\n  AccordionTrigger,\n} from \"./accordion\";\n\n// Feedback Components\nexport { Badge } from \"./badge\";\nexport { Progress } from \"./progress\";\nexport { Skeleton } from \"./skeleton\";\nexport { Alert, AlertDescription, AlertTitle } from \"./alert\";\nexport { useToast, toast } from \"./use-toast\";\n\n// Overlay Components\nexport {\n  Dialog,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogTitle,\n  DialogTrigger,\n} from \"./dialog\";\nexport {\n  Sheet,\n  SheetContent,\n  SheetDescription,\n  SheetFooter,\n  SheetHeader,\n  SheetTitle,\n  SheetTrigger,\n} from \"./sheet\";\nexport { Popover, PopoverContent, PopoverTrigger } from \"./popover\";\nexport {\n  Tooltip,\n  TooltipContent,\n  TooltipProvider,\n  TooltipTrigger,\n} from \"./tooltip\";\n\n// Navigation Components\nexport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from \"./dropdown-menu\";\nexport {\n  NavigationMenu,\n  NavigationMenuContent,\n  NavigationMenuItem,\n  NavigationMenuLink,\n  NavigationMenuList,\n  NavigationMenuTrigger,\n} from \"./navigation-menu\";\n\n// Data Display Components\nexport {\n  Table,\n  TableBody,\n  TableCaption,\n  TableCell,\n  TableHead,\n  TableHeader,\n  TableRow,\n} from \"./table\";\nexport { Avatar, AvatarFallback, AvatarImage } from \"./avatar\";\n\n// Custom Components\nexport { FileUpload } from \"./file-upload\";\nexport {\n  CustomDialog,\n  CustomDialogProvider,\n  useDialog,\n  showAlert,\n  showConfirm,\n  showPrompt,\n} from \"./custom-dialog\";\nexport { OTPInput } from \"./otp-input\";\nexport { SecureBulkDelete } from \"./secure-bulk-delete\";\nexport {\n  PageTransition,\n  SlideTransition,\n  FadeTransition,\n} from \"./page-transition\";\n\n// Animation Variants\nexport {\n  staggerContainer,\n  staggerItem,\n  loadingVariants,\n  modalVariants,\n  backdropVariants,\n  cardHoverVariants,\n  buttonPressVariants,\n  navLinkVariants,\n  toastVariants,\n  sidebarVariants,\n  fieldFocusVariants,\n  statusVariants,\n} from \"./page-transition\";\n\n// Types\n// export type { FileUploadProps } from \"./file-upload\"; // TODO: Add proper types\n// export type { OTPInputProps } from \"./otp-input\"; // TODO: Add proper types\n// export type { SecureBulkDeleteProps } from \"./secure-bulk-delete\"; // TODO: Add proper types\n"], "names": [], "mappings": "AAAA,8BAA8B;AAC9B,gEAAgE;AAEhE,qBAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAoIrB,QAAQ;CACR,kFAAkF;CAClF,8EAA8E;CAC9E,+FAA+F"}}, {"offset": {"line": 4232, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4276, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/Work/Repositories/templgen/src/components/ui/FuzzyText.tsx"], "sourcesContent": ["import React, { useEffect, useRef } from \"react\";\n\ninterface FuzzyTextProps {\n  children: React.ReactNode;\n  fontSize?: number | string;\n  fontWeight?: string | number;\n  fontFamily?: string;\n  color?: string;\n  enableHover?: boolean;\n  baseIntensity?: number;\n  hoverIntensity?: number;\n}\n\nconst FuzzyText: React.FC<FuzzyTextProps> = ({\n  children,\n  fontSize = \"clamp(2rem, 8vw, 8rem)\",\n  fontWeight = 900,\n  fontFamily = \"inherit\",\n  color = \"#fff\",\n  enableHover = true,\n  baseIntensity = 0.18,\n  hoverIntensity = 0.5,\n}) => {\n  const canvasRef = useRef<\n    HTMLCanvasElement & { cleanupFuzzyText?: () => void }\n  >(null);\n\n  useEffect(() => {\n    let animationFrameId: number;\n    let isCancelled = false;\n    const canvas = canvasRef.current;\n    if (!canvas) return;\n\n    const init = async () => {\n      if (document.fonts?.ready) {\n        await document.fonts.ready;\n      }\n      if (isCancelled) return;\n\n      const ctx = canvas.getContext(\"2d\");\n      if (!ctx) return;\n\n      const computedFontFamily =\n        fontFamily === \"inherit\"\n          ? window.getComputedStyle(canvas).fontFamily || \"sans-serif\"\n          : fontFamily;\n\n      const fontSizeStr =\n        typeof fontSize === \"number\" ? `${fontSize}px` : fontSize;\n      let numericFontSize: number;\n      if (typeof fontSize === \"number\") {\n        numericFontSize = fontSize;\n      } else {\n        const temp = document.createElement(\"span\");\n        temp.style.fontSize = fontSize;\n        document.body.appendChild(temp);\n        const computedSize = window.getComputedStyle(temp).fontSize;\n        numericFontSize = parseFloat(computedSize);\n        document.body.removeChild(temp);\n      }\n\n      const text = React.Children.toArray(children).join(\"\");\n\n      const offscreen = document.createElement(\"canvas\");\n      const offCtx = offscreen.getContext(\"2d\");\n      if (!offCtx) return;\n\n      offCtx.font = `${fontWeight} ${fontSizeStr} ${computedFontFamily}`;\n      offCtx.textBaseline = \"alphabetic\";\n      const metrics = offCtx.measureText(text);\n\n      const actualLeft = metrics.actualBoundingBoxLeft ?? 0;\n      const actualRight = metrics.actualBoundingBoxRight ?? metrics.width;\n      const actualAscent = metrics.actualBoundingBoxAscent ?? numericFontSize;\n      const actualDescent =\n        metrics.actualBoundingBoxDescent ?? numericFontSize * 0.2;\n\n      const textBoundingWidth = Math.ceil(actualLeft + actualRight);\n      const tightHeight = Math.ceil(actualAscent + actualDescent);\n\n      const extraWidthBuffer = 10;\n      const offscreenWidth = textBoundingWidth + extraWidthBuffer;\n\n      offscreen.width = offscreenWidth;\n      offscreen.height = tightHeight;\n\n      const xOffset = extraWidthBuffer / 2;\n      offCtx.font = `${fontWeight} ${fontSizeStr} ${computedFontFamily}`;\n      offCtx.textBaseline = \"alphabetic\";\n      offCtx.fillStyle = color;\n      offCtx.fillText(text, xOffset - actualLeft, actualAscent);\n\n      const horizontalMargin = 50;\n      const verticalMargin = 0;\n      canvas.width = offscreenWidth + horizontalMargin * 2;\n      canvas.height = tightHeight + verticalMargin * 2;\n      ctx.translate(horizontalMargin, verticalMargin);\n\n      const interactiveLeft = horizontalMargin + xOffset;\n      const interactiveTop = verticalMargin;\n      const interactiveRight = interactiveLeft + textBoundingWidth;\n      const interactiveBottom = interactiveTop + tightHeight;\n\n      let isHovering = false;\n      const fuzzRange = 30;\n\n      const run = () => {\n        if (isCancelled) return;\n        ctx.clearRect(\n          -fuzzRange,\n          -fuzzRange,\n          offscreenWidth + 2 * fuzzRange,\n          tightHeight + 2 * fuzzRange\n        );\n        const intensity = isHovering ? hoverIntensity : baseIntensity;\n        for (let j = 0; j < tightHeight; j++) {\n          const dx = Math.floor(intensity * (Math.random() - 0.5) * fuzzRange);\n          ctx.drawImage(\n            offscreen,\n            0,\n            j,\n            offscreenWidth,\n            1,\n            dx,\n            j,\n            offscreenWidth,\n            1\n          );\n        }\n        animationFrameId = window.requestAnimationFrame(run);\n      };\n\n      run();\n\n      const isInsideTextArea = (x: number, y: number) =>\n        x >= interactiveLeft &&\n        x <= interactiveRight &&\n        y >= interactiveTop &&\n        y <= interactiveBottom;\n\n      const handleMouseMove = (e: MouseEvent) => {\n        if (!enableHover) return;\n        const rect = canvas.getBoundingClientRect();\n        const x = e.clientX - rect.left;\n        const y = e.clientY - rect.top;\n        isHovering = isInsideTextArea(x, y);\n      };\n\n      const handleMouseLeave = () => {\n        isHovering = false;\n      };\n\n      const handleTouchMove = (e: TouchEvent) => {\n        if (!enableHover) return;\n        e.preventDefault();\n        const rect = canvas.getBoundingClientRect();\n        const touch = e.touches[0];\n        const x = touch.clientX - rect.left;\n        const y = touch.clientY - rect.top;\n        isHovering = isInsideTextArea(x, y);\n      };\n\n      const handleTouchEnd = () => {\n        isHovering = false;\n      };\n\n      if (enableHover) {\n        canvas.addEventListener(\"mousemove\", handleMouseMove);\n        canvas.addEventListener(\"mouseleave\", handleMouseLeave);\n        canvas.addEventListener(\"touchmove\", handleTouchMove, {\n          passive: false,\n        });\n        canvas.addEventListener(\"touchend\", handleTouchEnd);\n      }\n\n      const cleanup = () => {\n        window.cancelAnimationFrame(animationFrameId);\n        if (enableHover) {\n          canvas.removeEventListener(\"mousemove\", handleMouseMove);\n          canvas.removeEventListener(\"mouseleave\", handleMouseLeave);\n          canvas.removeEventListener(\"touchmove\", handleTouchMove);\n          canvas.removeEventListener(\"touchend\", handleTouchEnd);\n        }\n      };\n\n      canvas.cleanupFuzzyText = cleanup;\n    };\n\n    init();\n\n    return () => {\n      isCancelled = true;\n      window.cancelAnimationFrame(animationFrameId);\n      if (canvas && canvas.cleanupFuzzyText) {\n        canvas.cleanupFuzzyText();\n      }\n    };\n  }, [\n    children,\n    fontSize,\n    fontWeight,\n    fontFamily,\n    color,\n    enableHover,\n    baseIntensity,\n    hoverIntensity,\n  ]);\n\n  return <canvas ref={canvasRef} />;\n};\n\nexport default FuzzyText;\n"], "names": [], "mappings": ";;;;AAAA;;;;AAaA,MAAM,YAAsC,CAAC,EAC3C,QAAQ,EACR,WAAW,wBAAwB,EACnC,aAAa,GAAG,EAChB,aAAa,SAAS,EACtB,QAAQ,MAAM,EACd,cAAc,IAAI,EAClB,gBAAgB,IAAI,EACpB,iBAAiB,GAAG,EACrB;;IACC,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAErB;IAEF,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI;YACJ,IAAI,cAAc;YAClB,MAAM,SAAS,UAAU,OAAO;YAChC,IAAI,CAAC,QAAQ;YAEb,MAAM;4CAAO;oBACX,IAAI,SAAS,KAAK,EAAE,OAAO;wBACzB,MAAM,SAAS,KAAK,CAAC,KAAK;oBAC5B;oBACA,IAAI,aAAa;oBAEjB,MAAM,MAAM,OAAO,UAAU,CAAC;oBAC9B,IAAI,CAAC,KAAK;oBAEV,MAAM,qBACJ,eAAe,YACX,OAAO,gBAAgB,CAAC,QAAQ,UAAU,IAAI,eAC9C;oBAEN,MAAM,cACJ,OAAO,aAAa,WAAW,GAAG,SAAS,EAAE,CAAC,GAAG;oBACnD,IAAI;oBACJ,IAAI,OAAO,aAAa,UAAU;wBAChC,kBAAkB;oBACpB,OAAO;wBACL,MAAM,OAAO,SAAS,aAAa,CAAC;wBACpC,KAAK,KAAK,CAAC,QAAQ,GAAG;wBACtB,SAAS,IAAI,CAAC,WAAW,CAAC;wBAC1B,MAAM,eAAe,OAAO,gBAAgB,CAAC,MAAM,QAAQ;wBAC3D,kBAAkB,WAAW;wBAC7B,SAAS,IAAI,CAAC,WAAW,CAAC;oBAC5B;oBAEA,MAAM,OAAO,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAU,IAAI,CAAC;oBAEnD,MAAM,YAAY,SAAS,aAAa,CAAC;oBACzC,MAAM,SAAS,UAAU,UAAU,CAAC;oBACpC,IAAI,CAAC,QAAQ;oBAEb,OAAO,IAAI,GAAG,GAAG,WAAW,CAAC,EAAE,YAAY,CAAC,EAAE,oBAAoB;oBAClE,OAAO,YAAY,GAAG;oBACtB,MAAM,UAAU,OAAO,WAAW,CAAC;oBAEnC,MAAM,aAAa,QAAQ,qBAAqB,IAAI;oBACpD,MAAM,cAAc,QAAQ,sBAAsB,IAAI,QAAQ,KAAK;oBACnE,MAAM,eAAe,QAAQ,uBAAuB,IAAI;oBACxD,MAAM,gBACJ,QAAQ,wBAAwB,IAAI,kBAAkB;oBAExD,MAAM,oBAAoB,KAAK,IAAI,CAAC,aAAa;oBACjD,MAAM,cAAc,KAAK,IAAI,CAAC,eAAe;oBAE7C,MAAM,mBAAmB;oBACzB,MAAM,iBAAiB,oBAAoB;oBAE3C,UAAU,KAAK,GAAG;oBAClB,UAAU,MAAM,GAAG;oBAEnB,MAAM,UAAU,mBAAmB;oBACnC,OAAO,IAAI,GAAG,GAAG,WAAW,CAAC,EAAE,YAAY,CAAC,EAAE,oBAAoB;oBAClE,OAAO,YAAY,GAAG;oBACtB,OAAO,SAAS,GAAG;oBACnB,OAAO,QAAQ,CAAC,MAAM,UAAU,YAAY;oBAE5C,MAAM,mBAAmB;oBACzB,MAAM,iBAAiB;oBACvB,OAAO,KAAK,GAAG,iBAAiB,mBAAmB;oBACnD,OAAO,MAAM,GAAG,cAAc,iBAAiB;oBAC/C,IAAI,SAAS,CAAC,kBAAkB;oBAEhC,MAAM,kBAAkB,mBAAmB;oBAC3C,MAAM,iBAAiB;oBACvB,MAAM,mBAAmB,kBAAkB;oBAC3C,MAAM,oBAAoB,iBAAiB;oBAE3C,IAAI,aAAa;oBACjB,MAAM,YAAY;oBAElB,MAAM;wDAAM;4BACV,IAAI,aAAa;4BACjB,IAAI,SAAS,CACX,CAAC,WACD,CAAC,WACD,iBAAiB,IAAI,WACrB,cAAc,IAAI;4BAEpB,MAAM,YAAY,aAAa,iBAAiB;4BAChD,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,IAAK;gCACpC,MAAM,KAAK,KAAK,KAAK,CAAC,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gCAC1D,IAAI,SAAS,CACX,WACA,GACA,GACA,gBACA,GACA,IACA,GACA,gBACA;4BAEJ;4BACA,mBAAmB,OAAO,qBAAqB,CAAC;wBAClD;;oBAEA;oBAEA,MAAM;qEAAmB,CAAC,GAAW,IACnC,KAAK,mBACL,KAAK,oBACL,KAAK,kBACL,KAAK;;oBAEP,MAAM;oEAAkB,CAAC;4BACvB,IAAI,CAAC,aAAa;4BAClB,MAAM,OAAO,OAAO,qBAAqB;4BACzC,MAAM,IAAI,EAAE,OAAO,GAAG,KAAK,IAAI;4BAC/B,MAAM,IAAI,EAAE,OAAO,GAAG,KAAK,GAAG;4BAC9B,aAAa,iBAAiB,GAAG;wBACnC;;oBAEA,MAAM;qEAAmB;4BACvB,aAAa;wBACf;;oBAEA,MAAM;oEAAkB,CAAC;4BACvB,IAAI,CAAC,aAAa;4BAClB,EAAE,cAAc;4BAChB,MAAM,OAAO,OAAO,qBAAqB;4BACzC,MAAM,QAAQ,EAAE,OAAO,CAAC,EAAE;4BAC1B,MAAM,IAAI,MAAM,OAAO,GAAG,KAAK,IAAI;4BACnC,MAAM,IAAI,MAAM,OAAO,GAAG,KAAK,GAAG;4BAClC,aAAa,iBAAiB,GAAG;wBACnC;;oBAEA,MAAM;mEAAiB;4BACrB,aAAa;wBACf;;oBAEA,IAAI,aAAa;wBACf,OAAO,gBAAgB,CAAC,aAAa;wBACrC,OAAO,gBAAgB,CAAC,cAAc;wBACtC,OAAO,gBAAgB,CAAC,aAAa,iBAAiB;4BACpD,SAAS;wBACX;wBACA,OAAO,gBAAgB,CAAC,YAAY;oBACtC;oBAEA,MAAM;4DAAU;4BACd,OAAO,oBAAoB,CAAC;4BAC5B,IAAI,aAAa;gCACf,OAAO,mBAAmB,CAAC,aAAa;gCACxC,OAAO,mBAAmB,CAAC,cAAc;gCACzC,OAAO,mBAAmB,CAAC,aAAa;gCACxC,OAAO,mBAAmB,CAAC,YAAY;4BACzC;wBACF;;oBAEA,OAAO,gBAAgB,GAAG;gBAC5B;;YAEA;YAEA;uCAAO;oBACL,cAAc;oBACd,OAAO,oBAAoB,CAAC;oBAC5B,IAAI,UAAU,OAAO,gBAAgB,EAAE;wBACrC,OAAO,gBAAgB;oBACzB;gBACF;;QACF;8BAAG;QACD;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,qBAAO,6LAAC;QAAO,KAAK;;;;;;AACtB;GApMM;KAAA;uCAsMS"}}, {"offset": {"line": 4452, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4458, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/Work/Repositories/templgen/src/components/ui/text-randomized.tsx"], "sourcesContent": ["\"use client\";\nimport { useEffect, useState, useCallback } from \"react\";\n\nconst lettersAndSymbols = \"abcdefghijklmnopqrstuvwxyz!@#$%^&*-_+=;:<>,\";\n\ninterface AnimatedTextProps {\n  text: string;\n  className?: string;\n}\n\nexport function RandomizedTextEffect({\n  text,\n  className = \"\",\n}: AnimatedTextProps) {\n  const [animatedText, setAnimatedText] = useState(\"\");\n  const [hasSeenAnimation, setHasSeenAnimation] = useState<boolean>(false);\n\n  const getRandomChar = useCallback(\n    () =>\n      lettersAndSymbols[Math.floor(Math.random() * lettersAndSymbols.length)],\n    [],\n  );\n\n  const animateText = useCallback(async () => {\n    const duration = 30;\n    const revealDuration = 40;\n    const initialRandomDuration = 300;\n\n    const generateRandomText = () =>\n      text\n        .split(\"\")\n        .map(() => getRandomChar())\n        .join(\"\");\n\n    setAnimatedText(generateRandomText());\n\n    const endTime = Date.now() + initialRandomDuration;\n    while (Date.now() < endTime) {\n      await new Promise((resolve) => setTimeout(resolve, duration));\n      setAnimatedText(generateRandomText());\n    }\n\n    for (let i = 0; i < text.length; i++) {\n      await new Promise((resolve) => setTimeout(resolve, revealDuration));\n      setAnimatedText(\n        (prevText) =>\n          text.slice(0, i + 1) +\n          prevText\n            .slice(i + 1)\n            .split(\"\")\n            .map(() => getRandomChar())\n            .join(\"\"),\n      );\n    }\n  }, [text, getRandomChar]);\n\n  useEffect(() => {\n    const isFirstVisit = !localStorage.getItem(\"hasSeenAnimation\");\n\n    if (isFirstVisit) {\n      animateText();\n      localStorage.setItem(\"hasSeenAnimation\", \"true\");\n    } else {\n      setHasSeenAnimation(true);\n    }\n\n    const handleBeforeUnload = () => {\n      localStorage.removeItem(\"hasSeenAnimation\");\n    };\n\n    window.addEventListener(\"beforeunload\", handleBeforeUnload);\n\n    return () => {\n      window.removeEventListener(\"beforeunload\", handleBeforeUnload);\n    };\n  }, [animateText]);\n\n  return (\n    <div className={`relative inline-block ${className}`}>\n      {hasSeenAnimation ? text : animatedText}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;;;AADA;;AAGA,MAAM,oBAAoB;AAOnB,SAAS,qBAAqB,EACnC,IAAI,EACJ,YAAY,EAAE,EACI;;IAClB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAElE,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2DAC9B,IACE,iBAAiB,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,kBAAkB,MAAM,EAAE;0DACzE,EAAE;IAGJ,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yDAAE;YAC9B,MAAM,WAAW;YACjB,MAAM,iBAAiB;YACvB,MAAM,wBAAwB;YAE9B,MAAM;oFAAqB,IACzB,KACG,KAAK,CAAC,IACN,GAAG;4FAAC,IAAM;2FACV,IAAI,CAAC;;YAEV,gBAAgB;YAEhB,MAAM,UAAU,KAAK,GAAG,KAAK;YAC7B,MAAO,KAAK,GAAG,KAAK,QAAS;gBAC3B,MAAM,IAAI;qEAAQ,CAAC,UAAY,WAAW,SAAS;;gBACnD,gBAAgB;YAClB;YAEA,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;gBACpC,MAAM,IAAI;qEAAQ,CAAC,UAAY,WAAW,SAAS;;gBACnD;qEACE,CAAC,WACC,KAAK,KAAK,CAAC,GAAG,IAAI,KAClB,SACG,KAAK,CAAC,IAAI,GACV,KAAK,CAAC,IACN,GAAG;6EAAC,IAAM;4EACV,IAAI,CAAC;;YAEd;QACF;wDAAG;QAAC;QAAM;KAAc;IAExB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;YACR,MAAM,eAAe,CAAC,aAAa,OAAO,CAAC;YAE3C,IAAI,cAAc;gBAChB;gBACA,aAAa,OAAO,CAAC,oBAAoB;YAC3C,OAAO;gBACL,oBAAoB;YACtB;YAEA,MAAM;qEAAqB;oBACzB,aAAa,UAAU,CAAC;gBAC1B;;YAEA,OAAO,gBAAgB,CAAC,gBAAgB;YAExC;kDAAO;oBACL,OAAO,mBAAmB,CAAC,gBAAgB;gBAC7C;;QACF;yCAAG;QAAC;KAAY;IAEhB,qBACE,6LAAC;QAAI,WAAW,CAAC,sBAAsB,EAAE,WAAW;kBACjD,mBAAmB,OAAO;;;;;;AAGjC;GAxEgB;KAAA"}}, {"offset": {"line": 4548, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4554, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/Work/Repositories/templgen/src/app/not-found.tsx"], "sourcesContent": ["\"use client\";\n\nimport { But<PERSON> } from \"@/components/ui\";\nimport FuzzyText from \"@/components/ui/FuzzyText\";\nimport { RandomizedTextEffect } from \"@/components/ui/text-randomized\";\nimport Link from \"next/link\";\nimport { IoArrowBack } from \"react-icons/io5\";\nexport default function NotFoundPage() {\n  return (\n    <>\n      {/* Add structured data for the 404 page */}\n\n      <div className=\"fixed w-screen h-screen bg-[var(--background)]  flex flex-col justify-center items-center  z-50 inset-0\">\n        <div className=\"flex flex-col items-center justify-center text-center\">\n          <FuzzyText\n            fontSize={50}\n            baseIntensity={0.2}\n            hoverIntensity={2}\n            enableHover={false}\n          >\n            404 | Page Not Found\n          </FuzzyText>\n\n          <RandomizedTextEffect\n            className=\"mt-4 text-xl text-[var(--paragraph)]\"\n            text={\"The page you are looking for doesn't exist.\"}\n          />\n          <div className=\"mt-8 w-max\">\n            <Link href=\"/\">\n              <Button>\n                <IoArrowBack />\n                <span>Back To HomePage</span>\n              </Button>\n            </Link>\n          </div>\n        </div>\n      </div>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAHA;AAIA;AANA;;;;;;;AAOe,SAAS;IACtB,qBACE;kBAGE,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,wIAAA,CAAA,UAAS;wBACR,UAAU;wBACV,eAAe;wBACf,gBAAgB;wBAChB,aAAa;kCACd;;;;;;kCAID,6LAAC,iJAAA,CAAA,uBAAoB;wBACnB,WAAU;wBACV,MAAM;;;;;;kCAER,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;sCACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;;kDACL,6LAAC,kJAAA,CAAA,cAAW;;;;;kDACZ,6LAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtB;KAhCwB"}}, {"offset": {"line": 4650, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}
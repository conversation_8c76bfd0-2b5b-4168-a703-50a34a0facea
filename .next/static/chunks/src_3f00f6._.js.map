{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/src/components/ui/backgroundEffect.tsx"], "sourcesContent": ["\"use client\";\n\nimport { memo } from \"react\";\n\nconst BackgroundEffect = () => {\n  return (\n    <div className=\"absolute hidden inset-0 isolate z-10 contain-strict max-md:hidden\">\n      <div className=\"absolute left-0 top-0 h-[1280px] w-[560px] -translate-y-[350px] -rotate-45 rounded-full bg-[radial-gradient(68.54%_68.72%_at_55.02%_31.46%,hsla(0,0%,85%,.08)_0,hsla(0,0%,55%,.02)_50%,hsla(0,0%,45%,0)_80%)]\"></div>\n      <div className=\"absolute left-0 top-0 h-[1280px] w-[240px] -rotate-45 rounded-full bg-[radial-gradient(50%_50%_at_50%_50%,hsla(0,0%,85%,.06)_0,hsla(0,0%,45%,.02)_80%,transparent_100%)] [translate:5%_-50%]\"></div>\n      <div className=\"absolute left-0 top-0 h-[1280px] w-[240px] -translate-y-[350px] -rotate-45 bg-[radial-gradient(50%_50%_at_50%_50%,hsla(0,0%,85%,.04)_0,hsla(0,0%,45%,.02)_80%,transparent_100%)]\"></div>\n    </div>\n  );\n};\n\n// Memoize the component to prevent unnecessary re-renders\nexport default memo(BackgroundEffect);\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIA,MAAM,mBAAmB;IACvB,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB;KARM;2DAWS,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,EAAE"}}, {"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 61, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/src/components/ui/FloatingActionButton.tsx"], "sourcesContent": ["\"use client\";\n\nimport { AnimatePresence, motion } from \"framer-motion\";\nimport { Briefcase, FileText, Home, Mail, Menu, X } from \"lucide-react\";\nimport Link from \"next/link\";\nimport { useEffect, useState } from \"react\";\n\ninterface FloatingActionButtonProps {\n  threshold?: number;\n}\n\nexport default function FloatingActionButton({\n  threshold = 300,\n}: FloatingActionButtonProps) {\n  const [isVisible, setIsVisible] = useState(false);\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      if (window.scrollY > threshold) {\n        setIsVisible(true);\n      } else {\n        setIsVisible(false);\n        if (isMenuOpen) setIsMenuOpen(false);\n      }\n    };\n\n    window.addEventListener(\"scroll\", handleScroll);\n    return () => window.removeEventListener(\"scroll\", handleScroll);\n  }, [threshold, isMenuOpen]);\n\n  const menuItems = [\n    { icon: <Home className=\"h-5 w-5\" />, label: \"Home\", href: \"/\" },\n    { icon: <Briefcase className=\"h-5 w-5\" />, label: \"Work\", href: \"/#work\" },\n    {\n      icon: <FileText className=\"h-5 w-5\" />,\n      label: \"Projects\",\n      href: \"/projects\",\n    },\n    { icon: <Mail className=\"h-5 w-5\" />, label: \"Contact\", href: \"/contact\" },\n  ];\n\n  const handleMenuItemClick = (href: string) => {\n    setIsMenuOpen(false);\n    if (href === \"/#work\") {\n      const workSection = document.getElementById(\"work\");\n      if (workSection) {\n        workSection.scrollIntoView({ behavior: \"smooth\" });\n      }\n    }\n  };\n\n  return (\n    <AnimatePresence>\n      {isVisible && (\n        <motion.div\n          className=\"fixed bottom-6 right-6 z-30 flex flex-col items-end hidden\"\n          initial={{ opacity: 0, scale: 0.8 }}\n          animate={{ opacity: 1, scale: 1 }}\n          exit={{ opacity: 0, scale: 0.8 }}\n          transition={{ duration: 0.3 }}\n        >\n          {/* Menu items */}\n          <AnimatePresence>\n            {isMenuOpen && (\n              <motion.div\n                className=\"mb-4 flex flex-col gap-3\"\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                exit={{ opacity: 0, y: 20 }}\n                transition={{ duration: 0.3 }}\n              >\n                {menuItems.map((item, index) => (\n                  <motion.div\n                    key={item.label}\n                    initial={{ opacity: 0, x: 20 }}\n                    animate={{ opacity: 1, x: 0 }}\n                    transition={{ delay: index * 0.05 }}\n                  >\n                    <Link\n                      href={item.href}\n                      onClick={() => handleMenuItemClick(item.href)}\n                      className=\"flex items-center gap-2 rounded-full bg-[var(--card-background)] px-4 py-2 text-sm text-[var(--headline)] shadow-md hover:bg-[var(--link-color)] hover:text-white transition-colors\"\n                    >\n                      {item.icon}\n                      <span>{item.label}</span>\n                    </Link>\n                  </motion.div>\n                ))}\n              </motion.div>\n            )}\n          </AnimatePresence>\n\n          {/* Main button */}\n          <motion.button\n            className=\"flex h-12 w-12 items-center justify-center rounded-full bg-[var(--link-color)] text-white shadow-lg hover:bg-[var(--button)] transition-colors\"\n            onClick={() =>\n              isMenuOpen ? setIsMenuOpen(false) : setIsMenuOpen(true)\n            }\n            whileTap={{ scale: 0.9 }}\n            aria-label={isMenuOpen ? \"Close menu\" : \"Open menu\"}\n          >\n            {isMenuOpen ? (\n              <X className=\"h-5 w-5\" />\n            ) : (\n              <Menu className=\"h-5 w-5\" />\n            )}\n          </motion.button>\n\n          {/* Back to top button */}\n        </motion.div>\n      )}\n    </AnimatePresence>\n  );\n}\n"], "names": [], "mappings": ";;;;AAIA;AACA;AAFA;AAAA;AAAA;AAAA;AADA;AAAA;AACA;AAAA;;;AAHA;;;;;AAWe,SAAS,qBAAqB,EAC3C,YAAY,GAAG,EACW;;IAC1B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;YACR,MAAM;+DAAe;oBACnB,IAAI,OAAO,OAAO,GAAG,WAAW;wBAC9B,aAAa;oBACf,OAAO;wBACL,aAAa;wBACb,IAAI,YAAY,cAAc;oBAChC;gBACF;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC;kDAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;yCAAG;QAAC;QAAW;KAAW;IAE1B,MAAM,YAAY;QAChB;YAAE,oBAAM,6LAAC,sMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;YAAc,OAAO;YAAQ,MAAM;QAAI;QAC/D;YAAE,oBAAM,6LAAC,+MAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;YAAc,OAAO;YAAQ,MAAM;QAAS;QACzE;YACE,oBAAM,6LAAC,iNAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;YAC1B,OAAO;YACP,MAAM;QACR;QACA;YAAE,oBAAM,6LAAC,qMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;YAAc,OAAO;YAAW,MAAM;QAAW;KAC1E;IAED,MAAM,sBAAsB,CAAC;QAC3B,cAAc;QACd,IAAI,SAAS,UAAU;YACrB,MAAM,cAAc,SAAS,cAAc,CAAC;YAC5C,IAAI,aAAa;gBACf,YAAY,cAAc,CAAC;oBAAE,UAAU;gBAAS;YAClD;QACF;IACF;IAEA,qBACE,6LAAC,4LAAA,CAAA,kBAAe;kBACb,2BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,WAAU;YACV,SAAS;gBAAE,SAAS;gBAAG,OAAO;YAAI;YAClC,SAAS;gBAAE,SAAS;gBAAG,OAAO;YAAE;YAChC,MAAM;gBAAE,SAAS;gBAAG,OAAO;YAAI;YAC/B,YAAY;gBAAE,UAAU;YAAI;;8BAG5B,6LAAC,4LAAA,CAAA,kBAAe;8BACb,4BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,MAAM;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC1B,YAAY;4BAAE,UAAU;wBAAI;kCAE3B,UAAU,GAAG,CAAC,CAAC,MAAM,sBACpB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,OAAO,QAAQ;gCAAK;0CAElC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAM,KAAK,IAAI;oCACf,SAAS,IAAM,oBAAoB,KAAK,IAAI;oCAC5C,WAAU;;wCAET,KAAK,IAAI;sDACV,6LAAC;sDAAM,KAAK,KAAK;;;;;;;;;;;;+BAXd,KAAK,KAAK;;;;;;;;;;;;;;;8BAoBzB,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oBACZ,WAAU;oBACV,SAAS,IACP,aAAa,cAAc,SAAS,cAAc;oBAEpD,UAAU;wBAAE,OAAO;oBAAI;oBACvB,cAAY,aAAa,eAAe;8BAEvC,2BACC,6LAAC,+LAAA,CAAA,IAAC;wBAAC,WAAU;;;;;6CAEb,6LAAC,qMAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;AAS9B;GAvGwB;KAAA"}}, {"offset": {"line": 291, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 297, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\";\nimport { twMerge } from \"tailwind-merge\";\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB"}}, {"offset": {"line": 310, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 316, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport { cva, type VariantProps } from \"class-variance-authority\";\nimport { Slot } from \"@radix-ui/react-slot\";\n\nimport { cn } from \"@/lib/utils\";\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-[8px] text-sm font-medium transition-[color,box-shadow] disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 [&_svg]:shrink-0 outline-none focus-visible:border-[var(--highlight)] focus-visible:ring-[var(--highlight)]/50 focus-visible:ring-[3px]\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-[var(--button)] text-[var(--button-text)] shadow-sm hover:bg-[color-mix(in_srgb,var(--button),#00000020)]\",\n        destructive:\n          \"bg-[var(--tertiary)] text-[var(--button-text)] shadow-xs hover:bg-[color-mix(in_srgb,var(--tertiary),#00000020)] focus-visible:ring-[var(--tertiary)]/20\",\n        outline:\n          \"border border-[var(--input-border-color)] bg-[var(--background)] shadow-xs hover:bg-[var(--card-hover)] hover:text-[var(--highlight)]\",\n        secondary:\n          \"bg-[var(--secondary)] text-[var(--button-text)] shadow-xs hover:bg-[color-mix(in_srgb,var(--secondary),#00000020)]\",\n        ghost: \"hover:bg-[var(--card-hover)] hover:text-[var(--highlight)]\",\n        link: \"text-[var(--link-color)] underline-offset-4 hover:text-[var(--link-hover)] hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2\",\n        sm: \"h-8 rounded-[8px] px-3 text-xs\",\n        lg: \"h-10 rounded-[8px] px-8\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n);\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean;\n  }) {\n  const Comp = asChild ? Slot : \"button\";\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  );\n}\n\nexport { Button, buttonVariants };\n"], "names": [], "mappings": ";;;;;AACA;AAGA;AAFA;;;;;AAIA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,6XACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS"}}, {"offset": {"line": 373, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 379, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/src/components/common/Navbar.tsx"], "sourcesContent": ["\"use client\";\n\nimport Link from \"next/link\";\nimport { useState } from \"react\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { FiSearch } from \"react-icons/fi\";\n\nexport default function Navbar() {\n  const [search, setSearch] = useState(\"\");\n\n  return (\n    <header id=\"header-container\" className=\"border-b border-gray-200 bg-white\">\n      <div className=\"flex justify-between items-center px-6 py-3 max-w-[1200px] mx-auto\">\n        {/* Left side: logo + nav links */}\n        <div className=\"flex items-center gap-8\">\n          {/* Logo linking to /discover */}\n          <Link\n            href=\"/discover\"\n            aria-label=\"Cosmos\"\n            data-testid=\"TopNavBar_CosmosLogo\"\n            className=\"inline-block\"\n          >\n            <svg\n              width=\"90\"\n              height=\"14.169\"\n              viewBox=\"0 0 90 14.169\"\n              fill=\"none\"\n              xmlns=\"http://www.w3.org/2000/svg\"\n              className=\"text-black\"\n            >\n              <g\n                clipPath=\"url(#clip0_109_8737)\"\n                transform=\"translate(0 -3.006)\"\n              >\n                {/* Paste your full original path here */}\n                <path\n                  id=\"Path_10213\"\n                  data-name=\"Path 10213\"\n                  d=\"M.1.6a4.012,4.012,0,0,0,4-4,4.005,4.005,0,0,0-4-4,4.012,4.012,0,0,0-4,4A4.018,4.018,0,0,0,.1.6Zm0-.738A3.257,3.257,0,0,1-3.131-3.4,3.257,3.257,0,0,1,.1-6.662,3.257,3.257,0,0,1,3.331-3.4,3.257,3.257,0,0,1,.1-.138ZM.141-1.431c1.026,0,1.59-.5,1.641-1.374H.992a.733.733,0,0,1-.81.656H.1c-.667,0-.974-.595-.974-1.313S-.567-4.774.1-4.774H.182a.741.741,0,0,1,.81.656h.79C1.731-4.99,1.167-5.492.141-5.492c-1.077,0-1.815.7-1.815,2.031S-.936-1.431.141-1.431Z\"\n                  transform=\"translate(85.9 10.461)\"\n                  fill=\"currentColor\"\n                />\n                <g\n                  id=\"Group_2060\"\n                  data-name=\"Group 2060\"\n                  transform=\"translate(0 3.006)\"\n                >\n                  <path\n                    id=\"Path_10205\"\n                    data-name=\"Path 10205\"\n                    d=\"M-742.168-3903.765c0,4.428-2.862,7.085-6.6,7.085s-6.6-2.657-6.6-7.085,2.862-7.085,6.6-7.085S-742.168-3908.192-742.168-3903.765Zm-6.5,5.314c2.558,0,4.535-1.968,4.535-5.314s-1.976-5.313-4.535-5.313h-.2c-2.558,0-4.534,1.968-4.534,5.313s1.976,5.314,4.534,5.314Z\"\n                    transform=\"translate(768.296 3910.85)\"\n                    fill=\"currentColor\"\n                  />\n                  <path\n                    id=\"Path_10206\"\n                    data-name=\"Path 10206\"\n                    d=\"M56.475-3903.765c0,4.428-2.862,7.085-6.6,7.085s-6.6-2.657-6.6-7.085,2.862-7.085,6.6-7.085S56.475-3908.192,56.475-3903.765Zm-6.5,5.314c2.558,0,4.534-1.968,4.534-5.314s-1.976-5.313-4.534-5.313h-.2c-2.558,0-4.535,1.968-4.535,5.313s1.976,5.314,4.535,5.314Z\"\n                    transform=\"translate(12.227 3910.85)\"\n                    fill=\"currentColor\"\n                  />\n                  <path\n                    id=\"Path_10207\"\n                    data-name=\"Path 10207\"\n                    d=\"M-985.635-3901.206c-.193,3.012-2.884,4.526-5.617,4.526-3.739,0-6.6-2.657-6.6-7.085s2.862-7.085,6.6-7.085c2.954,0,5.406,1.613,5.617,4.527H-987.6c-.071-1.5-1.517-2.755-3.551-2.755h-.2c-2.558,0-4.534,1.968-4.534,5.313s1.976,5.314,4.534,5.314h.2c1.615,0,3.419-.868,3.551-2.755Z\"\n                    transform=\"translate(997.853 3910.85)\"\n                    fill=\"currentColor\"\n                  />\n                  <path\n                    id=\"Path_10208\"\n                    data-name=\"Path 10208\"\n                    d=\"M-489.865-3909.078h-.2c-1.712,0-2.854.925-2.854,2.145,0,1.063.728,1.692,1.889,1.928l2.2.433c2.381.473,3.759,1.732,3.759,3.975,0,2.42-2.047,3.916-5.1,3.916-3.247,0-5.313-1.574-5.412-4.625h1.968c0,1.869,1.456,2.854,3.523,2.854h.2c1.85,0,2.853-.925,2.853-2.067,0-1.121-.708-1.771-2.165-2.066l-2.2-.453c-2.2-.452-3.483-1.692-3.483-3.876,0-2.165,1.948-3.936,4.92-3.936,3.05,0,5,1.672,5.1,4.33h-1.968A2.777,2.777,0,0,0-489.865-3909.078Z\"\n                    transform=\"translate(522.347 3910.85)\"\n                    fill=\"currentColor\"\n                  />\n                  <path\n                    id=\"Path_10209\"\n                    data-name=\"Path 10209\"\n                    d=\"M-260.966-3905.426v12.043h-1.968v-13.776h3.267l4.113,11.926,4.034-11.926h3.247v13.776h-1.968v-12.1l-4.054,12.1h-2.519Z\"\n                    transform=\"translate(302.11 3907.355)\"\n                    fill=\"currentColor\"\n                  />\n                  <path\n                    id=\"Path_10210\"\n                    data-name=\"Path 10210\"\n                    d=\"M307.755-3909.078h-.2c-1.712,0-2.854.925-2.854,2.145,0,1.063.728,1.692,1.889,1.928l2.2.433c2.381.473,3.759,1.732,3.759,3.975,0,2.42-2.047,3.916-5.1,3.916-3.247,0-5.313-1.574-5.412-4.625h1.968c0,1.869,1.456,2.854,3.523,2.854h.2c1.85,0,2.854-.925,2.854-2.067,0-1.121-.708-1.771-2.165-2.066l-2.2-.453c-2.2-.452-3.483-1.692-3.483-3.876,0-2.165,1.948-3.936,4.92-3.936,3.05,0,5,1.672,5.1,4.33h-1.968A2.777,2.777,0,0,0,307.755-3909.078Z\"\n                    transform=\"translate(-232.754 3910.85)\"\n                    fill=\"currentColor\"\n                  />\n                </g>\n              </g>\n              <defs>\n                <clipPath id=\"clip0_109_8737\">\n                  <rect width=\"410.238\" height=\"68.013\" fill=\"currentColor\" />\n                </clipPath>\n              </defs>\n            </svg>\n          </Link>\n\n          {/* Nav links */}\n          <nav className=\"flex gap-6 items-center\">\n            <Link\n              href=\"/discover\"\n              data-testid=\"TopNavBar_DiscoverBtn\"\n              className=\"text-sm font-medium text-gray-800 hover:text-indigo-600\"\n            >\n              Discover\n            </Link>\n            <Link\n              href=\"/shop\"\n              data-testid=\"TopNavBar_HomeBtn\"\n              className=\"text-sm font-medium text-gray-800 hover:text-indigo-600\"\n            >\n              Shop\n            </Link>\n          </nav>\n        </div>\n\n        {/* Search bar */}\n        <div\n          role=\"combobox\"\n          aria-expanded=\"false\"\n          aria-controls=\"search-content\"\n          aria-label=\"Search\"\n          className=\"relative max-w-[460px] flex-grow mx-6\"\n        >\n          <div className=\"flex items-center bg-gray-100 rounded-md px-3 py-2 shadow-sm\">\n            <FiSearch className=\"text-gray-500 w-5 h-5\" aria-hidden=\"true\" />\n            <input\n              id=\"search-input\"\n              type=\"search\"\n              placeholder=\"Search Cosmos...\"\n              data-testid=\"Search_Input\"\n              autoComplete=\"off\"\n              value={search}\n              onChange={(e) => setSearch(e.target.value)}\n              className=\"bg-transparent border-none focus:ring-0 focus:outline-none flex-grow ml-2 placeholder-gray-500\"\n            />\n            <button\n              data-testid=\"TopNavBar_SearchPalette\"\n              aria-label=\"Filter by color\"\n              aria-expanded=\"false\"\n              className=\"ml-3 p-1 rounded-md hover:bg-gray-200\"\n              type=\"button\"\n            >\n              <svg\n                width=\"18\"\n                height=\"18\"\n                viewBox=\"0 0 18 18\"\n                fill=\"none\"\n                xmlns=\"http://www.w3.org/2000/svg\"\n                aria-hidden=\"true\"\n                focusable=\"false\"\n                className=\"text-gray-600\"\n              >\n                <path\n                  d=\"M12.589,13.571a13.131,13.131,0,0,1-.111,1.842A3.217,3.217,0,0,1,9.215,18a8.909,8.909,0,0,1-8.138-4.818A8.865,8.865,0,0,1,4.937,1.028,9.394,9.394,0,0,1,15.819,2.7a7.186,7.186,0,0,1,2.087,3.69,5.852,5.852,0,0,1,.064,1.647,2.032,2.032,0,0,1-2.028,1.786,7.718,7.718,0,0,0-1.48.092,2.39,2.39,0,0,0-1.9,2.3c-.016.448,0,.9,0,1.347h.025M4.436,9.427a1.625,1.625,0,1,0,1.62,1.619,1.627,1.627,0,0,0-1.62-1.619M8.516,12.7a1.622,1.622,0,0,0-1.633,1.608,1.631,1.631,0,1,0,3.262,0A1.615,1.615,0,0,0,8.516,12.7m4.074-6.158a1.631,1.631,0,0,0,.03-3.263,1.631,1.631,0,1,0-.03,3.263M5.64,6.16A1.622,1.622,0,0,0,4.024,4.511,1.642,1.642,0,0,0,2.387,6.123a1.624,1.624,0,0,0,1.6,1.639A1.6,1.6,0,0,0,5.64,6.16m2.466-.852A1.6,1.6,0,0,0,9.728,3.7a1.624,1.624,0,0,0-1.62-1.645A1.642,1.642,0,0,0,6.477,3.7a1.621,1.621,0,0,0,1.628,1.61\"\n                  fill=\"currentColor\"\n                />\n              </svg>\n            </button>\n          </div>\n        </div>\n\n        {/* Right side buttons */}\n        <div className=\"flex gap-4 items-center\">\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            data-testid=\"LoggedOutTopNavBar_SignInBtn\"\n            className=\"text-gray-700 hover:text-indigo-600\"\n          >\n            Log In\n          </Button>\n          <Button\n            size=\"sm\"\n            data-testid=\"LoggedOutTopNavBar_SignUpBtn\"\n            className=\"bg-indigo-600 text-white hover:bg-indigo-700\"\n          >\n            Sign Up\n          </Button>\n        </div>\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAOe,SAAS;;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,qBACE,6LAAC;QAAO,IAAG;QAAmB,WAAU;kBACtC,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,+JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,cAAW;4BACX,eAAY;4BACZ,WAAU;sCAEV,cAAA,6LAAC;gCACC,OAAM;gCACN,QAAO;gCACP,SAAQ;gCACR,MAAK;gCACL,OAAM;gCACN,WAAU;;kDAEV,6LAAC;wCACC,UAAS;wCACT,WAAU;;0DAGV,6LAAC;gDACC,IAAG;gDACH,aAAU;gDACV,GAAE;gDACF,WAAU;gDACV,MAAK;;;;;;0DAEP,6LAAC;gDACC,IAAG;gDACH,aAAU;gDACV,WAAU;;kEAEV,6LAAC;wDACC,IAAG;wDACH,aAAU;wDACV,GAAE;wDACF,WAAU;wDACV,MAAK;;;;;;kEAEP,6LAAC;wDACC,IAAG;wDACH,aAAU;wDACV,GAAE;wDACF,WAAU;wDACV,MAAK;;;;;;kEAEP,6LAAC;wDACC,IAAG;wDACH,aAAU;wDACV,GAAE;wDACF,WAAU;wDACV,MAAK;;;;;;kEAEP,6LAAC;wDACC,IAAG;wDACH,aAAU;wDACV,GAAE;wDACF,WAAU;wDACV,MAAK;;;;;;kEAEP,6LAAC;wDACC,IAAG;wDACH,aAAU;wDACV,GAAE;wDACF,WAAU;wDACV,MAAK;;;;;;kEAEP,6LAAC;wDACC,IAAG;wDACH,aAAU;wDACV,GAAE;wDACF,WAAU;wDACV,MAAK;;;;;;;;;;;;;;;;;;kDAIX,6LAAC;kDACC,cAAA,6LAAC;4CAAS,IAAG;sDACX,cAAA,6LAAC;gDAAK,OAAM;gDAAU,QAAO;gDAAS,MAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOnD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,eAAY;oCACZ,WAAU;8CACX;;;;;;8CAGD,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,eAAY;oCACZ,WAAU;8CACX;;;;;;;;;;;;;;;;;;8BAOL,6LAAC;oBACC,MAAK;oBACL,iBAAc;oBACd,iBAAc;oBACd,cAAW;oBACX,WAAU;8BAEV,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,iJAAA,CAAA,WAAQ;gCAAC,WAAU;gCAAwB,eAAY;;;;;;0CACxD,6LAAC;gCACC,IAAG;gCACH,MAAK;gCACL,aAAY;gCACZ,eAAY;gCACZ,cAAa;gCACb,OAAO;gCACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;gCACzC,WAAU;;;;;;0CAEZ,6LAAC;gCACC,eAAY;gCACZ,cAAW;gCACX,iBAAc;gCACd,WAAU;gCACV,MAAK;0CAEL,cAAA,6LAAC;oCACC,OAAM;oCACN,QAAO;oCACP,SAAQ;oCACR,MAAK;oCACL,OAAM;oCACN,eAAY;oCACZ,WAAU;oCACV,WAAU;8CAEV,cAAA,6LAAC;wCACC,GAAE;wCACF,MAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQf,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,eAAY;4BACZ,WAAU;sCACX;;;;;;sCAGD,6LAAC,qIAAA,CAAA,SAAM;4BACL,MAAK;4BACL,eAAY;4BACZ,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AAOX;GAlLwB;KAAA"}}, {"offset": {"line": 709, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}
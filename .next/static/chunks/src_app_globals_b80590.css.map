{"version": 3, "sources": [], "sections": [{"offset": {"line": 2, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/Work/Repositories/templgen/src/app/globals.css"], "sourcesContent": ["@import url(\"https://fonts.googleapis.com/css2?family=Jura:wght@300..700&display=swap\");\n\n*, ::before, ::after{\n  --tw-border-spacing-x: 0;\n  --tw-border-spacing-y: 0;\n  --tw-translate-x: 0;\n  --tw-translate-y: 0;\n  --tw-rotate: 0;\n  --tw-skew-x: 0;\n  --tw-skew-y: 0;\n  --tw-scale-x: 1;\n  --tw-scale-y: 1;\n  --tw-pan-x:  ;\n  --tw-pan-y:  ;\n  --tw-pinch-zoom:  ;\n  --tw-scroll-snap-strictness: proximity;\n  --tw-gradient-from-position:  ;\n  --tw-gradient-via-position:  ;\n  --tw-gradient-to-position:  ;\n  --tw-ordinal:  ;\n  --tw-slashed-zero:  ;\n  --tw-numeric-figure:  ;\n  --tw-numeric-spacing:  ;\n  --tw-numeric-fraction:  ;\n  --tw-ring-inset:  ;\n  --tw-ring-offset-width: 0px;\n  --tw-ring-offset-color: #fff;\n  --tw-ring-color: rgb(59 130 246 / 0.5);\n  --tw-ring-offset-shadow: 0 0 #0000;\n  --tw-ring-shadow: 0 0 #0000;\n  --tw-shadow: 0 0 #0000;\n  --tw-shadow-colored: 0 0 #0000;\n  --tw-blur:  ;\n  --tw-brightness:  ;\n  --tw-contrast:  ;\n  --tw-grayscale:  ;\n  --tw-hue-rotate:  ;\n  --tw-invert:  ;\n  --tw-saturate:  ;\n  --tw-sepia:  ;\n  --tw-drop-shadow:  ;\n  --tw-backdrop-blur:  ;\n  --tw-backdrop-brightness:  ;\n  --tw-backdrop-contrast:  ;\n  --tw-backdrop-grayscale:  ;\n  --tw-backdrop-hue-rotate:  ;\n  --tw-backdrop-invert:  ;\n  --tw-backdrop-opacity:  ;\n  --tw-backdrop-saturate:  ;\n  --tw-backdrop-sepia:  ;\n  --tw-contain-size:  ;\n  --tw-contain-layout:  ;\n  --tw-contain-paint:  ;\n  --tw-contain-style:  ;\n}\n\n::backdrop{\n  --tw-border-spacing-x: 0;\n  --tw-border-spacing-y: 0;\n  --tw-translate-x: 0;\n  --tw-translate-y: 0;\n  --tw-rotate: 0;\n  --tw-skew-x: 0;\n  --tw-skew-y: 0;\n  --tw-scale-x: 1;\n  --tw-scale-y: 1;\n  --tw-pan-x:  ;\n  --tw-pan-y:  ;\n  --tw-pinch-zoom:  ;\n  --tw-scroll-snap-strictness: proximity;\n  --tw-gradient-from-position:  ;\n  --tw-gradient-via-position:  ;\n  --tw-gradient-to-position:  ;\n  --tw-ordinal:  ;\n  --tw-slashed-zero:  ;\n  --tw-numeric-figure:  ;\n  --tw-numeric-spacing:  ;\n  --tw-numeric-fraction:  ;\n  --tw-ring-inset:  ;\n  --tw-ring-offset-width: 0px;\n  --tw-ring-offset-color: #fff;\n  --tw-ring-color: rgb(59 130 246 / 0.5);\n  --tw-ring-offset-shadow: 0 0 #0000;\n  --tw-ring-shadow: 0 0 #0000;\n  --tw-shadow: 0 0 #0000;\n  --tw-shadow-colored: 0 0 #0000;\n  --tw-blur:  ;\n  --tw-brightness:  ;\n  --tw-contrast:  ;\n  --tw-grayscale:  ;\n  --tw-hue-rotate:  ;\n  --tw-invert:  ;\n  --tw-saturate:  ;\n  --tw-sepia:  ;\n  --tw-drop-shadow:  ;\n  --tw-backdrop-blur:  ;\n  --tw-backdrop-brightness:  ;\n  --tw-backdrop-contrast:  ;\n  --tw-backdrop-grayscale:  ;\n  --tw-backdrop-hue-rotate:  ;\n  --tw-backdrop-invert:  ;\n  --tw-backdrop-opacity:  ;\n  --tw-backdrop-saturate:  ;\n  --tw-backdrop-sepia:  ;\n  --tw-contain-size:  ;\n  --tw-contain-layout:  ;\n  --tw-contain-paint:  ;\n  --tw-contain-style:  ;\n}\n\n/*\n! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com\n*/\n\n/*\n1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)\n2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)\n*/\n\n*,\n::before,\n::after {\n  box-sizing: border-box; /* 1 */\n  border-width: 0; /* 2 */\n  border-style: solid; /* 2 */\n  border-color: #e5e7eb; /* 2 */\n}\n\n::before,\n::after {\n  --tw-content: '';\n}\n\n/*\n1. Use a consistent sensible line-height in all browsers.\n2. Prevent adjustments of font size after orientation changes in iOS.\n3. Use a more readable tab size.\n4. Use the user's configured `sans` font-family by default.\n5. Use the user's configured `sans` font-feature-settings by default.\n6. Use the user's configured `sans` font-variation-settings by default.\n7. Disable tap highlights on iOS\n*/\n\nhtml,\n:host {\n  line-height: 1.5; /* 1 */\n  -webkit-text-size-adjust: 100%; /* 2 */\n  -moz-tab-size: 4; /* 3 */\n  tab-size: 4; /* 3 */\n  font-family: ui-sans-serif, system-ui, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\"; /* 4 */\n  font-feature-settings: normal; /* 5 */\n  font-variation-settings: normal; /* 6 */\n  -webkit-tap-highlight-color: transparent; /* 7 */\n}\n\n/*\n1. Remove the margin in all browsers.\n2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.\n*/\n\nbody {\n  margin: 0; /* 1 */\n  line-height: inherit; /* 2 */\n}\n\n/*\n1. Add the correct height in Firefox.\n2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)\n3. Ensure horizontal rules are visible by default.\n*/\n\nhr {\n  height: 0; /* 1 */\n  color: inherit; /* 2 */\n  border-top-width: 1px; /* 3 */\n}\n\n/*\nAdd the correct text decoration in Chrome, Edge, and Safari.\n*/\n\nabbr:where([title]) {\n  text-decoration: underline dotted;\n}\n\n/*\nRemove the default font size and weight for headings.\n*/\n\nh1,\nh2,\nh3,\nh4,\nh5,\nh6 {\n  font-size: inherit;\n  font-weight: inherit;\n}\n\n/*\nReset links to optimize for opt-in styling instead of opt-out.\n*/\n\na {\n  color: inherit;\n  text-decoration: inherit;\n}\n\n/*\nAdd the correct font weight in Edge and Safari.\n*/\n\nb,\nstrong {\n  font-weight: bolder;\n}\n\n/*\n1. Use the user's configured `mono` font-family by default.\n2. Use the user's configured `mono` font-feature-settings by default.\n3. Use the user's configured `mono` font-variation-settings by default.\n4. Correct the odd `em` font sizing in all browsers.\n*/\n\ncode,\nkbd,\nsamp,\npre {\n  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace; /* 1 */\n  font-feature-settings: normal; /* 2 */\n  font-variation-settings: normal; /* 3 */\n  font-size: 1em; /* 4 */\n}\n\n/*\nAdd the correct font size in all browsers.\n*/\n\nsmall {\n  font-size: 80%;\n}\n\n/*\nPrevent `sub` and `sup` elements from affecting the line height in all browsers.\n*/\n\nsub,\nsup {\n  font-size: 75%;\n  line-height: 0;\n  position: relative;\n  vertical-align: baseline;\n}\n\nsub {\n  bottom: -0.25em;\n}\n\nsup {\n  top: -0.5em;\n}\n\n/*\n1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)\n2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)\n3. Remove gaps between table borders by default.\n*/\n\ntable {\n  text-indent: 0; /* 1 */\n  border-color: inherit; /* 2 */\n  border-collapse: collapse; /* 3 */\n}\n\n/*\n1. Change the font styles in all browsers.\n2. Remove the margin in Firefox and Safari.\n3. Remove default padding in all browsers.\n*/\n\nbutton,\ninput,\noptgroup,\nselect,\ntextarea {\n  font-family: inherit; /* 1 */\n  font-feature-settings: inherit; /* 1 */\n  font-variation-settings: inherit; /* 1 */\n  font-size: 100%; /* 1 */\n  font-weight: inherit; /* 1 */\n  line-height: inherit; /* 1 */\n  letter-spacing: inherit; /* 1 */\n  color: inherit; /* 1 */\n  margin: 0; /* 2 */\n  padding: 0; /* 3 */\n}\n\n/*\nRemove the inheritance of text transform in Edge and Firefox.\n*/\n\nbutton,\nselect {\n  text-transform: none;\n}\n\n/*\n1. Correct the inability to style clickable types in iOS and Safari.\n2. Remove default button styles.\n*/\n\nbutton,\ninput:where([type='button']),\ninput:where([type='reset']),\ninput:where([type='submit']) {\n  -webkit-appearance: button; /* 1 */\n  background-color: transparent; /* 2 */\n  background-image: none; /* 2 */\n}\n\n/*\nUse the modern Firefox focus style for all focusable elements.\n*/\n\n:-moz-focusring {\n  outline: auto;\n}\n\n/*\nRemove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)\n*/\n\n:-moz-ui-invalid {\n  box-shadow: none;\n}\n\n/*\nAdd the correct vertical alignment in Chrome and Firefox.\n*/\n\nprogress {\n  vertical-align: baseline;\n}\n\n/*\nCorrect the cursor style of increment and decrement buttons in Safari.\n*/\n\n::-webkit-inner-spin-button,\n::-webkit-outer-spin-button {\n  height: auto;\n}\n\n/*\n1. Correct the odd appearance in Chrome and Safari.\n2. Correct the outline style in Safari.\n*/\n\n[type='search'] {\n  -webkit-appearance: textfield; /* 1 */\n  outline-offset: -2px; /* 2 */\n}\n\n/*\nRemove the inner padding in Chrome and Safari on macOS.\n*/\n\n::-webkit-search-decoration {\n  -webkit-appearance: none;\n}\n\n/*\n1. Correct the inability to style clickable types in iOS and Safari.\n2. Change font properties to `inherit` in Safari.\n*/\n\n::-webkit-file-upload-button {\n  -webkit-appearance: button; /* 1 */\n  font: inherit; /* 2 */\n}\n\n/*\nAdd the correct display in Chrome and Safari.\n*/\n\nsummary {\n  display: list-item;\n}\n\n/*\nRemoves the default spacing and border for appropriate elements.\n*/\n\nblockquote,\ndl,\ndd,\nh1,\nh2,\nh3,\nh4,\nh5,\nh6,\nhr,\nfigure,\np,\npre {\n  margin: 0;\n}\n\nfieldset {\n  margin: 0;\n  padding: 0;\n}\n\nlegend {\n  padding: 0;\n}\n\nol,\nul,\nmenu {\n  list-style: none;\n  margin: 0;\n  padding: 0;\n}\n\n/*\nReset default styling for dialogs.\n*/\n\ndialog {\n  padding: 0;\n}\n\n/*\nPrevent resizing textareas horizontally by default.\n*/\n\ntextarea {\n  resize: vertical;\n}\n\n/*\n1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)\n2. Set the default placeholder color to the user's configured gray 400 color.\n*/\n\ninput::placeholder,\ntextarea::placeholder {\n  opacity: 1; /* 1 */\n  color: #9ca3af; /* 2 */\n}\n\n/*\nSet the default cursor for buttons.\n*/\n\nbutton,\n[role=\"button\"] {\n  cursor: pointer;\n}\n\n/*\nMake sure disabled buttons don't get the pointer cursor.\n*/\n\n:disabled {\n  cursor: default;\n}\n\n/*\n1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)\n2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)\n   This can trigger a poorly considered lint error in some tools but is included by design.\n*/\n\nimg,\nsvg,\nvideo,\ncanvas,\naudio,\niframe,\nembed,\nobject {\n  display: block; /* 1 */\n  vertical-align: middle; /* 2 */\n}\n\n/*\nConstrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)\n*/\n\nimg,\nvideo {\n  max-width: 100%;\n  height: auto;\n}\n\n/* Make elements with the HTML hidden attribute stay hidden by default */\n\n[hidden]:where(:not([hidden=\"until-found\"])) {\n  display: none;\n}\n\n:root {\n    --background: #16161a;\n    --headline: #fffffe;\n    --paragraph: #94a1b2;\n    --button: #7f5af0;\n    --button2: #7f5af08a;\n    --button-border: #7f5af054;\n    --button-text: #fffffe;\n    --illustration-stroke: #01010179;\n    --main: #fffffe;\n    --highlight: #7f5af0;\n    --secondary: #72757e;\n    --tertiary: #2cb67d;\n    --card-background: #242629;\n    --card-background-effect: #2426295c;\n    --card-border-color: #ffffff16;\n    --border: #ffffff1a;\n    --card-headline: #fffffe;\n    --card-paragraph: #94a1b2;\n    --card-hover: #d9d9d91c;\n    --hover-select: #ffffff75;\n    --link-color: #7f5af0;\n    --link-hover: #fffffe;\n    --nav-item: #94a1b2;\n    --Logo-background: #fffffe;\n    --Logo-text-color: #fffffe;\n    --input-background: #242629;\n    --input-border-color: #7575755e;\n    --badge-background: #7f5af0;\n    --badge-text: #fefeff;\n    --skeleton-color: #353f4e;\n    --footer-border-color: #ffffff25;\n    --footer-text: #94a1b2;\n    --menu-color: #94a1b2;\n    --input-text: #fffffe;\n    --selectBox-border: #7f5af0;\n    --outline-button-text: #cbbaff;\n    --mark: #000;\n    --active: #fffffe;\n    --active-text: #16161a;\n    --mobile-nav: #16161aa6;\n    --gradient1: #7f5af0;\n    --gradient1: #baa3fd;\n  }\n\n*{\n  margin: 0px;\n  box-sizing: border-box;\n  border-color: var(--border);\n  padding: 0px;\n    scroll-behavior: smooth;\n}\n\nhtml{\n  font-size: 1rem;\n  line-height: 1.5rem;\n}\n\na{\n  cursor: pointer;\n}\n\nbody{\n  background-color: var(--background);\n  color: var(--foreground);\n    font-family: \"Cairo\", sans-serif;\n    overscroll-behavior: none;\n}\n.container{\n  width: 100%;\n  margin-right: auto;\n  margin-left: auto;\n  padding-right: 1rem;\n  padding-left: 1rem;\n}\n@media (min-width: 100%){\n\n  .container{\n    max-width: 100%;\n  }\n}\n@media (min-width: 1000px){\n\n  .container{\n    max-width: 1000px;\n  }\n}\n@media (min-width: 1200px){\n\n  .container{\n    max-width: 1200px;\n  }\n}\n@media (min-width: 1400px){\n\n  .container{\n    max-width: 1400px;\n  }\n}\n.container {\n    max-width: 1600px;\n    width: 100%;\n    margin-left: auto;\n    margin-right: auto;\n    padding-left: 16px;\n    padding-right: 16px;\n  }\nh1,\n  h2,\n  h3,\n  h4,\n  h5,\n  h6{\n  color: var(--headline);\n}\np{\n  color: var(--paragraph);\n}\na{\n  color: var(--link-color);\n}\n.sr-only{\n  position: absolute;\n  width: 1px;\n  height: 1px;\n  padding: 0;\n  margin: -1px;\n  overflow: hidden;\n  clip: rect(0, 0, 0, 0);\n  white-space: nowrap;\n  border-width: 0;\n}\n.pointer-events-none{\n  pointer-events: none;\n}\n.pointer-events-auto{\n  pointer-events: auto;\n}\n.visible{\n  visibility: visible;\n}\n.invisible{\n  visibility: hidden;\n}\n.fixed{\n  position: fixed;\n}\n.absolute{\n  position: absolute;\n}\n.relative{\n  position: relative;\n}\n.sticky{\n  position: sticky;\n}\n.-inset-px{\n  inset: -1px;\n}\n.inset-0{\n  inset: 0px;\n}\n.inset-x-0{\n  left: 0px;\n  right: 0px;\n}\n.inset-y-0{\n  top: 0px;\n  bottom: 0px;\n}\n.-bottom-12{\n  bottom: -3rem;\n}\n.-bottom-8{\n  bottom: -2rem;\n}\n.-left-12{\n  left: -3rem;\n}\n.-left-8{\n  left: -2rem;\n}\n.-right-1{\n  right: -0.25rem;\n}\n.-right-10{\n  right: -2.5rem;\n}\n.-right-12{\n  right: -3rem;\n}\n.-top-1{\n  top: -0.25rem;\n}\n.-top-10{\n  top: -2.5rem;\n}\n.-top-12{\n  top: -3rem;\n}\n.bottom-0{\n  bottom: 0px;\n}\n.bottom-6{\n  bottom: 1.5rem;\n}\n.left-0{\n  left: 0px;\n}\n.left-1{\n  left: 0.25rem;\n}\n.left-1\\/2{\n  left: 50%;\n}\n.left-14{\n  left: 3.5rem;\n}\n.left-2{\n  left: 0.5rem;\n}\n.left-3{\n  left: 0.75rem;\n}\n.left-8{\n  left: 2rem;\n}\n.left-\\[38px\\]{\n  left: 38px;\n}\n.left-\\[50\\%\\]{\n  left: 50%;\n}\n.right-0{\n  right: 0px;\n}\n.right-1{\n  right: 0.25rem;\n}\n.right-12{\n  right: 3rem;\n}\n.right-2{\n  right: 0.5rem;\n}\n.right-3{\n  right: 0.75rem;\n}\n.right-4{\n  right: 1rem;\n}\n.right-6{\n  right: 1.5rem;\n}\n.top-0{\n  top: 0px;\n}\n.top-1\\.5{\n  top: 0.375rem;\n}\n.top-1\\/2{\n  top: 50%;\n}\n.top-2{\n  top: 0.5rem;\n}\n.top-3\\.5{\n  top: 0.875rem;\n}\n.top-4{\n  top: 1rem;\n}\n.top-40{\n  top: 10rem;\n}\n.top-\\[1px\\]{\n  top: 1px;\n}\n.top-\\[50\\%\\]{\n  top: 50%;\n}\n.top-\\[60\\%\\]{\n  top: 60%;\n}\n.top-full{\n  top: 100%;\n}\n.isolate{\n  isolation: isolate;\n}\n.z-0{\n  z-index: 0;\n}\n.z-10{\n  z-index: 10;\n}\n.z-20{\n  z-index: 20;\n}\n.z-30{\n  z-index: 30;\n}\n.z-40{\n  z-index: 40;\n}\n.z-50{\n  z-index: 50;\n}\n.z-\\[100\\]{\n  z-index: 100;\n}\n.z-\\[1\\]{\n  z-index: 1;\n}\n.col-span-12{\n  grid-column: span 12 / span 12;\n}\n.m-auto{\n  margin: auto;\n}\n.-mx-1{\n  margin-left: -0.25rem;\n  margin-right: -0.25rem;\n}\n.-my-px{\n  margin-top: -1px;\n  margin-bottom: -1px;\n}\n.mx-2{\n  margin-left: 0.5rem;\n  margin-right: 0.5rem;\n}\n.mx-3\\.5{\n  margin-left: 0.875rem;\n  margin-right: 0.875rem;\n}\n.mx-4{\n  margin-left: 1rem;\n  margin-right: 1rem;\n}\n.mx-auto{\n  margin-left: auto;\n  margin-right: auto;\n}\n.my-0\\.5{\n  margin-top: 0.125rem;\n  margin-bottom: 0.125rem;\n}\n.my-1{\n  margin-top: 0.25rem;\n  margin-bottom: 0.25rem;\n}\n.my-6{\n  margin-top: 1.5rem;\n  margin-bottom: 1.5rem;\n}\n.-ml-4{\n  margin-left: -1rem;\n}\n.-mt-4{\n  margin-top: -1rem;\n}\n.mb-1{\n  margin-bottom: 0.25rem;\n}\n.mb-2{\n  margin-bottom: 0.5rem;\n}\n.mb-3{\n  margin-bottom: 0.75rem;\n}\n.mb-4{\n  margin-bottom: 1rem;\n}\n.ml-1{\n  margin-left: 0.25rem;\n}\n.ml-2{\n  margin-left: 0.5rem;\n}\n.ml-auto{\n  margin-left: auto;\n}\n.mr-2{\n  margin-right: 0.5rem;\n}\n.mt-0\\.5{\n  margin-top: 0.125rem;\n}\n.mt-1{\n  margin-top: 0.25rem;\n}\n.mt-1\\.5{\n  margin-top: 0.375rem;\n}\n.mt-2{\n  margin-top: 0.5rem;\n}\n.mt-24{\n  margin-top: 6rem;\n}\n.mt-4{\n  margin-top: 1rem;\n}\n.mt-8{\n  margin-top: 2rem;\n}\n.mt-auto{\n  margin-top: auto;\n}\n.mb-6{\n  margin-bottom: 1.5rem;\n}\n.mt-5{\n  margin-top: 1.25rem;\n}\n.mt-6{\n  margin-top: 1.5rem;\n}\n.ms-2{\n  margin-inline-start: 0.5rem;\n}\n.ms-\\[20px\\]{\n  margin-inline-start: 20px;\n}\n.block{\n  display: block;\n}\n.inline-block{\n  display: inline-block;\n}\n.flex{\n  display: flex;\n}\n.inline-flex{\n  display: inline-flex;\n}\n.table{\n  display: table;\n}\n.grid{\n  display: grid;\n}\n.hidden{\n  display: none;\n}\n.aspect-square{\n  aspect-ratio: 1 / 1;\n}\n.aspect-video{\n  aspect-ratio: 16 / 9;\n}\n.size-1{\n  width: 0.25rem;\n  height: 0.25rem;\n}\n.size-2\\.5{\n  width: 0.625rem;\n  height: 0.625rem;\n}\n.size-20{\n  width: 5rem;\n  height: 5rem;\n}\n.size-4{\n  width: 1rem;\n  height: 1rem;\n}\n.size-9{\n  width: 2.25rem;\n  height: 2.25rem;\n}\n.size-full{\n  width: 100%;\n  height: 100%;\n}\n.h-1\\.5{\n  height: 0.375rem;\n}\n.h-1\\/2{\n  height: 50%;\n}\n.h-10{\n  height: 2.5rem;\n}\n.h-11{\n  height: 2.75rem;\n}\n.h-12{\n  height: 3rem;\n}\n.h-16{\n  height: 4rem;\n}\n.h-2{\n  height: 0.5rem;\n}\n.h-2\\.5{\n  height: 0.625rem;\n}\n.h-24{\n  height: 6rem;\n}\n.h-3{\n  height: 0.75rem;\n}\n.h-3\\.5{\n  height: 0.875rem;\n}\n.h-32{\n  height: 8rem;\n}\n.h-4{\n  height: 1rem;\n}\n.h-5{\n  height: 1.25rem;\n}\n.h-6{\n  height: 1.5rem;\n}\n.h-7{\n  height: 1.75rem;\n}\n.h-8{\n  height: 2rem;\n}\n.h-9{\n  height: 2.25rem;\n}\n.h-\\[100vh\\]{\n  height: 100vh;\n}\n.h-\\[1280px\\]{\n  height: 1280px;\n}\n.h-\\[169\\%\\]{\n  height: 169%;\n}\n.h-\\[1px\\]{\n  height: 1px;\n}\n.h-\\[var\\(--radix-navigation-menu-viewport-height\\)\\]{\n  height: var(--radix-navigation-menu-viewport-height);\n}\n.h-\\[var\\(--radix-select-trigger-height\\)\\]{\n  height: var(--radix-select-trigger-height);\n}\n.h-auto{\n  height: auto;\n}\n.h-full{\n  height: 100%;\n}\n.h-max{\n  height: max-content;\n}\n.h-px{\n  height: 1px;\n}\n.h-screen{\n  height: 100vh;\n}\n.h-svh{\n  height: 100svh;\n}\n.max-h-48{\n  max-height: 12rem;\n}\n.max-h-96{\n  max-height: 24rem;\n}\n.max-h-\\[300px\\]{\n  max-height: 300px;\n}\n.max-h-\\[80vh\\]{\n  max-height: 80vh;\n}\n.max-h-screen{\n  max-height: 100vh;\n}\n.min-h-0{\n  min-height: 0px;\n}\n.min-h-32{\n  min-height: 8rem;\n}\n.min-h-\\[80px\\]{\n  min-height: 80px;\n}\n.min-h-screen{\n  min-height: 100vh;\n}\n.min-h-svh{\n  min-height: 100svh;\n}\n.w-0{\n  width: 0px;\n}\n.w-1{\n  width: 0.25rem;\n}\n.w-1\\/2{\n  width: 50%;\n}\n.w-1\\/3{\n  width: 33.333333%;\n}\n.w-10{\n  width: 2.5rem;\n}\n.w-11{\n  width: 2.75rem;\n}\n.w-12{\n  width: 3rem;\n}\n.w-16{\n  width: 4rem;\n}\n.w-2{\n  width: 0.5rem;\n}\n.w-2\\.5{\n  width: 0.625rem;\n}\n.w-24{\n  width: 6rem;\n}\n.w-3{\n  width: 0.75rem;\n}\n.w-3\\.5{\n  width: 0.875rem;\n}\n.w-3\\/4{\n  width: 75%;\n}\n.w-32{\n  width: 8rem;\n}\n.w-4{\n  width: 1rem;\n}\n.w-5{\n  width: 1.25rem;\n}\n.w-6{\n  width: 1.5rem;\n}\n.w-64{\n  width: 16rem;\n}\n.w-7{\n  width: 1.75rem;\n}\n.w-72{\n  width: 18rem;\n}\n.w-8{\n  width: 2rem;\n}\n.w-9{\n  width: 2.25rem;\n}\n.w-\\[--sidebar-width\\]{\n  width: var(--sidebar-width);\n}\n.w-\\[100px\\]{\n  width: 100px;\n}\n.w-\\[100vw\\]{\n  width: 100vw;\n}\n.w-\\[138\\%\\]{\n  width: 138%;\n}\n.w-\\[1px\\]{\n  width: 1px;\n}\n.w-\\[240px\\]{\n  width: 240px;\n}\n.w-\\[2px\\]{\n  width: 2px;\n}\n.w-\\[560px\\]{\n  width: 560px;\n}\n.w-auto{\n  width: auto;\n}\n.w-fit{\n  width: fit-content;\n}\n.w-full{\n  width: 100%;\n}\n.w-max{\n  width: max-content;\n}\n.w-px{\n  width: 1px;\n}\n.w-screen{\n  width: 100vw;\n}\n.w-\\[900px\\]{\n  width: 900px;\n}\n.min-w-0{\n  min-width: 0px;\n}\n.min-w-10{\n  min-width: 2.5rem;\n}\n.min-w-11{\n  min-width: 2.75rem;\n}\n.min-w-5{\n  min-width: 1.25rem;\n}\n.min-w-9{\n  min-width: 2.25rem;\n}\n.min-w-\\[100px\\]{\n  min-width: 100px;\n}\n.min-w-\\[12rem\\]{\n  min-width: 12rem;\n}\n.min-w-\\[140px\\]{\n  min-width: 140px;\n}\n.min-w-\\[8rem\\]{\n  min-width: 8rem;\n}\n.min-w-\\[var\\(--radix-select-trigger-width\\)\\]{\n  min-width: var(--radix-select-trigger-width);\n}\n.max-w-4xl{\n  max-width: 56rem;\n}\n.max-w-\\[--skeleton-width\\]{\n  max-width: var(--skeleton-width);\n}\n.max-w-\\[1400px\\]{\n  max-width: 1400px;\n}\n.max-w-\\[320px\\]{\n  max-width: 320px;\n}\n.max-w-\\[600px\\]{\n  max-width: 600px;\n}\n.max-w-\\[720px\\]{\n  max-width: 720px;\n}\n.max-w-lg{\n  max-width: 32rem;\n}\n.max-w-max{\n  max-width: max-content;\n}\n.max-w-md{\n  max-width: 28rem;\n}\n.max-w-xs{\n  max-width: 20rem;\n}\n.max-w-\\[360px\\]{\n  max-width: 360px;\n}\n.max-w-7xl{\n  max-width: 80rem;\n}\n.max-w-\\[90vw\\]{\n  max-width: 90vw;\n}\n.max-w-\\[95vw\\]{\n  max-width: 95vw;\n}\n.flex-1{\n  flex: 1 1 0%;\n}\n.flex-shrink-0{\n  flex-shrink: 0;\n}\n.shrink-0{\n  flex-shrink: 0;\n}\n.flex-grow{\n  flex-grow: 1;\n}\n.grow{\n  flex-grow: 1;\n}\n.grow-0{\n  flex-grow: 0;\n}\n.basis-full{\n  flex-basis: 100%;\n}\n.caption-bottom{\n  caption-side: bottom;\n}\n.border-collapse{\n  border-collapse: collapse;\n}\n.-translate-x-1\\/2{\n  --tw-translate-x: -50%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.-translate-x-8{\n  --tw-translate-x: -2rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.-translate-x-px{\n  --tw-translate-x: -1px;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.-translate-y-1\\/2{\n  --tw-translate-y: -50%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.-translate-y-\\[350px\\]{\n  --tw-translate-y: -350px;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.translate-x-0{\n  --tw-translate-x: 0px;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.translate-x-8{\n  --tw-translate-x: 2rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.translate-x-\\[-50\\%\\]{\n  --tw-translate-x: -50%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.translate-x-px{\n  --tw-translate-x: 1px;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.translate-y-\\[-50\\%\\]{\n  --tw-translate-y: -50%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.-rotate-45{\n  --tw-rotate: -45deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.rotate-45{\n  --tw-rotate: 45deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.rotate-90{\n  --tw-rotate: 90deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.transform{\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n@keyframes bounce{\n\n  0%, 100%{\n    transform: translateY(-25%);\n    animation-timing-function: cubic-bezier(0.8,0,1,1);\n  }\n\n  50%{\n    transform: none;\n    animation-timing-function: cubic-bezier(0,0,0.2,1);\n  }\n}\n.animate-bounce{\n  animation: bounce 1s infinite;\n}\n@keyframes pulse{\n\n  50%{\n    opacity: .5;\n  }\n}\n.animate-pulse{\n  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\n}\n@keyframes spin{\n\n  to{\n    transform: rotate(360deg);\n  }\n}\n.animate-spin{\n  animation: spin 1s linear infinite;\n}\n.cursor-default{\n  cursor: default;\n}\n.cursor-not-allowed{\n  cursor: not-allowed;\n}\n.cursor-pointer{\n  cursor: pointer;\n}\n.cursor-wait{\n  cursor: wait;\n}\n.touch-none{\n  touch-action: none;\n}\n.touch-manipulation{\n  touch-action: manipulation;\n}\n.select-none{\n  user-select: none;\n}\n.list-none{\n  list-style-type: none;\n}\n.grid-cols-1{\n  grid-template-columns: repeat(1, minmax(0, 1fr));\n}\n.grid-cols-2{\n  grid-template-columns: repeat(2, minmax(0, 1fr));\n}\n.grid-cols-8{\n  grid-template-columns: repeat(8, minmax(0, 1fr));\n}\n.grid-cols-12{\n  grid-template-columns: repeat(12, minmax(0, 1fr));\n}\n.grid-cols-3{\n  grid-template-columns: repeat(3, minmax(0, 1fr));\n}\n.flex-row{\n  flex-direction: row;\n}\n.flex-row-reverse{\n  flex-direction: row-reverse;\n}\n.flex-col{\n  flex-direction: column;\n}\n.flex-col-reverse{\n  flex-direction: column-reverse;\n}\n.flex-wrap{\n  flex-wrap: wrap;\n}\n.items-start{\n  align-items: flex-start;\n}\n.items-end{\n  align-items: flex-end;\n}\n.items-center{\n  align-items: center;\n}\n.items-stretch{\n  align-items: stretch;\n}\n.justify-start{\n  justify-content: flex-start;\n}\n.justify-end{\n  justify-content: flex-end;\n}\n.justify-center{\n  justify-content: center;\n}\n.justify-between{\n  justify-content: space-between;\n}\n.justify-around{\n  justify-content: space-around;\n}\n.gap-0\\.5{\n  gap: 0.125rem;\n}\n.gap-1{\n  gap: 0.25rem;\n}\n.gap-1\\.5{\n  gap: 0.375rem;\n}\n.gap-2{\n  gap: 0.5rem;\n}\n.gap-3{\n  gap: 0.75rem;\n}\n.gap-4{\n  gap: 1rem;\n}\n.gap-6{\n  gap: 1.5rem;\n}\n.gap-\\[4px\\]{\n  gap: 4px;\n}\n.gap-x-4{\n  column-gap: 1rem;\n}\n.gap-x-6{\n  column-gap: 1.5rem;\n}\n.gap-y-2{\n  row-gap: 0.5rem;\n}\n.gap-y-4{\n  row-gap: 1rem;\n}\n.space-x-1 > :not([hidden]) ~ :not([hidden]){\n  --tw-space-x-reverse: 0;\n  margin-right: calc(0.25rem * var(--tw-space-x-reverse));\n  margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));\n}\n.space-x-2 > :not([hidden]) ~ :not([hidden]){\n  --tw-space-x-reverse: 0;\n  margin-right: calc(0.5rem * var(--tw-space-x-reverse));\n  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\n}\n.space-x-4 > :not([hidden]) ~ :not([hidden]){\n  --tw-space-x-reverse: 0;\n  margin-right: calc(1rem * var(--tw-space-x-reverse));\n  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));\n}\n.space-y-1 > :not([hidden]) ~ :not([hidden]){\n  --tw-space-y-reverse: 0;\n  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));\n}\n.space-y-1\\.5 > :not([hidden]) ~ :not([hidden]){\n  --tw-space-y-reverse: 0;\n  margin-top: calc(0.375rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(0.375rem * var(--tw-space-y-reverse));\n}\n.space-y-2 > :not([hidden]) ~ :not([hidden]){\n  --tw-space-y-reverse: 0;\n  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));\n}\n.space-y-3 > :not([hidden]) ~ :not([hidden]){\n  --tw-space-y-reverse: 0;\n  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));\n}\n.space-y-4 > :not([hidden]) ~ :not([hidden]){\n  --tw-space-y-reverse: 0;\n  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(1rem * var(--tw-space-y-reverse));\n}\n.space-y-6 > :not([hidden]) ~ :not([hidden]){\n  --tw-space-y-reverse: 0;\n  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));\n}\n.self-start{\n  align-self: flex-start;\n}\n.overflow-auto{\n  overflow: auto;\n}\n.overflow-hidden{\n  overflow: hidden;\n}\n.overflow-y-auto{\n  overflow-y: auto;\n}\n.overflow-x-hidden{\n  overflow-x: hidden;\n}\n.truncate{\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n.whitespace-nowrap{\n  white-space: nowrap;\n}\n.whitespace-pre{\n  white-space: pre;\n}\n.whitespace-pre-wrap{\n  white-space: pre-wrap;\n}\n.break-words{\n  overflow-wrap: break-word;\n}\n.rounded{\n  border-radius: 0.25rem;\n}\n.rounded-\\[10px\\]{\n  border-radius: 10px;\n}\n.rounded-\\[12px\\]{\n  border-radius: 12px;\n}\n.rounded-\\[20px\\]{\n  border-radius: 20px;\n}\n.rounded-\\[2px\\]{\n  border-radius: 2px;\n}\n.rounded-\\[8px\\]{\n  border-radius: 8px;\n}\n.rounded-\\[inherit\\]{\n  border-radius: inherit;\n}\n.rounded-full{\n  border-radius: 9999px;\n}\n.rounded-md{\n  border-radius: calc(var(--radius) - 2px);\n}\n.rounded-sm{\n  border-radius: calc(var(--radius) - 4px);\n}\n.rounded-xl{\n  border-radius: 0.75rem;\n}\n.rounded-lg{\n  border-radius: var(--radius);\n}\n.rounded-t-\\[10px\\]{\n  border-top-left-radius: 10px;\n  border-top-right-radius: 10px;\n}\n.rounded-tl-sm{\n  border-top-left-radius: calc(var(--radius) - 4px);\n}\n.border{\n  border-width: 1px;\n}\n.border-2{\n  border-width: 2px;\n}\n.border-\\[1\\.5px\\]{\n  border-width: 1.5px;\n}\n.border-y{\n  border-top-width: 1px;\n  border-bottom-width: 1px;\n}\n.border-b{\n  border-bottom-width: 1px;\n}\n.border-b-2{\n  border-bottom-width: 2px;\n}\n.border-l{\n  border-left-width: 1px;\n}\n.border-r{\n  border-right-width: 1px;\n}\n.border-t{\n  border-top-width: 1px;\n}\n.border-dashed{\n  border-style: dashed;\n}\n.border-\\[--color-border\\]{\n  border-color: var(--color-border);\n}\n.border-\\[var\\(--border\\)\\]{\n  border-color: var(--border);\n}\n.border-\\[var\\(--card-border-color\\)\\]{\n  border-color: var(--card-border-color);\n}\n.border-\\[var\\(--input-border-color\\)\\]{\n  border-color: var(--input-border-color);\n}\n.border-\\[var\\(--link-color\\)\\]{\n  border-color: var(--link-color);\n}\n.border-blue-200{\n  --tw-border-opacity: 1;\n  border-color: rgb(191 219 254 / var(--tw-border-opacity, 1));\n}\n.border-blue-500{\n  --tw-border-opacity: 1;\n  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));\n}\n.border-destructive{\n  border-color: var(--destructive);\n}\n.border-gray-200{\n  --tw-border-opacity: 1;\n  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));\n}\n.border-gray-300{\n  --tw-border-opacity: 1;\n  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));\n}\n.border-green-200{\n  --tw-border-opacity: 1;\n  border-color: rgb(187 247 208 / var(--tw-border-opacity, 1));\n}\n.border-input{\n  border-color: var(--input);\n}\n.border-primary{\n  border-color: var(--primary);\n}\n.border-red-200{\n  --tw-border-opacity: 1;\n  border-color: rgb(254 202 202 / var(--tw-border-opacity, 1));\n}\n.border-red-500{\n  --tw-border-opacity: 1;\n  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));\n}\n.border-sidebar-border{\n  border-color: var(--sidebar-border);\n}\n.border-transparent{\n  border-color: transparent;\n}\n.border-\\[var\\(--button-border\\)\\]{\n  border-color: var(--button-border);\n}\n.border-\\[var\\(--selectBox-border\\)\\]{\n  border-color: var(--selectBox-border);\n}\n.border-red-400{\n  --tw-border-opacity: 1;\n  border-color: rgb(248 113 113 / var(--tw-border-opacity, 1));\n}\n.border-\\[var\\(--footer-border-color\\)\\]{\n  border-color: var(--footer-border-color);\n}\n.border-l-transparent{\n  border-left-color: transparent;\n}\n.border-t-transparent{\n  border-top-color: transparent;\n}\n.bg-\\[--color-bg\\]{\n  background-color: var(--color-bg);\n}\n.bg-\\[var\\(--background\\)\\]{\n  background-color: var(--background);\n}\n.bg-\\[var\\(--badge-background\\)\\]{\n  background-color: var(--badge-background);\n}\n.bg-\\[var\\(--border\\)\\]{\n  background-color: var(--border);\n}\n.bg-\\[var\\(--button\\)\\]{\n  background-color: var(--button);\n}\n.bg-\\[var\\(--card-background\\)\\]{\n  background-color: var(--card-background);\n}\n.bg-\\[var\\(--card-hover\\)\\]{\n  background-color: var(--card-hover);\n}\n.bg-\\[var\\(--headline\\)\\]{\n  background-color: var(--headline);\n}\n.bg-\\[var\\(--input-background\\)\\]{\n  background-color: var(--input-background);\n}\n.bg-\\[var\\(--link-color\\)\\]{\n  background-color: var(--link-color);\n}\n.bg-\\[var\\(--secondary\\)\\]{\n  background-color: var(--secondary);\n}\n.bg-\\[var\\(--tertiary\\)\\]{\n  background-color: var(--tertiary);\n}\n.bg-accent{\n  background-color: var(--accent);\n}\n.bg-background{\n  background-color: var(--background);\n}\n.bg-black{\n  --tw-bg-opacity: 1;\n  background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));\n}\n.bg-black\\/50{\n  background-color: rgb(0 0 0 / 0.5);\n}\n.bg-black\\/80{\n  background-color: rgb(0 0 0 / 0.8);\n}\n.bg-blue-100{\n  --tw-bg-opacity: 1;\n  background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));\n}\n.bg-blue-50{\n  --tw-bg-opacity: 1;\n  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));\n}\n.bg-blue-500{\n  --tw-bg-opacity: 1;\n  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));\n}\n.bg-blue-600{\n  --tw-bg-opacity: 1;\n  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));\n}\n.bg-border{\n  background-color: var(--border);\n}\n.bg-destructive{\n  background-color: var(--destructive);\n}\n.bg-foreground{\n  background-color: var(--foreground);\n}\n.bg-gray-200{\n  --tw-bg-opacity: 1;\n  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));\n}\n.bg-gray-50{\n  --tw-bg-opacity: 1;\n  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));\n}\n.bg-green-100{\n  --tw-bg-opacity: 1;\n  background-color: rgb(220 252 231 / var(--tw-bg-opacity, 1));\n}\n.bg-green-50{\n  --tw-bg-opacity: 1;\n  background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1));\n}\n.bg-green-500{\n  --tw-bg-opacity: 1;\n  background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1));\n}\n.bg-muted{\n  background-color: var(--muted);\n}\n.bg-popover{\n  background-color: var(--popover);\n}\n.bg-primary{\n  background-color: var(--primary);\n}\n.bg-purple-500{\n  --tw-bg-opacity: 1;\n  background-color: rgb(168 85 247 / var(--tw-bg-opacity, 1));\n}\n.bg-red-100{\n  --tw-bg-opacity: 1;\n  background-color: rgb(254 226 226 / var(--tw-bg-opacity, 1));\n}\n.bg-red-50{\n  --tw-bg-opacity: 1;\n  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));\n}\n.bg-red-600{\n  --tw-bg-opacity: 1;\n  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));\n}\n.bg-secondary{\n  background-color: var(--secondary);\n}\n.bg-sidebar{\n  background-color: var(--sidebar-background);\n}\n.bg-sidebar-border{\n  background-color: var(--sidebar-border);\n}\n.bg-transparent{\n  background-color: transparent;\n}\n.bg-\\[\\#fafafa\\]{\n  --tw-bg-opacity: 1;\n  background-color: rgb(250 250 250 / var(--tw-bg-opacity, 1));\n}\n.bg-white{\n  --tw-bg-opacity: 1;\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\n}\n.bg-\\[linear-gradient\\(to_bottom\\2c var\\(--tw-gradient-stops\\)\\)\\]{\n  background-image: linear-gradient(to bottom,var(--tw-gradient-stops));\n}\n.bg-\\[radial-gradient\\(50\\%_50\\%_at_50\\%_50\\%\\2c hsla\\(0\\2c 0\\%\\2c 85\\%\\2c \\.04\\)_0\\2c hsla\\(0\\2c 0\\%\\2c 45\\%\\2c \\.02\\)_80\\%\\2c transparent_100\\%\\)\\]{\n  background-image: radial-gradient(50% 50% at 50% 50%,hsla(0,0%,85%,.04) 0,hsla(0,0%,45%,.02) 80%,transparent 100%);\n}\n.bg-\\[radial-gradient\\(50\\%_50\\%_at_50\\%_50\\%\\2c hsla\\(0\\2c 0\\%\\2c 85\\%\\2c \\.06\\)_0\\2c hsla\\(0\\2c 0\\%\\2c 45\\%\\2c \\.02\\)_80\\%\\2c transparent_100\\%\\)\\]{\n  background-image: radial-gradient(50% 50% at 50% 50%,hsla(0,0%,85%,.06) 0,hsla(0,0%,45%,.02) 80%,transparent 100%);\n}\n.bg-\\[radial-gradient\\(68\\.54\\%_68\\.72\\%_at_55\\.02\\%_31\\.46\\%\\2c hsla\\(0\\2c 0\\%\\2c 85\\%\\2c \\.08\\)_0\\2c hsla\\(0\\2c 0\\%\\2c 55\\%\\2c \\.02\\)_50\\%\\2c hsla\\(0\\2c 0\\%\\2c 45\\%\\2c 0\\)_80\\%\\)\\]{\n  background-image: radial-gradient(68.54% 68.72% at 55.02% 31.46%,hsla(0,0%,85%,.08) 0,hsla(0,0%,55%,.02) 50%,hsla(0,0%,45%,0) 80%);\n}\n.bg-gradient-to-b{\n  background-image: linear-gradient(to bottom, var(--tw-gradient-stops));\n}\n.bg-gradient-to-l{\n  background-image: linear-gradient(to left, var(--tw-gradient-stops));\n}\n.bg-gradient-to-r{\n  background-image: linear-gradient(to right, var(--tw-gradient-stops));\n}\n.bg-gradient-to-t{\n  background-image: linear-gradient(to top, var(--tw-gradient-stops));\n}\n.from-\\[var\\(--button\\)\\]{\n  --tw-gradient-from: var(--button) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-\\[var\\(--card-border-color\\)\\]{\n  --tw-gradient-from: var(--card-border-color) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-\\[var\\(--link-color\\)\\]{\n  --tw-gradient-from: var(--link-color) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-background{\n  --tw-gradient-from: var(--background) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-transparent{\n  --tw-gradient-from: transparent var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-\\[0\\%\\]{\n  --tw-gradient-from-position: 0%;\n}\n.via-\\[var\\(--card-border-color\\)\\]{\n  --tw-gradient-to: rgb(255 255 255 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--card-border-color) var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\n.via-\\[var\\(--link-color\\)\\]{\n  --tw-gradient-to: rgb(255 255 255 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--link-color) var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\n.via-\\[8\\%\\]{\n  --tw-gradient-via-position: 8%;\n}\n.to-\\[var\\(--button2\\)\\]{\n  --tw-gradient-to: var(--button2) var(--tw-gradient-to-position);\n}\n.to-transparent{\n  --tw-gradient-to: transparent var(--tw-gradient-to-position);\n}\n.to-\\[99\\%\\]{\n  --tw-gradient-to-position: 99%;\n}\n.fill-background{\n  fill: var(--background);\n}\n.fill-blue-500{\n  fill: #3b82f6;\n}\n.fill-current{\n  fill: currentColor;\n}\n.fill-emerald-500{\n  fill: #10b981;\n}\n.fill-foreground{\n  fill: var(--foreground);\n}\n.fill-popover{\n  fill: var(--popover);\n}\n.fill-red-500{\n  fill: #ef4444;\n}\n.stroke-background{\n  stroke: var(--background);\n}\n.stroke-blue-500{\n  stroke: #3b82f6;\n}\n.stroke-emerald-500{\n  stroke: #10b981;\n}\n.stroke-foreground{\n  stroke: var(--foreground);\n}\n.stroke-red-500{\n  stroke: #ef4444;\n}\n.object-cover{\n  object-fit: cover;\n}\n.p-0{\n  padding: 0px;\n}\n.p-0\\.5{\n  padding: 0.125rem;\n}\n.p-1{\n  padding: 0.25rem;\n}\n.p-2{\n  padding: 0.5rem;\n}\n.p-3{\n  padding: 0.75rem;\n}\n.p-4{\n  padding: 1rem;\n}\n.p-6{\n  padding: 1.5rem;\n}\n.p-\\[1px\\]{\n  padding: 1px;\n}\n.px-0{\n  padding-left: 0px;\n  padding-right: 0px;\n}\n.px-1{\n  padding-left: 0.25rem;\n  padding-right: 0.25rem;\n}\n.px-2{\n  padding-left: 0.5rem;\n  padding-right: 0.5rem;\n}\n.px-2\\.5{\n  padding-left: 0.625rem;\n  padding-right: 0.625rem;\n}\n.px-3{\n  padding-left: 0.75rem;\n  padding-right: 0.75rem;\n}\n.px-4{\n  padding-left: 1rem;\n  padding-right: 1rem;\n}\n.px-5{\n  padding-left: 1.25rem;\n  padding-right: 1.25rem;\n}\n.px-8{\n  padding-left: 2rem;\n  padding-right: 2rem;\n}\n.py-0\\.5{\n  padding-top: 0.125rem;\n  padding-bottom: 0.125rem;\n}\n.py-1{\n  padding-top: 0.25rem;\n  padding-bottom: 0.25rem;\n}\n.py-1\\.5{\n  padding-top: 0.375rem;\n  padding-bottom: 0.375rem;\n}\n.py-12{\n  padding-top: 3rem;\n  padding-bottom: 3rem;\n}\n.py-2{\n  padding-top: 0.5rem;\n  padding-bottom: 0.5rem;\n}\n.py-3{\n  padding-top: 0.75rem;\n  padding-bottom: 0.75rem;\n}\n.py-4{\n  padding-top: 1rem;\n  padding-bottom: 1rem;\n}\n.py-6{\n  padding-top: 1.5rem;\n  padding-bottom: 1.5rem;\n}\n.py-8{\n  padding-top: 2rem;\n  padding-bottom: 2rem;\n}\n.px-6{\n  padding-left: 1.5rem;\n  padding-right: 1.5rem;\n}\n.pb-20{\n  padding-bottom: 5rem;\n}\n.pb-3{\n  padding-bottom: 0.75rem;\n}\n.pb-4{\n  padding-bottom: 1rem;\n}\n.pl-10{\n  padding-left: 2.5rem;\n}\n.pl-2\\.5{\n  padding-left: 0.625rem;\n}\n.pl-4{\n  padding-left: 1rem;\n}\n.pl-8{\n  padding-left: 2rem;\n}\n.pr-1{\n  padding-right: 0.25rem;\n}\n.pr-10{\n  padding-right: 2.5rem;\n}\n.pr-12{\n  padding-right: 3rem;\n}\n.pr-2{\n  padding-right: 0.5rem;\n}\n.pr-2\\.5{\n  padding-right: 0.625rem;\n}\n.pr-20{\n  padding-right: 5rem;\n}\n.pr-4{\n  padding-right: 1rem;\n}\n.pr-8{\n  padding-right: 2rem;\n}\n.ps-20{\n  padding-inline-start: 5rem;\n}\n.pt-0{\n  padding-top: 0px;\n}\n.pt-1{\n  padding-top: 0.25rem;\n}\n.pt-2{\n  padding-top: 0.5rem;\n}\n.pt-3{\n  padding-top: 0.75rem;\n}\n.pt-4{\n  padding-top: 1rem;\n}\n.pt-5{\n  padding-top: 1.25rem;\n}\n.text-left{\n  text-align: left;\n}\n.text-center{\n  text-align: center;\n}\n.text-start{\n  text-align: start;\n}\n.align-middle{\n  vertical-align: middle;\n}\n.font-mono{\n  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace;\n}\n.text-2xl{\n  font-size: 1.5rem;\n  line-height: 2rem;\n}\n.text-3xl{\n  font-size: 1.875rem;\n  line-height: 2.25rem;\n}\n.text-4xl{\n  font-size: 2.25rem;\n  line-height: 2.5rem;\n}\n.text-\\[0\\.8rem\\]{\n  font-size: 0.8rem;\n}\n.text-base{\n  font-size: 1rem;\n  line-height: 1.5rem;\n}\n.text-lg{\n  font-size: 1.125rem;\n  line-height: 1.75rem;\n}\n.text-sm{\n  font-size: 0.875rem;\n  line-height: 1.25rem;\n}\n.text-xl{\n  font-size: 1.25rem;\n  line-height: 1.75rem;\n}\n.text-xs{\n  font-size: 0.75rem;\n  line-height: 1rem;\n}\n.font-bold{\n  font-weight: 700;\n}\n.font-medium{\n  font-weight: 500;\n}\n.font-normal{\n  font-weight: 400;\n}\n.font-semibold{\n  font-weight: 600;\n}\n.uppercase{\n  text-transform: uppercase;\n}\n.tabular-nums{\n  --tw-numeric-spacing: tabular-nums;\n  font-variant-numeric: var(--tw-ordinal) var(--tw-slashed-zero) var(--tw-numeric-figure) var(--tw-numeric-spacing) var(--tw-numeric-fraction);\n}\n.leading-6{\n  line-height: 1.5rem;\n}\n.leading-none{\n  line-height: 1;\n}\n.leading-tight{\n  line-height: 1.25;\n}\n.tracking-tight{\n  letter-spacing: -0.025em;\n}\n.tracking-widest{\n  letter-spacing: 0.1em;\n}\n.tracking-wide{\n  letter-spacing: 0.025em;\n}\n.text-\\[var\\(--badge-text\\)\\]{\n  color: var(--badge-text);\n}\n.text-\\[var\\(--button\\)\\]{\n  color: var(--button);\n}\n.text-\\[var\\(--button-text\\)\\]{\n  color: var(--button-text);\n}\n.text-\\[var\\(--card-headline\\)\\]{\n  color: var(--card-headline);\n}\n.text-\\[var\\(--card-paragraph\\)\\]{\n  color: var(--card-paragraph);\n}\n.text-\\[var\\(--headline\\)\\]{\n  color: var(--headline);\n}\n.text-\\[var\\(--input-text\\)\\]{\n  color: var(--input-text);\n}\n.text-\\[var\\(--link-color\\)\\]{\n  color: var(--link-color);\n}\n.text-\\[var\\(--menu-color\\)\\]{\n  color: var(--menu-color);\n}\n.text-\\[var\\(--muted\\)\\]{\n  color: var(--muted);\n}\n.text-\\[var\\(--nav-item\\)\\]{\n  color: var(--nav-item);\n}\n.text-\\[var\\(--paragraph\\)\\]{\n  color: var(--paragraph);\n}\n.text-accent-foreground{\n  color: var(--accent-foreground);\n}\n.text-blue-500{\n  --tw-text-opacity: 1;\n  color: rgb(59 130 246 / var(--tw-text-opacity, 1));\n}\n.text-blue-600{\n  --tw-text-opacity: 1;\n  color: rgb(37 99 235 / var(--tw-text-opacity, 1));\n}\n.text-blue-700{\n  --tw-text-opacity: 1;\n  color: rgb(29 78 216 / var(--tw-text-opacity, 1));\n}\n.text-blue-800{\n  --tw-text-opacity: 1;\n  color: rgb(30 64 175 / var(--tw-text-opacity, 1));\n}\n.text-current{\n  color: currentColor;\n}\n.text-destructive{\n  color: var(--destructive);\n}\n.text-destructive-foreground{\n  color: var(--destructive-foreground);\n}\n.text-foreground{\n  color: var(--foreground);\n}\n.text-gray-400{\n  --tw-text-opacity: 1;\n  color: rgb(156 163 175 / var(--tw-text-opacity, 1));\n}\n.text-gray-500{\n  --tw-text-opacity: 1;\n  color: rgb(107 114 128 / var(--tw-text-opacity, 1));\n}\n.text-green-500{\n  --tw-text-opacity: 1;\n  color: rgb(34 197 94 / var(--tw-text-opacity, 1));\n}\n.text-green-600{\n  --tw-text-opacity: 1;\n  color: rgb(22 163 74 / var(--tw-text-opacity, 1));\n}\n.text-muted-foreground{\n  color: var(--muted-foreground);\n}\n.text-popover-foreground{\n  color: var(--popover-foreground);\n}\n.text-primary{\n  color: var(--primary);\n}\n.text-primary-foreground{\n  color: var(--primary-foreground);\n}\n.text-purple-500{\n  --tw-text-opacity: 1;\n  color: rgb(168 85 247 / var(--tw-text-opacity, 1));\n}\n.text-red-500{\n  --tw-text-opacity: 1;\n  color: rgb(239 68 68 / var(--tw-text-opacity, 1));\n}\n.text-red-600{\n  --tw-text-opacity: 1;\n  color: rgb(220 38 38 / var(--tw-text-opacity, 1));\n}\n.text-red-700{\n  --tw-text-opacity: 1;\n  color: rgb(185 28 28 / var(--tw-text-opacity, 1));\n}\n.text-red-800{\n  --tw-text-opacity: 1;\n  color: rgb(153 27 27 / var(--tw-text-opacity, 1));\n}\n.text-secondary-foreground{\n  color: var(--secondary-foreground);\n}\n.text-sidebar-foreground{\n  color: var(--sidebar-foreground);\n}\n.text-white{\n  --tw-text-opacity: 1;\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\n}\n.text-yellow-500{\n  --tw-text-opacity: 1;\n  color: rgb(234 179 8 / var(--tw-text-opacity, 1));\n}\n.text-\\[\\#555\\]{\n  --tw-text-opacity: 1;\n  color: rgb(85 85 85 / var(--tw-text-opacity, 1));\n}\n.text-black{\n  --tw-text-opacity: 1;\n  color: rgb(0 0 0 / var(--tw-text-opacity, 1));\n}\n.text-\\[var\\(--main\\)\\]{\n  color: var(--main);\n}\n.text-\\[var\\(--footer-text\\)\\]{\n  color: var(--footer-text);\n}\n.underline-offset-4{\n  text-underline-offset: 4px;\n}\n.placeholder-\\[var\\(--paragraph\\)\\]::placeholder{\n  color: var(--paragraph);\n}\n.placeholder-transparent::placeholder{\n  color: transparent;\n}\n.accent-foreground{\n  accent-color: var(--foreground);\n}\n.opacity-0{\n  opacity: 0;\n}\n.opacity-20{\n  opacity: 0.2;\n}\n.opacity-50{\n  opacity: 0.5;\n}\n.opacity-60{\n  opacity: 0.6;\n}\n.opacity-70{\n  opacity: 0.7;\n}\n.opacity-80{\n  opacity: 0.8;\n}\n.opacity-90{\n  opacity: 0.9;\n}\n.shadow{\n  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.shadow-\\[0_0_0_1px_hsl\\(var\\(--sidebar-border\\)\\)\\]{\n  --tw-shadow: 0 0 0 1px hsl(var(--sidebar-border));\n  --tw-shadow-colored: 0 0 0 1px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.shadow-lg{\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.shadow-md{\n  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.shadow-none{\n  --tw-shadow: 0 0 #0000;\n  --tw-shadow-colored: 0 0 #0000;\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.shadow-sm{\n  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);\n  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.shadow-xl{\n  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.outline-none{\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n}\n.outline{\n  outline-style: solid;\n}\n.ring{\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\n.ring-0{\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\n.ring-2{\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\n.ring-\\[var\\(--highlight\\)\\]{\n  --tw-ring-color: var(--highlight);\n}\n.ring-\\[var\\(--link-color\\)\\]{\n  --tw-ring-color: var(--link-color);\n}\n.ring-ring{\n  --tw-ring-color: var(--ring);\n}\n.ring-sidebar-ring{\n  --tw-ring-color: var(--sidebar-ring);\n}\n.ring-opacity-20{\n  --tw-ring-opacity: 0.2;\n}\n.ring-offset-background{\n  --tw-ring-offset-color: var(--background);\n}\n.blur{\n  --tw-blur: blur(8px);\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\n.drop-shadow-\\[0_1px_0_var\\(--border\\)\\]{\n  --tw-drop-shadow: drop-shadow(0 1px 0 var(--border));\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\n.\\!filter{\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow) !important;\n}\n.filter{\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\n.transition{\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n.transition-\\[color\\2c box-shadow\\]{\n  transition-property: color,box-shadow;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n.transition-\\[left\\2c right\\2c width\\]{\n  transition-property: left,right,width;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n.transition-\\[margin\\2c opa\\]{\n  transition-property: margin,opa;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n.transition-\\[width\\2c height\\2c padding\\]{\n  transition-property: width,height,padding;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n.transition-\\[width\\]{\n  transition-property: width;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n.transition-all{\n  transition-property: all;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n.transition-colors{\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n.transition-opacity{\n  transition-property: opacity;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n.transition-transform{\n  transition-property: transform;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n.transition-\\[background\\2c border-color\\]{\n  transition-property: background,border-color;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n.duration-1000{\n  transition-duration: 1000ms;\n}\n.duration-200{\n  transition-duration: 200ms;\n}\n.duration-300{\n  transition-duration: 300ms;\n}\n.duration-500{\n  transition-duration: 500ms;\n}\n.duration-150{\n  transition-duration: 150ms;\n}\n.ease-in{\n  transition-timing-function: cubic-bezier(0.4, 0, 1, 1);\n}\n.ease-in-out{\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n}\n.ease-linear{\n  transition-timing-function: linear;\n}\n.ease-out{\n  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);\n}\n.contain-strict{\n  contain: strict;\n}\n@keyframes enter{\n\n  from{\n    opacity: var(--tw-enter-opacity, 1);\n    transform: translate3d(var(--tw-enter-translate-x, 0), var(--tw-enter-translate-y, 0), 0) scale3d(var(--tw-enter-scale, 1), var(--tw-enter-scale, 1), var(--tw-enter-scale, 1)) rotate(var(--tw-enter-rotate, 0));\n  }\n}\n@keyframes exit{\n\n  to{\n    opacity: var(--tw-exit-opacity, 1);\n    transform: translate3d(var(--tw-exit-translate-x, 0), var(--tw-exit-translate-y, 0), 0) scale3d(var(--tw-exit-scale, 1), var(--tw-exit-scale, 1), var(--tw-exit-scale, 1)) rotate(var(--tw-exit-rotate, 0));\n  }\n}\n.animate-in{\n  animation-name: enter;\n  animation-duration: 150ms;\n  --tw-enter-opacity: initial;\n  --tw-enter-scale: initial;\n  --tw-enter-rotate: initial;\n  --tw-enter-translate-x: initial;\n  --tw-enter-translate-y: initial;\n}\n.fade-in-0{\n  --tw-enter-opacity: 0;\n}\n.fade-in-80{\n  --tw-enter-opacity: 0.8;\n}\n.zoom-in-95{\n  --tw-enter-scale: .95;\n}\n.duration-1000{\n  animation-duration: 1000ms;\n}\n.duration-200{\n  animation-duration: 200ms;\n}\n.duration-300{\n  animation-duration: 300ms;\n}\n.duration-500{\n  animation-duration: 500ms;\n}\n.duration-150{\n  animation-duration: 150ms;\n}\n.ease-in{\n  animation-timing-function: cubic-bezier(0.4, 0, 1, 1);\n}\n.ease-in-out{\n  animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n}\n.ease-linear{\n  animation-timing-function: linear;\n}\n.ease-out{\n  animation-timing-function: cubic-bezier(0, 0, 0.2, 1);\n}\n/* Animation utilities */\n/* Glass effect */\n/* Gradient text */\n/* Subtle hover effect */\n.hover-lift{\n  transition-property: all;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 300ms;\n  animation-duration: 300ms;\n    transform: translateY(0);\n}\n.hover-lift:hover {\n    transform: translateY(-5px);\n    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);\n  }\n/* Text highlight effect */\n/* Animated border */\n@keyframes border-rotate {\n    0% {\n      background-position: 0% center;\n    }\n    100% {\n      background-position: 200% center;\n    }\n  }\n/* Shimmer effect */\n@keyframes shimmer {\n    100% {\n      left: 150%;\n    }\n  }\n/* Glow effect */\n.glow-on-hover {\n    transition: all 0.3s ease;\n  }\n.glow-on-hover:hover {\n    box-shadow: 0 0 15px var(--link-color);\n  }\n@keyframes fade-in-right {\n    from {\n      opacity: 0;\n      transform: translateX(100%);\n    }\n    to {\n      opacity: 1;\n      transform: translateX(0);\n    }\n  }\n@keyframes fade-out-right {\n    from {\n      opacity: 1;\n      transform: translateX(0);\n    }\n    to {\n      opacity: 0;\n      transform: translateX(100%);\n    }\n  }\n@keyframes scale {\n    100% {\n      transform: scale(1);\n    }\n  }\n@keyframes fade-in {\n    100% {\n      opacity: 1;\n      filter: blur(0);\n    }\n  }\n@keyframes moving {\n    50% {\n      width: 100%;\n    }\n    100% {\n      width: 0;\n      right: 0;\n      left: unset;\n    }\n  }\n.\\[--duration\\:40s\\]{\n  --duration: 40s;\n}\n.\\[--gap\\:1rem\\]{\n  --gap: 1rem;\n}\n.\\[animation-delay\\:-0\\.13s\\]{\n  animation-delay: -0.13s;\n}\n.\\[animation-delay\\:-0\\.3s\\]{\n  animation-delay: -0.3s;\n}\n.\\[animation-direction\\:reverse\\]{\n  animation-direction: reverse;\n}\n.\\[gap\\:var\\(--gap\\)\\]{\n  gap: var(--gap);\n}\n.\\[mask-image\\:linear-gradient\\(to_bottom\\2c transparent_0\\%\\2c black_10\\%\\2c black_90\\%\\2c transparent_100\\%\\)\\]{\n  mask-image: linear-gradient(to bottom,transparent 0%,black 10%,black 90%,transparent 100%);\n}\n.\\[translate\\:5\\%_-50\\%\\]{\n  translate: 5% -50%;\n}\n\n.hoverd {\n  transition: all 0.3s ease 0s !important;\n}\n\n.page{\n  min-height: 100dvh;\n  width: 100%;\n}\n\n.test{\n  border-width: 1px;\n  --tw-border-opacity: 1;\n  border-color: rgb(0 0 0 / var(--tw-border-opacity, 1));\n  --tw-bg-opacity: 1;\n  background-color: rgb(134 239 172 / var(--tw-bg-opacity, 1));\n}\n\n.file\\:border-0::file-selector-button{\n  border-width: 0px;\n}\n\n.file\\:bg-transparent::file-selector-button{\n  background-color: transparent;\n}\n\n.file\\:text-sm::file-selector-button{\n  font-size: 0.875rem;\n  line-height: 1.25rem;\n}\n\n.file\\:font-medium::file-selector-button{\n  font-weight: 500;\n}\n\n.placeholder\\:text-\\[var\\(--paragraph\\)\\]::placeholder{\n  color: var(--paragraph);\n}\n\n.after\\:absolute::after{\n  content: var(--tw-content);\n  position: absolute;\n}\n\n.after\\:-inset-2::after{\n  content: var(--tw-content);\n  inset: -0.5rem;\n}\n\n.after\\:inset-y-0::after{\n  content: var(--tw-content);\n  top: 0px;\n  bottom: 0px;\n}\n\n.after\\:left-1\\/2::after{\n  content: var(--tw-content);\n  left: 50%;\n}\n\n.after\\:w-1::after{\n  content: var(--tw-content);\n  width: 0.25rem;\n}\n\n.after\\:w-\\[2px\\]::after{\n  content: var(--tw-content);\n  width: 2px;\n}\n\n.after\\:-translate-x-1\\/2::after{\n  content: var(--tw-content);\n  --tw-translate-x: -50%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.first\\:rounded-l-md:first-child{\n  border-top-left-radius: calc(var(--radius) - 2px);\n  border-bottom-left-radius: calc(var(--radius) - 2px);\n}\n\n.first\\:border-l:first-child{\n  border-left-width: 1px;\n}\n\n.last\\:rounded-r-md:last-child{\n  border-top-right-radius: calc(var(--radius) - 2px);\n  border-bottom-right-radius: calc(var(--radius) - 2px);\n}\n\n.focus-within\\:relative:focus-within{\n  position: relative;\n}\n\n.focus-within\\:z-20:focus-within{\n  z-index: 20;\n}\n\n.focus-within\\:scale-125:focus-within{\n  --tw-scale-x: 1.25;\n  --tw-scale-y: 1.25;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.hover\\:scale-125:hover{\n  --tw-scale-x: 1.25;\n  --tw-scale-y: 1.25;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.hover\\:border-\\[var\\(--link-color\\)\\]:hover{\n  border-color: var(--link-color);\n}\n\n.hover\\:bg-\\[color-mix\\(in_srgb\\2c var\\(--button\\)\\2c \\#00000020\\)\\]:hover{\n  background-color: color-mix(in srgb,var(--button),#00000020);\n}\n\n.hover\\:bg-\\[color-mix\\(in_srgb\\2c var\\(--secondary\\)\\2c \\#00000020\\)\\]:hover{\n  background-color: color-mix(in srgb,var(--secondary),#00000020);\n}\n\n.hover\\:bg-\\[color-mix\\(in_srgb\\2c var\\(--tertiary\\)\\2c \\#00000020\\)\\]:hover{\n  background-color: color-mix(in srgb,var(--tertiary),#00000020);\n}\n\n.hover\\:bg-\\[var\\(--button\\)\\]:hover{\n  background-color: var(--button);\n}\n\n.hover\\:bg-\\[var\\(--button2\\)\\]:hover{\n  background-color: var(--button2);\n}\n\n.hover\\:bg-\\[var\\(--card-hover\\)\\]:hover{\n  background-color: var(--card-hover);\n}\n\n.hover\\:bg-\\[var\\(--highlight\\)\\]:hover{\n  background-color: var(--highlight);\n}\n\n.hover\\:bg-\\[var\\(--link-color\\)\\]:hover{\n  background-color: var(--link-color);\n}\n\n.hover\\:bg-accent:hover{\n  background-color: var(--accent);\n}\n\n.hover\\:bg-muted:hover{\n  background-color: var(--muted);\n}\n\n.hover\\:bg-primary:hover{\n  background-color: var(--primary);\n}\n\n.hover\\:bg-red-700:hover{\n  --tw-bg-opacity: 1;\n  background-color: rgb(185 28 28 / var(--tw-bg-opacity, 1));\n}\n\n.hover\\:bg-secondary:hover{\n  background-color: var(--secondary);\n}\n\n.hover\\:bg-sidebar-accent:hover{\n  background-color: var(--sidebar-accent);\n}\n\n.hover\\:text-\\[var\\(--active-text\\)\\]:hover{\n  color: var(--active-text);\n}\n\n.hover\\:text-\\[var\\(--button-text\\)\\]:hover{\n  color: var(--button-text);\n}\n\n.hover\\:text-\\[var\\(--highlight\\)\\]:hover{\n  color: var(--highlight);\n}\n\n.hover\\:text-\\[var\\(--link-hover\\)\\]:hover{\n  color: var(--link-hover);\n}\n\n.hover\\:text-\\[var\\(--paragraph\\)\\]:hover{\n  color: var(--paragraph);\n}\n\n.hover\\:text-accent-foreground:hover{\n  color: var(--accent-foreground);\n}\n\n.hover\\:text-foreground:hover{\n  color: var(--foreground);\n}\n\n.hover\\:text-primary-foreground:hover{\n  color: var(--primary-foreground);\n}\n\n.hover\\:text-red-700:hover{\n  --tw-text-opacity: 1;\n  color: rgb(185 28 28 / var(--tw-text-opacity, 1));\n}\n\n.hover\\:text-sidebar-accent-foreground:hover{\n  color: var(--sidebar-accent-foreground);\n}\n\n.hover\\:text-white:hover{\n  --tw-text-opacity: 1;\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\n}\n\n.hover\\:underline:hover{\n  text-decoration-line: underline;\n}\n\n.hover\\:opacity-100:hover{\n  opacity: 1;\n}\n\n.hover\\:shadow-\\[0_0_0_1px_hsl\\(var\\(--sidebar-accent\\)\\)\\]:hover{\n  --tw-shadow: 0 0 0 1px hsl(var(--sidebar-accent));\n  --tw-shadow-colored: 0 0 0 1px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n\n.hover\\:after\\:bg-sidebar-border:hover::after{\n  content: var(--tw-content);\n  background-color: var(--sidebar-border);\n}\n\n.focus\\:border-\\[var\\(--link-color\\)\\]:focus{\n  border-color: var(--link-color);\n}\n\n.focus\\:border-\\[var\\(--selectBox-border\\)\\]:focus{\n  border-color: var(--selectBox-border);\n}\n\n.focus\\:bg-\\[var\\(--button\\)\\]:focus{\n  background-color: var(--button);\n}\n\n.focus\\:bg-accent:focus{\n  background-color: var(--accent);\n}\n\n.focus\\:bg-primary:focus{\n  background-color: var(--primary);\n}\n\n.focus\\:text-\\[var\\(--button-text\\)\\]:focus{\n  color: var(--button-text);\n}\n\n.focus\\:text-accent-foreground:focus{\n  color: var(--accent-foreground);\n}\n\n.focus\\:text-primary-foreground:focus{\n  color: var(--primary-foreground);\n}\n\n.focus\\:opacity-100:focus{\n  opacity: 1;\n}\n\n.focus\\:outline-none:focus{\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n}\n\n.focus\\:ring-2:focus{\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\n\n.focus\\:ring-\\[var\\(--link-color\\)\\]:focus{\n  --tw-ring-color: var(--link-color);\n}\n\n.focus\\:ring-blue-500:focus{\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1));\n}\n\n.focus\\:ring-ring:focus{\n  --tw-ring-color: var(--ring);\n}\n\n.focus\\:ring-offset-2:focus{\n  --tw-ring-offset-width: 2px;\n}\n\n.focus-visible\\:border-\\[var\\(--highlight\\)\\]:focus-visible{\n  border-color: var(--highlight);\n}\n\n.focus-visible\\:outline-none:focus-visible{\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n}\n\n.focus-visible\\:ring-1:focus-visible{\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\n\n.focus-visible\\:ring-2:focus-visible{\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\n\n.focus-visible\\:ring-\\[3px\\]:focus-visible{\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\n\n.focus-visible\\:ring-\\[var\\(--button\\)\\]:focus-visible{\n  --tw-ring-color: var(--button);\n}\n\n.focus-visible\\:ring-ring:focus-visible{\n  --tw-ring-color: var(--ring);\n}\n\n.focus-visible\\:ring-sidebar-ring:focus-visible{\n  --tw-ring-color: var(--sidebar-ring);\n}\n\n.focus-visible\\:ring-offset-1:focus-visible{\n  --tw-ring-offset-width: 1px;\n}\n\n.focus-visible\\:ring-offset-2:focus-visible{\n  --tw-ring-offset-width: 2px;\n}\n\n.focus-visible\\:ring-offset-background:focus-visible{\n  --tw-ring-offset-color: var(--background);\n}\n\n.active\\:bg-sidebar-accent:active{\n  background-color: var(--sidebar-accent);\n}\n\n.active\\:text-sidebar-accent-foreground:active{\n  color: var(--sidebar-accent-foreground);\n}\n\n.disabled\\:pointer-events-none:disabled{\n  pointer-events: none;\n}\n\n.disabled\\:cursor-not-allowed:disabled{\n  cursor: not-allowed;\n}\n\n.disabled\\:opacity-50:disabled{\n  opacity: 0.5;\n}\n\n.group\\/menu-item:focus-within .group-focus-within\\/menu-item\\:opacity-100{\n  opacity: 1;\n}\n\n.group\\/menu-item:hover .group-hover\\/menu-item\\:opacity-100{\n  opacity: 1;\n}\n\n.group:hover .group-hover\\:opacity-100{\n  opacity: 1;\n}\n\n.group:hover .group-hover\\:\\[animation-play-state\\:paused\\]{\n  animation-play-state: paused;\n}\n\n.group.toaster .group-\\[\\.toaster\\]\\:border-border{\n  border-color: var(--border);\n}\n\n.group.toast .group-\\[\\.toast\\]\\:bg-muted{\n  background-color: var(--muted);\n}\n\n.group.toast .group-\\[\\.toast\\]\\:bg-primary{\n  background-color: var(--primary);\n}\n\n.group.destructive .group-\\[\\.destructive\\]\\:text-red-300{\n  --tw-text-opacity: 1;\n  color: rgb(252 165 165 / var(--tw-text-opacity, 1));\n}\n\n.group.toast .group-\\[\\.toast\\]\\:text-\\[var\\(--paragraph\\)\\]{\n  color: var(--paragraph);\n}\n\n.group.toast .group-\\[\\.toast\\]\\:text-primary-foreground{\n  color: var(--primary-foreground);\n}\n\n.group.toaster .group-\\[\\.toaster\\]\\:shadow-lg{\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n\n.group.destructive .group-\\[\\.destructive\\]\\:hover\\:bg-destructive:hover{\n  background-color: var(--destructive);\n}\n\n.group.destructive .group-\\[\\.destructive\\]\\:hover\\:text-destructive-foreground:hover{\n  color: var(--destructive-foreground);\n}\n\n.group.destructive .group-\\[\\.destructive\\]\\:hover\\:text-red-50:hover{\n  --tw-text-opacity: 1;\n  color: rgb(254 242 242 / var(--tw-text-opacity, 1));\n}\n\n.group.destructive .group-\\[\\.destructive\\]\\:focus\\:ring-destructive:focus{\n  --tw-ring-color: var(--destructive);\n}\n\n.group.destructive .group-\\[\\.destructive\\]\\:focus\\:ring-red-400:focus{\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(248 113 113 / var(--tw-ring-opacity, 1));\n}\n\n.group.destructive .group-\\[\\.destructive\\]\\:focus\\:ring-offset-red-600:focus{\n  --tw-ring-offset-color: #dc2626;\n}\n\n.peer\\/menu-button:hover ~ .peer-hover\\/menu-button\\:text-sidebar-accent-foreground{\n  color: var(--sidebar-accent-foreground);\n}\n\n.peer:disabled ~ .peer-disabled\\:cursor-not-allowed{\n  cursor: not-allowed;\n}\n\n.peer:disabled ~ .peer-disabled\\:opacity-70{\n  opacity: 0.7;\n}\n\n.has-\\[\\[data-variant\\=inset\\]\\]\\:bg-sidebar:has([data-variant=inset]){\n  background-color: var(--sidebar-background);\n}\n\n.has-\\[\\:disabled\\]\\:opacity-50:has(:disabled){\n  opacity: 0.5;\n}\n\n.group\\/menu-item:has([data-sidebar=menu-action]) .group-has-\\[\\[data-sidebar\\=menu-action\\]\\]\\/menu-item\\:pr-8{\n  padding-right: 2rem;\n}\n\n.aria-disabled\\:pointer-events-none[aria-disabled=\"true\"]{\n  pointer-events: none;\n}\n\n.aria-disabled\\:opacity-50[aria-disabled=\"true\"]{\n  opacity: 0.5;\n}\n\n.aria-selected\\:bg-accent[aria-selected=\"true\"]{\n  background-color: var(--accent);\n}\n\n.aria-selected\\:text-\\[var\\(--paragraph\\)\\][aria-selected=\"true\"]{\n  color: var(--paragraph);\n}\n\n.aria-selected\\:text-accent-foreground[aria-selected=\"true\"]{\n  color: var(--accent-foreground);\n}\n\n.aria-selected\\:opacity-100[aria-selected=\"true\"]{\n  opacity: 1;\n}\n\n.data-\\[disabled\\=true\\]\\:pointer-events-none[data-disabled=\"true\"]{\n  pointer-events: none;\n}\n\n.data-\\[disabled\\]\\:pointer-events-none[data-disabled]{\n  pointer-events: none;\n}\n\n.data-\\[panel-group-direction\\=vertical\\]\\:h-px[data-panel-group-direction=\"vertical\"]{\n  height: 1px;\n}\n\n.data-\\[panel-group-direction\\=vertical\\]\\:w-full[data-panel-group-direction=\"vertical\"]{\n  width: 100%;\n}\n\n.data-\\[side\\=bottom\\]\\:translate-y-1[data-side=\"bottom\"]{\n  --tw-translate-y: 0.25rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.data-\\[side\\=left\\]\\:-translate-x-1[data-side=\"left\"]{\n  --tw-translate-x: -0.25rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.data-\\[side\\=right\\]\\:translate-x-1[data-side=\"right\"]{\n  --tw-translate-x: 0.25rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.data-\\[side\\=top\\]\\:-translate-y-1[data-side=\"top\"]{\n  --tw-translate-y: -0.25rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.data-\\[state\\=checked\\]\\:translate-x-5[data-state=\"checked\"]{\n  --tw-translate-x: 1.25rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.data-\\[state\\=unchecked\\]\\:translate-x-0[data-state=\"unchecked\"]{\n  --tw-translate-x: 0px;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.data-\\[swipe\\=cancel\\]\\:translate-x-0[data-swipe=\"cancel\"]{\n  --tw-translate-x: 0px;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.data-\\[swipe\\=end\\]\\:translate-x-\\[var\\(--radix-toast-swipe-end-x\\)\\][data-swipe=\"end\"]{\n  --tw-translate-x: var(--radix-toast-swipe-end-x);\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.data-\\[swipe\\=move\\]\\:translate-x-\\[var\\(--radix-toast-swipe-move-x\\)\\][data-swipe=\"move\"]{\n  --tw-translate-x: var(--radix-toast-swipe-move-x);\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.data-\\[panel-group-direction\\=vertical\\]\\:flex-col[data-panel-group-direction=\"vertical\"]{\n  flex-direction: column;\n}\n\n.data-\\[state\\=active\\]\\:rounded-\\[12px\\][data-state=\"active\"]{\n  border-radius: 12px;\n}\n\n.data-\\[active\\=true\\]\\:bg-sidebar-accent[data-active=\"true\"]{\n  background-color: var(--sidebar-accent);\n}\n\n.data-\\[active\\]\\:bg-\\[var\\(--button\\)\\][data-active]{\n  background-color: var(--button);\n}\n\n.data-\\[selected\\=\\'true\\'\\]\\:bg-accent[data-selected='true']{\n  background-color: var(--accent);\n}\n\n.data-\\[state\\=active\\]\\:bg-\\[var\\(--highlight\\)\\][data-state=\"active\"]{\n  background-color: var(--highlight);\n}\n\n.data-\\[state\\=checked\\]\\:bg-\\[var\\(--illustration-stroke\\)\\][data-state=\"checked\"]{\n  background-color: var(--illustration-stroke);\n}\n\n.data-\\[state\\=checked\\]\\:bg-primary[data-state=\"checked\"]{\n  background-color: var(--primary);\n}\n\n.data-\\[state\\=on\\]\\:bg-accent[data-state=\"on\"]{\n  background-color: var(--accent);\n}\n\n.data-\\[state\\=open\\]\\:bg-\\[var\\(--button2\\)\\][data-state=\"open\"]{\n  background-color: var(--button2);\n}\n\n.data-\\[state\\=open\\]\\:bg-accent[data-state=\"open\"]{\n  background-color: var(--accent);\n}\n\n.data-\\[state\\=open\\]\\:bg-secondary[data-state=\"open\"]{\n  background-color: var(--secondary);\n}\n\n.data-\\[state\\=selected\\]\\:bg-muted[data-state=\"selected\"]{\n  background-color: var(--muted);\n}\n\n.data-\\[state\\=unchecked\\]\\:bg-\\[var\\(--card-background\\)\\][data-state=\"unchecked\"]{\n  background-color: var(--card-background);\n}\n\n.data-\\[active\\=true\\]\\:font-medium[data-active=\"true\"]{\n  font-weight: 500;\n}\n\n.data-\\[active\\=true\\]\\:text-sidebar-accent-foreground[data-active=\"true\"]{\n  color: var(--sidebar-accent-foreground);\n}\n\n.data-\\[active\\]\\:text-\\[var\\(--button-text\\)\\][data-active]{\n  color: var(--button-text);\n}\n\n.data-\\[selected\\=true\\]\\:text-accent-foreground[data-selected=\"true\"]{\n  color: var(--accent-foreground);\n}\n\n.data-\\[state\\=active\\]\\:text-\\[var\\(--headline\\)\\][data-state=\"active\"]{\n  color: var(--headline);\n}\n\n.data-\\[state\\=checked\\]\\:text-primary-foreground[data-state=\"checked\"]{\n  color: var(--primary-foreground);\n}\n\n.data-\\[state\\=on\\]\\:text-accent-foreground[data-state=\"on\"]{\n  color: var(--accent-foreground);\n}\n\n.data-\\[state\\=open\\]\\:text-\\[var\\(--button-text\\)\\][data-state=\"open\"]{\n  color: var(--button-text);\n}\n\n.data-\\[state\\=open\\]\\:text-\\[var\\(--paragraph\\)\\][data-state=\"open\"]{\n  color: var(--paragraph);\n}\n\n.data-\\[state\\=open\\]\\:text-accent-foreground[data-state=\"open\"]{\n  color: var(--accent-foreground);\n}\n\n.data-\\[disabled\\=true\\]\\:opacity-50[data-disabled=\"true\"]{\n  opacity: 0.5;\n}\n\n.data-\\[disabled\\]\\:opacity-50[data-disabled]{\n  opacity: 0.5;\n}\n\n.data-\\[state\\=open\\]\\:opacity-100[data-state=\"open\"]{\n  opacity: 1;\n}\n\n.data-\\[state\\=active\\]\\:shadow-sm[data-state=\"active\"]{\n  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);\n  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n\n.data-\\[swipe\\=move\\]\\:transition-none[data-swipe=\"move\"]{\n  transition-property: none;\n}\n\n.data-\\[state\\=closed\\]\\:duration-300[data-state=\"closed\"]{\n  transition-duration: 300ms;\n}\n\n.data-\\[state\\=open\\]\\:duration-500[data-state=\"open\"]{\n  transition-duration: 500ms;\n}\n\n.data-\\[motion\\^\\=from-\\]\\:animate-in[data-motion^=\"from-\"]{\n  animation-name: enter;\n  animation-duration: 150ms;\n  --tw-enter-opacity: initial;\n  --tw-enter-scale: initial;\n  --tw-enter-rotate: initial;\n  --tw-enter-translate-x: initial;\n  --tw-enter-translate-y: initial;\n}\n\n.data-\\[state\\=open\\]\\:animate-in[data-state=\"open\"]{\n  animation-name: enter;\n  animation-duration: 150ms;\n  --tw-enter-opacity: initial;\n  --tw-enter-scale: initial;\n  --tw-enter-rotate: initial;\n  --tw-enter-translate-x: initial;\n  --tw-enter-translate-y: initial;\n}\n\n.data-\\[state\\=visible\\]\\:animate-in[data-state=\"visible\"]{\n  animation-name: enter;\n  animation-duration: 150ms;\n  --tw-enter-opacity: initial;\n  --tw-enter-scale: initial;\n  --tw-enter-rotate: initial;\n  --tw-enter-translate-x: initial;\n  --tw-enter-translate-y: initial;\n}\n\n.data-\\[motion\\^\\=to-\\]\\:animate-out[data-motion^=\"to-\"]{\n  animation-name: exit;\n  animation-duration: 150ms;\n  --tw-exit-opacity: initial;\n  --tw-exit-scale: initial;\n  --tw-exit-rotate: initial;\n  --tw-exit-translate-x: initial;\n  --tw-exit-translate-y: initial;\n}\n\n.data-\\[state\\=closed\\]\\:animate-out[data-state=\"closed\"]{\n  animation-name: exit;\n  animation-duration: 150ms;\n  --tw-exit-opacity: initial;\n  --tw-exit-scale: initial;\n  --tw-exit-rotate: initial;\n  --tw-exit-translate-x: initial;\n  --tw-exit-translate-y: initial;\n}\n\n.data-\\[state\\=hidden\\]\\:animate-out[data-state=\"hidden\"]{\n  animation-name: exit;\n  animation-duration: 150ms;\n  --tw-exit-opacity: initial;\n  --tw-exit-scale: initial;\n  --tw-exit-rotate: initial;\n  --tw-exit-translate-x: initial;\n  --tw-exit-translate-y: initial;\n}\n\n.data-\\[swipe\\=end\\]\\:animate-out[data-swipe=\"end\"]{\n  animation-name: exit;\n  animation-duration: 150ms;\n  --tw-exit-opacity: initial;\n  --tw-exit-scale: initial;\n  --tw-exit-rotate: initial;\n  --tw-exit-translate-x: initial;\n  --tw-exit-translate-y: initial;\n}\n\n.data-\\[motion\\^\\=from-\\]\\:fade-in[data-motion^=\"from-\"]{\n  --tw-enter-opacity: 0;\n}\n\n.data-\\[motion\\^\\=to-\\]\\:fade-out[data-motion^=\"to-\"]{\n  --tw-exit-opacity: 0;\n}\n\n.data-\\[state\\=closed\\]\\:fade-out-0[data-state=\"closed\"]{\n  --tw-exit-opacity: 0;\n}\n\n.data-\\[state\\=closed\\]\\:fade-out-80[data-state=\"closed\"]{\n  --tw-exit-opacity: 0.8;\n}\n\n.data-\\[state\\=hidden\\]\\:fade-out[data-state=\"hidden\"]{\n  --tw-exit-opacity: 0;\n}\n\n.data-\\[state\\=open\\]\\:fade-in-0[data-state=\"open\"]{\n  --tw-enter-opacity: 0;\n}\n\n.data-\\[state\\=visible\\]\\:fade-in[data-state=\"visible\"]{\n  --tw-enter-opacity: 0;\n}\n\n.data-\\[state\\=closed\\]\\:zoom-out-95[data-state=\"closed\"]{\n  --tw-exit-scale: .95;\n}\n\n.data-\\[state\\=open\\]\\:zoom-in-90[data-state=\"open\"]{\n  --tw-enter-scale: .9;\n}\n\n.data-\\[state\\=open\\]\\:zoom-in-95[data-state=\"open\"]{\n  --tw-enter-scale: .95;\n}\n\n.data-\\[motion\\=from-end\\]\\:slide-in-from-right-52[data-motion=\"from-end\"]{\n  --tw-enter-translate-x: 13rem;\n}\n\n.data-\\[motion\\=from-start\\]\\:slide-in-from-left-52[data-motion=\"from-start\"]{\n  --tw-enter-translate-x: -13rem;\n}\n\n.data-\\[motion\\=to-end\\]\\:slide-out-to-right-52[data-motion=\"to-end\"]{\n  --tw-exit-translate-x: 13rem;\n}\n\n.data-\\[motion\\=to-start\\]\\:slide-out-to-left-52[data-motion=\"to-start\"]{\n  --tw-exit-translate-x: -13rem;\n}\n\n.data-\\[side\\=bottom\\]\\:slide-in-from-top-2[data-side=\"bottom\"]{\n  --tw-enter-translate-y: -0.5rem;\n}\n\n.data-\\[side\\=left\\]\\:slide-in-from-right-2[data-side=\"left\"]{\n  --tw-enter-translate-x: 0.5rem;\n}\n\n.data-\\[side\\=right\\]\\:slide-in-from-left-2[data-side=\"right\"]{\n  --tw-enter-translate-x: -0.5rem;\n}\n\n.data-\\[side\\=top\\]\\:slide-in-from-bottom-2[data-side=\"top\"]{\n  --tw-enter-translate-y: 0.5rem;\n}\n\n.data-\\[state\\=closed\\]\\:slide-out-to-bottom[data-state=\"closed\"]{\n  --tw-exit-translate-y: 100%;\n}\n\n.data-\\[state\\=closed\\]\\:slide-out-to-left[data-state=\"closed\"]{\n  --tw-exit-translate-x: -100%;\n}\n\n.data-\\[state\\=closed\\]\\:slide-out-to-left-1\\/2[data-state=\"closed\"]{\n  --tw-exit-translate-x: -50%;\n}\n\n.data-\\[state\\=closed\\]\\:slide-out-to-right[data-state=\"closed\"]{\n  --tw-exit-translate-x: 100%;\n}\n\n.data-\\[state\\=closed\\]\\:slide-out-to-right-full[data-state=\"closed\"]{\n  --tw-exit-translate-x: 100%;\n}\n\n.data-\\[state\\=closed\\]\\:slide-out-to-top[data-state=\"closed\"]{\n  --tw-exit-translate-y: -100%;\n}\n\n.data-\\[state\\=closed\\]\\:slide-out-to-top-\\[48\\%\\][data-state=\"closed\"]{\n  --tw-exit-translate-y: -48%;\n}\n\n.data-\\[state\\=open\\]\\:slide-in-from-bottom[data-state=\"open\"]{\n  --tw-enter-translate-y: 100%;\n}\n\n.data-\\[state\\=open\\]\\:slide-in-from-left[data-state=\"open\"]{\n  --tw-enter-translate-x: -100%;\n}\n\n.data-\\[state\\=open\\]\\:slide-in-from-left-1\\/2[data-state=\"open\"]{\n  --tw-enter-translate-x: -50%;\n}\n\n.data-\\[state\\=open\\]\\:slide-in-from-right[data-state=\"open\"]{\n  --tw-enter-translate-x: 100%;\n}\n\n.data-\\[state\\=open\\]\\:slide-in-from-top[data-state=\"open\"]{\n  --tw-enter-translate-y: -100%;\n}\n\n.data-\\[state\\=open\\]\\:slide-in-from-top-\\[48\\%\\][data-state=\"open\"]{\n  --tw-enter-translate-y: -48%;\n}\n\n.data-\\[state\\=open\\]\\:slide-in-from-top-full[data-state=\"open\"]{\n  --tw-enter-translate-y: -100%;\n}\n\n.data-\\[state\\=closed\\]\\:duration-300[data-state=\"closed\"]{\n  animation-duration: 300ms;\n}\n\n.data-\\[state\\=open\\]\\:duration-500[data-state=\"open\"]{\n  animation-duration: 500ms;\n}\n\n.data-\\[panel-group-direction\\=vertical\\]\\:after\\:left-0[data-panel-group-direction=\"vertical\"]::after{\n  content: var(--tw-content);\n  left: 0px;\n}\n\n.data-\\[panel-group-direction\\=vertical\\]\\:after\\:h-1[data-panel-group-direction=\"vertical\"]::after{\n  content: var(--tw-content);\n  height: 0.25rem;\n}\n\n.data-\\[panel-group-direction\\=vertical\\]\\:after\\:w-full[data-panel-group-direction=\"vertical\"]::after{\n  content: var(--tw-content);\n  width: 100%;\n}\n\n.data-\\[panel-group-direction\\=vertical\\]\\:after\\:-translate-y-1\\/2[data-panel-group-direction=\"vertical\"]::after{\n  content: var(--tw-content);\n  --tw-translate-y: -50%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.data-\\[panel-group-direction\\=vertical\\]\\:after\\:translate-x-0[data-panel-group-direction=\"vertical\"]::after{\n  content: var(--tw-content);\n  --tw-translate-x: 0px;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.data-\\[active\\]\\:hover\\:bg-\\[var\\(--button\\)\\]:hover[data-active]{\n  background-color: var(--button);\n}\n\n.data-\\[state\\=open\\]\\:hover\\:bg-\\[var\\(--button\\)\\]:hover[data-state=\"open\"]{\n  background-color: var(--button);\n}\n\n.data-\\[state\\=open\\]\\:hover\\:bg-sidebar-accent:hover[data-state=\"open\"]{\n  background-color: var(--sidebar-accent);\n}\n\n.data-\\[state\\=open\\]\\:hover\\:text-sidebar-accent-foreground:hover[data-state=\"open\"]{\n  color: var(--sidebar-accent-foreground);\n}\n\n.data-\\[active\\]\\:focus\\:bg-\\[var\\(--button\\)\\]:focus[data-active]{\n  background-color: var(--button);\n}\n\n.group[data-collapsible=\"offcanvas\"] .group-data-\\[collapsible\\=offcanvas\\]\\:left-\\[calc\\(var\\(--sidebar-width\\)\\*-1\\)\\]{\n  left: calc(var(--sidebar-width) * -1);\n}\n\n.group[data-collapsible=\"offcanvas\"] .group-data-\\[collapsible\\=offcanvas\\]\\:right-\\[calc\\(var\\(--sidebar-width\\)\\*-1\\)\\]{\n  right: calc(var(--sidebar-width) * -1);\n}\n\n.group[data-side=\"left\"] .group-data-\\[side\\=left\\]\\:-right-4{\n  right: -1rem;\n}\n\n.group[data-side=\"right\"] .group-data-\\[side\\=right\\]\\:left-0{\n  left: 0px;\n}\n\n.group\\/navigation-menu[data-viewport=\"false\"] .group-data-\\[viewport\\=false\\]\\/navigation-menu\\:top-full{\n  top: 100%;\n}\n\n.group[data-collapsible=\"icon\"] .group-data-\\[collapsible\\=icon\\]\\:-mt-8{\n  margin-top: -2rem;\n}\n\n.group\\/navigation-menu[data-viewport=\"false\"] .group-data-\\[viewport\\=false\\]\\/navigation-menu\\:mt-1\\.5{\n  margin-top: 0.375rem;\n}\n\n.group[data-collapsible=\"icon\"] .group-data-\\[collapsible\\=icon\\]\\:hidden{\n  display: none;\n}\n\n.group[data-collapsible=\"icon\"] .group-data-\\[collapsible\\=icon\\]\\:\\!size-8{\n  width: 2rem !important;\n  height: 2rem !important;\n}\n\n.group[data-collapsible=\"icon\"] .group-data-\\[collapsible\\=icon\\]\\:w-\\[--sidebar-width-icon\\]{\n  width: var(--sidebar-width-icon);\n}\n\n.group[data-collapsible=\"icon\"] .group-data-\\[collapsible\\=icon\\]\\:w-\\[calc\\(var\\(--sidebar-width-icon\\)_\\+_theme\\(spacing\\.4\\)\\)\\]{\n  width: calc(var(--sidebar-width-icon) + 1rem);\n}\n\n.group[data-collapsible=\"icon\"] .group-data-\\[collapsible\\=icon\\]\\:w-\\[calc\\(var\\(--sidebar-width-icon\\)_\\+_theme\\(spacing\\.4\\)_\\+2px\\)\\]{\n  width: calc(var(--sidebar-width-icon) + 1rem + 2px);\n}\n\n.group[data-collapsible=\"offcanvas\"] .group-data-\\[collapsible\\=offcanvas\\]\\:w-0{\n  width: 0px;\n}\n\n.group[data-collapsible=\"offcanvas\"] .group-data-\\[collapsible\\=offcanvas\\]\\:translate-x-0{\n  --tw-translate-x: 0px;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.group[data-side=\"right\"] .group-data-\\[side\\=right\\]\\:rotate-180{\n  --tw-rotate: 180deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.group[data-state=\"open\"] .group-data-\\[state\\=open\\]\\:rotate-180{\n  --tw-rotate: 180deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.group[data-collapsible=\"icon\"] .group-data-\\[collapsible\\=icon\\]\\:overflow-hidden{\n  overflow: hidden;\n}\n\n.group\\/navigation-menu[data-viewport=\"false\"] .group-data-\\[viewport\\=false\\]\\/navigation-menu\\:overflow-hidden{\n  overflow: hidden;\n}\n\n.group[data-variant=\"floating\"] .group-data-\\[variant\\=floating\\]\\:rounded-\\[12px\\]{\n  border-radius: 12px;\n}\n\n.group\\/navigation-menu[data-viewport=\"false\"] .group-data-\\[viewport\\=false\\]\\/navigation-menu\\:rounded-md{\n  border-radius: calc(var(--radius) - 2px);\n}\n\n.group[data-variant=\"floating\"] .group-data-\\[variant\\=floating\\]\\:border{\n  border-width: 1px;\n}\n\n.group\\/navigation-menu[data-viewport=\"false\"] .group-data-\\[viewport\\=false\\]\\/navigation-menu\\:border{\n  border-width: 1px;\n}\n\n.group[data-side=\"left\"] .group-data-\\[side\\=left\\]\\:border-r{\n  border-right-width: 1px;\n}\n\n.group[data-side=\"right\"] .group-data-\\[side\\=right\\]\\:border-l{\n  border-left-width: 1px;\n}\n\n.group[data-variant=\"floating\"] .group-data-\\[variant\\=floating\\]\\:border-sidebar-border{\n  border-color: var(--sidebar-border);\n}\n\n.group\\/navigation-menu[data-viewport=\"false\"] .group-data-\\[viewport\\=false\\]\\/navigation-menu\\:border-\\[var\\(--card-border-color\\)\\]{\n  border-color: var(--card-border-color);\n}\n\n.group\\/navigation-menu[data-viewport=\"false\"] .group-data-\\[viewport\\=false\\]\\/navigation-menu\\:bg-\\[var\\(--card-background\\)\\]{\n  background-color: var(--card-background);\n}\n\n.group[data-collapsible=\"icon\"] .group-data-\\[collapsible\\=icon\\]\\:\\!p-0{\n  padding: 0px !important;\n}\n\n.group[data-collapsible=\"icon\"] .group-data-\\[collapsible\\=icon\\]\\:\\!p-2{\n  padding: 0.5rem !important;\n}\n\n.group\\/navigation-menu[data-viewport=\"false\"] .group-data-\\[viewport\\=false\\]\\/navigation-menu\\:text-\\[var\\(--headline\\)\\]{\n  color: var(--headline);\n}\n\n.group[data-collapsible=\"icon\"] .group-data-\\[collapsible\\=icon\\]\\:opacity-0{\n  opacity: 0;\n}\n\n.group[data-variant=\"floating\"] .group-data-\\[variant\\=floating\\]\\:shadow{\n  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n\n.group\\/navigation-menu[data-viewport=\"false\"] .group-data-\\[viewport\\=false\\]\\/navigation-menu\\:shadow{\n  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n\n.group\\/navigation-menu[data-viewport=\"false\"] .group-data-\\[viewport\\=false\\]\\/navigation-menu\\:duration-200{\n  transition-duration: 200ms;\n  animation-duration: 200ms;\n}\n\n.group[data-collapsible=\"offcanvas\"] .group-data-\\[collapsible\\=offcanvas\\]\\:after\\:left-full::after{\n  content: var(--tw-content);\n  left: 100%;\n}\n\n.group[data-collapsible=\"offcanvas\"] .group-data-\\[collapsible\\=offcanvas\\]\\:hover\\:bg-sidebar:hover{\n  background-color: var(--sidebar-background);\n}\n\n.group\\/navigation-menu[data-viewport=\"false\"] .group-data-\\[viewport\\=false\\]\\/navigation-menu\\:data-\\[state\\=open\\]\\:animate-in[data-state=\"open\"]{\n  animation-name: enter;\n  animation-duration: 150ms;\n  --tw-enter-opacity: initial;\n  --tw-enter-scale: initial;\n  --tw-enter-rotate: initial;\n  --tw-enter-translate-x: initial;\n  --tw-enter-translate-y: initial;\n}\n\n.group\\/navigation-menu[data-viewport=\"false\"] .group-data-\\[viewport\\=false\\]\\/navigation-menu\\:data-\\[state\\=closed\\]\\:animate-out[data-state=\"closed\"]{\n  animation-name: exit;\n  animation-duration: 150ms;\n  --tw-exit-opacity: initial;\n  --tw-exit-scale: initial;\n  --tw-exit-rotate: initial;\n  --tw-exit-translate-x: initial;\n  --tw-exit-translate-y: initial;\n}\n\n.group\\/navigation-menu[data-viewport=\"false\"] .group-data-\\[viewport\\=false\\]\\/navigation-menu\\:data-\\[state\\=closed\\]\\:fade-out-0[data-state=\"closed\"]{\n  --tw-exit-opacity: 0;\n}\n\n.group\\/navigation-menu[data-viewport=\"false\"] .group-data-\\[viewport\\=false\\]\\/navigation-menu\\:data-\\[state\\=open\\]\\:fade-in-0[data-state=\"open\"]{\n  --tw-enter-opacity: 0;\n}\n\n.group\\/navigation-menu[data-viewport=\"false\"] .group-data-\\[viewport\\=false\\]\\/navigation-menu\\:data-\\[state\\=closed\\]\\:zoom-out-95[data-state=\"closed\"]{\n  --tw-exit-scale: .95;\n}\n\n.group\\/navigation-menu[data-viewport=\"false\"] .group-data-\\[viewport\\=false\\]\\/navigation-menu\\:data-\\[state\\=open\\]\\:zoom-in-95[data-state=\"open\"]{\n  --tw-enter-scale: .95;\n}\n\n.peer\\/menu-button[data-size=\"default\"] ~ .peer-data-\\[size\\=default\\]\\/menu-button\\:top-1\\.5{\n  top: 0.375rem;\n}\n\n.peer\\/menu-button[data-size=\"lg\"] ~ .peer-data-\\[size\\=lg\\]\\/menu-button\\:top-2\\.5{\n  top: 0.625rem;\n}\n\n.peer\\/menu-button[data-size=\"sm\"] ~ .peer-data-\\[size\\=sm\\]\\/menu-button\\:top-1{\n  top: 0.25rem;\n}\n\n.peer[data-variant=\"inset\"] ~ .peer-data-\\[variant\\=inset\\]\\:min-h-\\[calc\\(100svh-theme\\(spacing\\.4\\)\\)\\]{\n  min-height: calc(100svh - 1rem);\n}\n\n.peer\\/menu-button[data-active=\"true\"] ~ .peer-data-\\[active\\=true\\]\\/menu-button\\:text-sidebar-accent-foreground{\n  color: var(--sidebar-accent-foreground);\n}\n\n.dark\\:border-blue-800:is(.dark *){\n  --tw-border-opacity: 1;\n  border-color: rgb(30 64 175 / var(--tw-border-opacity, 1));\n}\n\n.dark\\:border-destructive:is(.dark *){\n  border-color: var(--destructive);\n}\n\n.dark\\:border-gray-700:is(.dark *){\n  --tw-border-opacity: 1;\n  border-color: rgb(55 65 81 / var(--tw-border-opacity, 1));\n}\n\n.dark\\:border-red-800:is(.dark *){\n  --tw-border-opacity: 1;\n  border-color: rgb(153 27 27 / var(--tw-border-opacity, 1));\n}\n\n.dark\\:bg-blue-900\\/20:is(.dark *){\n  background-color: rgb(30 58 138 / 0.2);\n}\n\n.dark\\:bg-blue-950\\/20:is(.dark *){\n  background-color: rgb(23 37 84 / 0.2);\n}\n\n.dark\\:bg-gray-700:is(.dark *){\n  --tw-bg-opacity: 1;\n  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));\n}\n\n.dark\\:bg-gray-800:is(.dark *){\n  --tw-bg-opacity: 1;\n  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));\n}\n\n.dark\\:bg-gray-900\\/50:is(.dark *){\n  background-color: rgb(17 24 39 / 0.5);\n}\n\n.dark\\:bg-green-900\\/20:is(.dark *){\n  background-color: rgb(20 83 45 / 0.2);\n}\n\n.dark\\:bg-red-900\\/20:is(.dark *){\n  background-color: rgb(127 29 29 / 0.2);\n}\n\n.dark\\:bg-red-950\\/20:is(.dark *){\n  background-color: rgb(69 10 10 / 0.2);\n}\n\n.dark\\:text-blue-200:is(.dark *){\n  --tw-text-opacity: 1;\n  color: rgb(191 219 254 / var(--tw-text-opacity, 1));\n}\n\n.dark\\:text-blue-300:is(.dark *){\n  --tw-text-opacity: 1;\n  color: rgb(147 197 253 / var(--tw-text-opacity, 1));\n}\n\n.dark\\:text-red-200:is(.dark *){\n  --tw-text-opacity: 1;\n  color: rgb(254 202 202 / var(--tw-text-opacity, 1));\n}\n\n.dark\\:text-red-300:is(.dark *){\n  --tw-text-opacity: 1;\n  color: rgb(252 165 165 / var(--tw-text-opacity, 1));\n}\n\n@media not all and (min-width: 768px){\n\n  .max-md\\:left-\\[-40px\\]{\n    left: -40px;\n  }\n\n  .max-md\\:left-\\[10px\\]{\n    left: 10px;\n  }\n\n  .max-md\\:z-30{\n    z-index: 30;\n  }\n\n  .max-md\\:m-0{\n    margin: 0px;\n  }\n\n  .max-md\\:mb-0{\n    margin-bottom: 0px;\n  }\n\n  .max-md\\:mt-1{\n    margin-top: 0.25rem;\n  }\n\n  .max-md\\:mt-2{\n    margin-top: 0.5rem;\n  }\n\n  .max-md\\:hidden{\n    display: none;\n  }\n\n  .max-md\\:max-h-\\[150vh\\]{\n    max-height: 150vh;\n  }\n\n  .max-md\\:w-full{\n    width: 100%;\n  }\n\n  .max-md\\:flex-row{\n    flex-direction: row;\n  }\n\n  .max-md\\:flex-col{\n    flex-direction: column;\n  }\n\n  .max-md\\:items-start{\n    align-items: flex-start;\n  }\n\n  .max-md\\:items-end{\n    align-items: flex-end;\n  }\n\n  .max-md\\:p-0{\n    padding: 0px;\n  }\n\n  .max-md\\:px-0{\n    padding-left: 0px;\n    padding-right: 0px;\n  }\n\n  .max-md\\:px-4{\n    padding-left: 1rem;\n    padding-right: 1rem;\n  }\n\n  .max-md\\:ps-0{\n    padding-inline-start: 0px;\n  }\n\n  .max-md\\:ps-\\[30px\\]{\n    padding-inline-start: 30px;\n  }\n\n  .max-md\\:pt-10{\n    padding-top: 2.5rem;\n  }\n\n  .max-md\\:text-xl{\n    font-size: 1.25rem;\n    line-height: 1.75rem;\n  }\n}\n\n@media (min-width: 640px){\n\n  .sm\\:bottom-0{\n    bottom: 0px;\n  }\n\n  .sm\\:right-0{\n    right: 0px;\n  }\n\n  .sm\\:top-auto{\n    top: auto;\n  }\n\n  .sm\\:mt-0{\n    margin-top: 0px;\n  }\n\n  .sm\\:block{\n    display: block;\n  }\n\n  .sm\\:flex{\n    display: flex;\n  }\n\n  .sm\\:max-w-\\[320px\\]{\n    max-width: 320px;\n  }\n\n  .sm\\:max-w-lg{\n    max-width: 32rem;\n  }\n\n  .sm\\:max-w-md{\n    max-width: 28rem;\n  }\n\n  .sm\\:max-w-sm{\n    max-width: 24rem;\n  }\n\n  .sm\\:flex-row{\n    flex-direction: row;\n  }\n\n  .sm\\:flex-col{\n    flex-direction: column;\n  }\n\n  .sm\\:justify-end{\n    justify-content: flex-end;\n  }\n\n  .sm\\:gap-16{\n    gap: 4rem;\n  }\n\n  .sm\\:gap-8{\n    gap: 2rem;\n  }\n\n  .sm\\:space-x-2 > :not([hidden]) ~ :not([hidden]){\n    --tw-space-x-reverse: 0;\n    margin-right: calc(0.5rem * var(--tw-space-x-reverse));\n    margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\n  }\n\n  .sm\\:space-x-4 > :not([hidden]) ~ :not([hidden]){\n    --tw-space-x-reverse: 0;\n    margin-right: calc(1rem * var(--tw-space-x-reverse));\n    margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));\n  }\n\n  .sm\\:space-y-0 > :not([hidden]) ~ :not([hidden]){\n    --tw-space-y-reverse: 0;\n    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));\n    margin-bottom: calc(0px * var(--tw-space-y-reverse));\n  }\n\n  .sm\\:rounded-\\[12px\\]{\n    border-radius: 12px;\n  }\n\n  .sm\\:p-6{\n    padding: 1.5rem;\n  }\n\n  .sm\\:py-24{\n    padding-top: 6rem;\n    padding-bottom: 6rem;\n  }\n\n  .sm\\:text-left{\n    text-align: left;\n  }\n\n  .sm\\:text-5xl{\n    font-size: 3rem;\n    line-height: 1;\n  }\n\n  .sm\\:text-xl{\n    font-size: 1.25rem;\n    line-height: 1.75rem;\n  }\n\n  .sm\\:leading-tight{\n    line-height: 1.25;\n  }\n\n  .data-\\[state\\=open\\]\\:sm\\:slide-in-from-bottom-full[data-state=\"open\"]{\n    --tw-enter-translate-y: 100%;\n  }\n}\n\n@media (min-width: 768px){\n\n  .md\\:absolute{\n    position: absolute;\n  }\n\n  .md\\:left-3{\n    left: 0.75rem;\n  }\n\n  .md\\:left-8{\n    left: 2rem;\n  }\n\n  .md\\:col-span-3{\n    grid-column: span 3 / span 3;\n  }\n\n  .md\\:col-span-6{\n    grid-column: span 6 / span 6;\n  }\n\n  .md\\:block{\n    display: block;\n  }\n\n  .md\\:flex{\n    display: flex;\n  }\n\n  .md\\:hidden{\n    display: none;\n  }\n\n  .md\\:w-\\[var\\(--radix-navigation-menu-viewport-width\\)\\]{\n    width: var(--radix-navigation-menu-viewport-width);\n  }\n\n  .md\\:w-auto{\n    width: auto;\n  }\n\n  .md\\:w-full{\n    width: 100%;\n  }\n\n  .md\\:max-w-\\[420px\\]{\n    max-width: 420px;\n  }\n\n  .md\\:max-w-lg{\n    max-width: 32rem;\n  }\n\n  .md\\:max-w-xl{\n    max-width: 36rem;\n  }\n\n  .md\\:grid-cols-2{\n    grid-template-columns: repeat(2, minmax(0, 1fr));\n  }\n\n  .md\\:grid-cols-3{\n    grid-template-columns: repeat(3, minmax(0, 1fr));\n  }\n\n  .md\\:flex-row{\n    flex-direction: row;\n  }\n\n  .md\\:gap-10{\n    gap: 2.5rem;\n  }\n\n  .md\\:py-32{\n    padding-top: 8rem;\n    padding-bottom: 8rem;\n  }\n\n  .md\\:px-12{\n    padding-left: 3rem;\n    padding-right: 3rem;\n  }\n\n  .md\\:pl-16{\n    padding-left: 4rem;\n  }\n\n  .md\\:pl-4{\n    padding-left: 1rem;\n  }\n\n  .md\\:text-2xl{\n    font-size: 1.5rem;\n    line-height: 2rem;\n  }\n\n  .md\\:opacity-0{\n    opacity: 0;\n  }\n\n  .after\\:md\\:hidden::after{\n    content: var(--tw-content);\n    display: none;\n  }\n\n  .peer[data-variant=\"inset\"] ~ .md\\:peer-data-\\[variant\\=inset\\]\\:m-2{\n    margin: 0.5rem;\n  }\n\n  .peer[data-state=\"collapsed\"][data-variant=\"inset\"] ~ .md\\:peer-data-\\[state\\=collapsed\\]\\:peer-data-\\[variant\\=inset\\]\\:ml-2{\n    margin-left: 0.5rem;\n  }\n\n  .peer[data-variant=\"inset\"] ~ .md\\:peer-data-\\[variant\\=inset\\]\\:ml-0{\n    margin-left: 0px;\n  }\n\n  .peer[data-variant=\"inset\"] ~ .md\\:peer-data-\\[variant\\=inset\\]\\:rounded-xl{\n    border-radius: 0.75rem;\n  }\n\n  .peer[data-variant=\"inset\"] ~ .md\\:peer-data-\\[variant\\=inset\\]\\:shadow{\n    --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);\n    --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);\n    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n  }\n}\n\n@media (min-width: 1024px){\n\n  .lg\\:w-\\[84\\%\\]{\n    width: 84%;\n  }\n\n  .lg\\:max-w-sm{\n    max-width: 24rem;\n  }\n\n  .lg\\:grid-cols-3{\n    grid-template-columns: repeat(3, minmax(0, 1fr));\n  }\n\n  .lg\\:grid-cols-4{\n    grid-template-columns: repeat(4, minmax(0, 1fr));\n  }\n}\n\n.\\[\\&\\:has\\(\\[aria-selected\\]\\)\\]\\:bg-accent:has([aria-selected]){\n  background-color: var(--accent);\n}\n\n.first\\:\\[\\&\\:has\\(\\[aria-selected\\]\\)\\]\\:rounded-l-md:has([aria-selected]):first-child{\n  border-top-left-radius: calc(var(--radius) - 2px);\n  border-bottom-left-radius: calc(var(--radius) - 2px);\n}\n\n.last\\:\\[\\&\\:has\\(\\[aria-selected\\]\\)\\]\\:rounded-r-md:has([aria-selected]):last-child{\n  border-top-right-radius: calc(var(--radius) - 2px);\n  border-bottom-right-radius: calc(var(--radius) - 2px);\n}\n\n.\\[\\&\\:has\\(\\[aria-selected\\]\\.day-range-end\\)\\]\\:rounded-r-md:has([aria-selected].day-range-end){\n  border-top-right-radius: calc(var(--radius) - 2px);\n  border-bottom-right-radius: calc(var(--radius) - 2px);\n}\n\n.\\[\\&\\:has\\(\\[role\\=checkbox\\]\\)\\]\\:pr-0:has([role=checkbox]){\n  padding-right: 0px;\n}\n\n.\\[\\&\\>button\\]\\:hidden>button{\n  display: none;\n}\n\n.\\[\\&\\>span\\:last-child\\]\\:truncate>span:last-child{\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.\\[\\&\\>span\\]\\:line-clamp-1>span{\n  overflow: hidden;\n  display: -webkit-box;\n  -webkit-box-orient: vertical;\n  -webkit-line-clamp: 1;\n}\n\n.\\[\\&\\>svg\\+div\\]\\:translate-y-\\[-3px\\]>svg+div{\n  --tw-translate-y: -3px;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.\\[\\&\\>svg\\]\\:absolute>svg{\n  position: absolute;\n}\n\n.\\[\\&\\>svg\\]\\:left-4>svg{\n  left: 1rem;\n}\n\n.\\[\\&\\>svg\\]\\:top-4>svg{\n  top: 1rem;\n}\n\n.\\[\\&\\>svg\\]\\:size-3\\.5>svg{\n  width: 0.875rem;\n  height: 0.875rem;\n}\n\n.\\[\\&\\>svg\\]\\:size-4>svg{\n  width: 1rem;\n  height: 1rem;\n}\n\n.\\[\\&\\>svg\\]\\:h-2\\.5>svg{\n  height: 0.625rem;\n}\n\n.\\[\\&\\>svg\\]\\:h-3>svg{\n  height: 0.75rem;\n}\n\n.\\[\\&\\>svg\\]\\:w-2\\.5>svg{\n  width: 0.625rem;\n}\n\n.\\[\\&\\>svg\\]\\:w-3>svg{\n  width: 0.75rem;\n}\n\n.\\[\\&\\>svg\\]\\:shrink-0>svg{\n  flex-shrink: 0;\n}\n\n.\\[\\&\\>svg\\]\\:text-\\[var\\(--paragraph\\)\\]>svg{\n  color: var(--paragraph);\n}\n\n.\\[\\&\\>svg\\]\\:text-destructive>svg{\n  color: var(--destructive);\n}\n\n.\\[\\&\\>svg\\]\\:text-foreground>svg{\n  color: var(--foreground);\n}\n\n.\\[\\&\\>svg\\]\\:text-sidebar-accent-foreground>svg{\n  color: var(--sidebar-accent-foreground);\n}\n\n.\\[\\&\\>svg\\~\\*\\]\\:pl-7>svg~*{\n  padding-left: 1.75rem;\n}\n\n.\\[\\&\\>tr\\]\\:last\\:border-b-0:last-child>tr{\n  border-bottom-width: 0px;\n}\n\n.\\[\\&\\[data-panel-group-direction\\=vertical\\]\\>div\\]\\:rotate-90[data-panel-group-direction=vertical]>div{\n  --tw-rotate: 90deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.\\[\\&\\[data-state\\=open\\]\\>svg\\]\\:rotate-180[data-state=open]>svg{\n  --tw-rotate: 180deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.\\[\\&_\\.recharts-cartesian-axis-tick_text\\]\\:fill-muted-foreground .recharts-cartesian-axis-tick text{\n  fill: var(--muted-foreground);\n}\n\n.\\[\\&_\\.recharts-curve\\.recharts-tooltip-cursor\\]\\:stroke-border .recharts-curve.recharts-tooltip-cursor{\n  stroke: var(--border);\n}\n\n.\\[\\&_\\.recharts-dot\\[stroke\\=\\'\\#fff\\'\\]\\]\\:stroke-transparent .recharts-dot[stroke='#fff']{\n  stroke: transparent;\n}\n\n.\\[\\&_\\.recharts-layer\\]\\:outline-none .recharts-layer{\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n}\n\n.\\[\\&_\\.recharts-polar-grid_\\[stroke\\=\\'\\#ccc\\'\\]\\]\\:stroke-border .recharts-polar-grid [stroke='#ccc']{\n  stroke: var(--border);\n}\n\n.\\[\\&_\\.recharts-radial-bar-background-sector\\]\\:fill-muted .recharts-radial-bar-background-sector{\n  fill: var(--muted);\n}\n\n.\\[\\&_\\.recharts-rectangle\\.recharts-tooltip-cursor\\]\\:fill-muted .recharts-rectangle.recharts-tooltip-cursor{\n  fill: var(--muted);\n}\n\n.\\[\\&_\\.recharts-reference-line_\\[stroke\\=\\'\\#ccc\\'\\]\\]\\:stroke-border .recharts-reference-line [stroke='#ccc']{\n  stroke: var(--border);\n}\n\n.\\[\\&_\\.recharts-sector\\[stroke\\=\\'\\#fff\\'\\]\\]\\:stroke-transparent .recharts-sector[stroke='#fff']{\n  stroke: transparent;\n}\n\n.\\[\\&_\\.recharts-sector\\]\\:outline-none .recharts-sector{\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n}\n\n.\\[\\&_\\.recharts-surface\\]\\:outline-none .recharts-surface{\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n}\n\n.\\[\\&_\\[cmdk-group-heading\\]\\]\\:px-2 [cmdk-group-heading]{\n  padding-left: 0.5rem;\n  padding-right: 0.5rem;\n}\n\n.\\[\\&_\\[cmdk-group-heading\\]\\]\\:py-1\\.5 [cmdk-group-heading]{\n  padding-top: 0.375rem;\n  padding-bottom: 0.375rem;\n}\n\n.\\[\\&_\\[cmdk-group-heading\\]\\]\\:text-xs [cmdk-group-heading]{\n  font-size: 0.75rem;\n  line-height: 1rem;\n}\n\n.\\[\\&_\\[cmdk-group-heading\\]\\]\\:font-medium [cmdk-group-heading]{\n  font-weight: 500;\n}\n\n.\\[\\&_\\[cmdk-group-heading\\]\\]\\:text-\\[var\\(--paragraph\\)\\] [cmdk-group-heading]{\n  color: var(--paragraph);\n}\n\n.\\[\\&_\\[cmdk-group\\]\\:not\\(\\[hidden\\]\\)_\\~\\[cmdk-group\\]\\]\\:pt-0 [cmdk-group]:not([hidden]) ~[cmdk-group]{\n  padding-top: 0px;\n}\n\n.\\[\\&_\\[cmdk-group\\]\\]\\:px-2 [cmdk-group]{\n  padding-left: 0.5rem;\n  padding-right: 0.5rem;\n}\n\n.\\[\\&_\\[cmdk-input-wrapper\\]_svg\\]\\:h-5 [cmdk-input-wrapper] svg{\n  height: 1.25rem;\n}\n\n.\\[\\&_\\[cmdk-input-wrapper\\]_svg\\]\\:w-5 [cmdk-input-wrapper] svg{\n  width: 1.25rem;\n}\n\n.\\[\\&_\\[cmdk-input\\]\\]\\:h-12 [cmdk-input]{\n  height: 3rem;\n}\n\n.\\[\\&_\\[cmdk-item\\]\\]\\:px-2 [cmdk-item]{\n  padding-left: 0.5rem;\n  padding-right: 0.5rem;\n}\n\n.\\[\\&_\\[cmdk-item\\]\\]\\:py-3 [cmdk-item]{\n  padding-top: 0.75rem;\n  padding-bottom: 0.75rem;\n}\n\n.\\[\\&_\\[cmdk-item\\]_svg\\]\\:h-5 [cmdk-item] svg{\n  height: 1.25rem;\n}\n\n.\\[\\&_\\[cmdk-item\\]_svg\\]\\:w-5 [cmdk-item] svg{\n  width: 1.25rem;\n}\n\n.\\[\\&_p\\]\\:leading-relaxed p{\n  line-height: 1.625;\n}\n\n.\\[\\&_svg\\:not\\(\\[class\\*\\=\\'size-\\'\\]\\)\\]\\:size-4 svg:not([class*='size-']){\n  width: 1rem;\n  height: 1rem;\n}\n\n.\\[\\&_svg\\:not\\(\\[class\\*\\=\\'size-\\'\\]\\)\\]\\:h-4 svg:not([class*='size-']){\n  height: 1rem;\n}\n\n.\\[\\&_svg\\:not\\(\\[class\\*\\=\\'size-\\'\\]\\)\\]\\:w-4 svg:not([class*='size-']){\n  width: 1rem;\n}\n\n.\\[\\&_svg\\:not\\(\\[class\\*\\=\\'text-\\'\\]\\)\\]\\:text-\\[var\\(--menu-color\\)\\] svg:not([class*='text-']){\n  color: var(--menu-color);\n}\n\n.\\[\\&_svg\\]\\:pointer-events-none svg{\n  pointer-events: none;\n}\n\n.\\[\\&_svg\\]\\:size-4 svg{\n  width: 1rem;\n  height: 1rem;\n}\n\n.\\[\\&_svg\\]\\:shrink-0 svg{\n  flex-shrink: 0;\n}\n\n.\\[\\&_tr\\:last-child\\]\\:border-0 tr:last-child{\n  border-width: 0px;\n}\n\n.\\[\\&_tr\\]\\:border-b tr{\n  border-bottom-width: 1px;\n}\n\n[data-side=left][data-collapsible=offcanvas] .\\[\\[data-side\\=left\\]\\[data-collapsible\\=offcanvas\\]_\\&\\]\\:-right-2{\n  right: -0.5rem;\n}\n\n[data-side=left][data-state=collapsed] .\\[\\[data-side\\=left\\]\\[data-state\\=collapsed\\]_\\&\\]\\:cursor-e-resize{\n  cursor: e-resize;\n}\n\n[data-side=left] .\\[\\[data-side\\=left\\]_\\&\\]\\:cursor-w-resize{\n  cursor: w-resize;\n}\n\n[data-side=right][data-collapsible=offcanvas] .\\[\\[data-side\\=right\\]\\[data-collapsible\\=offcanvas\\]_\\&\\]\\:-left-2{\n  left: -0.5rem;\n}\n\n[data-side=right][data-state=collapsed] .\\[\\[data-side\\=right\\]\\[data-state\\=collapsed\\]_\\&\\]\\:cursor-w-resize{\n  cursor: w-resize;\n}\n\n[data-side=right] .\\[\\[data-side\\=right\\]_\\&\\]\\:cursor-e-resize{\n  cursor: e-resize;\n}\n"], "names": [], "mappings": "AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsDA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+DA;;;;;;;AASA;;;;AAeA;;;;;;;;;;;AAiBA;;;;;AAWA;;;;;;AAUA;;;;AAQA;;;;;AAcA;;;;;AASA;;;;AAYA;;;;;;;AAcA;;;;AAQA;;;;;;;AAQA;;;;AAIA;;;;AAUA;;;;;;AAYA;;;;;;;;;;;;;AAqBA;;;;AAUA;;;;;;AAaA;;;;AAQA;;;;AAQA;;;;AAQA;;;;AAUA;;;;;AASA;;;;AASA;;;;;AASA;;;;AAQA;;;;AAgBA;;;;;AAKA;;;;AAIA;;;;;;AAYA;;;;AAQA;;;;AASA;;;;;AAUA;;;;AASA;;;;AAUA;;;;;AAgBA;;;;;AAQA;;;;AAIA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6CA;;;;;;;;AAQA;;;;;AAKA;;;;AAIA;;;;;;;AAMA;;;;;;;;AAaA;EAEE;;;;;AAIF;EAEE;;;;;AAIF;EAEE;;;;;AAIF;;;;;;;;;AAQA;;;;AAQA;;;;AAGA;;;;AAGA;;;;;;;;;;;;AAWA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;;;;;;;;AAYA;;;;AAGA;;;;;;AAMA;;;;AAGA;;;;;;AAMA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;;;;AASA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAOA;;;;;;;;AAOA;;;;;AAMA;;;;;;;;;;AASA;;;;;;AAMA;;;;AAGA;;;;AAGA;;;;;;;;;;;;AAUA;;;;;;;;;;;;AAUA;;;;;;AAKA;;;;;;;AAMA;;;;;;;;;;;;AAUA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;;;;AAQA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;;;AAMA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;;AAMA;;;;;AAKA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;;;AAMA;;;;;;AAMA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;;;AAMA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;;AAMA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;;;AAMA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;;AAMA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;;;;;;AAUA;;;;;;;;;;AAUA;;;;;;;;;;AAUA;;;;;;;;;;AAUA;;;;;;;;;;AAUA;;;;;;;;;;AAUA;;;;;;;;;;AAUA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;;AAMA;;;;;;AAMA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;;AAMA;;;;;;AAMA;;;;;AAKA;;;;;AAKA;;;;AAIA;;;;;;;;;;AAUA;;;;;;;;;;AAUA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;EAEE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;;AAMF;EAEE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;;EAMA;;;;;;EAMA;;;;;;EAMA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;EAIA;;;;;AAKF;EAEE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;EAIA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;;;AAOF;EAEE;;;;EAIA;;;;EAIA;;;;EAIA;;;;;AAKF;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;;;AAMA;;;;;;;AAOA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA"}}, {"offset": {"line": 5030, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}
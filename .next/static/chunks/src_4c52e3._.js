(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["static/chunks/src_4c52e3._.js", {

"[project]/src/components/ui/FuzzyText.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
var _s = __turbopack_refresh__.signature();
;
const FuzzyText = ({ children, fontSize = "clamp(2rem, 8vw, 8rem)", fontWeight = 900, fontFamily = "inherit", color = "#fff", enableHover = true, baseIntensity = 0.18, hoverIntensity = 0.5 })=>{
    _s();
    const canvasRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "FuzzyText.useEffect": ()=>{
            let animationFrameId;
            let isCancelled = false;
            const canvas = canvasRef.current;
            if (!canvas) return;
            const init = {
                "FuzzyText.useEffect.init": async ()=>{
                    if (document.fonts?.ready) {
                        await document.fonts.ready;
                    }
                    if (isCancelled) return;
                    const ctx = canvas.getContext("2d");
                    if (!ctx) return;
                    const computedFontFamily = fontFamily === "inherit" ? window.getComputedStyle(canvas).fontFamily || "sans-serif" : fontFamily;
                    const fontSizeStr = typeof fontSize === "number" ? `${fontSize}px` : fontSize;
                    let numericFontSize;
                    if (typeof fontSize === "number") {
                        numericFontSize = fontSize;
                    } else {
                        const temp = document.createElement("span");
                        temp.style.fontSize = fontSize;
                        document.body.appendChild(temp);
                        const computedSize = window.getComputedStyle(temp).fontSize;
                        numericFontSize = parseFloat(computedSize);
                        document.body.removeChild(temp);
                    }
                    const text = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Children.toArray(children).join("");
                    const offscreen = document.createElement("canvas");
                    const offCtx = offscreen.getContext("2d");
                    if (!offCtx) return;
                    offCtx.font = `${fontWeight} ${fontSizeStr} ${computedFontFamily}`;
                    offCtx.textBaseline = "alphabetic";
                    const metrics = offCtx.measureText(text);
                    const actualLeft = metrics.actualBoundingBoxLeft ?? 0;
                    const actualRight = metrics.actualBoundingBoxRight ?? metrics.width;
                    const actualAscent = metrics.actualBoundingBoxAscent ?? numericFontSize;
                    const actualDescent = metrics.actualBoundingBoxDescent ?? numericFontSize * 0.2;
                    const textBoundingWidth = Math.ceil(actualLeft + actualRight);
                    const tightHeight = Math.ceil(actualAscent + actualDescent);
                    const extraWidthBuffer = 10;
                    const offscreenWidth = textBoundingWidth + extraWidthBuffer;
                    offscreen.width = offscreenWidth;
                    offscreen.height = tightHeight;
                    const xOffset = extraWidthBuffer / 2;
                    offCtx.font = `${fontWeight} ${fontSizeStr} ${computedFontFamily}`;
                    offCtx.textBaseline = "alphabetic";
                    offCtx.fillStyle = color;
                    offCtx.fillText(text, xOffset - actualLeft, actualAscent);
                    const horizontalMargin = 50;
                    const verticalMargin = 0;
                    canvas.width = offscreenWidth + horizontalMargin * 2;
                    canvas.height = tightHeight + verticalMargin * 2;
                    ctx.translate(horizontalMargin, verticalMargin);
                    const interactiveLeft = horizontalMargin + xOffset;
                    const interactiveTop = verticalMargin;
                    const interactiveRight = interactiveLeft + textBoundingWidth;
                    const interactiveBottom = interactiveTop + tightHeight;
                    let isHovering = false;
                    const fuzzRange = 30;
                    const run = {
                        "FuzzyText.useEffect.init.run": ()=>{
                            if (isCancelled) return;
                            ctx.clearRect(-fuzzRange, -fuzzRange, offscreenWidth + 2 * fuzzRange, tightHeight + 2 * fuzzRange);
                            const intensity = isHovering ? hoverIntensity : baseIntensity;
                            for(let j = 0; j < tightHeight; j++){
                                const dx = Math.floor(intensity * (Math.random() - 0.5) * fuzzRange);
                                ctx.drawImage(offscreen, 0, j, offscreenWidth, 1, dx, j, offscreenWidth, 1);
                            }
                            animationFrameId = window.requestAnimationFrame(run);
                        }
                    }["FuzzyText.useEffect.init.run"];
                    run();
                    const isInsideTextArea = {
                        "FuzzyText.useEffect.init.isInsideTextArea": (x, y)=>x >= interactiveLeft && x <= interactiveRight && y >= interactiveTop && y <= interactiveBottom
                    }["FuzzyText.useEffect.init.isInsideTextArea"];
                    const handleMouseMove = {
                        "FuzzyText.useEffect.init.handleMouseMove": (e)=>{
                            if (!enableHover) return;
                            const rect = canvas.getBoundingClientRect();
                            const x = e.clientX - rect.left;
                            const y = e.clientY - rect.top;
                            isHovering = isInsideTextArea(x, y);
                        }
                    }["FuzzyText.useEffect.init.handleMouseMove"];
                    const handleMouseLeave = {
                        "FuzzyText.useEffect.init.handleMouseLeave": ()=>{
                            isHovering = false;
                        }
                    }["FuzzyText.useEffect.init.handleMouseLeave"];
                    const handleTouchMove = {
                        "FuzzyText.useEffect.init.handleTouchMove": (e)=>{
                            if (!enableHover) return;
                            e.preventDefault();
                            const rect = canvas.getBoundingClientRect();
                            const touch = e.touches[0];
                            const x = touch.clientX - rect.left;
                            const y = touch.clientY - rect.top;
                            isHovering = isInsideTextArea(x, y);
                        }
                    }["FuzzyText.useEffect.init.handleTouchMove"];
                    const handleTouchEnd = {
                        "FuzzyText.useEffect.init.handleTouchEnd": ()=>{
                            isHovering = false;
                        }
                    }["FuzzyText.useEffect.init.handleTouchEnd"];
                    if (enableHover) {
                        canvas.addEventListener("mousemove", handleMouseMove);
                        canvas.addEventListener("mouseleave", handleMouseLeave);
                        canvas.addEventListener("touchmove", handleTouchMove, {
                            passive: false
                        });
                        canvas.addEventListener("touchend", handleTouchEnd);
                    }
                    const cleanup = {
                        "FuzzyText.useEffect.init.cleanup": ()=>{
                            window.cancelAnimationFrame(animationFrameId);
                            if (enableHover) {
                                canvas.removeEventListener("mousemove", handleMouseMove);
                                canvas.removeEventListener("mouseleave", handleMouseLeave);
                                canvas.removeEventListener("touchmove", handleTouchMove);
                                canvas.removeEventListener("touchend", handleTouchEnd);
                            }
                        }
                    }["FuzzyText.useEffect.init.cleanup"];
                    canvas.cleanupFuzzyText = cleanup;
                }
            }["FuzzyText.useEffect.init"];
            init();
            return ({
                "FuzzyText.useEffect": ()=>{
                    isCancelled = true;
                    window.cancelAnimationFrame(animationFrameId);
                    if (canvas && canvas.cleanupFuzzyText) {
                        canvas.cleanupFuzzyText();
                    }
                }
            })["FuzzyText.useEffect"];
        }
    }["FuzzyText.useEffect"], [
        children,
        fontSize,
        fontWeight,
        fontFamily,
        color,
        enableHover,
        baseIntensity,
        hoverIntensity
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("canvas", {
        ref: canvasRef
    }, void 0, false, {
        fileName: "[project]/src/components/ui/FuzzyText.tsx",
        lineNumber: 209,
        columnNumber: 10
    }, this);
};
_s(FuzzyText, "UJgi7ynoup7eqypjnwyX/s32POg=");
_c = FuzzyText;
const __TURBOPACK__default__export__ = FuzzyText;
var _c;
__turbopack_refresh__.register(_c, "FuzzyText");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/ui/text-randomized.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "RandomizedTextEffect": (()=>RandomizedTextEffect)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
var _s = __turbopack_refresh__.signature();
"use client";
;
const lettersAndSymbols = "abcdefghijklmnopqrstuvwxyz!@#$%^&*-_+=;:<>,";
function RandomizedTextEffect({ text, className = "" }) {
    _s();
    const [animatedText, setAnimatedText] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("");
    const [hasSeenAnimation, setHasSeenAnimation] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const getRandomChar = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "RandomizedTextEffect.useCallback[getRandomChar]": ()=>lettersAndSymbols[Math.floor(Math.random() * lettersAndSymbols.length)]
    }["RandomizedTextEffect.useCallback[getRandomChar]"], []);
    const animateText = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "RandomizedTextEffect.useCallback[animateText]": async ()=>{
            const duration = 30;
            const revealDuration = 40;
            const initialRandomDuration = 300;
            const generateRandomText = {
                "RandomizedTextEffect.useCallback[animateText].generateRandomText": ()=>text.split("").map({
                        "RandomizedTextEffect.useCallback[animateText].generateRandomText": ()=>getRandomChar()
                    }["RandomizedTextEffect.useCallback[animateText].generateRandomText"]).join("")
            }["RandomizedTextEffect.useCallback[animateText].generateRandomText"];
            setAnimatedText(generateRandomText());
            const endTime = Date.now() + initialRandomDuration;
            while(Date.now() < endTime){
                await new Promise({
                    "RandomizedTextEffect.useCallback[animateText]": (resolve)=>setTimeout(resolve, duration)
                }["RandomizedTextEffect.useCallback[animateText]"]);
                setAnimatedText(generateRandomText());
            }
            for(let i = 0; i < text.length; i++){
                await new Promise({
                    "RandomizedTextEffect.useCallback[animateText]": (resolve)=>setTimeout(resolve, revealDuration)
                }["RandomizedTextEffect.useCallback[animateText]"]);
                setAnimatedText({
                    "RandomizedTextEffect.useCallback[animateText]": (prevText)=>text.slice(0, i + 1) + prevText.slice(i + 1).split("").map({
                            "RandomizedTextEffect.useCallback[animateText]": ()=>getRandomChar()
                        }["RandomizedTextEffect.useCallback[animateText]"]).join("")
                }["RandomizedTextEffect.useCallback[animateText]"]);
            }
        }
    }["RandomizedTextEffect.useCallback[animateText]"], [
        text,
        getRandomChar
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "RandomizedTextEffect.useEffect": ()=>{
            const isFirstVisit = !localStorage.getItem("hasSeenAnimation");
            if (isFirstVisit) {
                animateText();
                localStorage.setItem("hasSeenAnimation", "true");
            } else {
                setHasSeenAnimation(true);
            }
            const handleBeforeUnload = {
                "RandomizedTextEffect.useEffect.handleBeforeUnload": ()=>{
                    localStorage.removeItem("hasSeenAnimation");
                }
            }["RandomizedTextEffect.useEffect.handleBeforeUnload"];
            window.addEventListener("beforeunload", handleBeforeUnload);
            return ({
                "RandomizedTextEffect.useEffect": ()=>{
                    window.removeEventListener("beforeunload", handleBeforeUnload);
                }
            })["RandomizedTextEffect.useEffect"];
        }
    }["RandomizedTextEffect.useEffect"], [
        animateText
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `relative inline-block ${className}`,
        children: hasSeenAnimation ? text : animatedText
    }, void 0, false, {
        fileName: "[project]/src/components/ui/text-randomized.tsx",
        lineNumber: 79,
        columnNumber: 5
    }, this);
}
_s(RandomizedTextEffect, "KOgIsSvDeKU0XLgQTccvk2p/t9Y=");
_c = RandomizedTextEffect;
var _c;
__turbopack_refresh__.register(_c, "RandomizedTextEffect");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/not-found.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>NotFoundPage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_import__("[project]/src/components/ui/index.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$FuzzyText$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/ui/FuzzyText.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$text$2d$randomized$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/ui/text-randomized.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/ui/button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$io5$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/react-icons/io5/index.mjs [app-client] (ecmascript)");
"use client";
;
;
;
;
;
;
function NotFoundPage() {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "fixed w-screen h-screen bg-[var(--background)]  flex flex-col justify-center items-center  z-50 inset-0",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex flex-col items-center justify-center text-center",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$FuzzyText$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                        fontSize: 50,
                        baseIntensity: 0.2,
                        hoverIntensity: 2,
                        enableHover: false,
                        children: "404 | Page Not Found"
                    }, void 0, false, {
                        fileName: "[project]/src/app/not-found.tsx",
                        lineNumber: 15,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$text$2d$randomized$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RandomizedTextEffect"], {
                        className: "mt-4 text-xl text-[var(--paragraph)]",
                        text: "The page you are looking for doesn't exist."
                    }, void 0, false, {
                        fileName: "[project]/src/app/not-found.tsx",
                        lineNumber: 24,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "mt-8 w-max",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                            href: "/",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$io5$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["IoArrowBack"], {}, void 0, false, {
                                        fileName: "[project]/src/app/not-found.tsx",
                                        lineNumber: 31,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        children: "Back To HomePage"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/not-found.tsx",
                                        lineNumber: 32,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/not-found.tsx",
                                lineNumber: 30,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/app/not-found.tsx",
                            lineNumber: 29,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/not-found.tsx",
                        lineNumber: 28,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/not-found.tsx",
                lineNumber: 14,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/app/not-found.tsx",
            lineNumber: 13,
            columnNumber: 7
        }, this)
    }, void 0, false);
}
_c = NotFoundPage;
var _c;
__turbopack_refresh__.register(_c, "NotFoundPage");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/not-found.tsx [app-rsc] (ecmascript, Next.js server component, client modules)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
}}),
}]);

//# sourceMappingURL=src_4c52e3._.js.map
{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/src/components/ui/backgroundEffect.tsx"], "sourcesContent": ["\"use client\";\n\nimport { memo } from \"react\";\n\nconst BackgroundEffect = () => {\n  return (\n    <div className=\"absolute hidden inset-0 isolate z-10 contain-strict max-md:hidden\">\n      <div className=\"absolute left-0 top-0 h-[1280px] w-[560px] -translate-y-[350px] -rotate-45 rounded-full bg-[radial-gradient(68.54%_68.72%_at_55.02%_31.46%,hsla(0,0%,85%,.08)_0,hsla(0,0%,55%,.02)_50%,hsla(0,0%,45%,0)_80%)]\"></div>\n      <div className=\"absolute left-0 top-0 h-[1280px] w-[240px] -rotate-45 rounded-full bg-[radial-gradient(50%_50%_at_50%_50%,hsla(0,0%,85%,.06)_0,hsla(0,0%,45%,.02)_80%,transparent_100%)] [translate:5%_-50%]\"></div>\n      <div className=\"absolute left-0 top-0 h-[1280px] w-[240px] -translate-y-[350px] -rotate-45 bg-[radial-gradient(50%_50%_at_50%_50%,hsla(0,0%,85%,.04)_0,hsla(0,0%,45%,.02)_80%,transparent_100%)]\"></div>\n    </div>\n  );\n};\n\n// Memoize the component to prevent unnecessary re-renders\nexport default memo(BackgroundEffect);\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIA,MAAM,mBAAmB;IACvB,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB;KARM;2DAWS,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,EAAE"}}, {"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 61, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/src/components/ui/FloatingActionButton.tsx"], "sourcesContent": ["\"use client\";\n\nimport { AnimatePresence, motion } from \"framer-motion\";\nimport { Briefcase, FileText, Home, Mail, Menu, X } from \"lucide-react\";\nimport Link from \"next/link\";\nimport { useEffect, useState } from \"react\";\n\ninterface FloatingActionButtonProps {\n  threshold?: number;\n}\n\nexport default function FloatingActionButton({\n  threshold = 300,\n}: FloatingActionButtonProps) {\n  const [isVisible, setIsVisible] = useState(false);\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      if (window.scrollY > threshold) {\n        setIsVisible(true);\n      } else {\n        setIsVisible(false);\n        if (isMenuOpen) setIsMenuOpen(false);\n      }\n    };\n\n    window.addEventListener(\"scroll\", handleScroll);\n    return () => window.removeEventListener(\"scroll\", handleScroll);\n  }, [threshold, isMenuOpen]);\n\n  const menuItems = [\n    { icon: <Home className=\"h-5 w-5\" />, label: \"Home\", href: \"/\" },\n    { icon: <Briefcase className=\"h-5 w-5\" />, label: \"Work\", href: \"/#work\" },\n    {\n      icon: <FileText className=\"h-5 w-5\" />,\n      label: \"Projects\",\n      href: \"/projects\",\n    },\n    { icon: <Mail className=\"h-5 w-5\" />, label: \"Contact\", href: \"/contact\" },\n  ];\n\n  const handleMenuItemClick = (href: string) => {\n    setIsMenuOpen(false);\n    if (href === \"/#work\") {\n      const workSection = document.getElementById(\"work\");\n      if (workSection) {\n        workSection.scrollIntoView({ behavior: \"smooth\" });\n      }\n    }\n  };\n\n  return (\n    <AnimatePresence>\n      {isVisible && (\n        <motion.div\n          className=\"fixed bottom-6 right-6 z-30 flex flex-col items-end hidden\"\n          initial={{ opacity: 0, scale: 0.8 }}\n          animate={{ opacity: 1, scale: 1 }}\n          exit={{ opacity: 0, scale: 0.8 }}\n          transition={{ duration: 0.3 }}\n        >\n          {/* Menu items */}\n          <AnimatePresence>\n            {isMenuOpen && (\n              <motion.div\n                className=\"mb-4 flex flex-col gap-3\"\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                exit={{ opacity: 0, y: 20 }}\n                transition={{ duration: 0.3 }}\n              >\n                {menuItems.map((item, index) => (\n                  <motion.div\n                    key={item.label}\n                    initial={{ opacity: 0, x: 20 }}\n                    animate={{ opacity: 1, x: 0 }}\n                    transition={{ delay: index * 0.05 }}\n                  >\n                    <Link\n                      href={item.href}\n                      onClick={() => handleMenuItemClick(item.href)}\n                      className=\"flex items-center gap-2 rounded-full bg-[var(--card-background)] px-4 py-2 text-sm text-[var(--headline)] shadow-md hover:bg-[var(--link-color)] hover:text-white transition-colors\"\n                    >\n                      {item.icon}\n                      <span>{item.label}</span>\n                    </Link>\n                  </motion.div>\n                ))}\n              </motion.div>\n            )}\n          </AnimatePresence>\n\n          {/* Main button */}\n          <motion.button\n            className=\"flex h-12 w-12 items-center justify-center rounded-full bg-[var(--link-color)] text-white shadow-lg hover:bg-[var(--button)] transition-colors\"\n            onClick={() =>\n              isMenuOpen ? setIsMenuOpen(false) : setIsMenuOpen(true)\n            }\n            whileTap={{ scale: 0.9 }}\n            aria-label={isMenuOpen ? \"Close menu\" : \"Open menu\"}\n          >\n            {isMenuOpen ? (\n              <X className=\"h-5 w-5\" />\n            ) : (\n              <Menu className=\"h-5 w-5\" />\n            )}\n          </motion.button>\n\n          {/* Back to top button */}\n        </motion.div>\n      )}\n    </AnimatePresence>\n  );\n}\n"], "names": [], "mappings": ";;;;AAIA;AACA;AAFA;AAAA;AAAA;AAAA;AADA;AAAA;AACA;AAAA;;;AAHA;;;;;AAWe,SAAS,qBAAqB,EAC3C,YAAY,GAAG,EACW;;IAC1B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;YACR,MAAM;+DAAe;oBACnB,IAAI,OAAO,OAAO,GAAG,WAAW;wBAC9B,aAAa;oBACf,OAAO;wBACL,aAAa;wBACb,IAAI,YAAY,cAAc;oBAChC;gBACF;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC;kDAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;yCAAG;QAAC;QAAW;KAAW;IAE1B,MAAM,YAAY;QAChB;YAAE,oBAAM,6LAAC,sMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;YAAc,OAAO;YAAQ,MAAM;QAAI;QAC/D;YAAE,oBAAM,6LAAC,+MAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;YAAc,OAAO;YAAQ,MAAM;QAAS;QACzE;YACE,oBAAM,6LAAC,iNAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;YAC1B,OAAO;YACP,MAAM;QACR;QACA;YAAE,oBAAM,6LAAC,qMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;YAAc,OAAO;YAAW,MAAM;QAAW;KAC1E;IAED,MAAM,sBAAsB,CAAC;QAC3B,cAAc;QACd,IAAI,SAAS,UAAU;YACrB,MAAM,cAAc,SAAS,cAAc,CAAC;YAC5C,IAAI,aAAa;gBACf,YAAY,cAAc,CAAC;oBAAE,UAAU;gBAAS;YAClD;QACF;IACF;IAEA,qBACE,6LAAC,4LAAA,CAAA,kBAAe;kBACb,2BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,WAAU;YACV,SAAS;gBAAE,SAAS;gBAAG,OAAO;YAAI;YAClC,SAAS;gBAAE,SAAS;gBAAG,OAAO;YAAE;YAChC,MAAM;gBAAE,SAAS;gBAAG,OAAO;YAAI;YAC/B,YAAY;gBAAE,UAAU;YAAI;;8BAG5B,6LAAC,4LAAA,CAAA,kBAAe;8BACb,4BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,MAAM;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC1B,YAAY;4BAAE,UAAU;wBAAI;kCAE3B,UAAU,GAAG,CAAC,CAAC,MAAM,sBACpB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,OAAO,QAAQ;gCAAK;0CAElC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAM,KAAK,IAAI;oCACf,SAAS,IAAM,oBAAoB,KAAK,IAAI;oCAC5C,WAAU;;wCAET,KAAK,IAAI;sDACV,6LAAC;sDAAM,KAAK,KAAK;;;;;;;;;;;;+BAXd,KAAK,KAAK;;;;;;;;;;;;;;;8BAoBzB,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oBACZ,WAAU;oBACV,SAAS,IACP,aAAa,cAAc,SAAS,cAAc;oBAEpD,UAAU;wBAAE,OAAO;oBAAI;oBACvB,cAAY,aAAa,eAAe;8BAEvC,2BACC,6LAAC,+LAAA,CAAA,IAAC;wBAAC,WAAU;;;;;6CAEb,6LAAC,qMAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;AAS9B;GAvGwB;KAAA"}}, {"offset": {"line": 291, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 297, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/src/components/common/Logo.tsx"], "sourcesContent": ["export default function Logo() {\n  return (\n    <div>\n      <h1>Logo</h1>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAe,SAAS;IACtB,qBACE,6LAAC;kBACC,cAAA,6LAAC;sBAAG;;;;;;;;;;;AAGV;KANwB"}}, {"offset": {"line": 323, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 329, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/src/components/common/navbar/LogoLink.tsx"], "sourcesContent": ["\"use client\";\nimport Link from \"next/link\";\nimport Logo from \"../Logo\";\n\nexport default function LogoLink() {\n  return (\n    <Link\n      href=\"/home\"\n      aria-label=\"Cosmos\"\n      className=\"text-[var(--link-color)] hover:text-[var(--link-hover)]\"\n    >\n      <Logo />\n    </Link>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AAFA;;;;AAIe,SAAS;IACtB,qBACE,6LAAC,+JAAA,CAAA,UAAI;QACH,MAAK;QACL,cAAW;QACX,WAAU;kBAEV,cAAA,6LAAC,uIAAA,CAAA,UAAI;;;;;;;;;;AAGX;KAVwB"}}, {"offset": {"line": 361, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 367, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\";\nimport { twMerge } from \"tailwind-merge\";\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB"}}, {"offset": {"line": 380, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 386, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/src/components/layouts/website/SearchBar.tsx"], "sourcesContent": ["import { useState, useRef } from \"react\";\nimport { cn } from \"@/lib/utils\"; // your classnames merging util\nimport { FiSearch, FiX } from \"react-icons/fi\"; // React Icons from Feather\n\nexport default function SearchBar() {\n  const [query, setQuery] = useState(\"\");\n  const [focused, setFocused] = useState(false);\n  const inputRef = useRef<HTMLInputElement>(null);\n\n  const suggestions = [\n    \"subtle feelings of melancholy\",\n    \"cosmos\",\n    \"blockchain\",\n    \"NFT\",\n    \"decentralized\",\n  ].filter((s) => s.toLowerCase().includes(query.toLowerCase()));\n\n  return (\n    <div\n      role=\"combobox\"\n      aria-expanded={focused}\n      aria-controls=\"search-listbox\"\n      aria-haspopup=\"listbox\"\n      className=\"relative w-full max-w-md md:max-w-lg\"\n    >\n      <div\n        className={cn(\n          \"flex items-center rounded-md px-3 py-2 border transition\",\n          focused\n            ? \"ring-2 ring-[var(--highlight)] border-[var(--input-border-color)]\"\n            : \"border-[var(--input-border-color)]\",\n          \"bg-[var(--input-background)]\"\n        )}\n      >\n        <FiSearch\n          className=\"w-5 h-5 text-[var(--menu-color)] mr-2 flex-shrink-0\"\n          aria-hidden=\"true\"\n          focusable=\"false\"\n        />\n        <input\n          ref={inputRef}\n          type=\"search\"\n          role=\"searchbox\"\n          aria-autocomplete=\"list\"\n          aria-controls=\"search-listbox\"\n          placeholder=\"Search Cosmos...\"\n          className=\"flex-grow bg-transparent text-[var(--input-text)] placeholder-[var(--paragraph)] outline-none text-sm\"\n          value={query}\n          onChange={(e) => setQuery(e.target.value)}\n          onFocus={() => setFocused(true)}\n          onBlur={() => {\n            setTimeout(() => setFocused(false), 150);\n          }}\n          autoComplete=\"off\"\n        />\n        {query && (\n          <button\n            aria-label=\"Clear search\"\n            onClick={() => {\n              setQuery(\"\");\n              inputRef.current?.focus();\n            }}\n            className=\"ml-2 text-[var(--menu-color)] hover:text-[var(--highlight)]\"\n          >\n            <FiX />\n          </button>\n        )}\n      </div>\n\n      {focused && suggestions.length > 0 && (\n        <ul\n          id=\"search-listbox\"\n          role=\"listbox\"\n          className=\"absolute z-50 mt-1 max-h-48 w-full overflow-auto rounded-md border border-[var(--input-border-color)] bg-[var(--input-background)] shadow-lg\"\n        >\n          {suggestions.map((item, i) => (\n            <li\n              key={i}\n              role=\"option\"\n              tabIndex={-1}\n              className=\"cursor-pointer px-4 py-2 text-sm text-[var(--paragraph)] hover:bg-[var(--highlight)] hover:text-[var(--active-text)]\"\n              onMouseDown={(e) => {\n                e.preventDefault();\n                setQuery(item);\n                setFocused(false);\n              }}\n            >\n              Try <span className=\"font-semibold\">‘{item}’</span>\n            </li>\n          ))}\n        </ul>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA,2MAAkC,+BAA+B;AACjE,2PAAgD,2BAA2B;;;;;;AAE5D,SAAS;;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAE1C,MAAM,cAAc;QAClB;QACA;QACA;QACA;QACA;KACD,CAAC,MAAM,CAAC,CAAC,IAAM,EAAE,WAAW,GAAG,QAAQ,CAAC,MAAM,WAAW;IAE1D,qBACE,6LAAC;QACC,MAAK;QACL,iBAAe;QACf,iBAAc;QACd,iBAAc;QACd,WAAU;;0BAEV,6LAAC;gBACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4DACA,UACI,sEACA,sCACJ;;kCAGF,6LAAC,iJAAA,CAAA,WAAQ;wBACP,WAAU;wBACV,eAAY;wBACZ,WAAU;;;;;;kCAEZ,6LAAC;wBACC,KAAK;wBACL,MAAK;wBACL,MAAK;wBACL,qBAAkB;wBAClB,iBAAc;wBACd,aAAY;wBACZ,WAAU;wBACV,OAAO;wBACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;wBACxC,SAAS,IAAM,WAAW;wBAC1B,QAAQ;4BACN,WAAW,IAAM,WAAW,QAAQ;wBACtC;wBACA,cAAa;;;;;;oBAEd,uBACC,6LAAC;wBACC,cAAW;wBACX,SAAS;4BACP,SAAS;4BACT,SAAS,OAAO,EAAE;wBACpB;wBACA,WAAU;kCAEV,cAAA,6LAAC,iJAAA,CAAA,MAAG;;;;;;;;;;;;;;;;YAKT,WAAW,YAAY,MAAM,GAAG,mBAC/B,6LAAC;gBACC,IAAG;gBACH,MAAK;gBACL,WAAU;0BAET,YAAY,GAAG,CAAC,CAAC,MAAM,kBACtB,6LAAC;wBAEC,MAAK;wBACL,UAAU,CAAC;wBACX,WAAU;wBACV,aAAa,CAAC;4BACZ,EAAE,cAAc;4BAChB,SAAS;4BACT,WAAW;wBACb;;4BACD;0CACK,6LAAC;gCAAK,WAAU;;oCAAgB;oCAAE;oCAAK;;;;;;;;uBAVtC;;;;;;;;;;;;;;;;AAiBnB;GA1FwB;KAAA"}}, {"offset": {"line": 524, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 530, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/src/components/common/navbar/SearchBarWrapper.tsx"], "sourcesContent": ["\"use client\";\nimport SearchBar from \"@/components/layouts/website/SearchBar\";\n\nexport default function SearchBarWrapper() {\n  return <SearchBar />;\n}\n"], "names": [], "mappings": ";;;;AACA;AADA;;;AAGe,SAAS;IACtB,qBAAO,6LAAC,wJAAA,CAAA,UAAS;;;;;AACnB;KAFwB"}}, {"offset": {"line": 551, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 557, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/src/components/ui/navigation-menu.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva } from \"class-variance-authority\"\nimport { ChevronDownIcon } from \"lucide-react\"\nimport { NavigationMenu as NavigationMenuPrimitive } from \"radix-ui\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction NavigationMenu({\n  className,\n  children,\n  viewport = true,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Root> & {\n  viewport?: boolean\n}) {\n  return (\n    <NavigationMenuPrimitive.Root\n      data-slot=\"navigation-menu\"\n      data-viewport={viewport}\n      className={cn(\n        \"group/navigation-menu relative flex max-w-max flex-1 items-center justify-center\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      {viewport && <NavigationMenuViewport />}\n    </NavigationMenuPrimitive.Root>\n  )\n}\n\nfunction NavigationMenuList({\n  className,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.List>) {\n  return (\n    <NavigationMenuPrimitive.List\n      data-slot=\"navigation-menu-list\"\n      className={cn(\n        \"group flex flex-1 list-none items-center justify-center gap-1\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction NavigationMenuItem({\n  className,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Item>) {\n  return (\n    <NavigationMenuPrimitive.Item\n      data-slot=\"navigation-menu-item\"\n      className={cn(\"relative\", className)}\n      {...props}\n    />\n  )\n}\n\nconst navigationMenuTriggerStyle = cva(\n  \"group inline-flex h-9 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground disabled:pointer-events-none disabled:opacity-50 data-[state=open]:hover:bg-accent data-[state=open]:text-accent-foreground data-[state=open]:focus:bg-accent data-[state=open]:bg-accent/50 focus-visible:ring-ring/50 outline-none transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1\"\n)\n\nfunction NavigationMenuTrigger({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Trigger>) {\n  return (\n    <NavigationMenuPrimitive.Trigger\n      data-slot=\"navigation-menu-trigger\"\n      className={cn(navigationMenuTriggerStyle(), \"group\", className)}\n      {...props}\n    >\n      {children}{\" \"}\n      <ChevronDownIcon\n        className=\"relative top-[1px] ml-1 size-3 transition duration-300 group-data-[state=open]:rotate-180\"\n        aria-hidden=\"true\"\n      />\n    </NavigationMenuPrimitive.Trigger>\n  )\n}\n\nfunction NavigationMenuContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Content>) {\n  return (\n    <NavigationMenuPrimitive.Content\n      data-slot=\"navigation-menu-content\"\n      className={cn(\n        \"data-[motion^=from-]:animate-in data-[motion^=to-]:animate-out data-[motion^=from-]:fade-in data-[motion^=to-]:fade-out data-[motion=from-end]:slide-in-from-right-52 data-[motion=from-start]:slide-in-from-left-52 data-[motion=to-end]:slide-out-to-right-52 data-[motion=to-start]:slide-out-to-left-52 top-0 left-0 w-full p-2 pr-2.5 md:absolute md:w-auto\",\n        \"group-data-[viewport=false]/navigation-menu:bg-popover group-data-[viewport=false]/navigation-menu:text-popover-foreground group-data-[viewport=false]/navigation-menu:data-[state=open]:animate-in group-data-[viewport=false]/navigation-menu:data-[state=closed]:animate-out group-data-[viewport=false]/navigation-menu:data-[state=closed]:zoom-out-95 group-data-[viewport=false]/navigation-menu:data-[state=open]:zoom-in-95 group-data-[viewport=false]/navigation-menu:data-[state=open]:fade-in-0 group-data-[viewport=false]/navigation-menu:data-[state=closed]:fade-out-0 group-data-[viewport=false]/navigation-menu:top-full group-data-[viewport=false]/navigation-menu:mt-1.5 group-data-[viewport=false]/navigation-menu:overflow-hidden group-data-[viewport=false]/navigation-menu:rounded-md group-data-[viewport=false]/navigation-menu:border group-data-[viewport=false]/navigation-menu:shadow group-data-[viewport=false]/navigation-menu:duration-200 **:data-[slot=navigation-menu-link]:focus:ring-0 **:data-[slot=navigation-menu-link]:focus:outline-none\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction NavigationMenuViewport({\n  className,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Viewport>) {\n  return (\n    <div\n      className={cn(\n        \"absolute top-full left-0 isolate z-50 flex justify-center\"\n      )}\n    >\n      <NavigationMenuPrimitive.Viewport\n        data-slot=\"navigation-menu-viewport\"\n        className={cn(\n          \"origin-top-center bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-90 relative mt-1.5 h-[var(--radix-navigation-menu-viewport-height)] w-full overflow-hidden rounded-md border shadow md:w-[var(--radix-navigation-menu-viewport-width)]\",\n          className\n        )}\n        {...props}\n      />\n    </div>\n  )\n}\n\nfunction NavigationMenuLink({\n  className,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Link>) {\n  return (\n    <NavigationMenuPrimitive.Link\n      data-slot=\"navigation-menu-link\"\n      className={cn(\n        \"data-[active]:focus:bg-accent data-[active]:hover:bg-accent data-[active]:bg-accent data-[active]:text-accent-foreground hover:bg-accent focus:bg-accent focus:text-accent-foreground focus-visible:ring-ring/50 [&_svg:not([class*='text-'])]:text-muted-foreground flex flex-col gap-1 rounded-sm p-2 text-sm transition-all outline-none focus-visible:ring-[3px] focus-visible:outline-1 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction NavigationMenuIndicator({\n  className,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Indicator>) {\n  return (\n    <NavigationMenuPrimitive.Indicator\n      data-slot=\"navigation-menu-indicator\"\n      className={cn(\n        \"data-[state=visible]:animate-in data-[state=hidden]:animate-out data-[state=hidden]:fade-out data-[state=visible]:fade-in top-full z-[1] flex h-1.5 items-end justify-center overflow-hidden\",\n        className\n      )}\n      {...props}\n    >\n      <div className=\"bg-border relative top-[60%] h-2 w-2 rotate-45 rounded-tl-sm shadow-md\" />\n    </NavigationMenuPrimitive.Indicator>\n  )\n}\n\nexport {\n  NavigationMenu,\n  NavigationMenuList,\n  NavigationMenuItem,\n  NavigationMenuContent,\n  NavigationMenuTrigger,\n  NavigationMenuLink,\n  NavigationMenuIndicator,\n  NavigationMenuViewport,\n  navigationMenuTriggerStyle,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AACA;AAIA;AAFA;AADA;;;;;;AAKA,SAAS,eAAe,EACtB,SAAS,EACT,QAAQ,EACR,WAAW,IAAI,EACf,GAAG,OAGJ;IACC,qBACE,6LAAC,2NAAA,CAAA,iBAAuB,CAAC,IAAI;QAC3B,aAAU;QACV,iBAAe;QACf,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oFACA;QAED,GAAG,KAAK;;YAER;YACA,0BAAY,6LAAC;;;;;;;;;;;AAGpB;KAtBS;AAwBT,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACuD;IAC1D,qBACE,6LAAC,2NAAA,CAAA,iBAAuB,CAAC,IAAI;QAC3B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACuD;IAC1D,qBACE,6LAAC,2NAAA,CAAA,iBAAuB,CAAC,IAAI;QAC3B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QACzB,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,MAAM,6BAA6B,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACnC;AAGF,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,6LAAC,2NAAA,CAAA,iBAAuB,CAAC,OAAO;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B,SAAS;QACpD,GAAG,KAAK;;YAER;YAAU;0BACX,6LAAC,2NAAA,CAAA,kBAAe;gBACd,WAAU;gBACV,eAAY;;;;;;;;;;;;AAIpB;MAlBS;AAoBT,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,6LAAC,2NAAA,CAAA,iBAAuB,CAAC,OAAO;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oWACA,6hCACA;QAED,GAAG,KAAK;;;;;;AAGf;MAfS;AAiBT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV;kBAGF,cAAA,6LAAC,2NAAA,CAAA,iBAAuB,CAAC,QAAQ;YAC/B,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sVACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;MApBS;AAsBT,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACuD;IAC1D,qBACE,6LAAC,2NAAA,CAAA,iBAAuB,CAAC,IAAI;QAC3B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qaACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,wBAAwB,EAC/B,SAAS,EACT,GAAG,OAC4D;IAC/D,qBACE,6LAAC,2NAAA,CAAA,iBAAuB,CAAC,SAAS;QAChC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gMACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC;YAAI,WAAU;;;;;;;;;;;AAGrB;MAhBS"}}, {"offset": {"line": 723, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 729, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/src/components/common/navbar/NavItems.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport {\n  NavigationMenu,\n  NavigationMenuContent,\n  NavigationMenuItem,\n  NavigationMenuLink,\n  NavigationMenuList,\n  NavigationMenuTrigger,\n} from \"@/components/ui/navigation-menu\";\nimport { cn } from \"@/lib/utils\";\nimport { FiChevronDown, FiChevronUp } from \"react-icons/fi\";\nimport { BookOpenIcon, InfoIcon, LifeBuoyIcon } from \"lucide-react\";\n\ntype NavLinkItem = {\n  href: string;\n  label: string;\n  description?: string;\n  icon?: \"BookOpenIcon\" | \"LifeBuoyIcon\" | \"InfoIcon\";\n};\n\ntype NavLink = {\n  href?: string;\n  label: string;\n  submenu?: boolean;\n  type?: \"description\" | \"simple\" | \"icon\";\n  items?: NavLinkItem[];\n};\n\nconst navigationLinks: NavLink[] = [\n  { href: \"#\", label: \"Home\" },\n  {\n    label: \"Features\",\n    submenu: true,\n    type: \"description\",\n    items: [\n      { href: \"#\", label: \"Components\", description: \"Browse all components.\" },\n      { href: \"#\", label: \"Documentation\", description: \"Learn to use it.\" },\n      { href: \"#\", label: \"Templates\", description: \"Pre-built layouts.\" },\n    ],\n  },\n  {\n    label: \"Pricing\",\n    submenu: true,\n    type: \"simple\",\n    items: [\n      { href: \"#\", label: \"Product A\" },\n      { href: \"#\", label: \"Product B\" },\n      { href: \"#\", label: \"Product C\" },\n      { href: \"#\", label: \"Product D\" },\n    ],\n  },\n  {\n    label: \"About\",\n    submenu: true,\n    type: \"icon\",\n    items: [\n      { href: \"#\", label: \"Getting Started\", icon: \"BookOpenIcon\" },\n      { href: \"#\", label: \"Tutorials\", icon: \"LifeBuoyIcon\" },\n      { href: \"#\", label: \"About Us\", icon: \"InfoIcon\" },\n    ],\n  },\n];\n\nconst renderIcon = (icon?: NavLinkItem[\"icon\"]) => {\n  switch (icon) {\n    case \"BookOpenIcon\":\n      return (\n        <BookOpenIcon\n          size={16}\n          className=\"text-[var(--menu-color)] opacity-60\"\n        />\n      );\n    case \"LifeBuoyIcon\":\n      return (\n        <LifeBuoyIcon\n          size={16}\n          className=\"text-[var(--menu-color)] opacity-60\"\n        />\n      );\n    case \"InfoIcon\":\n      return (\n        <InfoIcon size={16} className=\"text-[var(--menu-color)] opacity-60\" />\n      );\n    default:\n      return null;\n  }\n};\n\nexport default function NavItems({ isMobile = false }: { isMobile?: boolean }) {\n  const [openDropdownIndex, setOpenDropdownIndex] = useState<number | null>(\n    null\n  );\n\n  if (isMobile) {\n    return (\n      <NavigationMenu className=\"p-0\">\n        <NavigationMenuList className=\"flex flex-col gap-3 p-4\">\n          {navigationLinks.map((link, i) => (\n            <div key={i} className=\"flex flex-col\">\n              {link.submenu ? (\n                <>\n                  <button\n                    onClick={() =>\n                      setOpenDropdownIndex((prev) => (prev === i ? null : i))\n                    }\n                    className=\"flex justify-between items-center w-full text-[var(--nav-item)] px-2 py-2 text-sm font-medium hover:bg-[var(--card-hover)] rounded\"\n                  >\n                    {link.label}\n                    {openDropdownIndex === i ? (\n                      <FiChevronUp size={18} />\n                    ) : (\n                      <FiChevronDown size={18} />\n                    )}\n                  </button>\n                  <AnimatePresence>\n                    {openDropdownIndex === i && (\n                      <motion.ul\n                        initial={{ height: 0, opacity: 0 }}\n                        animate={{ height: \"auto\", opacity: 1 }}\n                        exit={{ height: 0, opacity: 0 }}\n                        transition={{ duration: 0.2 }}\n                        className={cn(\n                          \"overflow-hidden pl-4 flex flex-col gap-1\",\n                          link.type === \"description\" ? \"min-w-64\" : \"min-w-48\"\n                        )}\n                      >\n                        {link.items?.map((item, idx) => (\n                          <li key={idx}>\n                            <NavigationMenuLink\n                              href={item.href}\n                              className=\"block py-1.5 text-[var(--paragraph)] hover:text-[var(--highlight)]\"\n                            >\n                              {link.type === \"icon\" &&\n                                item.icon &&\n                                renderIcon(item.icon)}\n                              {item.label}\n                              {item.description && (\n                                <span className=\"ml-2 text-xs text-[var(--muted)]\">\n                                  {item.description}\n                                </span>\n                              )}\n                            </NavigationMenuLink>\n                          </li>\n                        ))}\n                      </motion.ul>\n                    )}\n                  </AnimatePresence>\n                </>\n              ) : (\n                <NavigationMenuLink\n                  href={link.href}\n                  className=\"py-2 px-2 text-[var(--paragraph)] hover:text-[var(--highlight)] block font-medium\"\n                >\n                  {link.label}\n                </NavigationMenuLink>\n              )}\n            </div>\n          ))}\n        </NavigationMenuList>\n      </NavigationMenu>\n    );\n  }\n\n  return (\n    <NavigationMenu viewport={false}>\n      <NavigationMenuList className=\"gap-2 flex\">\n        {navigationLinks.map((link, index) => (\n          <NavigationMenuItem key={index}>\n            {link.submenu ? (\n              <>\n                <NavigationMenuTrigger className=\"text-[var(--nav-item)] hover:text-[var(--highlight)] bg-transparent px-2 py-1.5 font-medium *:[svg]:-me-0.5 *:[svg]:size-3.5\">\n                  {link.label}\n                </NavigationMenuTrigger>\n                <NavigationMenuContent className=\"z-50 p-1 bg-[var(--background)] border border-[var(--border)]\">\n                  <ul\n                    className={cn(\n                      link.type === \"description\" ? \"min-w-64\" : \"min-w-48\"\n                    )}\n                  >\n                    {link.items?.map((item, idx) => (\n                      <li key={idx}>\n                        <NavigationMenuLink\n                          href={item.href}\n                          className={cn(\n                            \"py-1.5 text-[var(--paragraph)] hover:text-[var(--highlight)] flex items-center gap-2\",\n                            link.type === \"icon\" && \"pl-1\"\n                          )}\n                        >\n                          {link.type === \"icon\" &&\n                            item.icon &&\n                            renderIcon(item.icon)}\n                          <span>{item.label}</span>\n                          {item.description && (\n                            <span className=\"ml-2 text-xs text-[var(--muted)]\">\n                              {item.description}\n                            </span>\n                          )}\n                        </NavigationMenuLink>\n                      </li>\n                    ))}\n                  </ul>\n                </NavigationMenuContent>\n              </>\n            ) : (\n              <NavigationMenuLink\n                href={link.href}\n                className=\"text-[var(--nav-item)] hover:text-[var(--highlight)] py-1.5 font-medium\"\n              >\n                {link.label}\n              </NavigationMenuLink>\n            )}\n          </NavigationMenuItem>\n        ))}\n      </NavigationMenuList>\n    </NavigationMenu>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAQA;AAEA;AAAA;AAAA;AADA;AAVA;AAAA;;;AAHA;;;;;;;AA+BA,MAAM,kBAA6B;IACjC;QAAE,MAAM;QAAK,OAAO;IAAO;IAC3B;QACE,OAAO;QACP,SAAS;QACT,MAAM;QACN,OAAO;YACL;gBAAE,MAAM;gBAAK,OAAO;gBAAc,aAAa;YAAyB;YACxE;gBAAE,MAAM;gBAAK,OAAO;gBAAiB,aAAa;YAAmB;YACrE;gBAAE,MAAM;gBAAK,OAAO;gBAAa,aAAa;YAAqB;SACpE;IACH;IACA;QACE,OAAO;QACP,SAAS;QACT,MAAM;QACN,OAAO;YACL;gBAAE,MAAM;gBAAK,OAAO;YAAY;YAChC;gBAAE,MAAM;gBAAK,OAAO;YAAY;YAChC;gBAAE,MAAM;gBAAK,OAAO;YAAY;YAChC;gBAAE,MAAM;gBAAK,OAAO;YAAY;SACjC;IACH;IACA;QACE,OAAO;QACP,SAAS;QACT,MAAM;QACN,OAAO;YACL;gBAAE,MAAM;gBAAK,OAAO;gBAAmB,MAAM;YAAe;YAC5D;gBAAE,MAAM;gBAAK,OAAO;gBAAa,MAAM;YAAe;YACtD;gBAAE,MAAM;gBAAK,OAAO;gBAAY,MAAM;YAAW;SAClD;IACH;CACD;AAED,MAAM,aAAa,CAAC;IAClB,OAAQ;QACN,KAAK;YACH,qBACE,6LAAC,qNAAA,CAAA,eAAY;gBACX,MAAM;gBACN,WAAU;;;;;;QAGhB,KAAK;YACH,qBACE,6LAAC,qNAAA,CAAA,eAAY;gBACX,MAAM;gBACN,WAAU;;;;;;QAGhB,KAAK;YACH,qBACE,6LAAC,yMAAA,CAAA,WAAQ;gBAAC,MAAM;gBAAI,WAAU;;;;;;QAElC;YACE,OAAO;IACX;AACF;AAEe,SAAS,SAAS,EAAE,WAAW,KAAK,EAA0B;;IAC3E,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EACvD;IAGF,IAAI,UAAU;QACZ,qBACE,6LAAC,iJAAA,CAAA,iBAAc;YAAC,WAAU;sBACxB,cAAA,6LAAC,iJAAA,CAAA,qBAAkB;gBAAC,WAAU;0BAC3B,gBAAgB,GAAG,CAAC,CAAC,MAAM,kBAC1B,6LAAC;wBAAY,WAAU;kCACpB,KAAK,OAAO,iBACX;;8CACE,6LAAC;oCACC,SAAS,IACP,qBAAqB,CAAC,OAAU,SAAS,IAAI,OAAO;oCAEtD,WAAU;;wCAET,KAAK,KAAK;wCACV,sBAAsB,kBACrB,6LAAC,iJAAA,CAAA,cAAW;4CAAC,MAAM;;;;;iEAEnB,6LAAC,iJAAA,CAAA,gBAAa;4CAAC,MAAM;;;;;;;;;;;;8CAGzB,6LAAC,4LAAA,CAAA,kBAAe;8CACb,sBAAsB,mBACrB,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;wCACR,SAAS;4CAAE,QAAQ;4CAAG,SAAS;wCAAE;wCACjC,SAAS;4CAAE,QAAQ;4CAAQ,SAAS;wCAAE;wCACtC,MAAM;4CAAE,QAAQ;4CAAG,SAAS;wCAAE;wCAC9B,YAAY;4CAAE,UAAU;wCAAI;wCAC5B,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4CACA,KAAK,IAAI,KAAK,gBAAgB,aAAa;kDAG5C,KAAK,KAAK,EAAE,IAAI,CAAC,MAAM,oBACtB,6LAAC;0DACC,cAAA,6LAAC,iJAAA,CAAA,qBAAkB;oDACjB,MAAM,KAAK,IAAI;oDACf,WAAU;;wDAET,KAAK,IAAI,KAAK,UACb,KAAK,IAAI,IACT,WAAW,KAAK,IAAI;wDACrB,KAAK,KAAK;wDACV,KAAK,WAAW,kBACf,6LAAC;4DAAK,WAAU;sEACb,KAAK,WAAW;;;;;;;;;;;;+CAXhB;;;;;;;;;;;;;;;;yDAsBnB,6LAAC,iJAAA,CAAA,qBAAkB;4BACjB,MAAM,KAAK,IAAI;4BACf,WAAU;sCAET,KAAK,KAAK;;;;;;uBAvDP;;;;;;;;;;;;;;;IA+DpB;IAEA,qBACE,6LAAC,iJAAA,CAAA,iBAAc;QAAC,UAAU;kBACxB,cAAA,6LAAC,iJAAA,CAAA,qBAAkB;YAAC,WAAU;sBAC3B,gBAAgB,GAAG,CAAC,CAAC,MAAM,sBAC1B,6LAAC,iJAAA,CAAA,qBAAkB;8BAChB,KAAK,OAAO,iBACX;;0CACE,6LAAC,iJAAA,CAAA,wBAAqB;gCAAC,WAAU;0CAC9B,KAAK,KAAK;;;;;;0CAEb,6LAAC,iJAAA,CAAA,wBAAqB;gCAAC,WAAU;0CAC/B,cAAA,6LAAC;oCACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,KAAK,IAAI,KAAK,gBAAgB,aAAa;8CAG5C,KAAK,KAAK,EAAE,IAAI,CAAC,MAAM,oBACtB,6LAAC;sDACC,cAAA,6LAAC,iJAAA,CAAA,qBAAkB;gDACjB,MAAM,KAAK,IAAI;gDACf,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wFACA,KAAK,IAAI,KAAK,UAAU;;oDAGzB,KAAK,IAAI,KAAK,UACb,KAAK,IAAI,IACT,WAAW,KAAK,IAAI;kEACtB,6LAAC;kEAAM,KAAK,KAAK;;;;;;oDAChB,KAAK,WAAW,kBACf,6LAAC;wDAAK,WAAU;kEACb,KAAK,WAAW;;;;;;;;;;;;2CAdhB;;;;;;;;;;;;;;;;qDAwBjB,6LAAC,iJAAA,CAAA,qBAAkB;wBACjB,MAAM,KAAK,IAAI;wBACf,WAAU;kCAET,KAAK,KAAK;;;;;;mBAzCQ;;;;;;;;;;;;;;;AAiDnC;GAhIwB;KAAA"}}, {"offset": {"line": 1067, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1073, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/src/components/common/navbar/Navbar.tsx"], "sourcesContent": ["\"use client\";\n\nimport { AnimatePresence, motion } from \"framer-motion\";\nimport { useState } from \"react\";\nimport { FiMenu, FiX } from \"react-icons/fi\";\nimport LogoLink from \"./LogoLink\";\nimport SearchBarWrapper from \"./SearchBarWrapper\";\nimport NavItems from \"./NavItems\";\n\nexport default function Navbar() {\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);\n\n  return (\n    <header className=\"border-b border-[var(--border)] bg-[var(--background)]\">\n      <div className=\"container mx-auto px-4\">\n        {/* Mobile */}\n        <div className=\"flex items-center justify-between h-16 md:hidden gap-2\">\n          <LogoLink />\n          <SearchBarWrapper />\n          <button\n            aria-label={mobileMenuOpen ? \"Close menu\" : \"Open menu\"}\n            className=\"text-[var(--menu-color)]\"\n            onClick={() => setMobileMenuOpen(!mobileMenuOpen)}\n          >\n            {mobileMenuOpen ? <FiX size={24} /> : <FiMenu size={24} />}\n          </button>\n        </div>\n\n        <AnimatePresence>\n          {mobileMenuOpen && (\n            <motion.nav\n              key=\"mobile-menu\"\n              initial={{ x: \"100%\", opacity: 0 }}\n              animate={{ x: 0, opacity: 1 }}\n              exit={{ x: \"100%\", opacity: 0 }}\n              transition={{ duration: 0.3, ease: \"easeInOut\" }}\n              className=\"fixed top-16 right-0 bottom-0 w-72 bg-[var(--background)] border-l border-[var(--border)] shadow-lg z-50 overflow-y-auto\"\n              aria-label=\"Mobile navigation\"\n            >\n              <NavItems isMobile />\n            </motion.nav>\n          )}\n        </AnimatePresence>\n\n        {/* Desktop */}\n        <div className=\"hidden md:flex h-16 items-center justify-between gap-4\">\n          <div className=\"flex items-center gap-6 flex-1\">\n            <LogoLink />\n            <NavItems />\n          </div>\n          <div className=\"flex-1 flex justify-center\">\n            <SearchBarWrapper />\n          </div>\n          <div className=\"flex items-center gap-2 flex-1 justify-end\">\n            <AuthButtons />\n          </div>\n        </div>\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AACA;AACA;AAHA;AAFA;AAAA;;;AAFA;;;;;;;AASe,SAAS;;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,qJAAA,CAAA,UAAQ;;;;;sCACT,6LAAC,6JAAA,CAAA,UAAgB;;;;;sCACjB,6LAAC;4BACC,cAAY,iBAAiB,eAAe;4BAC5C,WAAU;4BACV,SAAS,IAAM,kBAAkB,CAAC;sCAEjC,+BAAiB,6LAAC,iJAAA,CAAA,MAAG;gCAAC,MAAM;;;;;qDAAS,6LAAC,iJAAA,CAAA,SAAM;gCAAC,MAAM;;;;;;;;;;;;;;;;;8BAIxD,6LAAC,4LAAA,CAAA,kBAAe;8BACb,gCACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,SAAS;4BAAE,GAAG;4BAAQ,SAAS;wBAAE;wBACjC,SAAS;4BAAE,GAAG;4BAAG,SAAS;wBAAE;wBAC5B,MAAM;4BAAE,GAAG;4BAAQ,SAAS;wBAAE;wBAC9B,YAAY;4BAAE,UAAU;4BAAK,MAAM;wBAAY;wBAC/C,WAAU;wBACV,cAAW;kCAEX,cAAA,6LAAC,qJAAA,CAAA,UAAQ;4BAAC,QAAQ;;;;;;uBARd;;;;;;;;;;8BAcV,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qJAAA,CAAA,UAAQ;;;;;8CACT,6LAAC,qJAAA,CAAA,UAAQ;;;;;;;;;;;sCAEX,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,6JAAA,CAAA,UAAgB;;;;;;;;;;sCAEnB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMb;GAnDwB;KAAA"}}, {"offset": {"line": 1250, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1256, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/src/contexts/NavbarContext.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { createContext, useContext, useState, ReactNode } from \"react\";\n\ntype NavbarContextType = {\n  mobileMenuOpen: boolean;\n  setMobileMenuOpen: (open: boolean) => void;\n  openDropdownIndex: number | null;\n  setOpenDropdownIndex: (index: number | null) => void;\n};\n\nconst NavbarContext = createContext<NavbarContextType | undefined>(undefined);\n\nexport function NavbarProvider({ children }: { children: ReactNode }) {\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);\n  const [openDropdownIndex, setOpenDropdownIndex] = useState<number | null>(\n    null\n  );\n\n  return (\n    <NavbarContext.Provider\n      value={{\n        mobileMenuOpen,\n        setMobileMenuOpen,\n        openDropdownIndex,\n        setOpenDropdownIndex,\n      }}\n    >\n      {children}\n    </NavbarContext.Provider>\n  );\n}\n\nexport function useNavbarContext() {\n  const context = useContext(NavbarContext);\n  if (!context) {\n    throw new Error(\"useNavbarContext must be used within a NavbarProvider\");\n  }\n  return context;\n}\n"], "names": [], "mappings": ";;;;;AAEA;;;AAFA;;AAWA,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAiC;AAE5D,SAAS,eAAe,EAAE,QAAQ,EAA2B;;IAClE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EACvD;IAGF,qBACE,6LAAC,cAAc,QAAQ;QACrB,OAAO;YACL;YACA;YACA;YACA;QACF;kBAEC;;;;;;AAGP;GAlBgB;KAAA;AAoBT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB"}}, {"offset": {"line": 1301, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}
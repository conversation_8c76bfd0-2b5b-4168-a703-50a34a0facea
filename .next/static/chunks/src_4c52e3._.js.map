{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/src/components/ui/FuzzyText.tsx"], "sourcesContent": ["import React, { useEffect, useRef } from \"react\";\n\ninterface FuzzyTextProps {\n  children: React.ReactNode;\n  fontSize?: number | string;\n  fontWeight?: string | number;\n  fontFamily?: string;\n  color?: string;\n  enableHover?: boolean;\n  baseIntensity?: number;\n  hoverIntensity?: number;\n}\n\nconst FuzzyText: React.FC<FuzzyTextProps> = ({\n  children,\n  fontSize = \"clamp(2rem, 8vw, 8rem)\",\n  fontWeight = 900,\n  fontFamily = \"inherit\",\n  color = \"#fff\",\n  enableHover = true,\n  baseIntensity = 0.18,\n  hoverIntensity = 0.5,\n}) => {\n  const canvasRef = useRef<\n    HTMLCanvasElement & { cleanupFuzzyText?: () => void }\n  >(null);\n\n  useEffect(() => {\n    let animationFrameId: number;\n    let isCancelled = false;\n    const canvas = canvasRef.current;\n    if (!canvas) return;\n\n    const init = async () => {\n      if (document.fonts?.ready) {\n        await document.fonts.ready;\n      }\n      if (isCancelled) return;\n\n      const ctx = canvas.getContext(\"2d\");\n      if (!ctx) return;\n\n      const computedFontFamily =\n        fontFamily === \"inherit\"\n          ? window.getComputedStyle(canvas).fontFamily || \"sans-serif\"\n          : fontFamily;\n\n      const fontSizeStr =\n        typeof fontSize === \"number\" ? `${fontSize}px` : fontSize;\n      let numericFontSize: number;\n      if (typeof fontSize === \"number\") {\n        numericFontSize = fontSize;\n      } else {\n        const temp = document.createElement(\"span\");\n        temp.style.fontSize = fontSize;\n        document.body.appendChild(temp);\n        const computedSize = window.getComputedStyle(temp).fontSize;\n        numericFontSize = parseFloat(computedSize);\n        document.body.removeChild(temp);\n      }\n\n      const text = React.Children.toArray(children).join(\"\");\n\n      const offscreen = document.createElement(\"canvas\");\n      const offCtx = offscreen.getContext(\"2d\");\n      if (!offCtx) return;\n\n      offCtx.font = `${fontWeight} ${fontSizeStr} ${computedFontFamily}`;\n      offCtx.textBaseline = \"alphabetic\";\n      const metrics = offCtx.measureText(text);\n\n      const actualLeft = metrics.actualBoundingBoxLeft ?? 0;\n      const actualRight = metrics.actualBoundingBoxRight ?? metrics.width;\n      const actualAscent = metrics.actualBoundingBoxAscent ?? numericFontSize;\n      const actualDescent =\n        metrics.actualBoundingBoxDescent ?? numericFontSize * 0.2;\n\n      const textBoundingWidth = Math.ceil(actualLeft + actualRight);\n      const tightHeight = Math.ceil(actualAscent + actualDescent);\n\n      const extraWidthBuffer = 10;\n      const offscreenWidth = textBoundingWidth + extraWidthBuffer;\n\n      offscreen.width = offscreenWidth;\n      offscreen.height = tightHeight;\n\n      const xOffset = extraWidthBuffer / 2;\n      offCtx.font = `${fontWeight} ${fontSizeStr} ${computedFontFamily}`;\n      offCtx.textBaseline = \"alphabetic\";\n      offCtx.fillStyle = color;\n      offCtx.fillText(text, xOffset - actualLeft, actualAscent);\n\n      const horizontalMargin = 50;\n      const verticalMargin = 0;\n      canvas.width = offscreenWidth + horizontalMargin * 2;\n      canvas.height = tightHeight + verticalMargin * 2;\n      ctx.translate(horizontalMargin, verticalMargin);\n\n      const interactiveLeft = horizontalMargin + xOffset;\n      const interactiveTop = verticalMargin;\n      const interactiveRight = interactiveLeft + textBoundingWidth;\n      const interactiveBottom = interactiveTop + tightHeight;\n\n      let isHovering = false;\n      const fuzzRange = 30;\n\n      const run = () => {\n        if (isCancelled) return;\n        ctx.clearRect(\n          -fuzzRange,\n          -fuzzRange,\n          offscreenWidth + 2 * fuzzRange,\n          tightHeight + 2 * fuzzRange\n        );\n        const intensity = isHovering ? hoverIntensity : baseIntensity;\n        for (let j = 0; j < tightHeight; j++) {\n          const dx = Math.floor(intensity * (Math.random() - 0.5) * fuzzRange);\n          ctx.drawImage(\n            offscreen,\n            0,\n            j,\n            offscreenWidth,\n            1,\n            dx,\n            j,\n            offscreenWidth,\n            1\n          );\n        }\n        animationFrameId = window.requestAnimationFrame(run);\n      };\n\n      run();\n\n      const isInsideTextArea = (x: number, y: number) =>\n        x >= interactiveLeft &&\n        x <= interactiveRight &&\n        y >= interactiveTop &&\n        y <= interactiveBottom;\n\n      const handleMouseMove = (e: MouseEvent) => {\n        if (!enableHover) return;\n        const rect = canvas.getBoundingClientRect();\n        const x = e.clientX - rect.left;\n        const y = e.clientY - rect.top;\n        isHovering = isInsideTextArea(x, y);\n      };\n\n      const handleMouseLeave = () => {\n        isHovering = false;\n      };\n\n      const handleTouchMove = (e: TouchEvent) => {\n        if (!enableHover) return;\n        e.preventDefault();\n        const rect = canvas.getBoundingClientRect();\n        const touch = e.touches[0];\n        const x = touch.clientX - rect.left;\n        const y = touch.clientY - rect.top;\n        isHovering = isInsideTextArea(x, y);\n      };\n\n      const handleTouchEnd = () => {\n        isHovering = false;\n      };\n\n      if (enableHover) {\n        canvas.addEventListener(\"mousemove\", handleMouseMove);\n        canvas.addEventListener(\"mouseleave\", handleMouseLeave);\n        canvas.addEventListener(\"touchmove\", handleTouchMove, {\n          passive: false,\n        });\n        canvas.addEventListener(\"touchend\", handleTouchEnd);\n      }\n\n      const cleanup = () => {\n        window.cancelAnimationFrame(animationFrameId);\n        if (enableHover) {\n          canvas.removeEventListener(\"mousemove\", handleMouseMove);\n          canvas.removeEventListener(\"mouseleave\", handleMouseLeave);\n          canvas.removeEventListener(\"touchmove\", handleTouchMove);\n          canvas.removeEventListener(\"touchend\", handleTouchEnd);\n        }\n      };\n\n      canvas.cleanupFuzzyText = cleanup;\n    };\n\n    init();\n\n    return () => {\n      isCancelled = true;\n      window.cancelAnimationFrame(animationFrameId);\n      if (canvas && canvas.cleanupFuzzyText) {\n        canvas.cleanupFuzzyText();\n      }\n    };\n  }, [\n    children,\n    fontSize,\n    fontWeight,\n    fontFamily,\n    color,\n    enableHover,\n    baseIntensity,\n    hoverIntensity,\n  ]);\n\n  return <canvas ref={canvasRef} />;\n};\n\nexport default FuzzyText;\n"], "names": [], "mappings": ";;;;AAAA;;;;AAaA,MAAM,YAAsC,CAAC,EAC3C,QAAQ,EACR,WAAW,wBAAwB,EACnC,aAAa,GAAG,EAChB,aAAa,SAAS,EACtB,QAAQ,MAAM,EACd,cAAc,IAAI,EAClB,gBAAgB,IAAI,EACpB,iBAAiB,GAAG,EACrB;;IACC,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAErB;IAEF,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI;YACJ,IAAI,cAAc;YAClB,MAAM,SAAS,UAAU,OAAO;YAChC,IAAI,CAAC,QAAQ;YAEb,MAAM;4CAAO;oBACX,IAAI,SAAS,KAAK,EAAE,OAAO;wBACzB,MAAM,SAAS,KAAK,CAAC,KAAK;oBAC5B;oBACA,IAAI,aAAa;oBAEjB,MAAM,MAAM,OAAO,UAAU,CAAC;oBAC9B,IAAI,CAAC,KAAK;oBAEV,MAAM,qBACJ,eAAe,YACX,OAAO,gBAAgB,CAAC,QAAQ,UAAU,IAAI,eAC9C;oBAEN,MAAM,cACJ,OAAO,aAAa,WAAW,GAAG,SAAS,EAAE,CAAC,GAAG;oBACnD,IAAI;oBACJ,IAAI,OAAO,aAAa,UAAU;wBAChC,kBAAkB;oBACpB,OAAO;wBACL,MAAM,OAAO,SAAS,aAAa,CAAC;wBACpC,KAAK,KAAK,CAAC,QAAQ,GAAG;wBACtB,SAAS,IAAI,CAAC,WAAW,CAAC;wBAC1B,MAAM,eAAe,OAAO,gBAAgB,CAAC,MAAM,QAAQ;wBAC3D,kBAAkB,WAAW;wBAC7B,SAAS,IAAI,CAAC,WAAW,CAAC;oBAC5B;oBAEA,MAAM,OAAO,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAU,IAAI,CAAC;oBAEnD,MAAM,YAAY,SAAS,aAAa,CAAC;oBACzC,MAAM,SAAS,UAAU,UAAU,CAAC;oBACpC,IAAI,CAAC,QAAQ;oBAEb,OAAO,IAAI,GAAG,GAAG,WAAW,CAAC,EAAE,YAAY,CAAC,EAAE,oBAAoB;oBAClE,OAAO,YAAY,GAAG;oBACtB,MAAM,UAAU,OAAO,WAAW,CAAC;oBAEnC,MAAM,aAAa,QAAQ,qBAAqB,IAAI;oBACpD,MAAM,cAAc,QAAQ,sBAAsB,IAAI,QAAQ,KAAK;oBACnE,MAAM,eAAe,QAAQ,uBAAuB,IAAI;oBACxD,MAAM,gBACJ,QAAQ,wBAAwB,IAAI,kBAAkB;oBAExD,MAAM,oBAAoB,KAAK,IAAI,CAAC,aAAa;oBACjD,MAAM,cAAc,KAAK,IAAI,CAAC,eAAe;oBAE7C,MAAM,mBAAmB;oBACzB,MAAM,iBAAiB,oBAAoB;oBAE3C,UAAU,KAAK,GAAG;oBAClB,UAAU,MAAM,GAAG;oBAEnB,MAAM,UAAU,mBAAmB;oBACnC,OAAO,IAAI,GAAG,GAAG,WAAW,CAAC,EAAE,YAAY,CAAC,EAAE,oBAAoB;oBAClE,OAAO,YAAY,GAAG;oBACtB,OAAO,SAAS,GAAG;oBACnB,OAAO,QAAQ,CAAC,MAAM,UAAU,YAAY;oBAE5C,MAAM,mBAAmB;oBACzB,MAAM,iBAAiB;oBACvB,OAAO,KAAK,GAAG,iBAAiB,mBAAmB;oBACnD,OAAO,MAAM,GAAG,cAAc,iBAAiB;oBAC/C,IAAI,SAAS,CAAC,kBAAkB;oBAEhC,MAAM,kBAAkB,mBAAmB;oBAC3C,MAAM,iBAAiB;oBACvB,MAAM,mBAAmB,kBAAkB;oBAC3C,MAAM,oBAAoB,iBAAiB;oBAE3C,IAAI,aAAa;oBACjB,MAAM,YAAY;oBAElB,MAAM;wDAAM;4BACV,IAAI,aAAa;4BACjB,IAAI,SAAS,CACX,CAAC,WACD,CAAC,WACD,iBAAiB,IAAI,WACrB,cAAc,IAAI;4BAEpB,MAAM,YAAY,aAAa,iBAAiB;4BAChD,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,IAAK;gCACpC,MAAM,KAAK,KAAK,KAAK,CAAC,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gCAC1D,IAAI,SAAS,CACX,WACA,GACA,GACA,gBACA,GACA,IACA,GACA,gBACA;4BAEJ;4BACA,mBAAmB,OAAO,qBAAqB,CAAC;wBAClD;;oBAEA;oBAEA,MAAM;qEAAmB,CAAC,GAAW,IACnC,KAAK,mBACL,KAAK,oBACL,KAAK,kBACL,KAAK;;oBAEP,MAAM;oEAAkB,CAAC;4BACvB,IAAI,CAAC,aAAa;4BAClB,MAAM,OAAO,OAAO,qBAAqB;4BACzC,MAAM,IAAI,EAAE,OAAO,GAAG,KAAK,IAAI;4BAC/B,MAAM,IAAI,EAAE,OAAO,GAAG,KAAK,GAAG;4BAC9B,aAAa,iBAAiB,GAAG;wBACnC;;oBAEA,MAAM;qEAAmB;4BACvB,aAAa;wBACf;;oBAEA,MAAM;oEAAkB,CAAC;4BACvB,IAAI,CAAC,aAAa;4BAClB,EAAE,cAAc;4BAChB,MAAM,OAAO,OAAO,qBAAqB;4BACzC,MAAM,QAAQ,EAAE,OAAO,CAAC,EAAE;4BAC1B,MAAM,IAAI,MAAM,OAAO,GAAG,KAAK,IAAI;4BACnC,MAAM,IAAI,MAAM,OAAO,GAAG,KAAK,GAAG;4BAClC,aAAa,iBAAiB,GAAG;wBACnC;;oBAEA,MAAM;mEAAiB;4BACrB,aAAa;wBACf;;oBAEA,IAAI,aAAa;wBACf,OAAO,gBAAgB,CAAC,aAAa;wBACrC,OAAO,gBAAgB,CAAC,cAAc;wBACtC,OAAO,gBAAgB,CAAC,aAAa,iBAAiB;4BACpD,SAAS;wBACX;wBACA,OAAO,gBAAgB,CAAC,YAAY;oBACtC;oBAEA,MAAM;4DAAU;4BACd,OAAO,oBAAoB,CAAC;4BAC5B,IAAI,aAAa;gCACf,OAAO,mBAAmB,CAAC,aAAa;gCACxC,OAAO,mBAAmB,CAAC,cAAc;gCACzC,OAAO,mBAAmB,CAAC,aAAa;gCACxC,OAAO,mBAAmB,CAAC,YAAY;4BACzC;wBACF;;oBAEA,OAAO,gBAAgB,GAAG;gBAC5B;;YAEA;YAEA;uCAAO;oBACL,cAAc;oBACd,OAAO,oBAAoB,CAAC;oBAC5B,IAAI,UAAU,OAAO,gBAAgB,EAAE;wBACrC,OAAO,gBAAgB;oBACzB;gBACF;;QACF;8BAAG;QACD;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,qBAAO,6LAAC;QAAO,KAAK;;;;;;AACtB;GApMM;KAAA;uCAsMS"}}, {"offset": {"line": 183, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 189, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/src/components/ui/text-randomized.tsx"], "sourcesContent": ["\"use client\";\nimport { useEffect, useState, useCallback } from \"react\";\n\nconst lettersAndSymbols = \"abcdefghijklmnopqrstuvwxyz!@#$%^&*-_+=;:<>,\";\n\ninterface AnimatedTextProps {\n  text: string;\n  className?: string;\n}\n\nexport function RandomizedTextEffect({\n  text,\n  className = \"\",\n}: AnimatedTextProps) {\n  const [animatedText, setAnimatedText] = useState(\"\");\n  const [hasSeenAnimation, setHasSeenAnimation] = useState<boolean>(false);\n\n  const getRandomChar = useCallback(\n    () =>\n      lettersAndSymbols[Math.floor(Math.random() * lettersAndSymbols.length)],\n    [],\n  );\n\n  const animateText = useCallback(async () => {\n    const duration = 30;\n    const revealDuration = 40;\n    const initialRandomDuration = 300;\n\n    const generateRandomText = () =>\n      text\n        .split(\"\")\n        .map(() => getRandomChar())\n        .join(\"\");\n\n    setAnimatedText(generateRandomText());\n\n    const endTime = Date.now() + initialRandomDuration;\n    while (Date.now() < endTime) {\n      await new Promise((resolve) => setTimeout(resolve, duration));\n      setAnimatedText(generateRandomText());\n    }\n\n    for (let i = 0; i < text.length; i++) {\n      await new Promise((resolve) => setTimeout(resolve, revealDuration));\n      setAnimatedText(\n        (prevText) =>\n          text.slice(0, i + 1) +\n          prevText\n            .slice(i + 1)\n            .split(\"\")\n            .map(() => getRandomChar())\n            .join(\"\"),\n      );\n    }\n  }, [text, getRandomChar]);\n\n  useEffect(() => {\n    const isFirstVisit = !localStorage.getItem(\"hasSeenAnimation\");\n\n    if (isFirstVisit) {\n      animateText();\n      localStorage.setItem(\"hasSeenAnimation\", \"true\");\n    } else {\n      setHasSeenAnimation(true);\n    }\n\n    const handleBeforeUnload = () => {\n      localStorage.removeItem(\"hasSeenAnimation\");\n    };\n\n    window.addEventListener(\"beforeunload\", handleBeforeUnload);\n\n    return () => {\n      window.removeEventListener(\"beforeunload\", handleBeforeUnload);\n    };\n  }, [animateText]);\n\n  return (\n    <div className={`relative inline-block ${className}`}>\n      {hasSeenAnimation ? text : animatedText}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;;;AADA;;AAGA,MAAM,oBAAoB;AAOnB,SAAS,qBAAqB,EACnC,IAAI,EACJ,YAAY,EAAE,EACI;;IAClB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAElE,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2DAC9B,IACE,iBAAiB,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,kBAAkB,MAAM,EAAE;0DACzE,EAAE;IAGJ,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yDAAE;YAC9B,MAAM,WAAW;YACjB,MAAM,iBAAiB;YACvB,MAAM,wBAAwB;YAE9B,MAAM;oFAAqB,IACzB,KACG,KAAK,CAAC,IACN,GAAG;4FAAC,IAAM;2FACV,IAAI,CAAC;;YAEV,gBAAgB;YAEhB,MAAM,UAAU,KAAK,GAAG,KAAK;YAC7B,MAAO,KAAK,GAAG,KAAK,QAAS;gBAC3B,MAAM,IAAI;qEAAQ,CAAC,UAAY,WAAW,SAAS;;gBACnD,gBAAgB;YAClB;YAEA,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;gBACpC,MAAM,IAAI;qEAAQ,CAAC,UAAY,WAAW,SAAS;;gBACnD;qEACE,CAAC,WACC,KAAK,KAAK,CAAC,GAAG,IAAI,KAClB,SACG,KAAK,CAAC,IAAI,GACV,KAAK,CAAC,IACN,GAAG;6EAAC,IAAM;4EACV,IAAI,CAAC;;YAEd;QACF;wDAAG;QAAC;QAAM;KAAc;IAExB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;YACR,MAAM,eAAe,CAAC,aAAa,OAAO,CAAC;YAE3C,IAAI,cAAc;gBAChB;gBACA,aAAa,OAAO,CAAC,oBAAoB;YAC3C,OAAO;gBACL,oBAAoB;YACtB;YAEA,MAAM;qEAAqB;oBACzB,aAAa,UAAU,CAAC;gBAC1B;;YAEA,OAAO,gBAAgB,CAAC,gBAAgB;YAExC;kDAAO;oBACL,OAAO,mBAAmB,CAAC,gBAAgB;gBAC7C;;QACF;yCAAG;QAAC;KAAY;IAEhB,qBACE,6LAAC;QAAI,WAAW,CAAC,sBAAsB,EAAE,WAAW;kBACjD,mBAAmB,OAAO;;;;;;AAGjC;GAxEgB;KAAA"}}, {"offset": {"line": 279, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 285, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/templgen/src/app/not-found.tsx"], "sourcesContent": ["\"use client\";\n\nimport { But<PERSON> } from \"@/components/ui\";\nimport FuzzyText from \"@/components/ui/FuzzyText\";\nimport { RandomizedTextEffect } from \"@/components/ui/text-randomized\";\nimport Link from \"next/link\";\nimport { IoArrowBack } from \"react-icons/io5\";\nexport default function NotFoundPage() {\n  return (\n    <>\n      {/* Add structured data for the 404 page */}\n\n      <div className=\"fixed w-screen h-screen bg-[var(--background)]  flex flex-col justify-center items-center  z-50 inset-0\">\n        <div className=\"flex flex-col items-center justify-center text-center\">\n          <FuzzyText\n            fontSize={50}\n            baseIntensity={0.2}\n            hoverIntensity={2}\n            enableHover={false}\n          >\n            404 | Page Not Found\n          </FuzzyText>\n\n          <RandomizedTextEffect\n            className=\"mt-4 text-xl text-[var(--paragraph)]\"\n            text={\"The page you are looking for doesn't exist.\"}\n          />\n          <div className=\"mt-8 w-max\">\n            <Link href=\"/\">\n              <Button>\n                <IoArrowBack />\n                <span>Back To HomePage</span>\n              </Button>\n            </Link>\n          </div>\n        </div>\n      </div>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAHA;AAIA;AANA;;;;;;;AAOe,SAAS;IACtB,qBACE;kBAGE,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,wIAAA,CAAA,UAAS;wBACR,UAAU;wBACV,eAAe;wBACf,gBAAgB;wBAChB,aAAa;kCACd;;;;;;kCAID,6LAAC,iJAAA,CAAA,uBAAoB;wBACnB,WAAU;wBACV,MAAM;;;;;;kCAER,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;sCACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;;kDACL,6LAAC,kJAAA,CAAA,cAAW;;;;;kDACZ,6LAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtB;KAhCwB"}}, {"offset": {"line": 381, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}